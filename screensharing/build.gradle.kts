plugins {
    alias(libs.plugins.library)
    alias(libs.plugins.kotlinAndroid)
}

private val pkgName = "io.agora.rtc.ss"
android.buildFeatures.buildConfig = true

android {
    namespace = pkgName
    compileSdk = libs.versions.compileSdkVersion.get().toInt()

    defaultConfig {
        minSdk = libs.versions.minSdkVersion.get().toInt()

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"

        setFlavorDimensions(listOf("constantEnv"))
    }
    buildTypes {
        create("unsigned") {
            isMinifyEnabled = false
        }
    }

    productFlavors {
        create("envProduct") {
            dimension = "constantEnv"
            buildConfigField("Integer", "CONSTANT_ENV", "0")
        }
        create("envTest") {
            dimension = "constantEnv"
            buildConfigField("Integer", "CONSTANT_ENV", "1")
        }
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }
    kotlinOptions {
        jvmTarget = "11"
    }
    buildFeatures {
        aidl = true
    }

}

dependencies {
    implementation(fileTree(baseDir = "libs") {
        include("*.jar")
    })
    api("io.agora.rtc:agora-special-full:3.8.201.2.2")

    implementation(libs.androidx.appcompat)
    implementation(libs.czurUtil)
}
