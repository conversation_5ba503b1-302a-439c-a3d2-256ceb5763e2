25_06_13 09:10:46.909 DEBUG BootReceiver NoticeCenter BootReceiver onReceive
25_06_13 09:11:34.825 DEBUG BootInterceptor Launcher 已经启动
25_06_13 09:11:34.832 VERBOSE BootInterceptor 启动监控Service
25_06_13 09:11:34.835 VERBOSE BootInterceptor 启动服务:OTA 升级服务
25_06_13 09:11:34.842 VERBOSE BootInterceptor 重置对焦状态
25_06_13 09:11:34.843 VERBOSE BootInterceptor 重置本地录制状态
25_06_13 09:11:34.845 VERBOSE BootInterceptor 重置本地录制状态
25_06_13 09:11:34.846 VERBOSE WatchService startWatchingService
25_06_13 09:11:34.847 VERBOSE BootInterceptor 重置一些默认的值
25_06_13 09:11:34.848 VERBOSE ProviderUtil ContentValues: vendor.czur.transcriptionIsTranslating=false  cv.size: 1
25_06_13 09:11:34.849 VERBOSE ProviderUtil Key: vendor.czur.transcriptionIsTranslating, Value: false
25_06_13 09:11:34.850 VERBOSE ProviderUtil update uri: content://com.czur.starry.device.transcription.provider.TranscriptionProvider/getSPValue , contentValues Size: 1
25_06_13 09:11:34.851 VERBOSE BootInterceptor 启动服务:内存信息服务
25_06_13 09:11:34.852 VERBOSE BootInterceptor 检查通知
25_06_13 09:11:34.852 VERBOSE ProviderUtil ContentValues: localMeetingRecordShare=true  cv.size: 1
25_06_13 09:11:34.853 DEBUG PopupMsgManager 检查通知
25_06_13 09:11:34.854 VERBOSE ProviderUtil Key: localMeetingRecordShare, Value: true
25_06_13 09:11:34.855 VERBOSE ProviderUtil update uri: content://com.czur.starry.device.localmeetingrecord.LocalMeetingProvider/getSPValue , contentValues Size: 1
25_06_13 09:11:34.875 VERBOSE BootInterceptor 启动服务:手写板服务
25_06_13 09:11:34.878 VERBOSE ProviderUtil update result: 1
25_06_13 09:11:34.880 INFO TransHandler 更新成功, 更新缓存
25_06_13 09:11:34.881 VERBOSE ProviderUtil ContentValues: vendor.czur.transcription.isStopTrans=false  cv.size: 1
25_06_13 09:11:34.884 VERBOSE ProviderUtil Key: vendor.czur.transcription.isStopTrans, Value: false
25_06_13 09:11:34.885 VERBOSE ProviderUtil update uri: content://com.czur.starry.device.transcription.provider.TranscriptionProvider/getSPValue , contentValues Size: 1
25_06_13 09:11:34.886 VERBOSE BootInterceptor 启动服务:file 监听未读文件服务
25_06_13 09:11:34.888 VERBOSE ProviderUtil update result: 1
25_06_13 09:11:34.890 INFO TransHandler 更新成功, 更新缓存
25_06_13 09:11:34.891 VERBOSE ProviderUtil ContentValues: vendor.czur.transcription.isShowSubtitles=false  cv.size: 1
25_06_13 09:11:34.893 VERBOSE ProviderUtil Key: vendor.czur.transcription.isShowSubtitles, Value: false
25_06_13 09:11:34.894 VERBOSE ProviderUtil update uri: content://com.czur.starry.device.transcription.provider.TranscriptionProvider/getSPValue , contentValues Size: 1
25_06_13 09:11:34.897 VERBOSE ProviderUtil update result: 1
25_06_13 09:11:34.898 INFO TransHandler 更新成功, 更新缓存
25_06_13 09:11:34.900 VERBOSE BootInterceptor 启动服务:file 监听文件传输服务
25_06_13 09:11:34.901 VERBOSE BootInterceptor 启动服务:file 监听妙传文件定期清理服务
25_06_13 09:11:34.903 VERBOSE ProviderUtil update result: 1
25_06_13 09:11:34.904 INFO MeetingHandler 更新成功, 更新缓存
25_06_13 09:11:34.930 VERBOSE BootInterceptor 启动服务:DataGather 数据收集服务
25_06_13 09:11:34.931 VERBOSE PopupMsgManager 需要展示的弹窗: null
25_06_13 09:11:34.932 VERBOSE PopupMsgManager 没有需要展示的通知
25_06_13 09:11:34.939 VERBOSE BootInterceptor 启动服务:语音助手服务
25_06_13 09:11:55.747 VERBOSE AlertWindowService 唤醒屏幕: com.czur.starry.device.noticecenter.hw.HWConnectAlertWindow
25_06_13 09:11:55.812 VERBOSE HWConnectAlertWindow 注册hover监听
25_06_13 09:11:55.815 VERBOSE HWConnectAlertWindow 5s后关闭
25_06_13 09:11:55.817 VERBOSE HWConnectAlertWindow onDataRefresh: SCREEN_SAVER
25_06_13 09:12:00.816 VERBOSE HWConnectAlertWindow 已经显示5s, stopSelf
25_06_13 09:28:49.808 DEBUG BootReceiver NoticeCenter BootReceiver onReceive
25_06_13 09:28:50.868 DEBUG BootInterceptor Launcher 已经启动
25_06_13 09:28:50.882 VERBOSE BootInterceptor 重置本地录制状态
25_06_13 09:28:50.885 VERBOSE ProviderUtil ContentValues: vendor.czur.transcriptionIsTranslating=false  cv.size: 1
25_06_13 09:28:50.886 VERBOSE ProviderUtil Key: vendor.czur.transcriptionIsTranslating, Value: false
25_06_13 09:28:50.887 VERBOSE ProviderUtil update uri: content://com.czur.starry.device.transcription.provider.TranscriptionProvider/getSPValue , contentValues Size: 1
25_06_13 09:28:50.914 VERBOSE ProviderUtil update result: 1
25_06_13 09:28:50.915 INFO TransHandler 更新成功, 更新缓存
25_06_13 09:28:50.916 VERBOSE ProviderUtil ContentValues: vendor.czur.transcription.isStopTrans=false  cv.size: 1
25_06_13 09:28:50.917 VERBOSE ProviderUtil Key: vendor.czur.transcription.isStopTrans, Value: false
25_06_13 09:28:50.918 VERBOSE ProviderUtil update uri: content://com.czur.starry.device.transcription.provider.TranscriptionProvider/getSPValue , contentValues Size: 1
25_06_13 09:28:50.921 VERBOSE BootInterceptor 重置本地录制状态
25_06_13 09:28:50.928 VERBOSE BootInterceptor 启动监控Service
25_06_13 09:28:50.929 VERBOSE BootInterceptor 启动服务:OTA 升级服务
25_06_13 09:28:50.930 VERBOSE WatchService startWatchingService
25_06_13 09:28:50.930 VERBOSE BootInterceptor 重置对焦状态
25_06_13 09:28:50.933 VERBOSE BootInterceptor 重置一些默认的值
25_06_13 09:28:50.936 VERBOSE BootInterceptor 检查通知
25_06_13 09:28:50.937 VERBOSE ProviderUtil update result: 1
25_06_13 09:28:50.938 INFO TransHandler 更新成功, 更新缓存
25_06_13 09:28:50.939 VERBOSE ProviderUtil ContentValues: vendor.czur.transcription.isShowSubtitles=false  cv.size: 1
25_06_13 09:28:50.942 VERBOSE ProviderUtil Key: vendor.czur.transcription.isShowSubtitles, Value: false
25_06_13 09:28:50.943 VERBOSE ProviderUtil update uri: content://com.czur.starry.device.transcription.provider.TranscriptionProvider/getSPValue , contentValues Size: 1
25_06_13 09:28:51.041 VERBOSE BootInterceptor 启动服务:内存信息服务
25_06_13 09:28:51.042 VERBOSE ProviderUtil update result: 1
25_06_13 09:28:51.043 INFO TransHandler 更新成功, 更新缓存
25_06_13 09:28:51.105 VERBOSE ProviderUtil ContentValues: localMeetingRecordShare=true  cv.size: 1
25_06_13 09:28:51.106 VERBOSE ProviderUtil Key: localMeetingRecordShare, Value: true
25_06_13 09:28:51.107 VERBOSE ProviderUtil update uri: content://com.czur.starry.device.localmeetingrecord.LocalMeetingProvider/getSPValue , contentValues Size: 1
25_06_13 09:28:51.108 DEBUG PopupMsgManager 检查通知
25_06_13 09:28:51.108 VERBOSE BootInterceptor 启动服务:手写板服务
25_06_13 09:28:51.109 VERBOSE ProviderUtil update result: 1
25_06_13 09:28:51.110 INFO MeetingHandler 更新成功, 更新缓存
25_06_13 09:28:51.111 VERBOSE BootInterceptor 启动服务:file 监听未读文件服务
25_06_13 09:28:51.112 VERBOSE BootInterceptor 启动服务:file 监听文件传输服务
25_06_13 09:28:51.113 VERBOSE BootInterceptor 启动服务:file 监听妙传文件定期清理服务
25_06_13 09:28:51.114 VERBOSE BootInterceptor 启动服务:DataGather 数据收集服务
25_06_13 09:28:51.115 VERBOSE BootInterceptor 启动服务:语音助手服务
25_06_13 09:28:51.116 VERBOSE PopupMsgManager 需要展示的弹窗: null
25_06_13 09:28:51.117 VERBOSE PopupMsgManager 没有需要展示的通知
