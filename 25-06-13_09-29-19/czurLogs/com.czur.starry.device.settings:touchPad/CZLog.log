25_06_13 09:10:46.457 DEBUG TouchPad-Service TouchPadService启动
25_06_13 09:10:46.476 DEBUG TouchPad-BluetoothUtil 蓝牙未打开, 开启蓝牙
25_06_13 09:10:46.546 DEBUG TouchPad-Service 当前状态:STATUS_INIT
25_06_13 09:10:46.548 DEBUG CustomShortcutKeyReceiver 注册自定义按键广播
25_06_13 09:10:46.549 VERBOSE TouchPad-Service TouchPad初始化
25_06_13 09:10:46.550 DEBUG TouchPad-Service 电量发生变化:-1
25_06_13 09:10:46.551 VERBOSE TouchPad-StatusBroadcastUtil updateBatteryLevel: -1, onPadDock: false
25_06_13 09:10:46.552 DEBUG TouchPad-Service 版本发生变化:
25_06_13 09:10:46.553 VERBOSE TouchPad-Service checkHasNewVersion:
25_06_13 09:10:46.555 VERBOSE TouchPad-Service 未获取触控板的版本号
25_06_13 09:10:46.556 DEBUG TouchPad-Service 连接状态发生改变:false
25_06_13 09:10:46.577 VERBOSE TouchPad-StatusBroadcastUtil updateStatus: false, lastConnectStatus: null
25_06_13 09:10:46.594 VERBOSE ProviderUtil ContentValues: touchPadHasNewVersion=false  cv.size: 1
25_06_13 09:10:46.595 VERBOSE ProviderUtil Key: touchPadHasNewVersion, Value: false
25_06_13 09:10:46.597 VERBOSE ProviderUtil update uri: content://com.czur.starry.device.update.otaprovider/getSPValue , contentValues Size: 1
25_06_13 09:10:46.611 VERBOSE ProviderUtil ContentValues: touchPadHasNewVersion=false  cv.size: 1
25_06_13 09:10:46.612 VERBOSE ProviderUtil Key: touchPadHasNewVersion, Value: false
25_06_13 09:10:46.620 VERBOSE ProviderUtil update uri: content://com.czur.starry.device.update.otaprovider/getSPValue , contentValues Size: 1
25_06_13 09:10:46.625 VERBOSE TouchPad-BluetoothUtil 没有接到触控板(by normal)
25_06_13 09:10:46.632 VERBOSE TouchPad-Service 手写板位置状态:false
25_06_13 09:10:46.633 VERBOSE TouchPad-Service 初始连接状态:false
25_06_13 09:10:46.652 DEBUG TouchPad-StatusManager 状态生成:STATUS_DIS_LEAVE,触控板位置:false,连接状态:false
25_06_13 09:10:46.653 VERBOSE TouchPad-Service 注册触控板状态改变回调
25_06_13 09:10:46.659 DEBUG TouchPad-Service 当前状态:STATUS_DIS_LEAVE
25_06_13 09:10:46.661 DEBUG TouchPad-Service 未连接, 没有触控板
25_06_13 09:10:46.757 VERBOSE ProviderUtil update result: 1
25_06_13 09:10:46.758 INFO OTAHandler 更新成功, 更新缓存
25_06_13 09:10:46.765 VERBOSE ProviderUtil update result: 1
25_06_13 09:10:46.766 INFO OTAHandler 更新成功, 更新缓存
25_06_13 09:10:49.359 VERBOSE TouchPad-BluetoothUtil 接到蓝牙广播:android.bluetooth.adapter.action.SCAN_MODE_CHANGED
25_06_13 09:10:49.362 DEBUG TouchPad-BluetoothUtil ACTION_SCAN_MODE_CHANGED
25_06_13 09:10:49.387 VERBOSE TouchPad-BluetoothUtil 接到蓝牙广播:android.bluetooth.adapter.action.STATE_CHANGED
25_06_13 09:10:49.535 VERBOSE TouchPad-BluetoothUtil 接到蓝牙广播:android.bluetooth.adapter.action.SCAN_MODE_CHANGED
25_06_13 09:10:49.537 DEBUG TouchPad-BluetoothUtil ACTION_SCAN_MODE_CHANGED
25_06_13 09:10:49.597 VERBOSE TouchPad-BluetoothUtil 接到蓝牙广播:android.bluetooth.adapter.action.STATE_CHANGED
25_06_13 09:28:50.818 DEBUG TouchPad-Service TouchPadService启动
25_06_13 09:28:50.868 DEBUG TouchPad-Service 当前状态:STATUS_INIT
25_06_13 09:28:50.869 VERBOSE TouchPad-Service TouchPad初始化
25_06_13 09:28:50.870 DEBUG TouchPad-Service 连接状态发生改变:false
25_06_13 09:28:50.872 VERBOSE TouchPad-StatusBroadcastUtil updateStatus: false, lastConnectStatus: null
25_06_13 09:28:50.875 VERBOSE TouchPad-Service 手写板位置状态:false
25_06_13 09:28:50.876 VERBOSE ProviderUtil ContentValues: touchPadHasNewVersion=false  cv.size: 1
25_06_13 09:28:50.878 VERBOSE ProviderUtil Key: touchPadHasNewVersion, Value: false
25_06_13 09:28:50.881 VERBOSE ProviderUtil update uri: content://com.czur.starry.device.update.otaprovider/getSPValue , contentValues Size: 1
25_06_13 09:28:50.883 DEBUG CustomShortcutKeyReceiver 注册自定义按键广播
25_06_13 09:28:50.894 DEBUG TouchPad-Service 电量发生变化:-1
25_06_13 09:28:50.902 VERBOSE TouchPad-StatusBroadcastUtil updateBatteryLevel: -1, onPadDock: false
25_06_13 09:28:50.904 DEBUG TouchPad-Service 版本发生变化:
25_06_13 09:28:50.906 VERBOSE TouchPad-Service checkHasNewVersion:
25_06_13 09:28:50.908 VERBOSE TouchPad-Service 未获取触控板的版本号
25_06_13 09:28:50.909 VERBOSE ProviderUtil ContentValues: touchPadHasNewVersion=false  cv.size: 1
25_06_13 09:28:50.911 VERBOSE ProviderUtil Key: touchPadHasNewVersion, Value: false
25_06_13 09:28:50.912 VERBOSE ProviderUtil update uri: content://com.czur.starry.device.update.otaprovider/getSPValue , contentValues Size: 1
25_06_13 09:28:50.913 VERBOSE TouchPad-BluetoothUtil 没有接到触控板(by normal)
25_06_13 09:28:50.915 VERBOSE TouchPad-Service 初始连接状态:false
25_06_13 09:28:50.937 DEBUG TouchPad-StatusManager 状态生成:STATUS_DIS_LEAVE,触控板位置:false,连接状态:false
25_06_13 09:28:50.940 VERBOSE TouchPad-Service 注册触控板状态改变回调
25_06_13 09:28:50.941 DEBUG TouchPad-Service 当前状态:STATUS_DIS_LEAVE
25_06_13 09:28:50.942 DEBUG TouchPad-Service 未连接, 没有触控板
25_06_13 09:28:50.944 VERBOSE ProviderUtil update result: 1
25_06_13 09:28:50.945 INFO OTAHandler 更新成功, 更新缓存
25_06_13 09:28:50.947 VERBOSE ProviderUtil update result: 1
25_06_13 09:28:50.980 INFO OTAHandler 更新成功, 更新缓存
