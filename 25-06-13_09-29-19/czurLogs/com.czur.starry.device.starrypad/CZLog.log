25_06_13 09:11:34.111 DEBUG SPContentProvider query:content://com.czur.starry.device.starrypad.provider.StarryPadPaintProvider/getSPValue?paramKeyName=new_paint_file_name&paramKeyDefault=-调用者:com.czur.starry.device.starrypad
25_06_13 09:11:34.934 DEBUG WritePadService WritePadService onCreate
25_06_13 09:11:34.940 VERBOSE WPDeviceInfoManager loadWhiteListFromDisk
25_06_13 09:11:34.945 INFO WPDeviceInfoManager 需要初始化配对数据库
25_06_13 09:11:34.948 INFO WPDeviceInfoManager 之前没有白名单, 初始化白名单
25_06_13 09:11:34.949 INFO WPDeviceInfoManager 从蓝牙初始化绑定数据库
25_06_13 09:11:34.957 VERBOSE WPDeviceInfoManager 初始化白名单: 
25_06_13 09:11:34.993 VERBOSE ShutdownReceiver 注册关机广播
25_06_13 09:11:34.995 DEBUG WritePadNettyServer 启动Netty
25_06_13 09:11:34.997 DEBUG LogcatNettyLogger format:-Dio.netty.eventLoopThreads: 16
25_06_13 09:11:35.007 DEBUG LogcatNettyLogger format:-Dio.netty.globalEventExecutor.quietPeriodSeconds: 1
25_06_13 09:11:35.012 DEBUG LogcatNettyLogger format:-Dio.netty.threadLocalMap.stringBuilder.initialSize: 1024
25_06_13 09:11:35.014 DEBUG LogcatNettyLogger format:-Dio.netty.threadLocalMap.stringBuilder.maxSize: 4096
25_06_13 09:11:35.016 DEBUG WritePadService 收到模式切换请求: null
25_06_13 09:11:35.017 DEBUG WritePadService 广播serverMode: MODE_MOUSE
25_06_13 09:11:35.026 DEBUG LogcatNettyLogger format:-Dio.netty.noUnsafe: false
25_06_13 09:11:35.030 DEBUG LogcatNettyLogger Platform: Android
25_06_13 09:11:35.031 DEBUG LogcatNettyLogger format:Java version: 6
25_06_13 09:11:35.032 DEBUG LogcatNettyLogger Platform: Android
25_06_13 09:11:35.038 DEBUG LogcatNettyLogger sun.misc.Unsafe.theUnsafe: available
25_06_13 09:11:35.040 DEBUG LogcatNettyLogger sun.misc.Unsafe method unavailable:java.lang.NoSuchMethodException: sun.misc.Unsafe.copyMemory [class java.lang.Object, long, class java.lang.Object, long, long]
25_06_13 09:11:35.040 DEBUG LogcatNettyLogger 	at java.lang.Class.getMethod(Class.java:2937)
25_06_13 09:11:35.040 DEBUG LogcatNettyLogger 	at java.lang.Class.getDeclaredMethod(Class.java:2914)
25_06_13 09:11:35.040 DEBUG LogcatNettyLogger 	at io.netty.util.internal.PlatformDependent0$2.run(PlatformDependent0.java:156)
25_06_13 09:11:35.040 DEBUG LogcatNettyLogger 	at java.security.AccessController.doPrivileged(AccessController.java:46)
25_06_13 09:11:35.040 DEBUG LogcatNettyLogger 	at io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:150)
25_06_13 09:11:35.040 DEBUG LogcatNettyLogger 	at io.netty.util.internal.PlatformDependent.isAndroid(PlatformDependent.java:334)
25_06_13 09:11:35.040 DEBUG LogcatNettyLogger 	at io.netty.util.internal.PlatformDependent.<clinit>(PlatformDependent.java:90)
25_06_13 09:11:35.040 DEBUG LogcatNettyLogger 	at io.netty.channel.nio.NioEventLoop.<clinit>(NioEventLoop.java:84)
25_06_13 09:11:35.040 DEBUG LogcatNettyLogger 	at io.netty.channel.nio.NioEventLoopGroup.newChild(NioEventLoopGroup.java:182)
25_06_13 09:11:35.040 DEBUG LogcatNettyLogger 	at io.netty.channel.nio.NioEventLoopGroup.newChild(NioEventLoopGroup.java:38)
25_06_13 09:11:35.040 DEBUG LogcatNettyLogger 	at io.netty.util.concurrent.MultithreadEventExecutorGroup.<init>(MultithreadEventExecutorGroup.java:84)
25_06_13 09:11:35.040 DEBUG LogcatNettyLogger 	at io.netty.util.concurrent.MultithreadEventExecutorGroup.<init>(MultithreadEventExecutorGroup.java:60)
25_06_13 09:11:35.040 DEBUG LogcatNettyLogger 	at io.netty.channel.MultithreadEventLoopGroup.<init>(MultithreadEventLoopGroup.java:52)
25_06_13 09:11:35.040 DEBUG LogcatNettyLogger 	at io.netty.channel.nio.NioEventLoopGroup.<init>(NioEventLoopGroup.java:97)
25_06_13 09:11:35.040 DEBUG LogcatNettyLogger 	at io.netty.channel.nio.NioEventLoopGroup.<init>(NioEventLoopGroup.java:92)
25_06_13 09:11:35.040 DEBUG LogcatNettyLogger 	at io.netty.channel.nio.NioEventLoopGroup.<init>(NioEventLoopGroup.java:73)
25_06_13 09:11:35.040 DEBUG LogcatNettyLogger 	at io.netty.channel.nio.NioEventLoopGroup.<init>(NioEventLoopGroup.java:53)
25_06_13 09:11:35.040 DEBUG LogcatNettyLogger 	at io.netty.channel.nio.NioEventLoopGroup.<init>(NioEventLoopGroup.java:45)
25_06_13 09:11:35.040 DEBUG LogcatNettyLogger 	at com.czur.starry.device.starrypad.hardware.trans.netty.WritePadNettyServer$startServer$2.invokeSuspend(WritePadNettyServer.kt:100)
25_06_13 09:11:35.040 DEBUG LogcatNettyLogger 	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
25_06_13 09:11:35.040 DEBUG LogcatNettyLogger 	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:100)
25_06_13 09:11:35.040 DEBUG LogcatNettyLogger 	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:124)
25_06_13 09:11:35.040 DEBUG LogcatNettyLogger 	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:89)
25_06_13 09:11:35.040 DEBUG LogcatNettyLogger 	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:586)
25_06_13 09:11:35.040 DEBUG LogcatNettyLogger 	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:820)
25_06_13 09:11:35.040 DEBUG LogcatNettyLogger 	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:717)
25_06_13 09:11:35.040 DEBUG LogcatNettyLogger 	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:704)
25_06_13 09:11:35.042 DEBUG LogcatNettyLogger format:java.nio.DirectByteBuffer.<init>(long, {int,long}): unavailable
25_06_13 09:11:35.043 DEBUG LogcatNettyLogger sun.misc.Unsafe: unavailable (Android)
25_06_13 09:11:35.045 DEBUG LogcatNettyLogger format:-Dio.netty.tmpdir: /data/user/0/com.czur.starry.device.starrypad/cache (java.io.tmpdir)
25_06_13 09:11:35.047 DEBUG LogcatNettyLogger format:-Dio.netty.maxDirectMemory: -1 bytes
25_06_13 09:11:35.049 DEBUG LogcatNettyLogger format:-Dio.netty.uninitializedArrayAllocationThreshold: -1
25_06_13 09:11:35.050 DEBUG LogcatNettyLogger format:-Dio.netty.noPreferDirect: true
25_06_13 09:11:35.052 INFO LogcatNettyLogger Your platform does not provide complete low-level API for accessing direct buffers reliably. Unless explicitly requested, heap buffer will always be preferred to avoid potential system instability.
25_06_13 09:11:35.054 DEBUG LogcatNettyLogger format:-Dio.netty.noKeySetOptimization: false
25_06_13 09:11:35.055 DEBUG LogcatNettyLogger format:-Dio.netty.selectorAutoRebuildThreshold: 512
25_06_13 09:11:35.057 DEBUG LogcatNettyLogger org.jctools-core.MpscChunkedArrayQueue: unavailable
25_06_13 09:11:35.058 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@a7fb5f
25_06_13 09:11:35.060 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@6175675
25_06_13 09:11:35.062 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@7c5a60a
25_06_13 09:11:35.064 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@233498
25_06_13 09:11:35.066 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@e93e4f1
25_06_13 09:11:35.067 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@82018d6
25_06_13 09:11:35.069 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@8bff757
25_06_13 09:11:35.070 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@b630244
25_06_13 09:11:35.072 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@5dcec62
25_06_13 09:11:35.074 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@4e2b1f3
25_06_13 09:11:35.075 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@baffeb0
25_06_13 09:11:35.077 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@5a94129
25_06_13 09:11:35.084 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@56c7a4f
25_06_13 09:11:35.085 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@48e55dc
25_06_13 09:11:35.087 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@2ebc6e5
25_06_13 09:11:35.088 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@3206c6b
25_06_13 09:11:35.092 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@154def8
25_06_13 09:11:35.100 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@6bcba36
25_06_13 09:11:35.101 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@adbed37
25_06_13 09:11:35.103 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@174f2a4
25_06_13 09:11:35.105 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@a9243c2
25_06_13 09:11:35.106 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@fea8809
25_06_13 09:11:35.107 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@8954c2f
25_06_13 09:11:35.109 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@21f23c
25_06_13 09:11:35.110 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@f78891a
25_06_13 09:11:35.112 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@4347c4b
25_06_13 09:11:35.118 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@539f628
25_06_13 09:11:35.119 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@b4c9227
25_06_13 09:11:35.121 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@54c4cd4
25_06_13 09:11:35.122 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@c872172
25_06_13 09:11:35.124 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@f8b6240
25_06_13 09:11:35.125 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@9a1e2be
25_06_13 09:11:35.126 DEBUG WritePadService hasDevicesScreenOnFlow:false
25_06_13 09:11:35.128 DEBUG LogcatNettyLogger Could not invoke ManagementFactory.getRuntimeMXBean().getName(); Android?java.lang.ClassNotFoundException: java.lang.management.ManagementFactory
25_06_13 09:11:35.128 DEBUG LogcatNettyLogger 	at java.lang.Class.classForName(Native Method)
25_06_13 09:11:35.128 DEBUG LogcatNettyLogger 	at java.lang.Class.forName(Class.java:536)
25_06_13 09:11:35.128 DEBUG LogcatNettyLogger 	at io.netty.channel.DefaultChannelId.jmxPid(DefaultChannelId.java:140)
25_06_13 09:11:35.128 DEBUG LogcatNettyLogger 	at io.netty.channel.DefaultChannelId.defaultProcessId(DefaultChannelId.java:187)
25_06_13 09:11:35.128 DEBUG LogcatNettyLogger 	at io.netty.channel.DefaultChannelId.<clinit>(DefaultChannelId.java:82)
25_06_13 09:11:35.128 DEBUG LogcatNettyLogger 	at io.netty.channel.AbstractChannel.newId(AbstractChannel.java:112)
25_06_13 09:11:35.128 DEBUG LogcatNettyLogger 	at io.netty.channel.AbstractChannel.<init>(AbstractChannel.java:72)
25_06_13 09:11:35.128 DEBUG LogcatNettyLogger 	at io.netty.channel.nio.AbstractNioChannel.<init>(AbstractNioChannel.java:80)
25_06_13 09:11:35.128 DEBUG LogcatNettyLogger 	at io.netty.channel.nio.AbstractNioMessageChannel.<init>(AbstractNioMessageChannel.java:42)
25_06_13 09:11:35.128 DEBUG LogcatNettyLogger 	at io.netty.channel.socket.nio.NioServerSocketChannel.<init>(NioServerSocketChannel.java:96)
25_06_13 09:11:35.128 DEBUG LogcatNettyLogger 	at io.netty.channel.socket.nio.NioServerSocketChannel.<init>(NioServerSocketChannel.java:89)
25_06_13 09:11:35.128 DEBUG LogcatNettyLogger 	at io.netty.channel.socket.nio.NioServerSocketChannel.<init>(NioServerSocketChannel.java:82)
25_06_13 09:11:35.128 DEBUG LogcatNettyLogger 	at io.netty.channel.socket.nio.NioServerSocketChannel.<init>(NioServerSocketChannel.java:75)
25_06_13 09:11:35.128 DEBUG LogcatNettyLogger 	at java.lang.reflect.Constructor.newInstance0(Native Method)
25_06_13 09:11:35.128 DEBUG LogcatNettyLogger 	at java.lang.reflect.Constructor.newInstance(Constructor.java:343)
25_06_13 09:11:35.128 DEBUG LogcatNettyLogger 	at io.netty.channel.ReflectiveChannelFactory.newChannel(ReflectiveChannelFactory.java:44)
25_06_13 09:11:35.128 DEBUG LogcatNettyLogger 	at io.netty.bootstrap.AbstractBootstrap.initAndRegister(AbstractBootstrap.java:326)
25_06_13 09:11:35.128 DEBUG LogcatNettyLogger 	at io.netty.bootstrap.AbstractBootstrap.doBind(AbstractBootstrap.java:288)
25_06_13 09:11:35.128 DEBUG LogcatNettyLogger 	at io.netty.bootstrap.AbstractBootstrap.bind(AbstractBootstrap.java:284)
25_06_13 09:11:35.128 DEBUG LogcatNettyLogger 	at io.netty.bootstrap.AbstractBootstrap.bind(AbstractBootstrap.java:262)
25_06_13 09:11:35.128 DEBUG LogcatNettyLogger 	at com.czur.starry.device.starrypad.hardware.trans.netty.WritePadNettyServer$startServer$2.invokeSuspend(WritePadNettyServer.kt:335)
25_06_13 09:11:35.128 DEBUG LogcatNettyLogger 	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
25_06_13 09:11:35.128 DEBUG LogcatNettyLogger 	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:100)
25_06_13 09:11:35.128 DEBUG LogcatNettyLogger 	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:124)
25_06_13 09:11:35.128 DEBUG LogcatNettyLogger 	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:89)
25_06_13 09:11:35.128 DEBUG LogcatNettyLogger 	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:586)
25_06_13 09:11:35.128 DEBUG LogcatNettyLogger 	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:820)
25_06_13 09:11:35.128 DEBUG LogcatNettyLogger 	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:717)
25_06_13 09:11:35.128 DEBUG LogcatNettyLogger 	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:704)
25_06_13 09:11:35.128 DEBUG LogcatNettyLogger Caused by: java.lang.ClassNotFoundException: java.lang.management.ManagementFactory
25_06_13 09:11:35.128 DEBUG LogcatNettyLogger 	... 29 more
25_06_13 09:11:35.131 DEBUG LogcatNettyLogger format:-Dio.netty.processId: 3428 (auto-detected)
25_06_13 09:11:35.134 DEBUG LogcatNettyLogger format:-Djava.net.preferIPv4Stack: false
25_06_13 09:11:35.135 DEBUG LogcatNettyLogger format:-Djava.net.preferIPv6Addresses: false
25_06_13 09:11:35.136 DEBUG LogcatNettyLogger format:Loopback interface: lo (lo, ::1)
25_06_13 09:11:35.140 DEBUG LogcatNettyLogger format:/proc/sys/net/core/somaxconn: 4096
25_06_13 09:11:35.142 DEBUG LogcatNettyLogger format:-Dio.netty.machineId: 9c:b8:b4:ff:fe:8e:35:e4 (auto-detected)
25_06_13 09:11:35.143 DEBUG LogcatNettyLogger -Dio.netty.initialSeedUniquifier: 0x5b16309e7e02ff63
25_06_13 09:11:35.144 DEBUG LogcatNettyLogger format:-Dio.netty.leakDetection.level: simple
25_06_13 09:11:35.146 DEBUG LogcatNettyLogger format:-Dio.netty.leakDetection.targetRecords: 4
25_06_13 09:11:35.150 DEBUG LogcatNettyLogger format:-Dio.netty.allocator.type: unpooled
25_06_13 09:11:35.151 DEBUG LogcatNettyLogger format:-Dio.netty.threadLocalDirectBufferSize: 0
25_06_13 09:11:35.152 DEBUG LogcatNettyLogger format:-Dio.netty.maxThreadLocalCharBufferSize: 16384
25_06_13 09:11:35.152 DEBUG LogcatNettyLogger format:-Dio.netty.bootstrap.extensions: {}
25_06_13 09:28:50.645 DEBUG SPContentProvider query:content://com.czur.starry.device.starrypad.provider.StarryPadPaintProvider/getSPValue?paramKeyName=new_paint_file_name&paramKeyDefault=-调用者:com.czur.starry.device.starrypad
25_06_13 09:28:51.049 DEBUG WritePadService WritePadService onCreate
25_06_13 09:28:51.051 VERBOSE WPDeviceInfoManager loadWhiteListFromDisk
25_06_13 09:28:51.079 VERBOSE WPDeviceInfoManager 不需要初始化配对设备数据库
25_06_13 09:28:51.206 DEBUG WritePadNettyServer 启动Netty
25_06_13 09:28:51.219 DEBUG LogcatNettyLogger format:-Dio.netty.eventLoopThreads: 16
25_06_13 09:28:51.221 DEBUG LogcatNettyLogger format:-Dio.netty.globalEventExecutor.quietPeriodSeconds: 1
25_06_13 09:28:51.224 DEBUG LogcatNettyLogger format:-Dio.netty.threadLocalMap.stringBuilder.initialSize: 1024
25_06_13 09:28:51.233 DEBUG LogcatNettyLogger format:-Dio.netty.threadLocalMap.stringBuilder.maxSize: 4096
25_06_13 09:28:51.239 DEBUG LogcatNettyLogger format:-Dio.netty.noUnsafe: false
25_06_13 09:28:51.241 DEBUG LogcatNettyLogger Platform: Android
25_06_13 09:28:51.242 DEBUG LogcatNettyLogger format:Java version: 6
25_06_13 09:28:51.247 DEBUG LogcatNettyLogger Platform: Android
25_06_13 09:28:51.256 DEBUG LogcatNettyLogger sun.misc.Unsafe.theUnsafe: available
25_06_13 09:28:51.258 DEBUG LogcatNettyLogger sun.misc.Unsafe method unavailable:java.lang.NoSuchMethodException: sun.misc.Unsafe.copyMemory [class java.lang.Object, long, class java.lang.Object, long, long]
25_06_13 09:28:51.258 DEBUG LogcatNettyLogger 	at java.lang.Class.getMethod(Class.java:2937)
25_06_13 09:28:51.258 DEBUG LogcatNettyLogger 	at java.lang.Class.getDeclaredMethod(Class.java:2914)
25_06_13 09:28:51.258 DEBUG LogcatNettyLogger 	at io.netty.util.internal.PlatformDependent0$2.run(PlatformDependent0.java:156)
25_06_13 09:28:51.258 DEBUG LogcatNettyLogger 	at java.security.AccessController.doPrivileged(AccessController.java:46)
25_06_13 09:28:51.258 DEBUG LogcatNettyLogger 	at io.netty.util.internal.PlatformDependent0.<clinit>(PlatformDependent0.java:150)
25_06_13 09:28:51.258 DEBUG LogcatNettyLogger 	at io.netty.util.internal.PlatformDependent.isAndroid(PlatformDependent.java:334)
25_06_13 09:28:51.258 DEBUG LogcatNettyLogger 	at io.netty.util.internal.PlatformDependent.<clinit>(PlatformDependent.java:90)
25_06_13 09:28:51.258 DEBUG LogcatNettyLogger 	at io.netty.channel.nio.NioEventLoop.<clinit>(NioEventLoop.java:84)
25_06_13 09:28:51.258 DEBUG LogcatNettyLogger 	at io.netty.channel.nio.NioEventLoopGroup.newChild(NioEventLoopGroup.java:182)
25_06_13 09:28:51.258 DEBUG LogcatNettyLogger 	at io.netty.channel.nio.NioEventLoopGroup.newChild(NioEventLoopGroup.java:38)
25_06_13 09:28:51.258 DEBUG LogcatNettyLogger 	at io.netty.util.concurrent.MultithreadEventExecutorGroup.<init>(MultithreadEventExecutorGroup.java:84)
25_06_13 09:28:51.258 DEBUG LogcatNettyLogger 	at io.netty.util.concurrent.MultithreadEventExecutorGroup.<init>(MultithreadEventExecutorGroup.java:60)
25_06_13 09:28:51.258 DEBUG LogcatNettyLogger 	at io.netty.channel.MultithreadEventLoopGroup.<init>(MultithreadEventLoopGroup.java:52)
25_06_13 09:28:51.258 DEBUG LogcatNettyLogger 	at io.netty.channel.nio.NioEventLoopGroup.<init>(NioEventLoopGroup.java:97)
25_06_13 09:28:51.258 DEBUG LogcatNettyLogger 	at io.netty.channel.nio.NioEventLoopGroup.<init>(NioEventLoopGroup.java:92)
25_06_13 09:28:51.258 DEBUG LogcatNettyLogger 	at io.netty.channel.nio.NioEventLoopGroup.<init>(NioEventLoopGroup.java:73)
25_06_13 09:28:51.258 DEBUG LogcatNettyLogger 	at io.netty.channel.nio.NioEventLoopGroup.<init>(NioEventLoopGroup.java:53)
25_06_13 09:28:51.258 DEBUG LogcatNettyLogger 	at io.netty.channel.nio.NioEventLoopGroup.<init>(NioEventLoopGroup.java:45)
25_06_13 09:28:51.258 DEBUG LogcatNettyLogger 	at com.czur.starry.device.starrypad.hardware.trans.netty.WritePadNettyServer$startServer$2.invokeSuspend(WritePadNettyServer.kt:100)
25_06_13 09:28:51.258 DEBUG LogcatNettyLogger 	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
25_06_13 09:28:51.258 DEBUG LogcatNettyLogger 	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:100)
25_06_13 09:28:51.258 DEBUG LogcatNettyLogger 	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:124)
25_06_13 09:28:51.258 DEBUG LogcatNettyLogger 	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:89)
25_06_13 09:28:51.258 DEBUG LogcatNettyLogger 	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:586)
25_06_13 09:28:51.258 DEBUG LogcatNettyLogger 	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:820)
25_06_13 09:28:51.258 DEBUG LogcatNettyLogger 	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:717)
25_06_13 09:28:51.258 DEBUG LogcatNettyLogger 	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:704)
25_06_13 09:28:51.260 DEBUG LogcatNettyLogger format:java.nio.DirectByteBuffer.<init>(long, {int,long}): unavailable
25_06_13 09:28:51.295 DEBUG LogcatNettyLogger sun.misc.Unsafe: unavailable (Android)
25_06_13 09:28:51.296 VERBOSE ShutdownReceiver 注册关机广播
25_06_13 09:28:51.298 DEBUG LogcatNettyLogger format:-Dio.netty.tmpdir: /data/user/0/com.czur.starry.device.starrypad/cache (java.io.tmpdir)
25_06_13 09:28:51.299 DEBUG LogcatNettyLogger format:-Dio.netty.maxDirectMemory: -1 bytes
25_06_13 09:28:51.300 DEBUG LogcatNettyLogger format:-Dio.netty.uninitializedArrayAllocationThreshold: -1
25_06_13 09:28:51.302 DEBUG LogcatNettyLogger format:-Dio.netty.noPreferDirect: true
25_06_13 09:28:51.329 INFO LogcatNettyLogger Your platform does not provide complete low-level API for accessing direct buffers reliably. Unless explicitly requested, heap buffer will always be preferred to avoid potential system instability.
25_06_13 09:28:51.330 DEBUG LogcatNettyLogger format:-Dio.netty.noKeySetOptimization: false
25_06_13 09:28:51.331 DEBUG LogcatNettyLogger format:-Dio.netty.selectorAutoRebuildThreshold: 512
25_06_13 09:28:51.332 DEBUG LogcatNettyLogger org.jctools-core.MpscChunkedArrayQueue: unavailable
25_06_13 09:28:51.332 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@674138e
25_06_13 09:28:51.333 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@b35c7af
25_06_13 09:28:51.334 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@91787bc
25_06_13 09:28:51.335 DEBUG WritePadService 收到模式切换请求: null
25_06_13 09:28:51.336 DEBUG WritePadService 广播serverMode: MODE_MOUSE
25_06_13 09:28:51.337 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@1c84345
25_06_13 09:28:51.357 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@7c1a1bb
25_06_13 09:28:51.361 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@e5b06d8
25_06_13 09:28:51.362 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@be7ea31
25_06_13 09:28:51.364 DEBUG WritePadService hasDevicesScreenOnFlow:false
25_06_13 09:28:51.367 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@dffd516
25_06_13 09:28:51.378 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@91ff86d
25_06_13 09:28:51.380 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@2faadfa
25_06_13 09:28:51.382 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@f1a6fab
25_06_13 09:28:51.383 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@589d608
25_06_13 09:28:51.385 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@d16c1a1
25_06_13 09:28:51.386 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@535afc6
25_06_13 09:28:51.391 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@c8e5b87
25_06_13 09:28:51.392 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@9ea4ab4
25_06_13 09:28:51.393 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@f46314d
25_06_13 09:28:51.394 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@276a402
25_06_13 09:28:51.397 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@e08a113
25_06_13 09:28:51.399 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@2728f50
25_06_13 09:28:51.400 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@a519549
25_06_13 09:28:51.401 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@faefe4e
25_06_13 09:28:51.402 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@d523b6f
25_06_13 09:28:51.403 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@f9507c
25_06_13 09:28:51.404 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@c953d05
25_06_13 09:28:51.419 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@abf503
25_06_13 09:28:51.449 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@1306c80
25_06_13 09:28:51.450 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@afd96fe
25_06_13 09:28:51.451 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@d5f4b98
25_06_13 09:28:51.496 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@a2baff1
25_06_13 09:28:51.499 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@99b87d6
25_06_13 09:28:51.505 VERBOSE LogcatNettyLogger instrumented a special java.util.Set into: sun.nio.ch.PollSelectorImpl@d0f6c29
25_06_13 09:28:51.506 DEBUG LogcatNettyLogger Could not invoke ManagementFactory.getRuntimeMXBean().getName(); Android?java.lang.ClassNotFoundException: java.lang.management.ManagementFactory
25_06_13 09:28:51.506 DEBUG LogcatNettyLogger 	at java.lang.Class.classForName(Native Method)
25_06_13 09:28:51.506 DEBUG LogcatNettyLogger 	at java.lang.Class.forName(Class.java:536)
25_06_13 09:28:51.506 DEBUG LogcatNettyLogger 	at io.netty.channel.DefaultChannelId.jmxPid(DefaultChannelId.java:140)
25_06_13 09:28:51.506 DEBUG LogcatNettyLogger 	at io.netty.channel.DefaultChannelId.defaultProcessId(DefaultChannelId.java:187)
25_06_13 09:28:51.506 DEBUG LogcatNettyLogger 	at io.netty.channel.DefaultChannelId.<clinit>(DefaultChannelId.java:82)
25_06_13 09:28:51.506 DEBUG LogcatNettyLogger 	at io.netty.channel.AbstractChannel.newId(AbstractChannel.java:112)
25_06_13 09:28:51.506 DEBUG LogcatNettyLogger 	at io.netty.channel.AbstractChannel.<init>(AbstractChannel.java:72)
25_06_13 09:28:51.506 DEBUG LogcatNettyLogger 	at io.netty.channel.nio.AbstractNioChannel.<init>(AbstractNioChannel.java:80)
25_06_13 09:28:51.506 DEBUG LogcatNettyLogger 	at io.netty.channel.nio.AbstractNioMessageChannel.<init>(AbstractNioMessageChannel.java:42)
25_06_13 09:28:51.506 DEBUG LogcatNettyLogger 	at io.netty.channel.socket.nio.NioServerSocketChannel.<init>(NioServerSocketChannel.java:96)
25_06_13 09:28:51.506 DEBUG LogcatNettyLogger 	at io.netty.channel.socket.nio.NioServerSocketChannel.<init>(NioServerSocketChannel.java:89)
25_06_13 09:28:51.506 DEBUG LogcatNettyLogger 	at io.netty.channel.socket.nio.NioServerSocketChannel.<init>(NioServerSocketChannel.java:82)
25_06_13 09:28:51.506 DEBUG LogcatNettyLogger 	at io.netty.channel.socket.nio.NioServerSocketChannel.<init>(NioServerSocketChannel.java:75)
25_06_13 09:28:51.506 DEBUG LogcatNettyLogger 	at java.lang.reflect.Constructor.newInstance0(Native Method)
25_06_13 09:28:51.506 DEBUG LogcatNettyLogger 	at java.lang.reflect.Constructor.newInstance(Constructor.java:343)
25_06_13 09:28:51.506 DEBUG LogcatNettyLogger 	at io.netty.channel.ReflectiveChannelFactory.newChannel(ReflectiveChannelFactory.java:44)
25_06_13 09:28:51.506 DEBUG LogcatNettyLogger 	at io.netty.bootstrap.AbstractBootstrap.initAndRegister(AbstractBootstrap.java:326)
25_06_13 09:28:51.506 DEBUG LogcatNettyLogger 	at io.netty.bootstrap.AbstractBootstrap.doBind(AbstractBootstrap.java:288)
25_06_13 09:28:51.506 DEBUG LogcatNettyLogger 	at io.netty.bootstrap.AbstractBootstrap.bind(AbstractBootstrap.java:284)
25_06_13 09:28:51.506 DEBUG LogcatNettyLogger 	at io.netty.bootstrap.AbstractBootstrap.bind(AbstractBootstrap.java:262)
25_06_13 09:28:51.506 DEBUG LogcatNettyLogger 	at com.czur.starry.device.starrypad.hardware.trans.netty.WritePadNettyServer$startServer$2.invokeSuspend(WritePadNettyServer.kt:335)
25_06_13 09:28:51.506 DEBUG LogcatNettyLogger 	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
25_06_13 09:28:51.506 DEBUG LogcatNettyLogger 	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:100)
25_06_13 09:28:51.506 DEBUG LogcatNettyLogger 	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:124)
25_06_13 09:28:51.506 DEBUG LogcatNettyLogger 	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:89)
25_06_13 09:28:51.506 DEBUG LogcatNettyLogger 	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:586)
25_06_13 09:28:51.506 DEBUG LogcatNettyLogger 	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:820)
25_06_13 09:28:51.506 DEBUG LogcatNettyLogger 	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:717)
25_06_13 09:28:51.506 DEBUG LogcatNettyLogger 	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:704)
25_06_13 09:28:51.506 DEBUG LogcatNettyLogger Caused by: java.lang.ClassNotFoundException: java.lang.management.ManagementFactory
25_06_13 09:28:51.506 DEBUG LogcatNettyLogger 	... 29 more
25_06_13 09:28:51.526 DEBUG LogcatNettyLogger format:-Dio.netty.processId: 5755 (auto-detected)
25_06_13 09:28:51.530 DEBUG LogcatNettyLogger format:-Djava.net.preferIPv4Stack: false
25_06_13 09:28:51.531 DEBUG LogcatNettyLogger format:-Djava.net.preferIPv6Addresses: false
25_06_13 09:28:51.539 DEBUG LogcatNettyLogger format:Loopback interface: lo (lo, ::1)
25_06_13 09:28:51.540 DEBUG LogcatNettyLogger format:/proc/sys/net/core/somaxconn: 4096
25_06_13 09:28:51.543 DEBUG LogcatNettyLogger format:-Dio.netty.machineId: 9c:b8:b4:ff:fe:8e:35:e4 (auto-detected)
25_06_13 09:28:51.544 DEBUG LogcatNettyLogger -Dio.netty.initialSeedUniquifier: 0xc6cb5ba94d21c57e
25_06_13 09:28:51.545 DEBUG LogcatNettyLogger format:-Dio.netty.leakDetection.level: simple
25_06_13 09:28:51.557 DEBUG LogcatNettyLogger format:-Dio.netty.leakDetection.targetRecords: 4
25_06_13 09:28:51.558 DEBUG LogcatNettyLogger format:-Dio.netty.allocator.type: unpooled
25_06_13 09:28:51.559 DEBUG LogcatNettyLogger format:-Dio.netty.threadLocalDirectBufferSize: 0
25_06_13 09:28:51.565 DEBUG LogcatNettyLogger format:-Dio.netty.maxThreadLocalCharBufferSize: 16384
25_06_13 09:28:51.566 DEBUG LogcatNettyLogger format:-Dio.netty.bootstrap.extensions: {}
