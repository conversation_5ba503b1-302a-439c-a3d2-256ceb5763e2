25_06_13 09:10:45.001 DEBUG UpdateReceiver action====android.hardware.usb.action.USB_STATE
25_06_13 09:10:45.005 DEBUG UpdateReceiver action====android.hardware.usb.action.USB_STATE
25_06_13 09:10:45.058 DEBUG UpdateReceiver action====android.intent.action.MEDIA_MOUNTED
25_06_13 09:10:45.526 DEBUG UpdateReceiver action====android.intent.action.BOOT_COMPLETED
25_06_13 09:10:46.603 DEBUG SPContentProvider update uri:content://com.czur.starry.device.update.otaprovider/getSPValue-调用者:com.czur.starry.device.settings
25_06_13 09:10:46.608 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=allMeetingStatus&paramKeyDefault=false-调用者:com.czur.starry.device.settings
25_06_13 09:10:46.622 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=firmware_is_need_update&paramKeyDefault=false-调用者:com.czur.starry.device.settings
25_06_13 09:10:46.646 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=cameraUpgradeSuccess&paramKeyDefault=true-调用者:com.czur.starry.device.settings
25_06_13 09:10:46.647 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=cameraFirmwareNeedUpdate&paramKeyDefault=null-调用者:com.czur.starry.device.settings
25_06_13 09:10:46.649 DEBUG SPContentProvider update uri:content://com.czur.starry.device.update.otaprovider/getSPValue-调用者:com.czur.starry.device.settings
25_06_13 09:10:46.651 VERBOSE SPContentProvider 查询KEY:touchPadHasNewVersion
25_06_13 09:10:46.652 VERBOSE SPContentProvider 更新数据
25_06_13 09:10:46.653 VERBOSE CZURLog 读取当前是否进行会议或录制
25_06_13 09:10:46.654 VERBOSE SPContentProvider 查询KEY:touchPadHasNewVersion
25_06_13 09:10:46.655 VERBOSE SPContentProvider 更新数据
25_06_13 09:10:46.656 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=cameraFirmwareNeedUpdate&paramKeyDefault=null-调用者:com.czur.starry.device.update
25_06_13 09:10:46.656 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=allMeetingStatus&paramKeyDefault=false-调用者:com.czur.starry.device.update
25_06_13 09:10:46.657 VERBOSE CZURLog 读取当前是否进行会议或录制
25_06_13 09:10:46.658 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=cameraVersionCheck&paramKeyDefault=false-调用者:com.czur.starry.device.update
25_06_13 09:10:46.662 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=touchPadHasNewVersion&paramKeyDefault=false-调用者:com.czur.starry.device.settings
25_06_13 09:10:46.663 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=cameraUpgradeSuccess&paramKeyDefault=true-调用者:com.czur.starry.device.update
25_06_13 09:10:46.664 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=cameraVersionCheck&paramKeyDefault=false-调用者:com.czur.starry.device.settings
25_06_13 09:10:46.665 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=firmware_is_need_update&paramKeyDefault=false-调用者:com.czur.starry.device.update
25_06_13 09:10:46.691 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=cycle_to_remind_time&paramKeyDefault=3-调用者:com.czur.starry.device.settings
25_06_13 09:10:46.692 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=cycle_to_remind_time&paramKeyDefault=3-调用者:com.czur.starry.device.update
25_06_13 09:10:46.697 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=touchPadHasNewVersion&paramKeyDefault=false-调用者:com.czur.starry.device.update
25_06_13 09:10:46.716 VERBOSE SPContentProvider 更新数据:touchPadHasNewVersion=false
25_06_13 09:10:46.742 VERBOSE SPContentProvider 通知数据改变:content://com.czur.starry.device.update.otaprovider/getSPValue?touchPadHasNewVersion=false
25_06_13 09:10:46.743 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=qrcode_upload_image&paramKeyDefault=-调用者:com.czur.starry.device.update
25_06_13 09:10:46.744 VERBOSE SPContentProvider 更新数据:touchPadHasNewVersion=false
25_06_13 09:10:46.757 VERBOSE SPContentProvider 通知数据改变:content://com.czur.starry.device.update.otaprovider/getSPValue?touchPadHasNewVersion=false
25_06_13 09:10:46.758 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=image_received_state&paramKeyDefault=false-调用者:com.czur.starry.device.update
25_06_13 09:10:46.759 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=image_received_state&paramKeyDefault=false-调用者:com.czur.starry.device.settings
25_06_13 09:10:46.761 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=systemTrackModeState&paramKeyDefault=false-调用者:com.czur.starry.device.update
25_06_13 09:10:46.762 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=systemTrackModeState&paramKeyDefault=false-调用者:com.czur.starry.device.settings
25_06_13 09:10:46.764 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=rtm_connected_state&paramKeyDefault=false-调用者:com.czur.starry.device.settings
25_06_13 09:10:46.764 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=rtm_connected_state&paramKeyDefault=false-调用者:com.czur.starry.device.update
25_06_13 09:10:46.765 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=qrcode_upload_image&paramKeyDefault=-调用者:com.czur.starry.device.settings
25_06_13 09:11:33.976 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=allMeetingStatus&paramKeyDefault=false-调用者:com.czur.starry.device.launcher
25_06_13 09:11:33.977 VERBOSE CZURLog 读取当前是否进行会议或录制
25_06_13 09:11:33.979 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=cycle_to_remind_time&paramKeyDefault=3-调用者:com.czur.starry.device.launcher
25_06_13 09:11:33.981 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=qrcode_upload_image&paramKeyDefault=-调用者:com.czur.starry.device.launcher
25_06_13 09:11:33.982 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=cameraFirmwareNeedUpdate&paramKeyDefault=null-调用者:com.czur.starry.device.launcher
25_06_13 09:11:33.983 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=cameraUpgradeSuccess&paramKeyDefault=true-调用者:com.czur.starry.device.launcher
25_06_13 09:11:33.986 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=firmware_is_need_update&paramKeyDefault=false-调用者:com.czur.starry.device.launcher
25_06_13 09:11:33.988 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=image_received_state&paramKeyDefault=false-调用者:com.czur.starry.device.launcher
25_06_13 09:11:33.990 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=rtm_connected_state&paramKeyDefault=false-调用者:com.czur.starry.device.launcher
25_06_13 09:11:33.991 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=cameraVersionCheck&paramKeyDefault=false-调用者:com.czur.starry.device.launcher
25_06_13 09:11:33.993 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=touchPadHasNewVersion&paramKeyDefault=false-调用者:com.czur.starry.device.launcher
25_06_13 09:11:33.995 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=systemTrackModeState&paramKeyDefault=false-调用者:com.czur.starry.device.launcher
25_06_13 09:11:34.884 DEBUG UpdateService ====onCreate
25_06_13 09:11:34.907 VERBOSE ProviderUtil ContentValues: allMeetingStatus=false  cv.size: 1
25_06_13 09:11:34.909 VERBOSE ProviderUtil Key: allMeetingStatus, Value: false
25_06_13 09:11:34.910 VERBOSE ProviderUtil update uri: content://com.czur.starry.device.update.otaprovider/getSPValue , contentValues Size: 1
25_06_13 09:11:34.911 DEBUG SPContentProvider update uri:content://com.czur.starry.device.update.otaprovider/getSPValue-调用者:com.czur.starry.device.update
25_06_13 09:11:34.913 VERBOSE SPContentProvider 查询KEY:allMeetingStatus
25_06_13 09:11:34.915 VERBOSE CZURLog 设置当前是否进行会议或录制
25_06_13 09:11:34.917 DEBUG UpdateService ====onStartCommand
25_06_13 09:11:34.918 DEBUG UpdateService onStartCommand: command = 3
25_06_13 09:11:34.920 VERBOSE SPContentProvider 通知数据改变:content://com.czur.starry.device.update.otaprovider/getSPValue?allMeetingStatus=false
25_06_13 09:11:34.922 DEBUG UpdateService 检测升级update包:本地路径
25_06_13 09:11:34.924 DEBUG UpdateService ===getBooleanSystemProp==true
25_06_13 09:11:34.925 VERBOSE ProviderUtil update result: 1
25_06_13 09:11:34.927 INFO OTAHandler 更新成功, 更新缓存
25_06_13 09:11:34.929 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=allMeetingStatus&paramKeyDefault=false-调用者:com.czur.starry.device.memoryservice
25_06_13 09:11:34.931 VERBOSE CZURLog 读取当前是否进行会议或录制
25_06_13 09:11:34.939 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=cycle_to_remind_time&paramKeyDefault=3-调用者:com.czur.starry.device.memoryservice
25_06_13 09:11:34.943 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=cameraFirmwareNeedUpdate&paramKeyDefault=null-调用者:com.czur.starry.device.memoryservice
25_06_13 09:11:34.944 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=image_received_state&paramKeyDefault=false-调用者:com.czur.starry.device.memoryservice
25_06_13 09:11:34.948 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=cameraUpgradeSuccess&paramKeyDefault=true-调用者:com.czur.starry.device.memoryservice
25_06_13 09:11:34.952 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=touchPadHasNewVersion&paramKeyDefault=false-调用者:com.czur.starry.device.memoryservice
25_06_13 09:11:34.955 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=rtm_connected_state&paramKeyDefault=false-调用者:com.czur.starry.device.memoryservice
25_06_13 09:11:34.956 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=qrcode_upload_image&paramKeyDefault=-调用者:com.czur.starry.device.memoryservice
25_06_13 09:11:34.963 DEBUG UpdateService ===allMeetingStateLive==false
25_06_13 09:11:34.964 DEBUG OTATask ====timerTask!!.schedule()
25_06_13 09:11:34.965 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=firmware_is_need_update&paramKeyDefault=false-调用者:com.czur.starry.device.memoryservice
25_06_13 09:11:34.977 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=systemTrackModeState&paramKeyDefault=false-调用者:com.czur.starry.device.memoryservice
25_06_13 09:11:34.979 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=cameraVersionCheck&paramKeyDefault=false-调用者:com.czur.starry.device.memoryservice
25_06_13 09:11:34.980 DEBUG UpdateService ===连接状态==DISCONNECT
25_06_13 09:11:34.981 DEBUG RKRecoverySystem getValidFirmwareImageFile() : --Target image file path : /data/media/0/update.zip
25_06_13 09:11:34.982 DEBUG RKRecoverySystem getValidFirmwareImageFile() : --Target image file path : /storage/emulated/0/update.zip
25_06_13 09:11:34.983 VERBOSE UpdateService 检测升级update包:usb路径
25_06_13 09:11:39.936 DEBUG OTATask =======OTATask===24
25_06_13 09:11:39.944 DEBUG OTATask 开始OTA检查
25_06_13 09:11:39.971 VERBOSE ActiveInterceptor 需要激活:/api/ota/starry/versionCheck
25_06_13 09:11:39.996 WARN SystemManagerProxy onSystemChangeListener is null,add default listener
25_06_13 09:11:40.024 VERBOSE CommonParamInterceptor Token为空
25_06_13 09:11:40.030 VERBOSE NetLogging 请求信息
25_06_13 09:11:40.030 VERBOSE NetLogging ╔══════════════════════════════
25_06_13 09:11:40.030 VERBOSE NetLogging ║ method:GET
25_06_13 09:11:40.030 VERBOSE NetLogging ║ host:test0927-starry.czur.cc
25_06_13 09:11:40.030 VERBOSE NetLogging ║ path:/api/starry/register/v2/active
25_06_13 09:11:40.030 VERBOSE NetLogging ║ -----
25_06_13 09:11:40.030 VERBOSE NetLogging ║ 请求头:
25_06_13 09:11:40.030 VERBOSE NetLogging ║ UDID: CBP02B2503000001
25_06_13 09:11:40.030 VERBOSE NetLogging ║ App-Key: com.czur.starry
25_06_13 09:11:40.030 VERBOSE NetLogging ║ Request-Timestamp: 1749777100023
25_06_13 09:11:40.030 VERBOSE NetLogging ║ 
25_06_13 09:11:40.030 VERBOSE NetLogging ║ url参数:
25_06_13 09:11:40.030 VERBOSE NetLogging ║ sn: CBP02B2503000001
25_06_13 09:11:40.030 VERBOSE NetLogging ║ secret: 1qaz2wsx3edc4rfv
25_06_13 09:11:40.030 VERBOSE NetLogging ║ voucher: C3190FB7E60F4F9689EA083680E3DCB7
25_06_13 09:11:40.030 VERBOSE NetLogging ║ mac: 9a:8e:5c:0b:11:26
25_06_13 09:11:40.030 VERBOSE NetLogging ║ udid: CBP02B2503000001
25_06_13 09:11:40.030 VERBOSE NetLogging ║ noncestr: 3030667814391850
25_06_13 09:11:40.030 VERBOSE NetLogging ║ timestamp: 1749777100024
25_06_13 09:11:40.030 VERBOSE NetLogging ║ sign: be49f2ff3ccfd73fe222979f4d99f85d84609d7a
25_06_13 09:11:40.030 VERBOSE NetLogging ╚══════════════════════════════
25_06_13 09:11:40.037 ERROR NetLogging 请求出错 path->/api/starry/register/v2/active
25_06_13 09:11:40.038 ERROR SyncHttpTask 请求出错:
25_06_13 09:11:40.042 ERROR NetResultUtil 网络错误
25_06_13 09:11:40.043 VERBOSE NetResultUtil 没有网络连接
25_06_13 09:11:40.044 DEBUG OTATask OTA检查请求失败
25_06_13 09:11:49.934 DEBUG ClickDropOTAUtil 开始ClickDrop OTA检查
25_06_13 09:11:49.936 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=clickDropCurrentVersion&paramKeyDefault=1-调用者:com.czur.starry.device.update
25_06_13 09:11:49.944 VERBOSE ActiveInterceptor 需要激活:/api/ota/starry/clickDrop/versionCheck
25_06_13 09:11:49.952 VERBOSE CommonParamInterceptor Token为空
25_06_13 09:11:49.957 VERBOSE NetLogging 请求信息
25_06_13 09:11:49.957 VERBOSE NetLogging ╔══════════════════════════════
25_06_13 09:11:49.957 VERBOSE NetLogging ║ method:GET
25_06_13 09:11:49.957 VERBOSE NetLogging ║ host:test0927-starry.czur.cc
25_06_13 09:11:49.957 VERBOSE NetLogging ║ path:/api/starry/register/v2/active
25_06_13 09:11:49.957 VERBOSE NetLogging ║ -----
25_06_13 09:11:49.957 VERBOSE NetLogging ║ 请求头:
25_06_13 09:11:49.957 VERBOSE NetLogging ║ UDID: CBP02B2503000001
25_06_13 09:11:49.957 VERBOSE NetLogging ║ App-Key: com.czur.starry
25_06_13 09:11:49.957 VERBOSE NetLogging ║ Request-Timestamp: 1749777109952
25_06_13 09:11:49.957 VERBOSE NetLogging ║ 
25_06_13 09:11:49.957 VERBOSE NetLogging ║ url参数:
25_06_13 09:11:49.957 VERBOSE NetLogging ║ sn: CBP02B2503000001
25_06_13 09:11:49.957 VERBOSE NetLogging ║ secret: 1qaz2wsx3edc4rfv
25_06_13 09:11:49.957 VERBOSE NetLogging ║ voucher: C3190FB7E60F4F9689EA083680E3DCB7
25_06_13 09:11:49.957 VERBOSE NetLogging ║ mac: 9a:8e:5c:0b:11:26
25_06_13 09:11:49.957 VERBOSE NetLogging ║ udid: CBP02B2503000001
25_06_13 09:11:49.957 VERBOSE NetLogging ║ noncestr: 3030667814391850
25_06_13 09:11:49.957 VERBOSE NetLogging ║ timestamp: 1749777109952
25_06_13 09:11:49.957 VERBOSE NetLogging ║ sign: f6106dd822ddea8e2caa3fa078c91c097b4aec89
25_06_13 09:11:49.957 VERBOSE NetLogging ╚══════════════════════════════
25_06_13 09:11:49.962 ERROR NetLogging 请求出错 path->/api/starry/register/v2/active
25_06_13 09:11:49.965 ERROR SyncHttpTask 请求出错:
25_06_13 09:11:49.968 ERROR NetResultUtil 网络错误
25_06_13 09:11:49.969 VERBOSE NetResultUtil 没有网络连接
25_06_13 09:11:49.971 DEBUG ClickDropOTAUtil OTA检查请求失败
25_06_13 09:28:48.464 DEBUG UpdateReceiver action====android.hardware.usb.action.USB_STATE
25_06_13 09:28:48.550 DEBUG UpdateReceiver action====android.hardware.usb.action.USB_STATE
25_06_13 09:28:48.909 DEBUG UpdateReceiver action====android.intent.action.BOOT_COMPLETED
25_06_13 09:28:49.298 DEBUG UpdateReceiver action====android.intent.action.MEDIA_MOUNTED
25_06_13 09:28:49.311 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=allMeetingStatus&paramKeyDefault=false-调用者:com.czur.starry.device.update
25_06_13 09:28:49.312 VERBOSE CZURLog 读取当前是否进行会议或录制
25_06_13 09:28:49.313 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=cameraVersionCheck&paramKeyDefault=false-调用者:com.czur.starry.device.update
25_06_13 09:28:49.315 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=allMeetingStatus&paramKeyDefault=false-调用者:com.czur.starry.device.update
25_06_13 09:28:49.316 VERBOSE CZURLog 读取当前是否进行会议或录制
25_06_13 09:28:49.320 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=touchPadHasNewVersion&paramKeyDefault=false-调用者:com.czur.starry.device.update
25_06_13 09:28:49.321 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=cameraFirmwareNeedUpdate&paramKeyDefault=null-调用者:com.czur.starry.device.update
25_06_13 09:28:49.322 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=cameraUpgradeSuccess&paramKeyDefault=true-调用者:com.czur.starry.device.update
25_06_13 09:28:49.327 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=cycle_to_remind_time&paramKeyDefault=3-调用者:com.czur.starry.device.update
25_06_13 09:28:49.332 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=qrcode_upload_image&paramKeyDefault=-调用者:com.czur.starry.device.update
25_06_13 09:28:49.334 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=image_received_state&paramKeyDefault=false-调用者:com.czur.starry.device.update
25_06_13 09:28:49.337 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=firmware_is_need_update&paramKeyDefault=false-调用者:com.czur.starry.device.update
25_06_13 09:28:49.340 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=rtm_connected_state&paramKeyDefault=false-调用者:com.czur.starry.device.update
25_06_13 09:28:49.342 DEBUG UpdateService ====onCreate
25_06_13 09:28:49.343 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=systemTrackModeState&paramKeyDefault=false-调用者:com.czur.starry.device.update
25_06_13 09:28:49.360 VERBOSE ProviderUtil ContentValues: allMeetingStatus=false  cv.size: 1
25_06_13 09:28:49.362 VERBOSE ProviderUtil Key: allMeetingStatus, Value: false
25_06_13 09:28:49.363 VERBOSE ProviderUtil update uri: content://com.czur.starry.device.update.otaprovider/getSPValue , contentValues Size: 1
25_06_13 09:28:49.364 DEBUG SPContentProvider update uri:content://com.czur.starry.device.update.otaprovider/getSPValue-调用者:com.czur.starry.device.update
25_06_13 09:28:49.365 VERBOSE SPContentProvider 查询KEY:allMeetingStatus
25_06_13 09:28:49.366 VERBOSE CZURLog 设置当前是否进行会议或录制
25_06_13 09:28:49.367 DEBUG UpdateService ====onStartCommand
25_06_13 09:28:49.368 DEBUG UpdateService onStartCommand: command = 2
25_06_13 09:28:49.369 VERBOSE SPContentProvider 通知数据改变:content://com.czur.starry.device.update.otaprovider/getSPValue?allMeetingStatus=false
25_06_13 09:28:49.370 VERBOSE UpdateService 检测升级update包:usb路径
25_06_13 09:28:49.371 VERBOSE ProviderUtil update result: 1
25_06_13 09:28:49.374 INFO OTAHandler 更新成功, 更新缓存
25_06_13 09:28:49.375 DEBUG UpdateService ===allMeetingStateLive==false
25_06_13 09:28:49.375 DEBUG OTATask ====timerTask!!.schedule()
25_06_13 09:28:49.377 DEBUG UpdateService ===连接状态==DISCONNECT
25_06_13 09:28:50.252 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=allMeetingStatus&paramKeyDefault=false-调用者:com.czur.starry.device.launcher
25_06_13 09:28:50.253 VERBOSE CZURLog 读取当前是否进行会议或录制
25_06_13 09:28:50.257 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=cameraVersionCheck&paramKeyDefault=false-调用者:com.czur.starry.device.launcher
25_06_13 09:28:50.267 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=touchPadHasNewVersion&paramKeyDefault=false-调用者:com.czur.starry.device.launcher
25_06_13 09:28:50.276 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=cycle_to_remind_time&paramKeyDefault=3-调用者:com.czur.starry.device.launcher
25_06_13 09:28:50.296 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=firmware_is_need_update&paramKeyDefault=false-调用者:com.czur.starry.device.launcher
25_06_13 09:28:50.297 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=rtm_connected_state&paramKeyDefault=false-调用者:com.czur.starry.device.launcher
25_06_13 09:28:50.299 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=systemTrackModeState&paramKeyDefault=false-调用者:com.czur.starry.device.launcher
25_06_13 09:28:50.300 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=image_received_state&paramKeyDefault=false-调用者:com.czur.starry.device.launcher
25_06_13 09:28:50.314 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=cameraFirmwareNeedUpdate&paramKeyDefault=null-调用者:com.czur.starry.device.launcher
25_06_13 09:28:50.315 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=cameraUpgradeSuccess&paramKeyDefault=true-调用者:com.czur.starry.device.launcher
25_06_13 09:28:50.323 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=qrcode_upload_image&paramKeyDefault=-调用者:com.czur.starry.device.launcher
25_06_13 09:28:50.878 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=allMeetingStatus&paramKeyDefault=false-调用者:com.czur.starry.device.settings
25_06_13 09:28:50.879 VERBOSE CZURLog 读取当前是否进行会议或录制
25_06_13 09:28:50.882 DEBUG SPContentProvider update uri:content://com.czur.starry.device.update.otaprovider/getSPValue-调用者:com.czur.starry.device.settings
25_06_13 09:28:50.883 VERBOSE SPContentProvider 查询KEY:touchPadHasNewVersion
25_06_13 09:28:50.884 VERBOSE SPContentProvider 更新数据
25_06_13 09:28:50.885 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=cameraFirmwareNeedUpdate&paramKeyDefault=null-调用者:com.czur.starry.device.settings
25_06_13 09:28:50.889 DEBUG SPContentProvider update uri:content://com.czur.starry.device.update.otaprovider/getSPValue-调用者:com.czur.starry.device.settings
25_06_13 09:28:50.890 VERBOSE SPContentProvider 查询KEY:touchPadHasNewVersion
25_06_13 09:28:50.930 VERBOSE SPContentProvider 更新数据
25_06_13 09:28:50.949 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=cameraVersionCheck&paramKeyDefault=false-调用者:com.czur.starry.device.settings
25_06_13 09:28:50.951 VERBOSE SPContentProvider 更新数据:touchPadHasNewVersion=false
25_06_13 09:28:50.953 VERBOSE SPContentProvider 更新数据:touchPadHasNewVersion=false
25_06_13 09:28:50.954 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=touchPadHasNewVersion&paramKeyDefault=false-调用者:com.czur.starry.device.settings
25_06_13 09:28:50.956 VERBOSE SPContentProvider 通知数据改变:content://com.czur.starry.device.update.otaprovider/getSPValue?touchPadHasNewVersion=false
25_06_13 09:28:50.957 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=cycle_to_remind_time&paramKeyDefault=3-调用者:com.czur.starry.device.settings
25_06_13 09:28:50.959 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=qrcode_upload_image&paramKeyDefault=-调用者:com.czur.starry.device.settings
25_06_13 09:28:50.960 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=cameraUpgradeSuccess&paramKeyDefault=true-调用者:com.czur.starry.device.settings
25_06_13 09:28:50.961 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=image_received_state&paramKeyDefault=false-调用者:com.czur.starry.device.settings
25_06_13 09:28:50.963 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=firmware_is_need_update&paramKeyDefault=false-调用者:com.czur.starry.device.settings
25_06_13 09:28:50.964 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=rtm_connected_state&paramKeyDefault=false-调用者:com.czur.starry.device.settings
25_06_13 09:28:50.965 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=systemTrackModeState&paramKeyDefault=false-调用者:com.czur.starry.device.settings
25_06_13 09:28:50.966 VERBOSE SPContentProvider 通知数据改变:content://com.czur.starry.device.update.otaprovider/getSPValue?touchPadHasNewVersion=false
25_06_13 09:28:50.968 DEBUG UpdateService ====onStartCommand
25_06_13 09:28:50.969 DEBUG UpdateService onStartCommand: command = 3
25_06_13 09:28:50.971 DEBUG UpdateService 检测升级update包:本地路径
25_06_13 09:28:50.972 DEBUG UpdateService ===getBooleanSystemProp==true
25_06_13 09:28:50.974 DEBUG RKRecoverySystem getValidFirmwareImageFile() : --Target image file path : /data/media/0/update.zip
25_06_13 09:28:50.976 DEBUG RKRecoverySystem getValidFirmwareImageFile() : --Target image file path : /storage/emulated/0/update.zip
25_06_13 09:28:50.977 VERBOSE UpdateService 检测升级update包:usb路径
25_06_13 09:28:51.081 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=allMeetingStatus&paramKeyDefault=false-调用者:com.czur.starry.device.memoryservice
25_06_13 09:28:51.083 VERBOSE CZURLog 读取当前是否进行会议或录制
25_06_13 09:28:51.085 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=cycle_to_remind_time&paramKeyDefault=3-调用者:com.czur.starry.device.memoryservice
25_06_13 09:28:51.098 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=cameraFirmwareNeedUpdate&paramKeyDefault=null-调用者:com.czur.starry.device.memoryservice
25_06_13 09:28:51.106 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=cameraVersionCheck&paramKeyDefault=false-调用者:com.czur.starry.device.memoryservice
25_06_13 09:28:51.107 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=cameraUpgradeSuccess&paramKeyDefault=true-调用者:com.czur.starry.device.memoryservice
25_06_13 09:28:51.114 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=firmware_is_need_update&paramKeyDefault=false-调用者:com.czur.starry.device.memoryservice
25_06_13 09:28:51.125 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=qrcode_upload_image&paramKeyDefault=-调用者:com.czur.starry.device.memoryservice
25_06_13 09:28:51.134 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=image_received_state&paramKeyDefault=false-调用者:com.czur.starry.device.memoryservice
25_06_13 09:28:51.136 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=rtm_connected_state&paramKeyDefault=false-调用者:com.czur.starry.device.memoryservice
25_06_13 09:28:51.137 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=systemTrackModeState&paramKeyDefault=false-调用者:com.czur.starry.device.memoryservice
25_06_13 09:28:51.141 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=touchPadHasNewVersion&paramKeyDefault=false-调用者:com.czur.starry.device.memoryservice
25_06_13 09:28:54.372 DEBUG OTATask =======OTATask===24
25_06_13 09:28:54.374 DEBUG OTATask 开始OTA检查
25_06_13 09:28:54.393 VERBOSE ActiveInterceptor 需要激活:/api/ota/starry/versionCheck
25_06_13 09:28:54.406 WARN SystemManagerProxy onSystemChangeListener is null,add default listener
25_06_13 09:28:54.426 VERBOSE CommonParamInterceptor Token为空
25_06_13 09:28:54.430 VERBOSE NetLogging 请求信息
25_06_13 09:28:54.430 VERBOSE NetLogging ╔══════════════════════════════
25_06_13 09:28:54.430 VERBOSE NetLogging ║ method:GET
25_06_13 09:28:54.430 VERBOSE NetLogging ║ host:test0927-starry.czur.cc
25_06_13 09:28:54.430 VERBOSE NetLogging ║ path:/api/starry/register/v2/active
25_06_13 09:28:54.430 VERBOSE NetLogging ║ -----
25_06_13 09:28:54.430 VERBOSE NetLogging ║ 请求头:
25_06_13 09:28:54.430 VERBOSE NetLogging ║ UDID: CBP02B2503000001
25_06_13 09:28:54.430 VERBOSE NetLogging ║ App-Key: com.czur.starry
25_06_13 09:28:54.430 VERBOSE NetLogging ║ Request-Timestamp: 1749778134425
25_06_13 09:28:54.430 VERBOSE NetLogging ║ 
25_06_13 09:28:54.430 VERBOSE NetLogging ║ url参数:
25_06_13 09:28:54.430 VERBOSE NetLogging ║ sn: CBP02B2503000001
25_06_13 09:28:54.430 VERBOSE NetLogging ║ secret: 1qaz2wsx3edc4rfv
25_06_13 09:28:54.430 VERBOSE NetLogging ║ voucher: C3190FB7E60F4F9689EA083680E3DCB7
25_06_13 09:28:54.430 VERBOSE NetLogging ║ mac: 9a:8e:5c:0b:11:26
25_06_13 09:28:54.430 VERBOSE NetLogging ║ udid: CBP02B2503000001
25_06_13 09:28:54.430 VERBOSE NetLogging ║ noncestr: 3030667814391850
25_06_13 09:28:54.430 VERBOSE NetLogging ║ timestamp: 1749778134426
25_06_13 09:28:54.430 VERBOSE NetLogging ║ sign: 593c582a69b0b6640ce53747d76fadeb5bcc3e8b
25_06_13 09:28:54.430 VERBOSE NetLogging ╚══════════════════════════════
25_06_13 09:28:54.435 ERROR NetLogging 请求出错 path->/api/starry/register/v2/active
25_06_13 09:28:54.436 ERROR SyncHttpTask 请求出错:
25_06_13 09:28:54.439 ERROR NetResultUtil 网络错误
25_06_13 09:28:54.441 VERBOSE NetResultUtil 没有网络连接
25_06_13 09:28:54.442 DEBUG OTATask OTA检查请求失败
25_06_13 09:29:04.378 DEBUG ClickDropOTAUtil 开始ClickDrop OTA检查
25_06_13 09:29:04.379 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=clickDropCurrentVersion&paramKeyDefault=1-调用者:com.czur.starry.device.update
25_06_13 09:29:04.384 VERBOSE ActiveInterceptor 需要激活:/api/ota/starry/clickDrop/versionCheck
25_06_13 09:29:04.412 VERBOSE CommonParamInterceptor Token为空
25_06_13 09:29:04.418 VERBOSE NetLogging 请求信息
25_06_13 09:29:04.418 VERBOSE NetLogging ╔══════════════════════════════
25_06_13 09:29:04.418 VERBOSE NetLogging ║ method:GET
25_06_13 09:29:04.418 VERBOSE NetLogging ║ host:test0927-starry.czur.cc
25_06_13 09:29:04.418 VERBOSE NetLogging ║ path:/api/starry/register/v2/active
25_06_13 09:29:04.418 VERBOSE NetLogging ║ -----
25_06_13 09:29:04.418 VERBOSE NetLogging ║ 请求头:
25_06_13 09:29:04.418 VERBOSE NetLogging ║ UDID: CBP02B2503000001
25_06_13 09:29:04.418 VERBOSE NetLogging ║ App-Key: com.czur.starry
25_06_13 09:29:04.418 VERBOSE NetLogging ║ Request-Timestamp: 1749778144412
25_06_13 09:29:04.418 VERBOSE NetLogging ║ 
25_06_13 09:29:04.418 VERBOSE NetLogging ║ url参数:
25_06_13 09:29:04.418 VERBOSE NetLogging ║ sn: CBP02B2503000001
25_06_13 09:29:04.418 VERBOSE NetLogging ║ secret: 1qaz2wsx3edc4rfv
25_06_13 09:29:04.418 VERBOSE NetLogging ║ voucher: C3190FB7E60F4F9689EA083680E3DCB7
25_06_13 09:29:04.418 VERBOSE NetLogging ║ mac: 9a:8e:5c:0b:11:26
25_06_13 09:29:04.418 VERBOSE NetLogging ║ udid: CBP02B2503000001
25_06_13 09:29:04.418 VERBOSE NetLogging ║ noncestr: 3030667814391850
25_06_13 09:29:04.418 VERBOSE NetLogging ║ timestamp: 1749778144412
25_06_13 09:29:04.418 VERBOSE NetLogging ║ sign: 21fe0897cca8af1ac6f942f2ff14badd076ae13e
25_06_13 09:29:04.418 VERBOSE NetLogging ╚══════════════════════════════
25_06_13 09:29:04.423 ERROR NetLogging 请求出错 path->/api/starry/register/v2/active
25_06_13 09:29:04.425 ERROR SyncHttpTask 请求出错:
25_06_13 09:29:04.426 ERROR NetResultUtil 网络错误
25_06_13 09:29:04.427 VERBOSE NetResultUtil 没有网络连接
25_06_13 09:29:04.429 DEBUG ClickDropOTAUtil OTA检查请求失败
25_06_13 09:29:11.273 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=cameraFirmwareNeedUpdate&paramKeyDefault=null-调用者:com.czur.starry.device.settings
25_06_13 09:29:11.289 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=cameraVersionCheck&paramKeyDefault=false-调用者:com.czur.starry.device.settings
25_06_13 09:29:11.292 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=qrcode_upload_image&paramKeyDefault=-调用者:com.czur.starry.device.settings
25_06_13 09:29:11.293 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=cameraUpgradeSuccess&paramKeyDefault=true-调用者:com.czur.starry.device.settings
25_06_13 09:29:11.294 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=touchPadHasNewVersion&paramKeyDefault=false-调用者:com.czur.starry.device.settings
25_06_13 09:29:11.295 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=firmware_is_need_update&paramKeyDefault=false-调用者:com.czur.starry.device.settings
25_06_13 09:29:11.302 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=allMeetingStatus&paramKeyDefault=false-调用者:com.czur.starry.device.settings
25_06_13 09:29:11.303 VERBOSE CZURLog 读取当前是否进行会议或录制
25_06_13 09:29:11.304 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=cycle_to_remind_time&paramKeyDefault=3-调用者:com.czur.starry.device.settings
25_06_13 09:29:11.305 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=image_received_state&paramKeyDefault=false-调用者:com.czur.starry.device.settings
25_06_13 09:29:11.314 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=rtm_connected_state&paramKeyDefault=false-调用者:com.czur.starry.device.settings
25_06_13 09:29:11.322 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=systemTrackModeState&paramKeyDefault=false-调用者:com.czur.starry.device.settings
25_06_13 09:29:11.347 DEBUG SPContentProvider query:content://com.czur.starry.device.update.otaprovider/getSPValue?paramKeyName=currentCameraFirmwareNeedUpdate&paramKeyDefault=400-调用者:com.czur.starry.device.settings
