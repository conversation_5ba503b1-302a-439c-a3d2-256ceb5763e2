25_06_13 09:10:45.343 VERBOSE CZURAtyManager 添加:AnimationActivity(onCreate)
25_06_13 09:10:45.462 DEBUG ShareViewModel peripheralUSBRunningFlow = false
25_06_13 09:10:45.464 VERBOSE ShareViewModel refreshGadgetState
25_06_13 09:10:45.482 DEBUG NoticeHandler 注册回调
25_06_13 09:10:45.483 DEBUG NoticeHandler 消息类型:type=( SYNC - [ 外设USB模式变化 ] )第一次注册
25_06_13 09:10:45.491 DEBUG AnimationActivity onStart
25_06_13 09:10:45.492 DEBUG AnimationActivity Activity 刚被创建，靠onWindowFocusChanged刷新
25_06_13 09:10:45.496 INFO CZBusinessAty page-start: AnimationActivity
25_06_13 09:10:45.498 DEBUG AnimationActivity Hdmi Out 背景动画
25_06_13 09:10:45.499 DEBUG AnimationActivity BYOM外设 false ，Launch启动设置完成通知
25_06_13 09:10:45.500 DEBUG AnimationActivity USB外设 false ，Launch启动设置完成通知
25_06_13 09:10:45.519 VERBOSE SystemManagerProxy getGadgetMode Start
25_06_13 09:10:45.523 DEBUG SystemManagerProxy isGadgetMode Value: 0
25_06_13 09:10:45.530 DEBUG ShareViewModel refreshBYOMState
25_06_13 09:10:45.576 DEBUG TextureVideoView prepareMediaPlayer: android.resource://com.czur.starry.device.smallroundscreen/2131623937
25_06_13 09:10:45.708 DEBUG AnimationActivity Hdmi Out 背景动画
25_06_13 09:10:45.710 DEBUG TextureVideoView prepareMediaPlayer: android.resource://com.czur.starry.device.smallroundscreen/2131623937
25_06_13 09:10:45.761 ERROR TextureVideoView Error preparing media playerjava.lang.IllegalStateException
25_06_13 09:10:45.761 ERROR TextureVideoView 	at android.media.MediaPlayer._setDataSource(Native Method)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at android.media.MediaPlayer.setDataSource(MediaPlayer.java:1298)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at android.media.MediaPlayer.setDataSource(MediaPlayer.java:1263)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at android.media.MediaPlayer.attemptDataSource(MediaPlayer.java:1156)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at android.media.MediaPlayer.setDataSource(MediaPlayer.java:1121)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at android.media.MediaPlayer.setDataSource(MediaPlayer.java:1049)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at com.czur.starry.device.smallroundscreen.widget.TextureVideoView.prepareMediaPlayer(TextureVideoView.kt:40)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at com.czur.starry.device.smallroundscreen.widget.TextureVideoView.setVideoURI(TextureVideoView.kt:32)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at com.czur.starry.device.smallroundscreen.animation.BaseVideoAnimation.startAnimation(BaseVideoAnimation.kt:38)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at com.czur.starry.device.smallroundscreen.animation.AnimationPlayerImpl.play(AnimationPlayerImpl.kt:9)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at com.czur.starry.device.smallroundscreen.AnimationActivity.startHdmiOutBGAnimation$lambda$4(AnimationActivity.kt:451)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at com.czur.starry.device.smallroundscreen.AnimationActivity.$r8$lambda$yMguFfY_BE9-0sW-rq7x6udmvfo(Unknown Source:0)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at com.czur.starry.device.smallroundscreen.AnimationActivity$$ExternalSyntheticLambda12.run(D8$$SyntheticClass:0)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at android.app.Activity.runOnUiThread(Activity.java:7664)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at com.czur.starry.device.smallroundscreen.AnimationActivity.startHdmiOutBGAnimation(AnimationActivity.kt:449)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at com.czur.starry.device.smallroundscreen.AnimationActivity.access$startHdmiOutBGAnimation(AnimationActivity.kt:58)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at com.czur.starry.device.smallroundscreen.AnimationActivity$initData$1.invokeSuspend(AnimationActivity.kt:133)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at com.czur.starry.device.smallroundscreen.AnimationActivity$initData$1.invoke(Unknown Source:8)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at com.czur.starry.device.smallroundscreen.AnimationActivity$initData$1.invoke(Unknown Source:4)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at androidx.lifecycle.RepeatOnLifecycleKt$repeatOnLifecycle$3$1$1$1$1$1$1.invokeSuspend(RepeatOnLifecycle.kt:111)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at androidx.lifecycle.RepeatOnLifecycleKt$repeatOnLifecycle$3$1$1$1$1$1$1.invoke(Unknown Source:8)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at androidx.lifecycle.RepeatOnLifecycleKt$repeatOnLifecycle$3$1$1$1$1$1$1.invoke(Unknown Source:4)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at kotlinx.coroutines.intrinsics.UndispatchedKt.startUndspatched(Undispatched.kt:66)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:43)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at kotlinx.coroutines.CoroutineScopeKt.coroutineScope(CoroutineScope.kt:286)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at androidx.lifecycle.RepeatOnLifecycleKt$repeatOnLifecycle$3$1$1$1$1.invokeSuspend(RepeatOnLifecycle.kt:110)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at kotlinx.coroutines.internal.DispatchedContinuationKt.resumeCancellableWith(DispatchedContinuation.kt:375)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at kotlinx.coroutines.intrinsics.CancellableKt.startCoroutineCancellable(Cancellable.kt:26)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at kotlinx.coroutines.CoroutineStart.invoke(CoroutineStart.kt:358)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at kotlinx.coroutines.AbstractCoroutine.start(AbstractCoroutine.kt:134)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at kotlinx.coroutines.BuildersKt__Builders_commonKt.launch(Builders.common.kt:53)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at kotlinx.coroutines.BuildersKt.launch(Unknown Source:1)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at kotlinx.coroutines.BuildersKt__Builders_commonKt.launch$default(Builders.common.kt:44)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at kotlinx.coroutines.BuildersKt.launch$default(Unknown Source:1)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at androidx.lifecycle.RepeatOnLifecycleKt$repeatOnLifecycle$3$1$1$1.onStateChanged(RepeatOnLifecycle.kt:106)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at androidx.lifecycle.LifecycleRegistry$ObserverWithState.dispatchEvent(LifecycleRegistry.jvm.kt:320)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at androidx.lifecycle.LifecycleRegistry.forwardPass(LifecycleRegistry.jvm.kt:257)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at androidx.lifecycle.LifecycleRegistry.sync(LifecycleRegistry.jvm.kt:293)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at androidx.lifecycle.LifecycleRegistry.moveToState(LifecycleRegistry.jvm.kt:142)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at androidx.lifecycle.LifecycleRegistry.handleLifecycleEvent(LifecycleRegistry.jvm.kt:124)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at androidx.lifecycle.ReportFragment$Companion.dispatch$lifecycle_runtime_release(ReportFragment.android.kt:190)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at androidx.lifecycle.ReportFragment$LifecycleCallbacks.onActivityPostResumed(ReportFragment.android.kt:125)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at android.app.Activity.dispatchActivityPostResumed(Activity.java:1509)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at android.app.Activity.performResume(Activity.java:8820)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at android.app.ActivityThread.performResumeActivity(ActivityThread.java:4993)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at android.app.ActivityThread.handleResumeActivity(ActivityThread.java:5036)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at android.app.servertransaction.TransactionExecutor.performLifecycleSequence(TransactionExecutor.java:229)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at android.app.servertransaction.TransactionExecutor.cycleToPath(TransactionExecutor.java:205)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at android.app.servertransaction.TransactionExecutor.executeCallbacks(TransactionExecutor.java:150)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at android.app.servertransaction.TransactionExecutor.execute(TransactionExecutor.java:96)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at android.app.ActivityThread$H.handleMessage(ActivityThread.java:2468)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at android.os.Handler.dispatchMessage(Handler.java:106)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at android.os.Looper.loopOnce(Looper.java:205)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at android.os.Looper.loop(Looper.java:294)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at android.app.ActivityThread.main(ActivityThread.java:8255)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at java.lang.reflect.Method.invoke(Native Method)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:552)
25_06_13 09:10:45.761 ERROR TextureVideoView 	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:974)
25_06_13 09:10:45.764 DEBUG AnimationActivity BYOM外设 false ，Launch启动设置完成通知
25_06_13 09:10:45.767 DEBUG AnimationActivity USB外设 false ，Launch启动设置完成通知
25_06_13 09:10:45.853 DEBUG AnimationActivity AnimationActivity 已经显示到屏幕上
25_06_13 09:10:47.857 DEBUG AnimationActivity HDMI OUT 已连接
25_06_13 09:10:47.874 DEBUG AnimationActivity Hdmi Out 文字已连接动画
25_06_13 09:10:47.879 DEBUG AnimationActivity 取消延迟设置低亮模式的任务
25_06_13 09:10:47.881 DEBUG AnimationActivity 设置屏幕亮度: 255
25_06_13 09:10:47.883 DEBUG AnimationActivity 设置屏幕亮度结果: true, 亮度值: 255
25_06_13 09:10:47.908 DEBUG AnimationActivity 屏幕亮度设置成功: 255
25_06_13 09:10:48.209 DEBUG BaseHdmiAnimationDrawable startPlayAnimation hdmi_out_normal
25_06_13 09:10:57.868 DEBUG AnimationActivity 添加文字跳动动画任务
25_06_13 09:10:57.871 DEBUG AnimationActivity Hdmi Out 文字跳动动画
25_06_13 09:10:57.874 DEBUG BaseHdmiAnimationDrawable hdmi_out_normal 结束
25_06_13 09:10:58.074 DEBUG BaseHdmiAnimationDrawable startPlayAnimation hdmi_flashing
25_06_13 09:11:26.525 DEBUG ShareViewModel eShareCallback key = eshare_show_pin_window
25_06_13 09:11:26.569 DEBUG ShareViewModel eShareCallback key = eshare_device_name
25_06_13 09:11:26.570 DEBUG ShareViewModel eShareCallback key = eshare_pin_refresh_interval
25_06_13 09:11:26.572 DEBUG ShareViewModel eShareCallback key = eshare_pin_code_mode
25_06_13 09:11:26.902 DEBUG ShareViewModel eShareCallback key = eshare_multi_screen_mode
25_06_13 09:11:26.918 DEBUG ShareViewModel eShareCallback key = eshare_cast_auth_mode
25_06_13 09:11:26.922 DEBUG ShareViewModel eShareCallback key = eshare_mirror_auth_mode
25_06_13 09:11:26.946 DEBUG ShareViewModel eShareCallback key = eshare_show_cast_client
25_06_13 09:11:26.947 DEBUG ShareViewModel eShareCallback key = eshare_cast_fullscreen
25_06_13 09:11:26.949 DEBUG ShareViewModel eShareCallback key = eshare_client_airplay_enable
25_06_13 09:11:26.950 DEBUG ShareViewModel eShareCallback key = eshare_miracast_enable
25_06_13 09:11:26.954 DEBUG ShareViewModel eShareCallback key = eshare_dlna_enable
25_06_13 09:11:26.955 DEBUG ShareViewModel eShareCallback key = eshare_chromecast_enable
25_06_13 09:11:26.959 DEBUG ShareViewModel eShareCallback key = eshare_webcast_enable
25_06_13 09:11:26.972 DEBUG ShareViewModel eShareCallback key = eshare_register_info
25_06_13 09:11:27.272 DEBUG ShareViewModel eShareCallback key = eshare_byom_running
25_06_13 09:11:27.274 DEBUG ShareViewModel eShareCallback isByomrunning = false
25_06_13 09:11:27.319 DEBUG ShareViewModel eShareCallback key = eshare_dlna_enable
25_06_13 09:11:27.427 DEBUG ShareViewModel eShareCallback key = eshare_pin_code
25_06_13 09:11:27.429 DEBUG ShareViewModel eShareCallback key = eshare_qrcode_info
25_06_13 09:11:27.430 DEBUG ShareViewModel eShareCallback key = eshare_network_info
25_06_13 09:11:27.486 DEBUG ShareViewModel eShareCallback key = eshare_registered
25_06_13 09:11:27.502 DEBUG ShareViewModel eShareCallback key = eshare_registered
25_06_13 09:11:27.673 DEBUG ShareViewModel eShareCallback key = eshare_miracast_enable
25_06_13 09:11:30.986 VERBOSE CZBusinessAty 壁纸更换了==com.czur.starry.device.smallroundscreen
25_06_13 09:11:31.767 DEBUG ShareViewModel category = 2, eventID = 4, para1 = 0, extend1 = , extend2 = 
25_06_13 09:11:31.768 DEBUG AnimationActivity onSystemChange category = 2, eventID = 4, para1 = 0, extend1 = , extend2 = 
25_06_13 09:11:33.039 DEBUG AnimationActivity 收到Launch启动设置完成通知
25_06_13 09:11:33.076 DEBUG AnimationActivity Hdmi Out 背景动画
25_06_13 09:11:33.078 DEBUG TextureVideoView prepareMediaPlayer: android.resource://com.czur.starry.device.smallroundscreen/2131623937
25_06_13 09:11:33.089 ERROR TextureVideoView Error preparing media playerjava.lang.IllegalStateException
25_06_13 09:11:33.089 ERROR TextureVideoView 	at android.media.MediaPlayer._setDataSource(Native Method)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at android.media.MediaPlayer.setDataSource(MediaPlayer.java:1298)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at android.media.MediaPlayer.setDataSource(MediaPlayer.java:1263)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at android.media.MediaPlayer.attemptDataSource(MediaPlayer.java:1156)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at android.media.MediaPlayer.setDataSource(MediaPlayer.java:1121)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at android.media.MediaPlayer.setDataSource(MediaPlayer.java:1049)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at com.czur.starry.device.smallroundscreen.widget.TextureVideoView.prepareMediaPlayer(TextureVideoView.kt:40)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at com.czur.starry.device.smallroundscreen.widget.TextureVideoView.setVideoURI(TextureVideoView.kt:32)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at com.czur.starry.device.smallroundscreen.animation.BaseVideoAnimation.startAnimation(BaseVideoAnimation.kt:38)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at com.czur.starry.device.smallroundscreen.animation.AnimationPlayerImpl.play(AnimationPlayerImpl.kt:9)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at com.czur.starry.device.smallroundscreen.AnimationActivity.startHdmiOutBGAnimation$lambda$4(AnimationActivity.kt:451)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at com.czur.starry.device.smallroundscreen.AnimationActivity.$r8$lambda$yMguFfY_BE9-0sW-rq7x6udmvfo(Unknown Source:0)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at com.czur.starry.device.smallroundscreen.AnimationActivity$$ExternalSyntheticLambda12.run(D8$$SyntheticClass:0)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at android.app.Activity.runOnUiThread(Activity.java:7664)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at com.czur.starry.device.smallroundscreen.AnimationActivity.startHdmiOutBGAnimation(AnimationActivity.kt:449)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at com.czur.starry.device.smallroundscreen.AnimationActivity.access$startHdmiOutBGAnimation(AnimationActivity.kt:58)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at com.czur.starry.device.smallroundscreen.AnimationActivity$initData$1.invokeSuspend(AnimationActivity.kt:133)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at com.czur.starry.device.smallroundscreen.AnimationActivity$initData$1.invoke(Unknown Source:8)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at com.czur.starry.device.smallroundscreen.AnimationActivity$initData$1.invoke(Unknown Source:4)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at androidx.lifecycle.RepeatOnLifecycleKt$repeatOnLifecycle$3$1$1$1$1$1$1.invokeSuspend(RepeatOnLifecycle.kt:111)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at androidx.lifecycle.RepeatOnLifecycleKt$repeatOnLifecycle$3$1$1$1$1$1$1.invoke(Unknown Source:8)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at androidx.lifecycle.RepeatOnLifecycleKt$repeatOnLifecycle$3$1$1$1$1$1$1.invoke(Unknown Source:4)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at kotlinx.coroutines.intrinsics.UndispatchedKt.startUndspatched(Undispatched.kt:66)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:43)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at kotlinx.coroutines.CoroutineScopeKt.coroutineScope(CoroutineScope.kt:286)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at androidx.lifecycle.RepeatOnLifecycleKt$repeatOnLifecycle$3$1$1$1$1.invokeSuspend(RepeatOnLifecycle.kt:110)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at kotlinx.coroutines.internal.DispatchedContinuationKt.resumeCancellableWith(DispatchedContinuation.kt:375)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at kotlinx.coroutines.intrinsics.CancellableKt.startCoroutineCancellable(Cancellable.kt:26)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at kotlinx.coroutines.CoroutineStart.invoke(CoroutineStart.kt:358)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at kotlinx.coroutines.AbstractCoroutine.start(AbstractCoroutine.kt:134)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at kotlinx.coroutines.BuildersKt__Builders_commonKt.launch(Builders.common.kt:53)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at kotlinx.coroutines.BuildersKt.launch(Unknown Source:1)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at kotlinx.coroutines.BuildersKt__Builders_commonKt.launch$default(Builders.common.kt:44)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at kotlinx.coroutines.BuildersKt.launch$default(Unknown Source:1)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at androidx.lifecycle.RepeatOnLifecycleKt$repeatOnLifecycle$3$1$1$1.onStateChanged(RepeatOnLifecycle.kt:106)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at androidx.lifecycle.LifecycleRegistry$ObserverWithState.dispatchEvent(LifecycleRegistry.jvm.kt:320)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at androidx.lifecycle.LifecycleRegistry.forwardPass(LifecycleRegistry.jvm.kt:257)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at androidx.lifecycle.LifecycleRegistry.sync(LifecycleRegistry.jvm.kt:293)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at androidx.lifecycle.LifecycleRegistry.moveToState(LifecycleRegistry.jvm.kt:142)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at androidx.lifecycle.LifecycleRegistry.handleLifecycleEvent(LifecycleRegistry.jvm.kt:124)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at androidx.lifecycle.ReportFragment$Companion.dispatch$lifecycle_runtime_release(ReportFragment.android.kt:190)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at androidx.lifecycle.ReportFragment$LifecycleCallbacks.onActivityPostResumed(ReportFragment.android.kt:125)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at android.app.Activity.dispatchActivityPostResumed(Activity.java:1509)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at android.app.Activity.performResume(Activity.java:8820)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at android.app.ActivityThread.performResumeActivity(ActivityThread.java:4993)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at android.app.ActivityThread.handleResumeActivity(ActivityThread.java:5036)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at android.app.servertransaction.TransactionExecutor.performLifecycleSequence(TransactionExecutor.java:229)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at android.app.servertransaction.TransactionExecutor.cycleToPath(TransactionExecutor.java:205)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at android.app.servertransaction.TransactionExecutor.executeCallbacks(TransactionExecutor.java:150)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at android.app.servertransaction.TransactionExecutor.execute(TransactionExecutor.java:96)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at android.app.ActivityThread$H.handleMessage(ActivityThread.java:2468)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at android.os.Handler.dispatchMessage(Handler.java:106)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at android.os.Looper.loopOnce(Looper.java:205)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at android.os.Looper.loop(Looper.java:294)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at android.app.ActivityThread.main(ActivityThread.java:8255)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at java.lang.reflect.Method.invoke(Native Method)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:552)
25_06_13 09:11:33.089 ERROR TextureVideoView 	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:974)
25_06_13 09:11:57.985 DEBUG UsbDeviceReceiver =====android.hardware.usb.action.USB_DEVICE_DETACHED
25_06_13 09:11:57.992 DEBUG AnimationActivity USB设备拔出: /dev/bus/usb/001/003
25_06_13 09:11:57.994 DEBUG AnimationViewModel emit:OUT
25_06_13 09:11:57.998 DEBUG AnimationActivity USBState:OUT
25_06_13 09:11:57.999 DEBUG AnimationActivity USB 拔出动画
25_06_13 09:11:58.001 DEBUG AnimationActivity 取消延迟设置低亮模式的任务
25_06_13 09:11:58.002 DEBUG AnimationActivity 设置屏幕亮度: 255
25_06_13 09:11:58.005 DEBUG AnimationActivity 设置屏幕亮度结果: true, 亮度值: 255
25_06_13 09:11:58.049 DEBUG AnimationActivity 屏幕亮度设置成功: 255
25_06_13 09:11:58.280 DEBUG BaseHdmiAnimationDrawable startPlayAnimation usb_extract
25_06_13 09:12:57.876 DEBUG AnimationActivity Hdmi Out 文字跳动动画
25_06_13 09:12:57.880 DEBUG BaseHdmiAnimationDrawable hdmi_flashing 结束
25_06_13 09:12:58.215 DEBUG BaseHdmiAnimationDrawable startPlayAnimation hdmi_flashing
25_06_13 09:14:57.879 DEBUG AnimationActivity Hdmi Out 文字跳动动画
25_06_13 09:14:57.888 DEBUG BaseHdmiAnimationDrawable hdmi_flashing 结束
25_06_13 09:14:58.108 DEBUG BaseHdmiAnimationDrawable startPlayAnimation hdmi_flashing
25_06_13 09:16:57.880 DEBUG AnimationActivity Hdmi Out 文字跳动动画
25_06_13 09:16:57.892 DEBUG BaseHdmiAnimationDrawable hdmi_flashing 结束
25_06_13 09:16:57.951 DEBUG BaseHdmiAnimationDrawable startPlayAnimation hdmi_flashing
25_06_13 09:18:57.884 DEBUG AnimationActivity Hdmi Out 文字跳动动画
25_06_13 09:18:57.887 DEBUG BaseHdmiAnimationDrawable hdmi_flashing 结束
25_06_13 09:18:57.958 DEBUG BaseHdmiAnimationDrawable startPlayAnimation hdmi_flashing
25_06_13 09:20:57.901 DEBUG AnimationActivity Hdmi Out 文字跳动动画
25_06_13 09:20:57.914 DEBUG BaseHdmiAnimationDrawable hdmi_flashing 结束
25_06_13 09:20:57.983 DEBUG BaseHdmiAnimationDrawable startPlayAnimation hdmi_flashing
25_06_13 09:22:57.916 DEBUG AnimationActivity Hdmi Out 文字跳动动画
25_06_13 09:22:57.923 DEBUG BaseHdmiAnimationDrawable hdmi_flashing 结束
25_06_13 09:22:58.030 DEBUG BaseHdmiAnimationDrawable startPlayAnimation hdmi_flashing
25_06_13 09:24:57.920 DEBUG AnimationActivity Hdmi Out 文字跳动动画
25_06_13 09:24:57.923 DEBUG BaseHdmiAnimationDrawable hdmi_flashing 结束
25_06_13 09:24:57.948 DEBUG BaseHdmiAnimationDrawable startPlayAnimation hdmi_flashing
25_06_13 09:26:48.672 DEBUG AnimationActivity Received SCREEN_OFF_SOON broadcast
25_06_13 09:26:57.923 DEBUG AnimationActivity Hdmi Out 文字跳动动画
25_06_13 09:26:57.925 DEBUG BaseHdmiAnimationDrawable hdmi_flashing 结束
25_06_13 09:26:58.000 DEBUG BaseHdmiAnimationDrawable startPlayAnimation hdmi_flashing
25_06_13 09:28:48.587 VERBOSE CZURAtyManager 添加:AnimationActivity(onCreate)
25_06_13 09:28:48.654 DEBUG ShareViewModel peripheralUSBRunningFlow = false
25_06_13 09:28:48.655 VERBOSE ShareViewModel refreshGadgetState
25_06_13 09:28:48.669 DEBUG NoticeHandler 注册回调
25_06_13 09:28:48.669 DEBUG NoticeHandler 消息类型:type=( SYNC - [ 外设USB模式变化 ] )第一次注册
25_06_13 09:28:48.679 VERBOSE SystemManagerProxy getGadgetMode Start
25_06_13 09:28:48.683 DEBUG SystemManagerProxy isGadgetMode Value: 0
25_06_13 09:28:48.684 DEBUG AnimationActivity onStart
25_06_13 09:28:48.685 DEBUG AnimationActivity Activity 刚被创建，靠onWindowFocusChanged刷新
25_06_13 09:28:48.689 INFO CZBusinessAty page-start: AnimationActivity
25_06_13 09:28:48.690 DEBUG AnimationActivity Hdmi Out 背景动画
25_06_13 09:28:48.691 DEBUG AnimationActivity BYOM外设 false ，Launch启动设置完成通知
25_06_13 09:28:48.692 DEBUG AnimationActivity USB外设 false ，Launch启动设置完成通知
25_06_13 09:28:48.719 DEBUG ShareViewModel refreshBYOMState
25_06_13 09:28:48.763 DEBUG TextureVideoView prepareMediaPlayer: android.resource://com.czur.starry.device.smallroundscreen/2131623937
25_06_13 09:28:50.375 DEBUG AnimationActivity AnimationActivity 已经显示到屏幕上
25_06_13 09:28:51.178 DEBUG ShareViewModel eShareCallback key = eshare_register_info
25_06_13 09:28:51.879 DEBUG ShareViewModel eShareCallback key = eshare_byom_running
25_06_13 09:28:51.880 DEBUG ShareViewModel eShareCallback isByomrunning = false
25_06_13 09:28:52.240 DEBUG ShareViewModel eShareCallback key = eshare_qrcode_info
25_06_13 09:28:52.246 DEBUG ShareViewModel eShareCallback key = eshare_network_info
25_06_13 09:28:52.380 DEBUG AnimationActivity HDMI OUT 已连接
25_06_13 09:28:52.382 DEBUG AnimationActivity Hdmi Out 文字已连接动画
25_06_13 09:28:52.386 DEBUG AnimationActivity 取消延迟设置低亮模式的任务
25_06_13 09:28:52.386 DEBUG AnimationActivity 设置屏幕亮度: 255
25_06_13 09:28:52.387 DEBUG AnimationActivity 设置屏幕亮度结果: true, 亮度值: 255
25_06_13 09:28:52.391 DEBUG AnimationActivity 屏幕亮度设置成功: 255
25_06_13 09:28:52.689 DEBUG BaseHdmiAnimationDrawable startPlayAnimation hdmi_out_normal
25_06_13 09:29:02.378 DEBUG AnimationActivity 添加文字跳动动画任务
25_06_13 09:29:02.385 DEBUG AnimationActivity Hdmi Out 文字跳动动画
25_06_13 09:29:02.387 DEBUG BaseHdmiAnimationDrawable hdmi_out_normal 结束
25_06_13 09:29:02.704 DEBUG BaseHdmiAnimationDrawable startPlayAnimation hdmi_flashing
25_06_13 09:29:09.049 DEBUG UsbDeviceReceiver =====android.hardware.usb.action.USB_DEVICE_ATTACHED
25_06_13 09:29:09.058 DEBUG AnimationActivity USB设备插入: /dev/bus/usb/001/005
25_06_13 09:29:09.059 DEBUG AnimationViewModel emit:IN
25_06_13 09:29:09.068 DEBUG AnimationActivity USBState:IN
25_06_13 09:29:09.069 DEBUG AnimationActivity USB 插入动画
25_06_13 09:29:09.070 DEBUG AnimationActivity 取消延迟设置低亮模式的任务
25_06_13 09:29:09.071 DEBUG AnimationActivity 设置屏幕亮度: 255
25_06_13 09:29:09.072 DEBUG AnimationActivity 设置屏幕亮度结果: true, 亮度值: 255
25_06_13 09:29:09.110 DEBUG AnimationActivity 屏幕亮度设置成功: 255
25_06_13 09:29:09.343 DEBUG BaseHdmiAnimationDrawable startPlayAnimation usb_insert
