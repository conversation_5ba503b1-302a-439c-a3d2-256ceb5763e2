25_06_13 09:10:53.960 INFO WatchService 监控服务启动...
25_06_13 09:10:54.018 VERBOSE CameraWatcher startWatchCamera
25_06_13 09:10:54.021 DEBUG CompatibleTools 兼容系统层
25_06_13 09:10:54.026 VERBOSE WatchService 监控是否有网
25_06_13 09:10:54.028 WARN SystemManagerProxy onSystemChangeListener is null,add default listener
25_06_13 09:10:54.029 VERBOSE SystemManagerProxy getGadgetMode Start
25_06_13 09:10:54.032 DEBUG SystemManagerProxy isGadgetMode Value: 0
25_06_13 09:10:54.033 DEBUG WatchService byomRunningFlow:false
25_06_13 09:10:54.035 DEBUG SystemManagerProxy updateAudioPolicyByByom: false
25_06_13 09:10:54.036 VERBOSE WatchService enableHDMI Audio:true
25_06_13 09:10:54.038 DEBUG WatchService 开始监控 登录状态和网络
25_06_13 09:10:54.041 VERBOSE WatchService BYOM状态变化:es:0 usb:0
25_06_13 09:10:54.042 VERBOSE WatchService 释放CPU锁
25_06_13 09:10:54.044 DEBUG WatchService 写入BYOM(eshare&usb)状态变化(for system prop):0
25_06_13 09:10:54.046 VERBOSE WatchService 监控网络状态
25_06_13 09:10:54.048 DEBUG WatchService 手动刷新Netty状态
25_06_13 09:10:54.059 INFO AppInstallWatch 用户关闭了提醒, 不再请求该接口
25_06_13 09:10:54.209 VERBOSE ActiveInterceptor 需要激活:/api/app/store/app/whitelist
25_06_13 09:10:54.211 WARN SystemManagerProxy onSystemChangeListener is null,add default listener
25_06_13 09:10:54.212 VERBOSE WatchService 用户登出
25_06_13 09:10:54.214 DEBUG WatchService doOnLogOut
25_06_13 09:10:54.215 VERBOSE WatchService 停止NettyService
25_06_13 09:10:54.223 VERBOSE CommonParamInterceptor Token为空
25_06_13 09:10:54.225 VERBOSE ProviderUtil ContentValues: unReadFileTabUpdate=8  cv.size: 1
25_06_13 09:10:54.226 VERBOSE ProviderUtil Key: unReadFileTabUpdate, Value: 8
25_06_13 09:10:54.227 VERBOSE ProviderUtil update uri: content://com.czur.starry.device.livedata.fileprovider/getSPValue , contentValues Size: 1
25_06_13 09:10:54.228 VERBOSE NetLogging 请求信息
25_06_13 09:10:54.228 VERBOSE NetLogging ╔══════════════════════════════
25_06_13 09:10:54.228 VERBOSE NetLogging ║ method:GET
25_06_13 09:10:54.228 VERBOSE NetLogging ║ host:test0927-starry.czur.cc
25_06_13 09:10:54.228 VERBOSE NetLogging ║ path:/api/starry/register/v2/active
25_06_13 09:10:54.228 VERBOSE NetLogging ║ -----
25_06_13 09:10:54.228 VERBOSE NetLogging ║ 请求头:
25_06_13 09:10:54.228 VERBOSE NetLogging ║ UDID: CBP02B2503000001
25_06_13 09:10:54.228 VERBOSE NetLogging ║ App-Key: com.czur.starry
25_06_13 09:10:54.228 VERBOSE NetLogging ║ Request-Timestamp: 1749777054221
25_06_13 09:10:54.228 VERBOSE NetLogging ║ 
25_06_13 09:10:54.228 VERBOSE NetLogging ║ url参数:
25_06_13 09:10:54.228 VERBOSE NetLogging ║ sn: CBP02B2503000001
25_06_13 09:10:54.228 VERBOSE NetLogging ║ secret: 1qaz2wsx3edc4rfv
25_06_13 09:10:54.228 VERBOSE NetLogging ║ voucher: C3190FB7E60F4F9689EA083680E3DCB7
25_06_13 09:10:54.228 VERBOSE NetLogging ║ mac: 9a:8e:5c:0b:11:26
25_06_13 09:10:54.228 VERBOSE NetLogging ║ udid: CBP02B2503000001
25_06_13 09:10:54.228 VERBOSE NetLogging ║ noncestr: 3030667814391850
25_06_13 09:10:54.228 VERBOSE NetLogging ║ timestamp: 1749777054221
25_06_13 09:10:54.228 VERBOSE NetLogging ║ sign: 8c584eab867e22e61c8097171db91e618126091a
25_06_13 09:10:54.228 VERBOSE NetLogging ╚══════════════════════════════
25_06_13 09:10:54.230 ERROR NetLogging 请求出错 path->/api/starry/register/v2/active
25_06_13 09:10:54.231 ERROR SyncHttpTask 请求出错:
25_06_13 09:10:54.233 ERROR NetResultUtil 网络错误
25_06_13 09:10:54.234 VERBOSE NetResultUtil 没有网络连接
25_06_13 09:10:54.236 DEBUG AppInstallWatch 获取白名单失败: 没有连接任何网络
25_06_13 09:10:54.406 VERBOSE ProviderUtil ContentValues: is_file_share_code_status=8066  cv.size: 1
25_06_13 09:10:54.408 VERBOSE ProviderUtil Key: is_file_share_code_status, Value: 8066
25_06_13 09:10:54.409 VERBOSE ProviderUtil update uri: content://com.czur.starry.device.livedata.fileprovider/getSPValue , contentValues Size: 1
25_06_13 09:10:54.437 VERBOSE ProviderUtil update result: 1
25_06_13 09:10:54.438 INFO FileHandlerLive 更新成功, 更新缓存
25_06_13 09:10:54.440 DEBUG WatchService 标记AI字幕临时文件删除成功
25_06_13 09:10:54.446 VERBOSE ProviderUtil update result: 1
25_06_13 09:10:54.448 INFO FileHandlerLive 更新成功, 更新缓存
25_06_13 09:10:55.440 VERBOSE ProviderUtil ContentValues: unReadFileTabUpdate=0  cv.size: 1
25_06_13 09:10:55.442 VERBOSE ProviderUtil Key: unReadFileTabUpdate, Value: 0
25_06_13 09:10:55.444 VERBOSE ProviderUtil update uri: content://com.czur.starry.device.livedata.fileprovider/getSPValue , contentValues Size: 1
25_06_13 09:10:55.459 VERBOSE ProviderUtil update result: 1
25_06_13 09:10:55.461 INFO FileHandlerLive 更新成功, 更新缓存
25_06_13 09:11:31.768 VERBOSE SystemManagerProxy category: 2, eventID: 4, para1: 0, extend1: , extend2: 
25_06_13 09:11:31.773 VERBOSE SystemManagerProxy category: 2, eventID: 4, para1: 0, extend1: , extend2: 
25_06_13 09:11:34.849 DEBUG WatchService 手动刷新Netty状态
25_06_13 09:13:54.028 VERBOSE WatchService 停止NettyService
25_06_13 09:16:54.028 VERBOSE WatchService 停止NettyService
25_06_13 09:19:54.033 VERBOSE WatchService 停止NettyService
25_06_13 09:20:54.046 DEBUG NetStatusUtil 手动更新网络状态
25_06_13 09:20:54.059 VERBOSE WatchService 停止NettyService
25_06_13 09:22:54.033 VERBOSE WatchService 停止NettyService
25_06_13 09:25:54.037 VERBOSE WatchService 停止NettyService
25_06_13 09:28:51.283 INFO WatchService 监控服务启动...
25_06_13 09:28:51.482 VERBOSE CameraWatcher startWatchCamera
25_06_13 09:28:51.492 DEBUG CompatibleTools 兼容系统层
25_06_13 09:28:51.495 VERBOSE WatchService 监控是否有网
25_06_13 09:28:51.500 WARN SystemManagerProxy onSystemChangeListener is null,add default listener
25_06_13 09:28:51.505 VERBOSE SystemManagerProxy getGadgetMode Start
25_06_13 09:28:51.541 DEBUG SystemManagerProxy isGadgetMode Value: 0
25_06_13 09:28:51.543 DEBUG WatchService byomRunningFlow:false
25_06_13 09:28:51.545 DEBUG SystemManagerProxy updateAudioPolicyByByom: false
25_06_13 09:28:51.546 VERBOSE WatchService enableHDMI Audio:true
25_06_13 09:28:51.556 DEBUG WatchService 开始监控 登录状态和网络
25_06_13 09:28:51.564 VERBOSE WatchService BYOM状态变化:es:0 usb:0
25_06_13 09:28:51.567 VERBOSE WatchService 释放CPU锁
25_06_13 09:28:51.569 DEBUG WatchService 写入BYOM(eshare&usb)状态变化(for system prop):0
25_06_13 09:28:51.631 VERBOSE WatchService 监控网络状态
25_06_13 09:28:51.638 DEBUG WatchService 手动刷新Netty状态
25_06_13 09:28:51.683 VERBOSE WatchService 停止NettyService
25_06_13 09:28:51.684 VERBOSE WatchService 用户登出
25_06_13 09:28:51.694 DEBUG WatchService doOnLogOut
25_06_13 09:28:51.697 INFO AppInstallWatch 用户关闭了提醒, 不再请求该接口
25_06_13 09:28:51.699 VERBOSE ProviderUtil ContentValues: unReadFileTabUpdate=8  cv.size: 1
25_06_13 09:28:51.701 VERBOSE ProviderUtil Key: unReadFileTabUpdate, Value: 8
25_06_13 09:28:51.702 VERBOSE ProviderUtil update uri: content://com.czur.starry.device.livedata.fileprovider/getSPValue , contentValues Size: 1
25_06_13 09:28:51.761 VERBOSE ProviderUtil update result: 1
25_06_13 09:28:51.763 INFO FileHandlerLive 更新成功, 更新缓存
25_06_13 09:28:51.764 DEBUG WatchService 标记AI字幕临时文件删除成功
25_06_13 09:28:51.863 VERBOSE ActiveInterceptor 需要激活:/api/app/store/app/whitelist
25_06_13 09:28:51.865 WARN SystemManagerProxy onSystemChangeListener is null,add default listener
25_06_13 09:28:51.915 VERBOSE CommonParamInterceptor Token为空
25_06_13 09:28:51.953 VERBOSE NetLogging 请求信息
25_06_13 09:28:51.953 VERBOSE NetLogging ╔══════════════════════════════
25_06_13 09:28:51.953 VERBOSE NetLogging ║ method:GET
25_06_13 09:28:51.953 VERBOSE NetLogging ║ host:test0927-starry.czur.cc
25_06_13 09:28:51.953 VERBOSE NetLogging ║ path:/api/starry/register/v2/active
25_06_13 09:28:51.953 VERBOSE NetLogging ║ -----
25_06_13 09:28:51.953 VERBOSE NetLogging ║ 请求头:
25_06_13 09:28:51.953 VERBOSE NetLogging ║ UDID: CBP02B2503000001
25_06_13 09:28:51.953 VERBOSE NetLogging ║ App-Key: com.czur.starry
25_06_13 09:28:51.953 VERBOSE NetLogging ║ Request-Timestamp: 1749778131913
25_06_13 09:28:51.953 VERBOSE NetLogging ║ 
25_06_13 09:28:51.953 VERBOSE NetLogging ║ url参数:
25_06_13 09:28:51.953 VERBOSE NetLogging ║ sn: CBP02B2503000001
25_06_13 09:28:51.953 VERBOSE NetLogging ║ secret: 1qaz2wsx3edc4rfv
25_06_13 09:28:51.953 VERBOSE NetLogging ║ voucher: C3190FB7E60F4F9689EA083680E3DCB7
25_06_13 09:28:51.953 VERBOSE NetLogging ║ mac: 9a:8e:5c:0b:11:26
25_06_13 09:28:51.953 VERBOSE NetLogging ║ udid: CBP02B2503000001
25_06_13 09:28:51.953 VERBOSE NetLogging ║ noncestr: 3030667814391850
25_06_13 09:28:51.953 VERBOSE NetLogging ║ timestamp: 1749778131913
25_06_13 09:28:51.953 VERBOSE NetLogging ║ sign: 45989e28e3992982bfcd82524d1f5a7a0a406753
25_06_13 09:28:51.953 VERBOSE NetLogging ╚══════════════════════════════
25_06_13 09:28:52.081 ERROR NetLogging 请求出错 path->/api/starry/register/v2/active
25_06_13 09:28:52.087 ERROR SyncHttpTask 请求出错:
25_06_13 09:28:52.091 ERROR NetResultUtil 网络错误
25_06_13 09:28:52.094 VERBOSE NetResultUtil 没有网络连接
25_06_13 09:28:52.096 DEBUG AppInstallWatch 获取白名单失败: 没有连接任何网络
25_06_13 09:28:52.762 VERBOSE ProviderUtil ContentValues: unReadFileTabUpdate=0  cv.size: 1
25_06_13 09:28:52.763 VERBOSE ProviderUtil Key: unReadFileTabUpdate, Value: 0
25_06_13 09:28:52.764 VERBOSE ProviderUtil update uri: content://com.czur.starry.device.livedata.fileprovider/getSPValue , contentValues Size: 1
25_06_13 09:28:52.771 VERBOSE ProviderUtil update result: 1
25_06_13 09:28:52.772 INFO FileHandlerLive 更新成功, 更新缓存
