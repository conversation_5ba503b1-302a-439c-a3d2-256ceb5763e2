25_06_13 09:11:35.116 DEBUG FileWatcherService ======onCreate
25_06_13 09:11:35.125 DEBUG FileWatcherService action: 
25_06_13 09:11:35.149 VERBOSE ProviderUtil ContentValues: is_unread_file_name=null  cv.size: 1
25_06_13 09:11:35.151 VERBOSE ProviderUtil Key: is_unread_file_name, Value: null
25_06_13 09:11:35.152 VERBOSE ProviderUtil update uri: content://com.czur.starry.device.livedata.fileprovider/getSPValue , contentValues Size: 1
25_06_13 09:11:35.196 VERBOSE ProviderUtil update result: 1
25_06_13 09:11:35.198 INFO FileHandlerLive 更新成功, 更新缓存
25_06_13 09:11:35.200 DEBUG UnReadFileManager =====pathType=TYPE_LOCAL_FILE
25_06_13 09:11:35.203 VERBOSE ProviderUtil ContentValues: is_unread_file_status_local=false  cv.size: 1
25_06_13 09:11:35.205 VERBOSE ProviderUtil Key: is_unread_file_status_local, Value: false
25_06_13 09:11:35.206 VERBOSE ProviderUtil update uri: content://com.czur.starry.device.livedata.fileprovider/getSPValue , contentValues Size: 1
25_06_13 09:11:35.215 VERBOSE ProviderUtil update result: 1
25_06_13 09:11:35.216 INFO FileHandlerLive 更新成功, 更新缓存
25_06_13 09:11:35.217 DEBUG UnReadFileManager =====pathType=TYPE_SCREEN_SHORT_FILE
25_06_13 09:11:35.220 VERBOSE ProviderUtil ContentValues: is_unread_file_status_screenshort=false  cv.size: 1
25_06_13 09:11:35.221 VERBOSE ProviderUtil Key: is_unread_file_status_screenshort, Value: false
25_06_13 09:11:35.223 VERBOSE ProviderUtil update uri: content://com.czur.starry.device.livedata.fileprovider/getSPValue , contentValues Size: 1
25_06_13 09:11:35.242 VERBOSE ProviderUtil update result: 1
25_06_13 09:11:35.246 INFO FileHandlerLive 更新成功, 更新缓存
25_06_13 09:11:35.248 DEBUG UnReadFileManager =====pathType=TYPE_DOWNLOAD_FILE
25_06_13 09:11:35.251 VERBOSE ProviderUtil ContentValues: is_unread_file_status_download=false  cv.size: 1
25_06_13 09:11:35.253 VERBOSE ProviderUtil Key: is_unread_file_status_download, Value: false
25_06_13 09:11:35.254 VERBOSE ProviderUtil update uri: content://com.czur.starry.device.livedata.fileprovider/getSPValue , contentValues Size: 1
25_06_13 09:11:35.282 VERBOSE ProviderUtil update result: 1
25_06_13 09:11:35.284 INFO FileHandlerLive 更新成功, 更新缓存
25_06_13 09:11:35.285 DEBUG UnReadFileManager =====pathType=TYPE_SHARE_FILE
25_06_13 09:11:35.288 VERBOSE ProviderUtil ContentValues: is_unread_file_status_share=false  cv.size: 1
25_06_13 09:11:35.292 VERBOSE ProviderUtil Key: is_unread_file_status_share, Value: false
25_06_13 09:11:35.303 VERBOSE ProviderUtil update uri: content://com.czur.starry.device.livedata.fileprovider/getSPValue , contentValues Size: 1
25_06_13 09:11:35.322 VERBOSE ProviderUtil update result: 1
25_06_13 09:11:35.324 INFO FileHandlerLive 更新成功, 更新缓存
25_06_13 09:11:35.325 DEBUG UnReadFileManager =====pathType=TYPE_MEETING_FILE
25_06_13 09:11:35.326 VERBOSE ProviderUtil ContentValues: is_unread_file_status_meeting=false  cv.size: 1
25_06_13 09:11:35.328 VERBOSE ProviderUtil Key: is_unread_file_status_meeting, Value: false
25_06_13 09:11:35.329 VERBOSE ProviderUtil update uri: content://com.czur.starry.device.livedata.fileprovider/getSPValue , contentValues Size: 1
25_06_13 09:11:35.347 VERBOSE ProviderUtil update result: 1
25_06_13 09:11:35.369 INFO FileHandlerLive 更新成功, 更新缓存
25_06_13 09:11:35.372 DEBUG UnReadFileManager =====pathType=TYPE_AI_TRANS_FILE
25_06_13 09:11:35.374 VERBOSE ProviderUtil ContentValues: is_unread_file_status_ai_trans=false  cv.size: 1
25_06_13 09:11:35.375 VERBOSE ProviderUtil Key: is_unread_file_status_ai_trans, Value: false
25_06_13 09:11:35.376 VERBOSE ProviderUtil update uri: content://com.czur.starry.device.livedata.fileprovider/getSPValue , contentValues Size: 1
25_06_13 09:11:35.381 VERBOSE ProviderUtil update result: 1
25_06_13 09:11:35.389 INFO FileHandlerLive 更新成功, 更新缓存
25_06_13 09:11:35.390 VERBOSE ProviderUtil ContentValues: unReadFileTabUpdate=0  cv.size: 1
25_06_13 09:11:35.392 VERBOSE ProviderUtil Key: unReadFileTabUpdate, Value: 0
25_06_13 09:11:35.393 VERBOSE ProviderUtil update uri: content://com.czur.starry.device.livedata.fileprovider/getSPValue , contentValues Size: 1
25_06_13 09:11:35.395 VERBOSE ProviderUtil update result: 1
25_06_13 09:11:35.396 INFO FileHandlerLive 更新成功, 更新缓存
25_06_13 09:11:36.399 DEBUG FileWatcherService ====fileTabUpdateLive==0
25_06_13 09:11:36.403 DEBUG UnReadFileManager ==showTypeKey==0
25_06_13 09:11:36.407 VERBOSE ProviderUtil ContentValues: is_unread_file_name=null  cv.size: 1
25_06_13 09:11:36.408 VERBOSE ProviderUtil Key: is_unread_file_name, Value: null
25_06_13 09:11:36.410 VERBOSE ProviderUtil update uri: content://com.czur.starry.device.livedata.fileprovider/getSPValue , contentValues Size: 1
25_06_13 09:11:36.424 VERBOSE ProviderUtil update result: 1
25_06_13 09:11:36.428 INFO FileHandlerLive 更新成功, 更新缓存
25_06_13 09:28:51.530 DEBUG FileWatcherService ======onCreate
25_06_13 09:28:51.636 VERBOSE ProviderUtil ContentValues: is_unread_file_name=null  cv.size: 1
25_06_13 09:28:51.648 VERBOSE ProviderUtil Key: is_unread_file_name, Value: null
25_06_13 09:28:51.649 VERBOSE ProviderUtil update uri: content://com.czur.starry.device.livedata.fileprovider/getSPValue , contentValues Size: 1
25_06_13 09:28:51.660 VERBOSE ProviderUtil update result: 1
25_06_13 09:28:51.661 INFO FileHandlerLive 更新成功, 更新缓存
25_06_13 09:28:51.665 DEBUG FileWatcherService action: 
25_06_13 09:28:51.679 DEBUG UnReadFileManager =====pathType=TYPE_LOCAL_FILE
25_06_13 09:28:51.755 VERBOSE ProviderUtil ContentValues: is_unread_file_status_local=false  cv.size: 1
25_06_13 09:28:51.757 VERBOSE ProviderUtil Key: is_unread_file_status_local, Value: false
25_06_13 09:28:51.758 VERBOSE ProviderUtil update uri: content://com.czur.starry.device.livedata.fileprovider/getSPValue , contentValues Size: 1
25_06_13 09:28:51.771 VERBOSE ProviderUtil update result: 1
25_06_13 09:28:51.772 INFO FileHandlerLive 更新成功, 更新缓存
25_06_13 09:28:51.852 DEBUG UnReadFileManager =====pathType=TYPE_SCREEN_SHORT_FILE
25_06_13 09:28:51.853 VERBOSE ProviderUtil ContentValues: is_unread_file_status_screenshort=false  cv.size: 1
25_06_13 09:28:51.854 VERBOSE ProviderUtil Key: is_unread_file_status_screenshort, Value: false
25_06_13 09:28:51.855 VERBOSE ProviderUtil update uri: content://com.czur.starry.device.livedata.fileprovider/getSPValue , contentValues Size: 1
25_06_13 09:28:51.935 VERBOSE ProviderUtil update result: 1
25_06_13 09:28:51.937 INFO FileHandlerLive 更新成功, 更新缓存
25_06_13 09:28:51.940 DEBUG UnReadFileManager =====pathType=TYPE_DOWNLOAD_FILE
25_06_13 09:28:51.942 VERBOSE ProviderUtil ContentValues: is_unread_file_status_download=false  cv.size: 1
25_06_13 09:28:51.943 VERBOSE ProviderUtil Key: is_unread_file_status_download, Value: false
25_06_13 09:28:51.945 VERBOSE ProviderUtil update uri: content://com.czur.starry.device.livedata.fileprovider/getSPValue , contentValues Size: 1
25_06_13 09:28:51.983 VERBOSE ProviderUtil update result: 1
25_06_13 09:28:51.989 INFO FileHandlerLive 更新成功, 更新缓存
25_06_13 09:28:51.991 DEBUG UnReadFileManager =====pathType=TYPE_SHARE_FILE
25_06_13 09:28:51.993 VERBOSE ProviderUtil ContentValues: is_unread_file_status_share=false  cv.size: 1
25_06_13 09:28:51.995 VERBOSE ProviderUtil Key: is_unread_file_status_share, Value: false
25_06_13 09:28:51.996 VERBOSE ProviderUtil update uri: content://com.czur.starry.device.livedata.fileprovider/getSPValue , contentValues Size: 1
25_06_13 09:28:52.013 VERBOSE ProviderUtil update result: 1
25_06_13 09:28:52.017 INFO FileHandlerLive 更新成功, 更新缓存
25_06_13 09:28:52.019 DEBUG UnReadFileManager =====pathType=TYPE_MEETING_FILE
25_06_13 09:28:52.025 VERBOSE ProviderUtil ContentValues: is_unread_file_status_meeting=false  cv.size: 1
25_06_13 09:28:52.027 VERBOSE ProviderUtil Key: is_unread_file_status_meeting, Value: false
25_06_13 09:28:52.028 VERBOSE ProviderUtil update uri: content://com.czur.starry.device.livedata.fileprovider/getSPValue , contentValues Size: 1
25_06_13 09:28:52.061 VERBOSE ProviderUtil update result: 1
25_06_13 09:28:52.063 INFO FileHandlerLive 更新成功, 更新缓存
25_06_13 09:28:52.069 DEBUG UnReadFileManager =====pathType=TYPE_AI_TRANS_FILE
25_06_13 09:28:52.071 VERBOSE ProviderUtil ContentValues: is_unread_file_status_ai_trans=false  cv.size: 1
25_06_13 09:28:52.094 VERBOSE ProviderUtil Key: is_unread_file_status_ai_trans, Value: false
25_06_13 09:28:52.095 VERBOSE ProviderUtil update uri: content://com.czur.starry.device.livedata.fileprovider/getSPValue , contentValues Size: 1
25_06_13 09:28:52.097 VERBOSE ProviderUtil update result: 1
25_06_13 09:28:52.103 INFO FileHandlerLive 更新成功, 更新缓存
25_06_13 09:28:52.105 VERBOSE ProviderUtil ContentValues: unReadFileTabUpdate=0  cv.size: 1
25_06_13 09:28:52.108 VERBOSE ProviderUtil Key: unReadFileTabUpdate, Value: 0
25_06_13 09:28:52.111 VERBOSE ProviderUtil update uri: content://com.czur.starry.device.livedata.fileprovider/getSPValue , contentValues Size: 1
25_06_13 09:28:52.132 VERBOSE ProviderUtil update result: 1
25_06_13 09:28:52.134 INFO FileHandlerLive 更新成功, 更新缓存
25_06_13 09:28:53.142 DEBUG FileWatcherService ====fileTabUpdateLive==0
25_06_13 09:28:53.143 DEBUG UnReadFileManager ==showTypeKey==0
25_06_13 09:28:53.145 VERBOSE ProviderUtil ContentValues: is_unread_file_name=null  cv.size: 1
25_06_13 09:28:53.146 VERBOSE ProviderUtil Key: is_unread_file_name, Value: null
25_06_13 09:28:53.147 VERBOSE ProviderUtil update uri: content://com.czur.starry.device.livedata.fileprovider/getSPValue , contentValues Size: 1
25_06_13 09:28:53.158 VERBOSE ProviderUtil update result: 1
25_06_13 09:28:53.159 INFO FileHandlerLive 更新成功, 更新缓存
