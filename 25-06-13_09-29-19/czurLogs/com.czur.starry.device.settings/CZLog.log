25_06_13 09:10:45.652 DEBUG CZURSettingsProvider 查询:language
25_06_13 09:10:46.264 DEBUG TouchPad-BootReceiver 启动TouchPadService
25_06_13 09:10:46.273 VERBOSE ArmyKeyboardUsbReceiver 非军工版,不执行对应逻辑
25_06_13 09:10:46.275 DEBUG ArmyKeyboardUsbReceiver ACTION_BOOT_COMPLETED
25_06_13 09:10:46.281 VERBOSE ArmyUsbManager 从键盘获取时间 START
25_06_13 09:10:46.290 WARN ArmyUsbManager 没有找到USB键盘设备
25_06_13 09:11:20.861 VERBOSE CZURAtyManager 添加:StartUpTimeZoneActivity(onCreate)
25_06_13 09:11:21.055 VERBOSE StartUpTimeZoneActivity 初始化选中时区
25_06_13 09:11:22.878 DEBUG StartUpTimeZoneActivity 设置时区:TimeZoneEntity(id=Asia/Shanghai, name=中国标准时间, pinyin=ZHONGGUOBIAOZHUNSHIJIAN, gmt=GMT+08:00, offset=28800000)
25_06_13 09:11:22.881 DEBUG TimeManager 设置时区:Asia/Shanghai
25_06_13 09:11:22.885 DEBUG BaseStartupActivity 下一步
25_06_13 09:11:23.011 VERBOSE CZURAtyManager 添加:StartUpWifiActivity(onCreate)
25_06_13 09:11:23.038 VERBOSE StartUpWifiActivity 移动到列表页面
25_06_13 09:11:23.047 DEBUG StartUpWifiVM 开始扫描WIFI
25_06_13 09:11:23.048 DEBUG WifiUtil 开始扫描WIFI
25_06_13 09:11:23.057 DEBUG StartUpWifiVM 扫描WIFI信号完成, 扫描到了52个信号
25_06_13 09:11:23.077 VERBOSE StartUpWifiActivity 更新下一步按钮状态
25_06_13 09:11:23.078 VERBOSE StartUpWifiActivity 当前是列表页面
25_06_13 09:11:23.079 VERBOSE StartUpWifiActivity 更新下一步按钮状态
25_06_13 09:11:23.080 VERBOSE StartUpWifiActivity 当前是列表页面
25_06_13 09:11:23.081 VERBOSE StartUpWifiActivity 更新下一步按钮状态
25_06_13 09:11:23.084 VERBOSE StartUpWifiActivity 当前是列表页面
25_06_13 09:11:23.085 DEBUG StartUpWifiVM receiver#action: android.net.wifi.WIFI_STATE_CHANGED
25_06_13 09:11:23.086 DEBUG StartUpWifiVM WIFI状态改变
25_06_13 09:11:23.089 DEBUG WifiUtil 连接信息: SSID: <unknown ssid>, BSSID: <none>, MAC: 9c:b8:b4:86:48:2c, IP: null, Security type: -1, Supplicant state: DISCONNECTED, Wi-Fi standard: 0, RSSI: -127, Link speed: -1Mbps, Tx Link speed: -1Mbps, Max Supported Tx Link speed: -1Mbps, Rx Link speed: -1Mbps, Max Supported Rx Link speed: -1Mbps, Frequency: -1MHz, Net ID: -1, Metered hint: false, score: 0, isUsable: true, CarrierMerged: false, SubscriptionId: -1, IsPrimary: 0, Trusted: false, Restricted: false, Ephemeral: false, OEM paid: false, OEM private: false, OSU AP: false, FQDN: <none>, Provider friendly name: <none>, Requesting package name: <none><none>MLO Information: , Is TID-To-Link negotiation supported by the AP: false, AP MLD Address: <none>, AP MLO Link Id: <none>, AP MLO Affiliated links: <none>
25_06_13 09:11:23.475 VERBOSE CZURAtyManager remove:StartUpTimeZoneActivity(onDestroy)
25_06_13 09:11:25.247 VERBOSE StartUpWifiActivity 立即销毁loadingDialog
25_06_13 09:11:25.248 VERBOSE LoadingDialog 取消延迟显示
25_06_13 09:11:25.253 WARN LoadingDialog Dialog还没有调用Show方法
25_06_13 09:11:25.254 VERBOSE CZURAtyManager remove:StartUpWifiActivity(onDestroy)
25_06_13 09:11:25.256 DEBUG CZURAtyManager com.czur.starry.device.settings已经没有可见页面
25_06_13 09:11:34.406 DEBUG CZURSettingsProvider 查询:language
25_06_13 09:11:34.843 DEBUG CZURSettingsProvider 更新对焦>>>
25_06_13 09:11:34.849 DEBUG CZURSettingsProvider 更新状态:0
25_06_13 09:11:34.869 DEBUG CZURSettingsProvider currentStatus:0,status:0
25_06_13 09:11:34.878 DEBUG CZURSettingsProvider 更新对焦结束: 0<<<
25_06_13 09:11:55.961 DEBUG CZURSettingsProvider 查询:language
25_06_13 09:13:56.016 DEBUG CZURSettingsProvider 查询:language
25_06_13 09:15:56.056 DEBUG CZURSettingsProvider 查询:language
25_06_13 09:17:56.097 DEBUG CZURSettingsProvider 查询:language
25_06_13 09:19:56.148 DEBUG CZURSettingsProvider 查询:language
25_06_13 09:21:56.179 DEBUG CZURSettingsProvider 查询:language
25_06_13 09:23:56.245 DEBUG CZURSettingsProvider 查询:language
25_06_13 09:25:56.293 DEBUG CZURSettingsProvider 查询:language
25_06_13 09:28:50.363 DEBUG TouchPad-BootReceiver 启动TouchPadService
25_06_13 09:28:50.386 VERBOSE ArmyKeyboardUsbReceiver 非军工版,不执行对应逻辑
25_06_13 09:28:50.388 DEBUG ArmyKeyboardUsbReceiver ACTION_BOOT_COMPLETED
25_06_13 09:28:50.394 VERBOSE ArmyUsbManager 从键盘获取时间 START
25_06_13 09:28:50.400 WARN ArmyUsbManager 没有找到USB键盘设备
25_06_13 09:28:50.448 DEBUG CZURSettingsProvider 查询:language
25_06_13 09:28:50.939 DEBUG CZURSettingsProvider 更新对焦>>>
25_06_13 09:28:50.940 DEBUG CZURSettingsProvider 更新状态:0
25_06_13 09:28:51.032 DEBUG CZURSettingsProvider currentStatus:0,status:0
25_06_13 09:28:51.038 DEBUG CZURSettingsProvider 更新对焦结束: 0<<<
25_06_13 09:29:09.060 VERBOSE ArmyKeyboardUsbReceiver 非军工版,不执行对应逻辑
25_06_13 09:29:09.061 DEBUG ArmyKeyboardUsbReceiver ACTION_USB_DEVICE_ATTACHED
25_06_13 09:29:09.062 VERBOSE ArmyKeyboardUsbReceiver 需要同步时间到系统
25_06_13 09:29:09.063 VERBOSE ArmyUsbManager 从键盘获取时间 START
25_06_13 09:29:09.066 WARN ArmyUsbManager 没有找到USB键盘设备
25_06_13 09:29:11.142 VERBOSE CZURAtyManager 添加:SettingActivity(onCreate)
25_06_13 09:29:11.316 INFO CZBusinessAty page-start: SettingActivity
25_06_13 09:29:11.330 DEBUG SettingActivity freePopBackStack
25_06_13 09:29:12.619 DEBUG MenuAdapter 点击:system
25_06_13 09:29:12.626 DEBUG SettingActivity freePopBackStack
25_06_13 09:29:12.753 DEBUG TimeZoneFragment 搜索模式:false
25_06_13 09:29:13.946 DEBUG SettingActivity freePopBackStack
25_06_13 09:29:14.106 WARN SystemManagerProxy onSystemChangeListener is null,add default listener
25_06_13 09:29:15.565 VERBOSE SystemInfoFragment 启动控制台页面
25_06_13 09:29:15.693 VERBOSE CZURAtyManager 添加:CZConsoleActivity(onCreate)
25_06_13 09:29:15.741 INFO CZBusinessAty page-start: CZConsoleActivity
25_06_13 09:29:15.772 DEBUG Switch textPadding: w=15.0
25_06_13 09:29:15.781 DEBUG Switch textPadding: w=15.0
25_06_13 09:29:15.782 DEBUG Switch textPadding: w=15.0
25_06_13 09:29:15.783 DEBUG Switch textPadding: w=15.0
25_06_13 09:29:15.836 DEBUG Switch textPadding: w=15.0
25_06_13 09:29:15.848 DEBUG Switch textPadding: w=15.0
25_06_13 09:29:15.851 DEBUG Switch textPadding: w=15.0
25_06_13 09:29:15.857 DEBUG Switch textPadding: w=15.0
25_06_13 09:29:15.862 DEBUG Switch textPadding: w=15.0
25_06_13 09:29:15.864 DEBUG Switch textPadding: w=15.0
25_06_13 09:29:15.866 DEBUG Switch textPadding: w=15.0
25_06_13 09:29:15.868 DEBUG Switch textPadding: w=15.0
25_06_13 09:29:15.869 DEBUG Switch textPadding: w=15.0
25_06_13 09:29:15.871 DEBUG Switch textPadding: w=15.0
25_06_13 09:29:15.872 DEBUG Switch textPadding: w=15.0
25_06_13 09:29:15.874 DEBUG Switch textPadding: w=15.0
25_06_13 09:29:15.883 DEBUG Switch textPadding: w=15.0
25_06_13 09:29:15.884 DEBUG Switch textPadding: w=15.0
25_06_13 09:29:15.886 DEBUG Switch textPadding: w=15.0
25_06_13 09:29:15.887 DEBUG Switch textPadding: w=15.0
25_06_13 09:29:15.900 DEBUG Switch textPadding: w=15.0
25_06_13 09:29:15.901 DEBUG Switch textPadding: w=15.0
25_06_13 09:29:15.902 DEBUG Switch textPadding: w=15.0
25_06_13 09:29:15.903 DEBUG Switch textPadding: w=15.0
25_06_13 09:29:15.918 DEBUG Switch textPadding: w=15.0
25_06_13 09:29:15.919 DEBUG Switch textPadding: w=15.0
25_06_13 09:29:15.920 DEBUG Switch textPadding: w=15.0
25_06_13 09:29:15.921 DEBUG Switch textPadding: w=15.0
25_06_13 09:29:15.942 DEBUG Switch textPadding: w=15.0
25_06_13 09:29:15.943 DEBUG Switch textPadding: w=15.0
25_06_13 09:29:15.945 DEBUG Switch textPadding: w=15.0
25_06_13 09:29:15.948 DEBUG Switch textPadding: w=15.0
25_06_13 09:29:15.952 DEBUG Switch textPadding: w=15.0
25_06_13 09:29:15.954 DEBUG Switch textPadding: w=15.0
25_06_13 09:29:15.956 DEBUG Switch textPadding: w=15.0
25_06_13 09:29:15.957 DEBUG Switch textPadding: w=15.0
25_06_13 09:29:15.978 DEBUG Switch textPadding: w=15.0
25_06_13 09:29:15.981 DEBUG Switch textPadding: w=15.0
25_06_13 09:29:15.984 DEBUG Switch textPadding: w=15.0
25_06_13 09:29:15.986 DEBUG Switch textPadding: w=15.0
25_06_13 09:29:16.010 DEBUG Switch textPadding: w=15.0
25_06_13 09:29:16.014 DEBUG Switch textPadding: w=15.0
25_06_13 09:29:16.017 DEBUG Switch textPadding: w=15.0
25_06_13 09:29:16.019 DEBUG Switch textPadding: w=15.0
25_06_13 09:29:16.025 DEBUG Switch textPadding: w=15.0
25_06_13 09:29:16.026 DEBUG Switch textPadding: w=15.0
25_06_13 09:29:16.028 DEBUG Switch textPadding: w=15.0
25_06_13 09:29:16.029 DEBUG Switch textPadding: w=15.0
25_06_13 09:29:16.078 DEBUG Switch textPadding: w=15.0
25_06_13 09:29:16.080 DEBUG Switch textPadding: w=15.0
25_06_13 09:29:16.082 DEBUG Switch textPadding: w=15.0
25_06_13 09:29:16.085 DEBUG Switch textPadding: w=15.0
25_06_13 09:29:16.437 INFO CZBusinessAty page-stop: SettingActivity
25_06_13 09:29:18.188 INFO CZBusinessAty page-stop: CZConsoleActivity
25_06_13 09:29:18.195 VERBOSE CZURAtyManager App:com.czur.starry.device.settings 处于后台, 清理禁止后台显示的Activity
25_06_13 09:29:18.205 VERBOSE CZURAtyManager remove:CZConsoleActivity(onDestroy)
