25_06_13 09:11:33.638 VERBOSE SPContentProvider 更新数据:voice_assistant_is_support_scene=true
25_06_13 09:11:33.679 VERBOSE SPContentProvider 通知数据改变:content://com.czur.starry.device.voiceassistant.provider.initProvider/getSPValue?voice_assistant_is_support_scene=true
25_06_13 09:11:33.686 DEBUG SPContentProvider query:content://com.czur.starry.device.voiceassistant.provider.initProvider/getSPValue?paramKeyName=voice_assistant_total_switch&paramKeyDefault=true-调用者:com.czur.starry.device.voiceassistant
25_06_13 09:11:33.717 DEBUG SPContentProvider update uri:content://com.czur.starry.device.voiceassistant.provider.initProvider/getSPValue-调用者:com.czur.starry.device.launcher
25_06_13 09:11:33.718 VERBOSE SPContentProvider 查询KEY:voice_assistant_is_support_scene
25_06_13 09:11:33.719 VERBOSE SPContentProvider 更新数据
25_06_13 09:11:33.723 VERBOSE SPContentProvider 更新数据:voice_assistant_is_support_scene=true
25_06_13 09:11:33.725 VERBOSE SPContentProvider 通知数据改变:content://com.czur.starry.device.voiceassistant.provider.initProvider/getSPValue?voice_assistant_is_support_scene=true
25_06_13 09:11:34.257 DEBUG SPContentProvider update uri:content://com.czur.starry.device.voiceassistant.provider.initProvider/getSPValue-调用者:com.czur.starry.device.launcher
25_06_13 09:11:34.258 VERBOSE SPContentProvider 查询KEY:voice_assistant_is_support_scene
25_06_13 09:11:34.259 VERBOSE SPContentProvider 更新数据
25_06_13 09:11:34.261 VERBOSE SPContentProvider 更新数据:voice_assistant_is_support_scene=true
25_06_13 09:11:34.263 VERBOSE SPContentProvider 通知数据改变:content://com.czur.starry.device.voiceassistant.provider.initProvider/getSPValue?voice_assistant_is_support_scene=true
25_06_13 09:11:34.266 DEBUG SPContentProvider update uri:content://com.czur.starry.device.voiceassistant.provider.initProvider/getSPValue-调用者:com.czur.starry.device.launcher
25_06_13 09:11:34.267 VERBOSE SPContentProvider 查询KEY:voice_assistant_is_support_scene
25_06_13 09:11:34.268 VERBOSE SPContentProvider 更新数据
25_06_13 09:11:34.270 VERBOSE SPContentProvider 更新数据:voice_assistant_is_support_scene=true
25_06_13 09:11:34.271 VERBOSE SPContentProvider 通知数据改变:content://com.czur.starry.device.voiceassistant.provider.initProvider/getSPValue?voice_assistant_is_support_scene=true
25_06_13 09:11:34.954 DEBUG VoiceAssistantService ---onCreate-----
25_06_13 09:11:35.038 DEBUG VoiceAssistantService =====unregisterCallback==false===
25_06_13 09:11:55.802 DEBUG SPContentProvider update uri:content://com.czur.starry.device.voiceassistant.provider.initProvider/getSPValue-调用者:com.czur.starry.device.launcher
25_06_13 09:11:55.804 VERBOSE SPContentProvider 查询KEY:voice_assistant_is_support_scene
25_06_13 09:11:55.805 VERBOSE SPContentProvider 更新数据
25_06_13 09:11:55.808 VERBOSE SPContentProvider 更新数据:voice_assistant_is_support_scene=true
25_06_13 09:11:55.811 VERBOSE SPContentProvider 通知数据改变:content://com.czur.starry.device.voiceassistant.provider.initProvider/getSPValue?voice_assistant_is_support_scene=true
25_06_13 09:11:55.825 DEBUG SPContentProvider update uri:content://com.czur.starry.device.voiceassistant.provider.initProvider/getSPValue-调用者:com.czur.starry.device.launcher
25_06_13 09:11:55.827 VERBOSE SPContentProvider 查询KEY:voice_assistant_is_support_scene
25_06_13 09:11:55.831 VERBOSE SPContentProvider 更新数据
25_06_13 09:11:55.835 VERBOSE SPContentProvider 更新数据:voice_assistant_is_support_scene=true
25_06_13 09:11:55.837 VERBOSE SPContentProvider 通知数据改变:content://com.czur.starry.device.voiceassistant.provider.initProvider/getSPValue?voice_assistant_is_support_scene=true
25_06_13 09:11:55.849 DEBUG SPContentProvider update uri:content://com.czur.starry.device.voiceassistant.provider.initProvider/getSPValue-调用者:com.czur.starry.device.launcher
25_06_13 09:11:55.851 VERBOSE SPContentProvider 查询KEY:voice_assistant_is_support_scene
25_06_13 09:11:55.852 VERBOSE SPContentProvider 更新数据
25_06_13 09:11:55.861 VERBOSE SPContentProvider 更新数据:voice_assistant_is_support_scene=true
25_06_13 09:11:55.863 VERBOSE SPContentProvider 通知数据改变:content://com.czur.starry.device.voiceassistant.provider.initProvider/getSPValue?voice_assistant_is_support_scene=true
25_06_13 09:11:55.870 DEBUG SPContentProvider update uri:content://com.czur.starry.device.voiceassistant.provider.initProvider/getSPValue-调用者:com.czur.starry.device.launcher
25_06_13 09:11:55.871 VERBOSE SPContentProvider 查询KEY:voice_assistant_is_support_scene
25_06_13 09:11:55.871 VERBOSE SPContentProvider 更新数据
25_06_13 09:11:55.873 VERBOSE SPContentProvider 更新数据:voice_assistant_is_support_scene=true
25_06_13 09:11:55.875 VERBOSE SPContentProvider 通知数据改变:content://com.czur.starry.device.voiceassistant.provider.initProvider/getSPValue?voice_assistant_is_support_scene=true
25_06_13 09:11:55.893 DEBUG SPContentProvider update uri:content://com.czur.starry.device.voiceassistant.provider.initProvider/getSPValue-调用者:com.czur.starry.device.launcher
25_06_13 09:11:55.894 VERBOSE SPContentProvider 查询KEY:voice_assistant_is_support_scene
25_06_13 09:11:55.894 VERBOSE SPContentProvider 更新数据
25_06_13 09:11:55.897 VERBOSE SPContentProvider 更新数据:voice_assistant_is_support_scene=true
25_06_13 09:11:55.899 VERBOSE SPContentProvider 通知数据改变:content://com.czur.starry.device.voiceassistant.provider.initProvider/getSPValue?voice_assistant_is_support_scene=true
25_06_13 09:28:49.698 VERBOSE SPContentProvider 更新数据:voice_assistant_is_support_scene=true
25_06_13 09:28:49.715 VERBOSE SPContentProvider 通知数据改变:content://com.czur.starry.device.voiceassistant.provider.initProvider/getSPValue?voice_assistant_is_support_scene=true
25_06_13 09:28:49.716 DEBUG SPContentProvider query:content://com.czur.starry.device.voiceassistant.provider.initProvider/getSPValue?paramKeyName=voice_assistant_total_switch&paramKeyDefault=true-调用者:com.czur.starry.device.voiceassistant
25_06_13 09:28:49.748 DEBUG SPContentProvider update uri:content://com.czur.starry.device.voiceassistant.provider.initProvider/getSPValue-调用者:com.czur.starry.device.launcher
25_06_13 09:28:49.749 VERBOSE SPContentProvider 查询KEY:voice_assistant_is_support_scene
25_06_13 09:28:49.750 VERBOSE SPContentProvider 更新数据
25_06_13 09:28:49.753 VERBOSE SPContentProvider 更新数据:voice_assistant_is_support_scene=true
25_06_13 09:28:49.756 VERBOSE SPContentProvider 通知数据改变:content://com.czur.starry.device.voiceassistant.provider.initProvider/getSPValue?voice_assistant_is_support_scene=true
25_06_13 09:28:50.269 DEBUG SPContentProvider update uri:content://com.czur.starry.device.voiceassistant.provider.initProvider/getSPValue-调用者:com.czur.starry.device.launcher
25_06_13 09:28:50.270 VERBOSE SPContentProvider 查询KEY:voice_assistant_is_support_scene
25_06_13 09:28:50.271 VERBOSE SPContentProvider 更新数据
25_06_13 09:28:50.283 VERBOSE SPContentProvider 更新数据:voice_assistant_is_support_scene=true
25_06_13 09:28:50.284 VERBOSE SPContentProvider 通知数据改变:content://com.czur.starry.device.voiceassistant.provider.initProvider/getSPValue?voice_assistant_is_support_scene=true
25_06_13 09:28:50.298 DEBUG SPContentProvider update uri:content://com.czur.starry.device.voiceassistant.provider.initProvider/getSPValue-调用者:com.czur.starry.device.launcher
25_06_13 09:28:50.299 VERBOSE SPContentProvider 查询KEY:voice_assistant_is_support_scene
25_06_13 09:28:50.300 VERBOSE SPContentProvider 更新数据
25_06_13 09:28:50.320 VERBOSE SPContentProvider 更新数据:voice_assistant_is_support_scene=true
25_06_13 09:28:50.323 VERBOSE SPContentProvider 通知数据改变:content://com.czur.starry.device.voiceassistant.provider.initProvider/getSPValue?voice_assistant_is_support_scene=true
25_06_13 09:28:51.036 DEBUG VoiceAssistantService ---onCreate-----
25_06_13 09:28:51.254 DEBUG VoiceAssistantService =====unregisterCallback==false===
