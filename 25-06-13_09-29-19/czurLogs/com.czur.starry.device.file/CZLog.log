25_06_13 09:10:54.379 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_share_code_status&paramKeyDefault=-调用者:com.czur.starry.device.noticecenter
25_06_13 09:10:54.384 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_share_enable_status&paramKeyDefault=true-调用者:com.czur.starry.device.noticecenter
25_06_13 09:10:54.385 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_share_code_enable&paramKeyDefault=true-调用者:com.czur.starry.device.noticecenter
25_06_13 09:10:54.385 VERBOSE SPContentProvider 查询KEY:unReadFileTabUpdate
25_06_13 09:10:54.386 VERBOSE SPContentProvider 更新数据
25_06_13 09:10:54.387 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=unReadFileTabUpdate&paramKeyDefault=0-调用者:com.czur.starry.device.file
25_06_13 09:10:54.388 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_name&paramKeyDefault=null-调用者:com.czur.starry.device.file
25_06_13 09:10:54.388 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_status_share&paramKeyDefault=false-调用者:com.czur.starry.device.file
25_06_13 09:10:54.390 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_status_local&paramKeyDefault=false-调用者:com.czur.starry.device.file
25_06_13 09:10:54.391 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_status_download&paramKeyDefault=false-调用者:com.czur.starry.device.file
25_06_13 09:10:54.392 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_share_code_status&paramKeyDefault=-调用者:com.czur.starry.device.file
25_06_13 09:10:54.392 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_share_code_enable&paramKeyDefault=true-调用者:com.czur.starry.device.file
25_06_13 09:10:54.393 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_share_enable_status&paramKeyDefault=true-调用者:com.czur.starry.device.file
25_06_13 09:10:54.407 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_status_screenshort&paramKeyDefault=false-调用者:com.czur.starry.device.file
25_06_13 09:10:54.407 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_status_meeting&paramKeyDefault=false-调用者:com.czur.starry.device.file
25_06_13 09:10:54.408 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_meeting_enable_status&paramKeyDefault=true-调用者:com.czur.starry.device.file
25_06_13 09:10:54.409 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_share_time_out_status&paramKeyDefault=4-调用者:com.czur.starry.device.file
25_06_13 09:10:54.410 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_status_ai_trans&paramKeyDefault=false-调用者:com.czur.starry.device.file
25_06_13 09:10:54.411 VERBOSE SPContentProvider 更新数据:unReadFileTabUpdate=8
25_06_13 09:10:54.412 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_keycode_status&paramKeyDefault=-1-调用者:com.czur.starry.device.file
25_06_13 09:10:54.413 DEBUG SPContentProvider update uri:content://com.czur.starry.device.livedata.fileprovider/getSPValue-调用者:com.czur.starry.device.noticecenter
25_06_13 09:10:54.414 VERBOSE SPContentProvider 查询KEY:is_file_share_code_status
25_06_13 09:10:54.415 VERBOSE SPContentProvider 更新数据
25_06_13 09:10:54.418 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_sort_type_status_state&paramKeyDefault=-1-调用者:com.czur.starry.device.file
25_06_13 09:10:54.419 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_meeting_enable_status&paramKeyDefault=true-调用者:com.czur.starry.device.noticecenter
25_06_13 09:10:54.420 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_media_status&paramKeyDefault=-调用者:com.czur.starry.device.file
25_06_13 09:10:54.421 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_status_ai_trans&paramKeyDefault=false-调用者:com.czur.starry.device.noticecenter
25_06_13 09:10:54.422 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_status_meeting&paramKeyDefault=false-调用者:com.czur.starry.device.noticecenter
25_06_13 09:10:54.422 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_status_screenshort&paramKeyDefault=false-调用者:com.czur.starry.device.noticecenter
25_06_13 09:10:54.424 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_sort_type_status_state&paramKeyDefault=-1-调用者:com.czur.starry.device.noticecenter
25_06_13 09:10:54.424 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_media_status&paramKeyDefault=-调用者:com.czur.starry.device.noticecenter
25_06_13 09:10:54.425 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_keycode_status&paramKeyDefault=-1-调用者:com.czur.starry.device.noticecenter
25_06_13 09:10:54.426 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_share_time_out_status&paramKeyDefault=4-调用者:com.czur.starry.device.noticecenter
25_06_13 09:10:54.427 VERBOSE ProviderUtil ContentValues: is_file_share_code_status=1531  cv.size: 1
25_06_13 09:10:54.429 VERBOSE ProviderUtil Key: is_file_share_code_status, Value: 1531
25_06_13 09:10:54.430 VERBOSE ProviderUtil update uri: content://com.czur.starry.device.livedata.fileprovider/getSPValue , contentValues Size: 1
25_06_13 09:10:54.430 DEBUG SPContentProvider update uri:content://com.czur.starry.device.livedata.fileprovider/getSPValue-调用者:com.czur.starry.device.file
25_06_13 09:10:54.431 VERBOSE SPContentProvider 查询KEY:is_file_share_code_status
25_06_13 09:10:54.431 VERBOSE SPContentProvider 更新数据
25_06_13 09:10:54.435 VERBOSE SPContentProvider 通知数据改变:content://com.czur.starry.device.livedata.fileprovider/getSPValue?unReadFileTabUpdate=8
25_06_13 09:10:54.439 VERBOSE SPContentProvider 更新数据:is_file_share_code_status=8066
25_06_13 09:10:54.445 VERBOSE SPContentProvider 通知数据改变:content://com.czur.starry.device.livedata.fileprovider/getSPValue?is_file_share_code_status=8066
25_06_13 09:10:54.445 VERBOSE SPContentProvider 更新数据:is_file_share_code_status=1531
25_06_13 09:10:54.452 VERBOSE SPContentProvider 通知数据改变:content://com.czur.starry.device.livedata.fileprovider/getSPValue?is_file_share_code_status=1531
25_06_13 09:10:54.453 VERBOSE ProviderUtil update result: 1
25_06_13 09:10:54.454 INFO FileHandlerLive 更新成功, 更新缓存
25_06_13 09:10:55.443 DEBUG SPContentProvider update uri:content://com.czur.starry.device.livedata.fileprovider/getSPValue-调用者:com.czur.starry.device.noticecenter
25_06_13 09:10:55.444 VERBOSE SPContentProvider 查询KEY:unReadFileTabUpdate
25_06_13 09:10:55.444 VERBOSE SPContentProvider 更新数据
25_06_13 09:10:55.447 VERBOSE SPContentProvider 更新数据:unReadFileTabUpdate=0
25_06_13 09:10:55.456 VERBOSE SPContentProvider 通知数据改变:content://com.czur.starry.device.livedata.fileprovider/getSPValue?unReadFileTabUpdate=0
25_06_13 09:11:26.690 DEBUG ShareFileProvider 数据复制预制文件
25_06_13 09:11:26.691 VERBOSE ShareFileProvider getLocalFileDir
25_06_13 09:11:26.694 VERBOSE ShareFileProvider 使用filePath:/data/user/0/com.czur.starry.device.file/files/LocalFileRoot
25_06_13 09:11:26.695 VERBOSE ShareFileProvider copy开始
25_06_13 09:11:26.696 VERBOSE ShareFileProvider dir:prefiles
25_06_13 09:11:26.700 VERBOSE ShareFileProvider copy:产品入门指南.pdf
25_06_13 09:11:26.729 VERBOSE ShareFileProvider copy:产品图示01.jpeg
25_06_13 09:11:26.734 VERBOSE ShareFileProvider copy:产品图示02.jpeg
25_06_13 09:11:26.740 VERBOSE ShareFileProvider copy:产品图示03.jpeg
25_06_13 09:11:26.748 VERBOSE ShareFileProvider copy:产品图示04.jpeg
25_06_13 09:11:26.753 VERBOSE ShareFileProvider copy:产品图示05.jpeg
25_06_13 09:11:26.758 VERBOSE ShareFileProvider copy结束
25_06_13 09:11:33.956 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_name&paramKeyDefault=null-调用者:com.czur.starry.device.launcher
25_06_13 09:11:33.958 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_status_local&paramKeyDefault=false-调用者:com.czur.starry.device.launcher
25_06_13 09:11:33.960 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=unReadFileTabUpdate&paramKeyDefault=0-调用者:com.czur.starry.device.launcher
25_06_13 09:11:33.961 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_status_download&paramKeyDefault=false-调用者:com.czur.starry.device.launcher
25_06_13 09:11:33.962 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_share_enable_status&paramKeyDefault=true-调用者:com.czur.starry.device.launcher
25_06_13 09:11:33.963 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_meeting_enable_status&paramKeyDefault=true-调用者:com.czur.starry.device.launcher
25_06_13 09:11:33.965 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_share_time_out_status&paramKeyDefault=4-调用者:com.czur.starry.device.launcher
25_06_13 09:11:33.974 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_status_screenshort&paramKeyDefault=false-调用者:com.czur.starry.device.launcher
25_06_13 09:11:33.975 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_status_meeting&paramKeyDefault=false-调用者:com.czur.starry.device.launcher
25_06_13 09:11:33.975 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_status_ai_trans&paramKeyDefault=false-调用者:com.czur.starry.device.launcher
25_06_13 09:11:33.977 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_sort_type_status_state&paramKeyDefault=-1-调用者:com.czur.starry.device.launcher
25_06_13 09:11:33.979 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_status_share&paramKeyDefault=false-调用者:com.czur.starry.device.launcher
25_06_13 09:11:33.980 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_media_status&paramKeyDefault=-调用者:com.czur.starry.device.launcher
25_06_13 09:11:33.981 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_share_code_status&paramKeyDefault=-调用者:com.czur.starry.device.launcher
25_06_13 09:11:33.982 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_keycode_status&paramKeyDefault=-1-调用者:com.czur.starry.device.launcher
25_06_13 09:11:33.983 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_share_code_enable&paramKeyDefault=true-调用者:com.czur.starry.device.launcher
25_06_13 09:11:34.943 DEBUG ServerService ========启动服务=======
25_06_13 09:11:34.950 DEBUG ComponentRepository scannerAllComponent
25_06_13 09:11:34.983 DEBUG ServerService fileShareEnable: true
25_06_13 09:11:35.142 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=unReadFileTabUpdate&paramKeyDefault=0-调用者:com.czur.starry.device.file
25_06_13 09:11:35.149 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=unReadFileTabUpdate&paramKeyDefault=0-调用者:com.czur.starry.device.file
25_06_13 09:11:35.149 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_name&paramKeyDefault=null-调用者:com.czur.starry.device.file
25_06_13 09:11:35.150 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_status_share&paramKeyDefault=false-调用者:com.czur.starry.device.file
25_06_13 09:11:35.151 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_status_download&paramKeyDefault=false-调用者:com.czur.starry.device.file
25_06_13 09:11:35.152 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_share_enable_status&paramKeyDefault=true-调用者:com.czur.starry.device.file
25_06_13 09:11:35.152 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_status_share&paramKeyDefault=false-调用者:com.czur.starry.device.file
25_06_13 09:11:35.160 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_status_local&paramKeyDefault=false-调用者:com.czur.starry.device.file
25_06_13 09:11:35.161 DEBUG SPContentProvider update uri:content://com.czur.starry.device.livedata.fileprovider/getSPValue-调用者:com.czur.starry.device.file
25_06_13 09:11:35.163 VERBOSE SPContentProvider 查询KEY:is_unread_file_name
25_06_13 09:11:35.164 VERBOSE SPContentProvider 更新数据
25_06_13 09:11:35.165 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_share_time_out_status&paramKeyDefault=4-调用者:com.czur.starry.device.file
25_06_13 09:11:35.170 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_share_code_enable&paramKeyDefault=true-调用者:com.czur.starry.device.file
25_06_13 09:11:35.189 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_share_enable_status&paramKeyDefault=true-调用者:com.czur.starry.device.file
25_06_13 09:11:35.191 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_share_code_enable&paramKeyDefault=true-调用者:com.czur.starry.device.file
25_06_13 09:11:35.192 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_status_local&paramKeyDefault=false-调用者:com.czur.starry.device.file
25_06_13 09:11:35.193 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_share_code_status&paramKeyDefault=-调用者:com.czur.starry.device.file
25_06_13 09:11:35.194 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_status_screenshort&paramKeyDefault=false-调用者:com.czur.starry.device.file
25_06_13 09:11:35.195 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_share_code_status&paramKeyDefault=-调用者:com.czur.starry.device.file
25_06_13 09:11:35.196 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_meeting_enable_status&paramKeyDefault=true-调用者:com.czur.starry.device.file
25_06_13 09:11:35.197 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_share_time_out_status&paramKeyDefault=4-调用者:com.czur.starry.device.file
25_06_13 09:11:35.198 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_name&paramKeyDefault=null-调用者:com.czur.starry.device.file
25_06_13 09:11:35.199 VERBOSE SPContentProvider 更新数据:is_unread_file_name=null
25_06_13 09:11:35.200 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_status_screenshort&paramKeyDefault=false-调用者:com.czur.starry.device.file
25_06_13 09:11:35.201 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_status_download&paramKeyDefault=false-调用者:com.czur.starry.device.file
25_06_13 09:11:35.202 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_status_meeting&paramKeyDefault=false-调用者:com.czur.starry.device.file
25_06_13 09:11:35.203 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_status_meeting&paramKeyDefault=false-调用者:com.czur.starry.device.file
25_06_13 09:11:35.204 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_sort_type_status_state&paramKeyDefault=-1-调用者:com.czur.starry.device.file
25_06_13 09:11:35.205 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_meeting_enable_status&paramKeyDefault=true-调用者:com.czur.starry.device.file
25_06_13 09:11:35.206 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_media_status&paramKeyDefault=-调用者:com.czur.starry.device.file
25_06_13 09:11:35.207 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_status_ai_trans&paramKeyDefault=false-调用者:com.czur.starry.device.file
25_06_13 09:11:35.208 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_keycode_status&paramKeyDefault=-1-调用者:com.czur.starry.device.file
25_06_13 09:11:35.210 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_sort_type_status_state&paramKeyDefault=-1-调用者:com.czur.starry.device.file
25_06_13 09:11:35.211 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_share_time_out_status&paramKeyDefault=4-调用者:com.czur.starry.device.file
25_06_13 09:11:35.213 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_keycode_status&paramKeyDefault=-1-调用者:com.czur.starry.device.file
25_06_13 09:11:35.216 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_media_status&paramKeyDefault=-调用者:com.czur.starry.device.file
25_06_13 09:11:35.216 VERBOSE SPContentProvider 通知数据改变:content://com.czur.starry.device.livedata.fileprovider/getSPValue?is_unread_file_name=null
25_06_13 09:11:35.217 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_status_ai_trans&paramKeyDefault=false-调用者:com.czur.starry.device.file
25_06_13 09:11:35.218 DEBUG SPContentProvider update uri:content://com.czur.starry.device.livedata.fileprovider/getSPValue-调用者:com.czur.starry.device.file
25_06_13 09:11:35.221 VERBOSE SPContentProvider 查询KEY:is_unread_file_status_local
25_06_13 09:11:35.222 VERBOSE SPContentProvider 更新数据
25_06_13 09:11:35.225 VERBOSE SPContentProvider 更新数据:is_unread_file_status_local=false
25_06_13 09:11:35.229 VERBOSE SPContentProvider 通知数据改变:content://com.czur.starry.device.livedata.fileprovider/getSPValue?is_unread_file_status_local=false
25_06_13 09:11:35.231 DEBUG SPContentProvider update uri:content://com.czur.starry.device.livedata.fileprovider/getSPValue-调用者:com.czur.starry.device.file
25_06_13 09:11:35.232 VERBOSE SPContentProvider 查询KEY:is_unread_file_status_screenshort
25_06_13 09:11:35.234 VERBOSE SPContentProvider 更新数据
25_06_13 09:11:35.235 VERBOSE SPContentProvider 更新数据:is_unread_file_status_screenshort=false
25_06_13 09:11:35.240 VERBOSE SPContentProvider 通知数据改变:content://com.czur.starry.device.livedata.fileprovider/getSPValue?is_unread_file_status_screenshort=false
25_06_13 09:11:35.250 DEBUG SPContentProvider update uri:content://com.czur.starry.device.livedata.fileprovider/getSPValue-调用者:com.czur.starry.device.file
25_06_13 09:11:35.255 VERBOSE SPContentProvider 查询KEY:is_unread_file_status_download
25_06_13 09:11:35.256 VERBOSE SPContentProvider 更新数据
25_06_13 09:11:35.256 VERBOSE SPContentProvider 更新数据:is_unread_file_status_download=false
25_06_13 09:11:35.265 VERBOSE SPContentProvider 通知数据改变:content://com.czur.starry.device.livedata.fileprovider/getSPValue?is_unread_file_status_download=false
25_06_13 09:11:35.290 DEBUG SPContentProvider update uri:content://com.czur.starry.device.livedata.fileprovider/getSPValue-调用者:com.czur.starry.device.file
25_06_13 09:11:35.292 VERBOSE SPContentProvider 查询KEY:is_unread_file_status_share
25_06_13 09:11:35.293 VERBOSE SPContentProvider 更新数据
25_06_13 09:11:35.297 VERBOSE SPContentProvider 更新数据:is_unread_file_status_share=false
25_06_13 09:11:35.309 VERBOSE SPContentProvider 通知数据改变:content://com.czur.starry.device.livedata.fileprovider/getSPValue?is_unread_file_status_share=false
25_06_13 09:11:35.317 DEBUG SPContentProvider update uri:content://com.czur.starry.device.livedata.fileprovider/getSPValue-调用者:com.czur.starry.device.file
25_06_13 09:11:35.318 VERBOSE SPContentProvider 查询KEY:is_unread_file_status_meeting
25_06_13 09:11:35.319 VERBOSE SPContentProvider 更新数据
25_06_13 09:11:35.320 VERBOSE SPContentProvider 更新数据:is_unread_file_status_meeting=false
25_06_13 09:11:35.328 VERBOSE SPContentProvider 通知数据改变:content://com.czur.starry.device.livedata.fileprovider/getSPValue?is_unread_file_status_meeting=false
25_06_13 09:11:35.337 DEBUG SPContentProvider update uri:content://com.czur.starry.device.livedata.fileprovider/getSPValue-调用者:com.czur.starry.device.file
25_06_13 09:11:35.338 VERBOSE SPContentProvider 查询KEY:is_unread_file_status_ai_trans
25_06_13 09:11:35.340 VERBOSE SPContentProvider 更新数据
25_06_13 09:11:35.341 VERBOSE SPContentProvider 更新数据:is_unread_file_status_ai_trans=false
25_06_13 09:11:35.351 VERBOSE SPContentProvider 通知数据改变:content://com.czur.starry.device.livedata.fileprovider/getSPValue?is_unread_file_status_ai_trans=false
25_06_13 09:11:35.372 DEBUG SPContentProvider update uri:content://com.czur.starry.device.livedata.fileprovider/getSPValue-调用者:com.czur.starry.device.file
25_06_13 09:11:35.372 VERBOSE SPContentProvider 查询KEY:unReadFileTabUpdate
25_06_13 09:11:35.374 VERBOSE SPContentProvider 更新数据
25_06_13 09:11:35.375 VERBOSE SPContentProvider 更新数据:unReadFileTabUpdate=0
25_06_13 09:11:35.379 VERBOSE SPContentProvider 通知数据改变:content://com.czur.starry.device.livedata.fileprovider/getSPValue?unReadFileTabUpdate=0
25_06_13 09:11:35.580 DEBUG ClassScanner servletClasses.size():12
25_06_13 09:11:35.581 DEBUG ClassScanner /api/starry/fileShare/checkFile
25_06_13 09:11:35.581 DEBUG ClassScanner /api/starry/fileShare/checkPwd
25_06_13 09:11:35.582 DEBUG ClassScanner /api/starry/fileShare/deleteTempFile
25_06_13 09:11:35.583 DEBUG ClassScanner /api/starry/fileShare/download
25_06_13 09:11:35.584 DEBUG ClassScanner /api/starry/fileShare/getFileList
25_06_13 09:11:35.585 DEBUG ClassScanner /api/starry/fileShare/functions
25_06_13 09:11:35.586 DEBUG ClassScanner /api/starry/fileShare/getStarryStatus
25_06_13 09:11:35.586 DEBUG ClassScanner /api/starry/fileShare/getStartBytes
25_06_13 09:11:35.587 DEBUG ClassScanner /api/starry/fileShare/getRootList
25_06_13 09:11:35.588 DEBUG ClassScanner /api/starry/fileShare/getThumb
25_06_13 09:11:35.588 DEBUG ClassScanner /api/starry/fileShare/getVerifyCode
25_06_13 09:11:35.589 DEBUG ClassScanner /api/starry/fileShare/uploadFile
25_06_13 09:11:35.590 DEBUG ComponentRepository servletMap:/api/starry/fileShare/checkFile, /api/starry/fileShare/checkPwd, /api/starry/fileShare/deleteTempFile, /api/starry/fileShare/download, /api/starry/fileShare/getFileList, /api/starry/fileShare/functions, /api/starry/fileShare/getStarryStatus, /api/starry/fileShare/getStartBytes, /api/starry/fileShare/getRootList, /api/starry/fileShare/getThumb, /api/starry/fileShare/getVerifyCode, /api/starry/fileShare/uploadFile
25_06_13 09:11:35.590 DEBUG ServerService port: 8652
25_06_13 09:11:35.591 DEBUG ServerService ====initServerBootstrap== port:8652
25_06_13 09:11:35.612 DEBUG ServerService 服务端开启成功
25_06_13 09:11:36.408 DEBUG SPContentProvider update uri:content://com.czur.starry.device.livedata.fileprovider/getSPValue-调用者:com.czur.starry.device.file
25_06_13 09:11:36.409 VERBOSE SPContentProvider 查询KEY:is_unread_file_name
25_06_13 09:11:36.410 VERBOSE SPContentProvider 更新数据
25_06_13 09:11:36.413 VERBOSE SPContentProvider 更新数据:is_unread_file_name=null
25_06_13 09:11:36.414 VERBOSE SPContentProvider 通知数据改变:content://com.czur.starry.device.livedata.fileprovider/getSPValue?is_unread_file_name=null
25_06_13 09:11:40.593 DEBUG ServerService localRequest
25_06_13 09:11:40.630 VERBOSE ServerHandler ===startFileUploadOrDownload=
25_06_13 09:11:40.994 DEBUG ServerHandler ===endFileUpload=
25_06_13 09:11:40.995 VERBOSE ServerHandler ===endFileUploadOrDownload=
25_06_13 09:11:41.006 VERBOSE ServerHandler ===startFileUploadOrDownload=
25_06_13 09:11:41.009 VERBOSE RootList RootListServlet onConnect
25_06_13 09:11:41.010 VERBOSE RootList clientAPIVersion: 0
25_06_13 09:11:42.512 DEBUG ServerHandler ===endFileUpload=
25_06_13 09:11:42.515 VERBOSE ServerHandler ===endFileUploadOrDownload=
25_06_13 09:11:45.264 DEBUG CpuUtils ========== cpuUsage: 28.033472
25_06_13 09:11:45.279 VERBOSE ServerService CPU负载正常
25_06_13 09:11:55.577 DEBUG CpuUtils ========== cpuUsage: 18.644068
25_06_13 09:11:55.582 VERBOSE ServerService CPU负载正常
25_06_13 09:12:05.889 DEBUG CpuUtils ========== cpuUsage: 28.451883
25_06_13 09:12:05.893 VERBOSE ServerService CPU负载正常
25_06_13 09:12:16.202 DEBUG CpuUtils ========== cpuUsage: 28.630705
25_06_13 09:12:16.215 VERBOSE ServerService CPU负载正常
25_06_13 09:12:26.515 DEBUG CpuUtils ========== cpuUsage: 27.310925
25_06_13 09:12:26.517 VERBOSE ServerService CPU负载正常
25_06_13 09:12:36.827 DEBUG CpuUtils ========== cpuUsage: 31.06383
25_06_13 09:12:36.830 VERBOSE ServerService CPU负载正常
25_06_13 09:12:47.148 DEBUG CpuUtils ========== cpuUsage: 27.685951
25_06_13 09:12:47.150 VERBOSE ServerService CPU负载正常
25_06_13 09:12:57.463 DEBUG CpuUtils ========== cpuUsage: 30.833334
25_06_13 09:12:57.471 VERBOSE ServerService CPU负载正常
25_06_13 09:13:07.775 DEBUG CpuUtils ========== cpuUsage: 29.918034
25_06_13 09:13:07.792 VERBOSE ServerService CPU负载正常
25_06_13 09:13:18.091 DEBUG CpuUtils ========== cpuUsage: 24.180328
25_06_13 09:13:18.124 VERBOSE ServerService CPU负载正常
25_06_13 09:13:28.437 DEBUG CpuUtils ========== cpuUsage: 27.083334
25_06_13 09:13:28.442 VERBOSE ServerService CPU负载正常
25_06_13 09:13:38.765 DEBUG CpuUtils ========== cpuUsage: 29.583334
25_06_13 09:13:38.769 VERBOSE ServerService CPU负载正常
25_06_13 09:13:49.077 DEBUG CpuUtils ========== cpuUsage: 27.800829
25_06_13 09:13:49.080 VERBOSE ServerService CPU负载正常
25_06_13 09:13:59.384 DEBUG CpuUtils ========== cpuUsage: 29.46058
25_06_13 09:13:59.386 VERBOSE ServerService CPU负载正常
25_06_13 09:14:09.698 DEBUG CpuUtils ========== cpuUsage: 28.813559
25_06_13 09:14:09.700 VERBOSE ServerService CPU负载正常
25_06_13 09:14:20.023 DEBUG CpuUtils ========== cpuUsage: 29.62963
25_06_13 09:14:20.026 VERBOSE ServerService CPU负载正常
25_06_13 09:14:30.342 DEBUG CpuUtils ========== cpuUsage: 30.543934
25_06_13 09:14:30.353 VERBOSE ServerService CPU负载正常
25_06_13 09:14:40.651 DEBUG CpuUtils ========== cpuUsage: 27.542374
25_06_13 09:14:40.653 VERBOSE ServerService CPU负载正常
25_06_13 09:14:50.965 DEBUG CpuUtils ========== cpuUsage: 29.787233
25_06_13 09:14:50.968 VERBOSE ServerService CPU负载正常
25_06_13 09:15:01.287 DEBUG CpuUtils ========== cpuUsage: 24.152542
25_06_13 09:15:01.320 VERBOSE ServerService CPU负载正常
25_06_13 09:15:11.603 DEBUG CpuUtils ========== cpuUsage: 26.778242
25_06_13 09:15:11.606 VERBOSE ServerService CPU负载正常
25_06_13 09:15:21.915 DEBUG CpuUtils ========== cpuUsage: 27.966103
25_06_13 09:15:21.921 VERBOSE ServerService CPU负载正常
25_06_13 09:15:32.227 DEBUG CpuUtils ========== cpuUsage: 26.58228
25_06_13 09:15:32.232 VERBOSE ServerService CPU负载正常
25_06_13 09:15:42.540 DEBUG CpuUtils ========== cpuUsage: 30.125523
25_06_13 09:15:42.543 VERBOSE ServerService CPU负载正常
25_06_13 09:15:52.856 DEBUG CpuUtils ========== cpuUsage: 28.215767
25_06_13 09:15:52.870 VERBOSE ServerService CPU负载正常
25_06_13 09:16:03.171 DEBUG CpuUtils ========== cpuUsage: 28.870293
25_06_13 09:16:03.174 VERBOSE ServerService CPU负载正常
25_06_13 09:16:13.496 DEBUG CpuUtils ========== cpuUsage: 30.364372
25_06_13 09:16:13.502 VERBOSE ServerService CPU负载正常
25_06_13 09:16:23.813 DEBUG CpuUtils ========== cpuUsage: 25.738396
25_06_13 09:16:23.820 VERBOSE ServerService CPU负载正常
25_06_13 09:16:34.139 DEBUG CpuUtils ========== cpuUsage: 26.31579
25_06_13 09:16:34.142 VERBOSE ServerService CPU负载正常
25_06_13 09:16:44.471 DEBUG CpuUtils ========== cpuUsage: 29.957806
25_06_13 09:16:44.475 VERBOSE ServerService CPU负载正常
25_06_13 09:16:54.777 DEBUG CpuUtils ========== cpuUsage: 27.800829
25_06_13 09:16:54.780 VERBOSE ServerService CPU负载正常
25_06_13 09:17:05.128 DEBUG CpuUtils ========== cpuUsage: 25.630253
25_06_13 09:17:05.132 VERBOSE ServerService CPU负载正常
25_06_13 09:17:15.440 DEBUG CpuUtils ========== cpuUsage: 31.355932
25_06_13 09:17:15.470 VERBOSE ServerService CPU负载正常
25_06_13 09:17:25.773 DEBUG CpuUtils ========== cpuUsage: 29.957806
25_06_13 09:17:25.776 VERBOSE ServerService CPU负载正常
25_06_13 09:17:36.088 DEBUG CpuUtils ========== cpuUsage: 26.446281
25_06_13 09:17:36.094 VERBOSE ServerService CPU负载正常
25_06_13 09:17:46.407 DEBUG CpuUtils ========== cpuUsage: 28.691982
25_06_13 09:17:46.410 VERBOSE ServerService CPU负载正常
25_06_13 09:17:56.728 DEBUG CpuUtils ========== cpuUsage: 22.357723
25_06_13 09:17:56.731 VERBOSE ServerService CPU负载正常
25_06_13 09:18:07.046 DEBUG CpuUtils ========== cpuUsage: 28.870293
25_06_13 09:18:07.051 VERBOSE ServerService CPU负载正常
25_06_13 09:18:17.361 DEBUG CpuUtils ========== cpuUsage: 30.2521
25_06_13 09:18:17.363 VERBOSE ServerService CPU负载正常
25_06_13 09:18:27.673 DEBUG CpuUtils ========== cpuUsage: 29.535866
25_06_13 09:18:27.676 VERBOSE ServerService CPU负载正常
25_06_13 09:18:38.005 DEBUG CpuUtils ========== cpuUsage: 29.083666
25_06_13 09:18:38.008 VERBOSE ServerService CPU负载正常
25_06_13 09:18:48.323 DEBUG CpuUtils ========== cpuUsage: 29.113924
25_06_13 09:18:48.331 VERBOSE ServerService CPU负载正常
25_06_13 09:18:58.635 DEBUG CpuUtils ========== cpuUsage: 28.991596
25_06_13 09:18:58.637 VERBOSE ServerService CPU负载正常
25_06_13 09:19:08.948 DEBUG CpuUtils ========== cpuUsage: 26.068377
25_06_13 09:19:08.955 VERBOSE ServerService CPU负载正常
25_06_13 09:19:19.262 DEBUG CpuUtils ========== cpuUsage: 29.831932
25_06_13 09:19:19.264 VERBOSE ServerService CPU负载正常
25_06_13 09:19:29.576 DEBUG CpuUtils ========== cpuUsage: 27.083334
25_06_13 09:19:29.578 VERBOSE ServerService CPU负载正常
25_06_13 09:19:39.887 DEBUG CpuUtils ========== cpuUsage: 27.350428
25_06_13 09:19:39.889 VERBOSE ServerService CPU负载正常
25_06_13 09:19:50.199 DEBUG CpuUtils ========== cpuUsage: 27.5
25_06_13 09:19:50.201 VERBOSE ServerService CPU负载正常
25_06_13 09:20:00.511 DEBUG CpuUtils ========== cpuUsage: 26.446281
25_06_13 09:20:00.530 VERBOSE ServerService CPU负载正常
25_06_13 09:20:10.825 DEBUG CpuUtils ========== cpuUsage: 27.615063
25_06_13 09:20:10.829 VERBOSE ServerService CPU负载正常
25_06_13 09:20:21.138 DEBUG CpuUtils ========== cpuUsage: 26.80851
25_06_13 09:20:21.141 VERBOSE ServerService CPU负载正常
25_06_13 09:20:31.454 DEBUG CpuUtils ========== cpuUsage: 25.847458
25_06_13 09:20:31.456 VERBOSE ServerService CPU负载正常
25_06_13 09:20:41.772 DEBUG CpuUtils ========== cpuUsage: 28.15126
25_06_13 09:20:41.776 VERBOSE ServerService CPU负载正常
25_06_13 09:20:52.087 DEBUG CpuUtils ========== cpuUsage: 25.210085
25_06_13 09:20:52.089 VERBOSE ServerService CPU负载正常
25_06_13 09:21:02.420 DEBUG CpuUtils ========== cpuUsage: 27.385893
25_06_13 09:21:02.424 VERBOSE ServerService CPU负载正常
25_06_13 09:21:12.738 DEBUG CpuUtils ========== cpuUsage: 30.833334
25_06_13 09:21:12.741 VERBOSE ServerService CPU负载正常
25_06_13 09:21:23.054 DEBUG CpuUtils ========== cpuUsage: 28.870293
25_06_13 09:21:23.056 VERBOSE ServerService CPU负载正常
25_06_13 09:21:33.369 DEBUG CpuUtils ========== cpuUsage: 27.196653
25_06_13 09:21:33.376 VERBOSE ServerService CPU负载正常
25_06_13 09:21:43.687 DEBUG CpuUtils ========== cpuUsage: 23.529411
25_06_13 09:21:43.691 VERBOSE ServerService CPU负载正常
25_06_13 09:21:54.002 DEBUG CpuUtils ========== cpuUsage: 27.385893
25_06_13 09:21:54.005 VERBOSE ServerService CPU负载正常
25_06_13 09:22:04.316 DEBUG CpuUtils ========== cpuUsage: 25.416666
25_06_13 09:22:04.320 VERBOSE ServerService CPU负载正常
25_06_13 09:22:14.631 DEBUG CpuUtils ========== cpuUsage: 27.196653
25_06_13 09:22:14.648 VERBOSE ServerService CPU负载正常
25_06_13 09:22:24.945 DEBUG CpuUtils ========== cpuUsage: 25.514402
25_06_13 09:22:24.947 VERBOSE ServerService CPU负载正常
25_06_13 09:22:35.266 DEBUG CpuUtils ========== cpuUsage: 28.806585
25_06_13 09:22:35.272 VERBOSE ServerService CPU负载正常
25_06_13 09:22:45.581 DEBUG CpuUtils ========== cpuUsage: 27.966103
25_06_13 09:22:45.585 VERBOSE ServerService CPU负载正常
25_06_13 09:22:55.895 DEBUG CpuUtils ========== cpuUsage: 29.957806
25_06_13 09:22:55.896 VERBOSE ServerService CPU负载正常
25_06_13 09:23:06.209 DEBUG CpuUtils ========== cpuUsage: 25.210085
25_06_13 09:23:06.220 VERBOSE ServerService CPU负载正常
25_06_13 09:23:16.525 DEBUG CpuUtils ========== cpuUsage: 29.875519
25_06_13 09:23:16.531 VERBOSE ServerService CPU负载正常
25_06_13 09:23:26.837 DEBUG CpuUtils ========== cpuUsage: 27.350428
25_06_13 09:23:26.839 VERBOSE ServerService CPU负载正常
25_06_13 09:23:37.152 DEBUG CpuUtils ========== cpuUsage: 25.523012
25_06_13 09:23:37.160 VERBOSE ServerService CPU负载正常
25_06_13 09:23:47.463 DEBUG CpuUtils ========== cpuUsage: 27.083334
25_06_13 09:23:47.472 VERBOSE ServerService CPU负载正常
25_06_13 09:23:57.778 DEBUG CpuUtils ========== cpuUsage: 27.731092
25_06_13 09:23:57.781 VERBOSE ServerService CPU负载正常
25_06_13 09:24:08.092 DEBUG CpuUtils ========== cpuUsage: 26.58228
25_06_13 09:24:08.094 VERBOSE ServerService CPU负载正常
25_06_13 09:24:18.404 DEBUG CpuUtils ========== cpuUsage: 28.991596
25_06_13 09:24:18.407 VERBOSE ServerService CPU负载正常
25_06_13 09:24:28.724 DEBUG CpuUtils ========== cpuUsage: 31.147541
25_06_13 09:24:28.731 VERBOSE ServerService CPU负载正常
25_06_13 09:24:39.037 DEBUG CpuUtils ========== cpuUsage: 26.446281
25_06_13 09:24:39.040 VERBOSE ServerService CPU负载正常
25_06_13 09:24:49.355 DEBUG CpuUtils ========== cpuUsage: 26.58228
25_06_13 09:24:49.359 VERBOSE ServerService CPU负载正常
25_06_13 09:24:59.669 DEBUG CpuUtils ========== cpuUsage: 29.707113
25_06_13 09:24:59.671 VERBOSE ServerService CPU负载正常
25_06_13 09:25:09.985 DEBUG CpuUtils ========== cpuUsage: 30.2521
25_06_13 09:25:09.989 VERBOSE ServerService CPU负载正常
25_06_13 09:25:20.302 DEBUG CpuUtils ========== cpuUsage: 29.583334
25_06_13 09:25:20.304 VERBOSE ServerService CPU负载正常
25_06_13 09:25:30.620 DEBUG CpuUtils ========== cpuUsage: 27.800829
25_06_13 09:25:30.622 VERBOSE ServerService CPU负载正常
25_06_13 09:25:40.935 DEBUG CpuUtils ========== cpuUsage: 28.451883
25_06_13 09:25:40.939 VERBOSE ServerService CPU负载正常
25_06_13 09:25:51.255 DEBUG CpuUtils ========== cpuUsage: 26.890757
25_06_13 09:25:51.258 VERBOSE ServerService CPU负载正常
25_06_13 09:26:01.569 DEBUG CpuUtils ========== cpuUsage: 27.5
25_06_13 09:26:01.570 VERBOSE ServerService CPU负载正常
25_06_13 09:26:11.880 DEBUG CpuUtils ========== cpuUsage: 30.125523
25_06_13 09:26:11.888 VERBOSE ServerService CPU负载正常
25_06_13 09:26:22.190 DEBUG CpuUtils ========== cpuUsage: 28.691982
25_06_13 09:26:22.194 VERBOSE ServerService CPU负载正常
25_06_13 09:26:32.507 DEBUG CpuUtils ========== cpuUsage: 24.166666
25_06_13 09:26:32.509 VERBOSE ServerService CPU负载正常
25_06_13 09:26:42.822 DEBUG CpuUtils ========== cpuUsage: 26.890757
25_06_13 09:26:42.826 VERBOSE ServerService CPU负载正常
25_06_13 09:26:53.140 DEBUG CpuUtils ========== cpuUsage: 29.46058
25_06_13 09:26:53.145 VERBOSE ServerService CPU负载正常
25_06_13 09:27:03.453 DEBUG CpuUtils ========== cpuUsage: 7.5313807
25_06_13 09:27:03.454 VERBOSE ServerService CPU负载正常
25_06_13 09:27:13.768 DEBUG CpuUtils ========== cpuUsage: 6.382979
25_06_13 09:27:13.772 VERBOSE ServerService CPU负载正常
25_06_13 09:27:24.077 DEBUG CpuUtils ========== cpuUsage: 7.563025
25_06_13 09:27:24.078 VERBOSE ServerService CPU负载正常
25_06_13 09:27:34.389 DEBUG CpuUtils ========== cpuUsage: 8.474576
25_06_13 09:27:34.390 VERBOSE ServerService CPU负载正常
25_06_13 09:27:44.702 DEBUG CpuUtils ========== cpuUsage: 6.779661
25_06_13 09:27:44.703 VERBOSE ServerService CPU负载正常
25_06_13 09:27:55.013 DEBUG CpuUtils ========== cpuUsage: 12.8630705
25_06_13 09:27:55.015 VERBOSE ServerService CPU负载正常
25_06_13 09:28:05.326 DEBUG CpuUtils ========== cpuUsage: 7.9831934
25_06_13 09:28:05.327 VERBOSE ServerService CPU负载正常
25_06_13 09:28:15.638 DEBUG CpuUtils ========== cpuUsage: 7.949791
25_06_13 09:28:15.639 VERBOSE ServerService CPU负载正常
25_06_13 09:28:25.946 DEBUG CpuUtils ========== cpuUsage: 8.713693
25_06_13 09:28:25.947 VERBOSE ServerService CPU负载正常
25_06_13 09:28:36.258 DEBUG CpuUtils ========== cpuUsage: 9.46502
25_06_13 09:28:36.260 VERBOSE ServerService CPU负载正常
25_06_13 09:28:50.170 DEBUG SPContentProvider binder:5675_3, com.czur.starry.device.baselib.handler.SPContentProvider.query(SPContentProvider.kt:99)
25_06_13 09:28:50.170 DEBUG SPContentProvider args[0] = query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_name&paramKeyDefault=null-调用者:com.czur.starry.device.launcher
25_06_13 09:28:50.190 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_share_code_status&paramKeyDefault=-调用者:com.czur.starry.device.file
25_06_13 09:28:50.191 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=unReadFileTabUpdate&paramKeyDefault=0-调用者:com.czur.starry.device.file
25_06_13 09:28:50.193 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_status_share&paramKeyDefault=false-调用者:com.czur.starry.device.file
25_06_13 09:28:50.193 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_status_download&paramKeyDefault=false-调用者:com.czur.starry.device.file
25_06_13 09:28:50.194 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_status_local&paramKeyDefault=false-调用者:com.czur.starry.device.file
25_06_13 09:28:50.203 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_name&paramKeyDefault=null-调用者:com.czur.starry.device.file
25_06_13 09:28:50.205 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_share_code_enable&paramKeyDefault=true-调用者:com.czur.starry.device.launcher
25_06_13 09:28:50.214 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_share_code_enable&paramKeyDefault=true-调用者:com.czur.starry.device.file
25_06_13 09:28:50.228 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_share_code_status&paramKeyDefault=-调用者:com.czur.starry.device.launcher
25_06_13 09:28:50.229 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_status_download&paramKeyDefault=false-调用者:com.czur.starry.device.launcher
25_06_13 09:28:50.231 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_share_enable_status&paramKeyDefault=true-调用者:com.czur.starry.device.file
25_06_13 09:28:50.234 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_status_share&paramKeyDefault=false-调用者:com.czur.starry.device.launcher
25_06_13 09:28:50.236 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=unReadFileTabUpdate&paramKeyDefault=0-调用者:com.czur.starry.device.launcher
25_06_13 09:28:50.239 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_meeting_enable_status&paramKeyDefault=true-调用者:com.czur.starry.device.launcher
25_06_13 09:28:50.241 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_status_screenshort&paramKeyDefault=false-调用者:com.czur.starry.device.launcher
25_06_13 09:28:50.244 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_status_meeting&paramKeyDefault=false-调用者:com.czur.starry.device.launcher
25_06_13 09:28:50.247 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_share_time_out_status&paramKeyDefault=4-调用者:com.czur.starry.device.launcher
25_06_13 09:28:50.249 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_share_enable_status&paramKeyDefault=true-调用者:com.czur.starry.device.launcher
25_06_13 09:28:50.252 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_sort_type_status_state&paramKeyDefault=-1-调用者:com.czur.starry.device.launcher
25_06_13 09:28:50.254 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_keycode_status&paramKeyDefault=-1-调用者:com.czur.starry.device.launcher
25_06_13 09:28:50.255 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_media_status&paramKeyDefault=-调用者:com.czur.starry.device.launcher
25_06_13 09:28:50.257 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_status_ai_trans&paramKeyDefault=false-调用者:com.czur.starry.device.launcher
25_06_13 09:28:50.258 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_meeting_enable_status&paramKeyDefault=true-调用者:com.czur.starry.device.file
25_06_13 09:28:50.259 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_share_time_out_status&paramKeyDefault=4-调用者:com.czur.starry.device.file
25_06_13 09:28:50.287 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_status_meeting&paramKeyDefault=false-调用者:com.czur.starry.device.file
25_06_13 09:28:50.291 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_media_status&paramKeyDefault=-调用者:com.czur.starry.device.file
25_06_13 09:28:50.294 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_keycode_status&paramKeyDefault=-1-调用者:com.czur.starry.device.file
25_06_13 09:28:50.299 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_sort_type_status_state&paramKeyDefault=-1-调用者:com.czur.starry.device.file
25_06_13 09:28:50.301 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_status_ai_trans&paramKeyDefault=false-调用者:com.czur.starry.device.file
25_06_13 09:28:50.311 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_status_screenshort&paramKeyDefault=false-调用者:com.czur.starry.device.file
25_06_13 09:28:51.072 DEBUG ServerService ========启动服务=======
25_06_13 09:28:51.080 DEBUG ComponentRepository scannerAllComponent
25_06_13 09:28:51.102 DEBUG ServerService fileShareEnable: true
25_06_13 09:28:51.496 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=unReadFileTabUpdate&paramKeyDefault=0-调用者:com.czur.starry.device.file
25_06_13 09:28:51.497 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_status_local&paramKeyDefault=false-调用者:com.czur.starry.device.file
25_06_13 09:28:51.503 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_status_download&paramKeyDefault=false-调用者:com.czur.starry.device.file
25_06_13 09:28:51.504 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_share_code_status&paramKeyDefault=-调用者:com.czur.starry.device.file
25_06_13 09:28:51.509 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_status_share&paramKeyDefault=false-调用者:com.czur.starry.device.file
25_06_13 09:28:51.518 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_share_enable_status&paramKeyDefault=true-调用者:com.czur.starry.device.file
25_06_13 09:28:51.521 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_share_code_enable&paramKeyDefault=true-调用者:com.czur.starry.device.file
25_06_13 09:28:51.527 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_name&paramKeyDefault=null-调用者:com.czur.starry.device.file
25_06_13 09:28:51.528 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_meeting_enable_status&paramKeyDefault=true-调用者:com.czur.starry.device.file
25_06_13 09:28:51.549 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_status_meeting&paramKeyDefault=false-调用者:com.czur.starry.device.file
25_06_13 09:28:51.550 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_share_time_out_status&paramKeyDefault=4-调用者:com.czur.starry.device.file
25_06_13 09:28:51.551 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_sort_type_status_state&paramKeyDefault=-1-调用者:com.czur.starry.device.file
25_06_13 09:28:51.552 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_media_status&paramKeyDefault=-调用者:com.czur.starry.device.file
25_06_13 09:28:51.553 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_keycode_status&paramKeyDefault=-1-调用者:com.czur.starry.device.file
25_06_13 09:28:51.554 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_status_ai_trans&paramKeyDefault=false-调用者:com.czur.starry.device.file
25_06_13 09:28:51.555 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_status_screenshort&paramKeyDefault=false-调用者:com.czur.starry.device.file
25_06_13 09:28:51.589 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_status_share&paramKeyDefault=false-调用者:com.czur.starry.device.file
25_06_13 09:28:51.590 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_name&paramKeyDefault=null-调用者:com.czur.starry.device.file
25_06_13 09:28:51.591 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_status_download&paramKeyDefault=false-调用者:com.czur.starry.device.file
25_06_13 09:28:51.592 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=unReadFileTabUpdate&paramKeyDefault=0-调用者:com.czur.starry.device.file
25_06_13 09:28:51.613 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_share_code_status&paramKeyDefault=-调用者:com.czur.starry.device.file
25_06_13 09:28:51.614 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_status_local&paramKeyDefault=false-调用者:com.czur.starry.device.file
25_06_13 09:28:51.614 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_share_enable_status&paramKeyDefault=true-调用者:com.czur.starry.device.file
25_06_13 09:28:51.616 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_share_code_enable&paramKeyDefault=true-调用者:com.czur.starry.device.file
25_06_13 09:28:51.616 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_share_time_out_status&paramKeyDefault=4-调用者:com.czur.starry.device.file
25_06_13 09:28:51.617 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_status_screenshort&paramKeyDefault=false-调用者:com.czur.starry.device.file
25_06_13 09:28:51.618 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_meeting_enable_status&paramKeyDefault=true-调用者:com.czur.starry.device.file
25_06_13 09:28:51.650 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_status_meeting&paramKeyDefault=false-调用者:com.czur.starry.device.file
25_06_13 09:28:51.651 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_keycode_status&paramKeyDefault=-1-调用者:com.czur.starry.device.file
25_06_13 09:28:51.652 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_media_status&paramKeyDefault=-调用者:com.czur.starry.device.file
25_06_13 09:28:51.653 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_sort_type_status_state&paramKeyDefault=-1-调用者:com.czur.starry.device.file
25_06_13 09:28:51.653 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_status_ai_trans&paramKeyDefault=false-调用者:com.czur.starry.device.file
25_06_13 09:28:51.655 DEBUG SPContentProvider update uri:content://com.czur.starry.device.livedata.fileprovider/getSPValue-调用者:com.czur.starry.device.file
25_06_13 09:28:51.657 VERBOSE SPContentProvider 查询KEY:is_unread_file_name
25_06_13 09:28:51.658 VERBOSE SPContentProvider 更新数据
25_06_13 09:28:51.814 VERBOSE SPContentProvider 更新数据:is_unread_file_name=null
25_06_13 09:28:51.815 VERBOSE SPContentProvider 通知数据改变:content://com.czur.starry.device.livedata.fileprovider/getSPValue?is_unread_file_name=null
25_06_13 09:28:51.816 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_share_enable_status&paramKeyDefault=true-调用者:com.czur.starry.device.noticecenter
25_06_13 09:28:51.817 DEBUG SPContentProvider update uri:content://com.czur.starry.device.livedata.fileprovider/getSPValue-调用者:com.czur.starry.device.noticecenter
25_06_13 09:28:51.817 VERBOSE SPContentProvider 查询KEY:unReadFileTabUpdate
25_06_13 09:28:51.818 VERBOSE SPContentProvider 更新数据
25_06_13 09:28:51.819 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_status_local&paramKeyDefault=false-调用者:com.czur.starry.device.noticecenter
25_06_13 09:28:51.820 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=unReadFileTabUpdate&paramKeyDefault=0-调用者:com.czur.starry.device.noticecenter
25_06_13 09:28:51.821 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_name&paramKeyDefault=null-调用者:com.czur.starry.device.noticecenter
25_06_13 09:28:51.822 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_status_share&paramKeyDefault=false-调用者:com.czur.starry.device.noticecenter
25_06_13 09:28:51.823 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_share_code_enable&paramKeyDefault=true-调用者:com.czur.starry.device.noticecenter
25_06_13 09:28:51.824 VERBOSE SPContentProvider 更新数据:unReadFileTabUpdate=8
25_06_13 09:28:51.825 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_status_download&paramKeyDefault=false-调用者:com.czur.starry.device.noticecenter
25_06_13 09:28:51.826 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_meeting_enable_status&paramKeyDefault=true-调用者:com.czur.starry.device.noticecenter
25_06_13 09:28:51.826 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_status_meeting&paramKeyDefault=false-调用者:com.czur.starry.device.noticecenter
25_06_13 09:28:51.827 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_share_time_out_status&paramKeyDefault=4-调用者:com.czur.starry.device.noticecenter
25_06_13 09:28:51.828 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_share_code_status&paramKeyDefault=-调用者:com.czur.starry.device.noticecenter
25_06_13 09:28:51.829 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_status_ai_trans&paramKeyDefault=false-调用者:com.czur.starry.device.noticecenter
25_06_13 09:28:51.830 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_sort_type_status_state&paramKeyDefault=-1-调用者:com.czur.starry.device.noticecenter
25_06_13 09:28:51.831 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_keycode_status&paramKeyDefault=-1-调用者:com.czur.starry.device.noticecenter
25_06_13 09:28:51.832 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_file_media_status&paramKeyDefault=-调用者:com.czur.starry.device.noticecenter
25_06_13 09:28:51.833 VERBOSE SPContentProvider 通知数据改变:content://com.czur.starry.device.livedata.fileprovider/getSPValue?unReadFileTabUpdate=8
25_06_13 09:28:51.834 DEBUG SPContentProvider query:content://com.czur.starry.device.livedata.fileprovider/getSPValue?paramKeyName=is_unread_file_status_screenshort&paramKeyDefault=false-调用者:com.czur.starry.device.noticecenter
25_06_13 09:28:51.835 DEBUG SPContentProvider update uri:content://com.czur.starry.device.livedata.fileprovider/getSPValue-调用者:com.czur.starry.device.file
25_06_13 09:28:51.836 VERBOSE SPContentProvider 查询KEY:is_unread_file_status_local
25_06_13 09:28:51.837 VERBOSE SPContentProvider 更新数据
25_06_13 09:28:51.838 VERBOSE SPContentProvider 更新数据:is_unread_file_status_local=false
25_06_13 09:28:51.839 VERBOSE SPContentProvider 通知数据改变:content://com.czur.starry.device.livedata.fileprovider/getSPValue?is_unread_file_status_local=false
25_06_13 09:28:51.859 DEBUG SPContentProvider update uri:content://com.czur.starry.device.livedata.fileprovider/getSPValue-调用者:com.czur.starry.device.file
25_06_13 09:28:51.860 VERBOSE SPContentProvider 查询KEY:is_unread_file_status_screenshort
25_06_13 09:28:51.862 VERBOSE SPContentProvider 更新数据
25_06_13 09:28:51.867 VERBOSE SPContentProvider 更新数据:is_unread_file_status_screenshort=false
25_06_13 09:28:51.870 VERBOSE SPContentProvider 通知数据改变:content://com.czur.starry.device.livedata.fileprovider/getSPValue?is_unread_file_status_screenshort=false
25_06_13 09:28:51.948 DEBUG SPContentProvider update uri:content://com.czur.starry.device.livedata.fileprovider/getSPValue-调用者:com.czur.starry.device.file
25_06_13 09:28:51.950 VERBOSE SPContentProvider 查询KEY:is_unread_file_status_download
25_06_13 09:28:51.952 VERBOSE SPContentProvider 更新数据
25_06_13 09:28:51.965 VERBOSE SPContentProvider 更新数据:is_unread_file_status_download=false
25_06_13 09:28:51.969 VERBOSE SPContentProvider 通知数据改变:content://com.czur.starry.device.livedata.fileprovider/getSPValue?is_unread_file_status_download=false
25_06_13 09:28:51.995 DEBUG SPContentProvider update uri:content://com.czur.starry.device.livedata.fileprovider/getSPValue-调用者:com.czur.starry.device.file
25_06_13 09:28:51.996 VERBOSE SPContentProvider 查询KEY:is_unread_file_status_share
25_06_13 09:28:52.002 VERBOSE SPContentProvider 更新数据
25_06_13 09:28:52.005 VERBOSE SPContentProvider 更新数据:is_unread_file_status_share=false
25_06_13 09:28:52.006 VERBOSE SPContentProvider 通知数据改变:content://com.czur.starry.device.livedata.fileprovider/getSPValue?is_unread_file_status_share=false
25_06_13 09:28:52.027 DEBUG SPContentProvider update uri:content://com.czur.starry.device.livedata.fileprovider/getSPValue-调用者:com.czur.starry.device.file
25_06_13 09:28:52.028 VERBOSE SPContentProvider 查询KEY:is_unread_file_status_meeting
25_06_13 09:28:52.033 VERBOSE SPContentProvider 更新数据
25_06_13 09:28:52.037 VERBOSE SPContentProvider 更新数据:is_unread_file_status_meeting=false
25_06_13 09:28:52.039 VERBOSE SPContentProvider 通知数据改变:content://com.czur.starry.device.livedata.fileprovider/getSPValue?is_unread_file_status_meeting=false
25_06_13 09:28:52.073 DEBUG SPContentProvider update uri:content://com.czur.starry.device.livedata.fileprovider/getSPValue-调用者:com.czur.starry.device.file
25_06_13 09:28:52.076 VERBOSE SPContentProvider 查询KEY:is_unread_file_status_ai_trans
25_06_13 09:28:52.076 VERBOSE SPContentProvider 更新数据
25_06_13 09:28:52.080 VERBOSE SPContentProvider 更新数据:is_unread_file_status_ai_trans=false
25_06_13 09:28:52.083 VERBOSE SPContentProvider 通知数据改变:content://com.czur.starry.device.livedata.fileprovider/getSPValue?is_unread_file_status_ai_trans=false
25_06_13 09:28:52.101 DEBUG SPContentProvider update uri:content://com.czur.starry.device.livedata.fileprovider/getSPValue-调用者:com.czur.starry.device.file
25_06_13 09:28:52.103 VERBOSE SPContentProvider 查询KEY:unReadFileTabUpdate
25_06_13 09:28:52.104 VERBOSE SPContentProvider 更新数据
25_06_13 09:28:52.109 VERBOSE SPContentProvider 更新数据:unReadFileTabUpdate=0
25_06_13 09:28:52.124 VERBOSE SPContentProvider 通知数据改变:content://com.czur.starry.device.livedata.fileprovider/getSPValue?unReadFileTabUpdate=0
25_06_13 09:28:52.297 DEBUG ClassScanner servletClasses.size():12
25_06_13 09:28:52.298 DEBUG ClassScanner /api/starry/fileShare/checkFile
25_06_13 09:28:52.302 DEBUG ClassScanner /api/starry/fileShare/checkPwd
25_06_13 09:28:52.303 DEBUG ClassScanner /api/starry/fileShare/deleteTempFile
25_06_13 09:28:52.304 DEBUG ClassScanner /api/starry/fileShare/download
25_06_13 09:28:52.305 DEBUG ClassScanner /api/starry/fileShare/getFileList
25_06_13 09:28:52.305 DEBUG ClassScanner /api/starry/fileShare/functions
25_06_13 09:28:52.307 DEBUG ClassScanner /api/starry/fileShare/getStarryStatus
25_06_13 09:28:52.308 DEBUG ClassScanner /api/starry/fileShare/getStartBytes
25_06_13 09:28:52.308 DEBUG ClassScanner /api/starry/fileShare/getRootList
25_06_13 09:28:52.309 DEBUG ClassScanner /api/starry/fileShare/getThumb
25_06_13 09:28:52.310 DEBUG ClassScanner /api/starry/fileShare/getVerifyCode
25_06_13 09:28:52.311 DEBUG ClassScanner /api/starry/fileShare/uploadFile
25_06_13 09:28:52.312 DEBUG ComponentRepository servletMap:/api/starry/fileShare/checkFile, /api/starry/fileShare/checkPwd, /api/starry/fileShare/deleteTempFile, /api/starry/fileShare/download, /api/starry/fileShare/getFileList, /api/starry/fileShare/functions, /api/starry/fileShare/getStarryStatus, /api/starry/fileShare/getStartBytes, /api/starry/fileShare/getRootList, /api/starry/fileShare/getThumb, /api/starry/fileShare/getVerifyCode, /api/starry/fileShare/uploadFile
25_06_13 09:28:52.313 DEBUG ServerService port: 8652
25_06_13 09:28:52.314 DEBUG ServerService ====initServerBootstrap== port:8652
25_06_13 09:28:52.386 DEBUG ServerService 服务端开启成功
25_06_13 09:28:52.763 DEBUG SPContentProvider update uri:content://com.czur.starry.device.livedata.fileprovider/getSPValue-调用者:com.czur.starry.device.noticecenter
25_06_13 09:28:52.764 VERBOSE SPContentProvider 查询KEY:unReadFileTabUpdate
25_06_13 09:28:52.765 VERBOSE SPContentProvider 更新数据
25_06_13 09:28:52.767 VERBOSE SPContentProvider 更新数据:unReadFileTabUpdate=0
25_06_13 09:28:52.768 VERBOSE SPContentProvider 通知数据改变:content://com.czur.starry.device.livedata.fileprovider/getSPValue?unReadFileTabUpdate=0
25_06_13 09:28:53.147 DEBUG SPContentProvider update uri:content://com.czur.starry.device.livedata.fileprovider/getSPValue-调用者:com.czur.starry.device.file
25_06_13 09:28:53.148 VERBOSE SPContentProvider 查询KEY:is_unread_file_name
25_06_13 09:28:53.149 VERBOSE SPContentProvider 更新数据
25_06_13 09:28:53.152 VERBOSE SPContentProvider 更新数据:is_unread_file_name=null
25_06_13 09:28:53.154 VERBOSE SPContentProvider 通知数据改变:content://com.czur.starry.device.livedata.fileprovider/getSPValue?is_unread_file_name=null
25_06_13 09:28:57.311 DEBUG ServerService localRequest
25_06_13 09:28:57.395 VERBOSE ServerHandler ===startFileUploadOrDownload=
25_06_13 09:28:57.738 DEBUG ServerHandler ===endFileUpload=
25_06_13 09:28:57.739 VERBOSE ServerHandler ===endFileUploadOrDownload=
25_06_13 09:28:57.748 VERBOSE ServerHandler ===startFileUploadOrDownload=
25_06_13 09:28:57.750 VERBOSE RootList RootListServlet onConnect
25_06_13 09:28:57.751 VERBOSE RootList clientAPIVersion: 0
25_06_13 09:28:59.254 DEBUG ServerHandler ===endFileUpload=
25_06_13 09:28:59.254 VERBOSE ServerHandler ===endFileUploadOrDownload=
25_06_13 09:29:01.392 DEBUG CpuUtils ========== cpuUsage: 32.489452
25_06_13 09:29:01.393 VERBOSE ServerService CPU负载正常
25_06_13 09:29:11.696 DEBUG CpuUtils ========== cpuUsage: 98.34025
25_06_13 09:29:11.701 DEBUG ServerService CPU负载高
