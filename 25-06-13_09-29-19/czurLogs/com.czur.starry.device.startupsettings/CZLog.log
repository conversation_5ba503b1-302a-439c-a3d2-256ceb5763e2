25_06_13 09:10:44.993 INFO StartUpApp StartUpApp 启动:com.czur.starry.device.startupsettings.StartUpApp@7931f31
25_06_13 09:10:45.074 VERBOSE CZURAtyManager 添加:WelcomeActivity(onCreate)
25_06_13 09:10:45.161 DEBUG WelcomeActivity 启动小圆屏
25_06_13 09:10:45.164 DEBUG WelcomeActivity 找到了尺寸为 360x360 的屏幕
25_06_13 09:10:45.214 VERBOSE WelcomeActivity 国内版, 简体中文
25_06_13 09:10:45.462 VERBOSE CZURAtyManager remove:WelcomeActivity(onDestroy)
25_06_13 09:10:45.468 DEBUG CZURAtyManager com.czur.starry.device.startupsettings已经没有可见页面
25_06_13 09:10:45.508 VERBOSE CZURAtyManager 添加:WelcomeActivity(onCreate)
25_06_13 09:10:45.582 DEBUG LanguageManager code:zh
25_06_13 09:10:45.616 DEBUG WelcomeActivity 启动小圆屏
25_06_13 09:10:45.621 DEBUG WelcomeActivity 找到了尺寸为 360x360 的屏幕
25_06_13 09:10:45.647 VERBOSE WelcomeActivity 国内版, 简体中文
25_06_13 09:10:45.663 DEBUG LanguageManager code:zh
25_06_13 09:10:46.647 VERBOSE WelcomeActivity 检测厂测程序第1次
25_06_13 09:10:46.651 DEBUG FactoryTestHelper 不符合启动厂测/老化程序条件
25_06_13 09:10:47.654 VERBOSE WelcomeActivity 检测厂测程序第2次
25_06_13 09:10:47.656 DEBUG FactoryTestHelper 不符合启动厂测/老化程序条件
25_06_13 09:10:48.657 VERBOSE WelcomeActivity 检测厂测程序第3次
25_06_13 09:10:48.659 DEBUG FactoryTestHelper 不符合启动厂测/老化程序条件
25_06_13 09:10:48.663 DEBUG WelcomeActivity 无线投屏
25_06_13 09:10:48.667 VERBOSE WelcomeActivity 设置音量:streamType:0, 目标音量:12(12), 最大音量:15
25_06_13 09:10:48.676 DEBUG WelcomeActivity 设置时区:Asia/Shanghai
25_06_13 09:10:48.682 VERBOSE WelcomeActivity 设置音量:streamType:3, 目标音量:7(7), 最大音量:15
25_06_13 09:10:48.828 VERBOSE WelcomeActivity 启动自动对焦服务
25_06_13 09:10:48.901 VERBOSE WelcomeActivity 检查是否需要引导连接触控板
25_06_13 09:10:48.902 VERBOSE WelcomeActivity 飞行模式未开启
25_06_13 09:10:53.829 VERBOSE WelcomeActivity 启动监控服务
25_06_13 09:10:53.838 VERBOSE WelcomeActivity 没有连接过触控板
25_06_13 09:11:20.398 VERBOSE CZURAtyManager 添加:StartUpActivity(onCreate)
25_06_13 09:11:20.749 VERBOSE StartUpActivity onWindowFocusChanged - moveToNextStep
25_06_13 09:11:20.750 DEBUG StartUpActivity moveToNextStep->
25_06_13 09:11:20.751 DEBUG StartUpActivity 第一步:201
25_06_13 09:11:21.362 VERBOSE CZURAtyManager App:com.czur.starry.device.startupsettings 处于后台, 清理禁止后台显示的Activity
25_06_13 09:11:21.392 VERBOSE CZURAtyManager remove:WelcomeActivity(onDestroy)
25_06_13 09:11:22.910 DEBUG StartUpActivity 设定完成:211, 结果:-1
25_06_13 09:11:22.912 DEBUG StartUpActivity moveToNextStep->
25_06_13 09:11:22.914 DEBUG StartUpActivity 下一步ID:202
25_06_13 09:11:24.440 DEBUG StartUpActivity 设定完成:202, 结果:0
25_06_13 09:11:24.446 DEBUG StartUpActivity moveToNextStep->
25_06_13 09:11:24.447 DEBUG StartUpActivity 下一步ID:300
25_06_13 09:11:24.515 VERBOSE CZURAtyManager 添加:StartUpFinishActivity(onCreate)
25_06_13 09:11:24.560 DEBUG TouchPadGuideFragment 国内版本, 按照触控板一代引导
25_06_13 09:11:25.202 VERBOSE CZURAtyManager remove:StartUpActivity(onDestroy)
25_06_13 09:11:26.334 DEBUG StartUpFinishActivity moveToFinish
25_06_13 09:11:26.350 VERBOSE AlertWindowService 唤醒屏幕: com.czur.starry.device.startupsettings.StartUpFinishSettingWindow
25_06_13 09:11:26.393 DEBUG StartUpFinishActivity moveToFinish
25_06_13 09:11:26.424 VERBOSE StartUpFinishFragment 执行任务:enableLauncher
25_06_13 09:11:26.426 INFO StartUpFinishFragment 启用Launcher
25_06_13 09:11:26.433 INFO StartUpFinishFragment Launcher 已成功启用
25_06_13 09:11:26.472 DEBUG StartUpFinishActivity moveToFinish
25_06_13 09:11:26.494 VERBOSE StartUpFinishFragment 执行任务:saveVersion
25_06_13 09:11:26.514 VERBOSE StartUpFinishFragment 执行任务:initScreenShare
25_06_13 09:11:26.519 DEBUG StartUpFinishFragment 初始化无线投屏
25_06_13 09:11:26.528 DEBUG StartUpFinishFragment 关闭宜享悬浮窗
25_06_13 09:11:26.529 DEBUG StartUpFinishFragment 设置设备名悬浮窗到Default:false
25_06_13 09:11:26.531 VERBOSE StartUpFinishFragment 设置设备名称
25_06_13 09:11:26.545 VERBOSE StartUpFinishFragment 设置eShare名称成功
25_06_13 09:11:26.552 VERBOSE StartUpFinishFragment 关闭投屏码
25_06_13 09:11:26.555 DEBUG StartUpFinishFragment 设置PINCode Mode为Disable
25_06_13 09:11:26.562 VERBOSE StartUpFinishFragment 设置EShare Pin Code Mode成功
25_06_13 09:11:26.564 VERBOSE StartUpFinishFragment 打开EShare
25_06_13 09:11:26.581 VERBOSE StartUpFinishFragment 打开DLNA
25_06_13 09:11:26.642 VERBOSE StartUpFinishFragment 打开Miracast
25_06_13 09:11:26.648 VERBOSE StartUpFinishFragment 打开Chromecast
25_06_13 09:11:26.650 VERBOSE StartUpFinishFragment 写入SN
25_06_13 09:11:26.659 VERBOSE StartUpFinishFragment 打开AirPlay
25_06_13 09:11:26.668 INFO StartUpFinishFragment 无线投屏初始化完成
25_06_13 09:11:26.671 VERBOSE StartUpFinishFragment 执行任务:initOtherSettings
25_06_13 09:11:26.672 DEBUG StartUpFinishFragment 设定HDMI自动打开
25_06_13 09:11:26.676 VERBOSE VersionUtil 记录当前版本号
25_06_13 09:11:26.688 VERBOSE StartUpFinishFragment 执行任务:copyExplainFile
25_06_13 09:11:26.689 VERBOSE ProviderUtil ContentValues:   cv.size: 0
25_06_13 09:11:26.690 VERBOSE ProviderUtil update uri: content://com.czur.starry.device.file.share.ShareFileProvider/copyExplainFile , contentValues Size: 0
25_06_13 09:11:26.759 VERBOSE ProviderUtil update result: 101
25_06_13 09:11:26.760 DEBUG StartUpFinishFragment copyExplainFile:true
25_06_13 09:11:26.761 VERBOSE StartUpFinishFragment 执行任务:setSystemWallpaper
25_06_13 09:11:26.762 DEBUG StartUpFinishFragment 设置壁纸
25_06_13 09:11:31.719 DEBUG StartUpFinishFragment 设置壁纸完成
25_06_13 09:11:31.723 VERBOSE StartUpFinishFragment 执行任务:setCZURAppConfig
25_06_13 09:11:31.724 DEBUG StartUpFinishFragment 设置成者APP的默认值
25_06_13 09:11:31.725 DEBUG PersonalizationSetting resetLauncherConfig 到默认值
25_06_13 09:11:31.726 VERBOSE PersonalizationSetting setLauncherFavApps: com.czur.starry.device.appstore, com.czur.starry.device.localmeetingrecord, com.czur.starry.device.wallpaperdisplay
25_06_13 09:11:31.728 DEBUG StartUpFinishFragment 设置拾音器参数
25_06_13 09:11:31.730 DEBUG CameraAndMicSetting setAudioAlgoNSLevel: 2
25_06_13 09:11:31.732 DEBUG CameraAndMicSetting setAudioAlgoAGCLevel: 1
25_06_13 09:11:31.734 VERBOSE StartUpFinishFragment 执行任务:otherSystemSettings
25_06_13 09:11:31.736 DEBUG StartUpFinishFragment 设置其他系统设置
25_06_13 09:11:31.737 DEBUG StartUpFinishFragment 设置为: 智能人像画面
25_06_13 09:11:31.754 WARN SystemManagerProxy onSystemChangeListener is null,add default listener
25_06_13 09:11:31.755 VERBOSE SystemManagerProxy setTrackMode: TRACK_MODE_VISION
25_06_13 09:11:31.767 VERBOSE SystemManagerProxy category: 2, eventID: 4, para1: 0, extend1: , extend2: 
25_06_13 09:11:31.770 VERBOSE StartUpFinishFragment 执行任务:markSetupComplete
25_06_13 09:11:31.771 DEBUG StartUpFinishFragment 标记初期设定已完成
25_06_13 09:11:31.802 VERBOSE StartUpFinishFragment 执行任务:disableSelf
25_06_13 09:11:31.811 INFO StartUpFinishFragment 禁用 StartUP
25_06_13 09:11:31.812 INFO StartUpFinishFragment StartUP 已成功禁用
25_06_13 09:11:33.135 VERBOSE CZURAtyManager App:com.czur.starry.device.startupsettings 处于后台, 清理禁止后台显示的Activity
25_06_13 09:11:33.141 VERBOSE CZURAtyManager remove:StartUpFinishActivity(onDestroy)
25_06_13 09:11:33.144 DEBUG CZURAtyManager com.czur.starry.device.startupsettings已经没有可见页面
25_06_13 09:11:41.426 DEBUG StartUpFinishFragment 全部设定任务已完成
25_06_13 09:11:41.428 DEBUG StartUpFinishFragment 结束初期设定
25_06_13 09:11:41.429 DEBUG StartUpFinishFragment 标记初期设定完成
25_06_13 09:11:46.435 DEBUG StartUpFinishFragment 标记初期设定完成 For System
25_06_13 09:11:46.460 DEBUG StartUpFinishFragment 结束自己
