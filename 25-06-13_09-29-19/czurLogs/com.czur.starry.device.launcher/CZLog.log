25_06_13 09:10:54.026 DEBUG CZNotificationManager clearAllPkgNotification: com.czur.starry.device.noticecenter
25_06_13 09:11:30.985 VERBOSE BackdropChangeReceiver 壁纸更换了
25_06_13 09:11:31.738 DEBUG BackdropManager 主导色分析: RGB(40,72,192), 亮度=73.86080000000001
25_06_13 09:11:31.739 DEBUG BackdropManager 背景颜色分析结果: Dark, 选中样本颜色: #2848C0
25_06_13 09:11:31.740 DEBUG BackdropManager 背景颜色分析结果: Dark
25_06_13 09:11:32.971 DEBUG CZURAtyManager com.czur.starry.device.launcher.pages.view.launcher.LauncherActivity没有继承BaseActivity,不进行管理
25_06_13 09:11:33.037 VERBOSE WelcomeActivity 直接启动主画面
25_06_13 09:11:33.037 DEBUG WelcomeActivity 设置完毕, 发送广播
25_06_13 09:11:33.052 DEBUG WelcomeActivity completeUserSetupForSystem
25_06_13 09:11:33.053 DEBUG WelcomeActivity 启动小圆屏
25_06_13 09:11:33.055 DEBUG WelcomeActivity 找到了尺寸为 360x360 的屏幕
25_06_13 09:11:33.175 VERBOSE CZURAtyManager 添加:LauncherMainActivity(onCreate)
25_06_13 09:11:33.177 VERBOSE LauncherActivity 更新壁纸
25_06_13 09:11:33.230 DEBUG AppInfoViewModel 注册App安装广播
25_06_13 09:11:33.235 VERBOSE OtherQuickBootAppManager 加载tag:VideoMeeting
25_06_13 09:11:33.236 VERBOSE OtherMeetingQuickBootAppManager 下一次自动刷新时间:120s 后
25_06_13 09:11:33.237 INFO AppInfoViewModel 直接绑定AppStore Service
25_06_13 09:11:33.238 DEBUG AppInfoViewModel 绑定应用市场Service
25_06_13 09:11:33.341 DEBUG NoticeHandler 注册回调
25_06_13 09:11:33.342 DEBUG NoticeHandler 消息类型:type=( NOTIFY_MSG -[ 最新一条消息 ])第一次注册
25_06_13 09:11:33.344 DEBUG NoticeHandler 该owner没有之前添加过监听
25_06_13 09:11:33.357 DEBUG ShareViewModel peripheralUSBRunningFlow = false
25_06_13 09:11:33.358 VERBOSE ShareViewModel refreshGadgetState
25_06_13 09:11:33.367 DEBUG NoticeHandler 注册回调
25_06_13 09:11:33.376 DEBUG NoticeHandler 消息类型:type=( SYNC - [ 外设USB模式变化 ] )第一次注册
25_06_13 09:11:33.379 VERBOSE ShareViewModel peripheralModeRunningFlow byom = false, usb = false
25_06_13 09:11:33.390 VERBOSE SystemManagerProxy getGadgetMode Start
25_06_13 09:11:33.394 DEBUG SystemManagerProxy isGadgetMode Value: 0
25_06_13 09:11:33.395 VERBOSE ShareViewModel USB外设模式 = USB_GADGET_MODE_OFF
25_06_13 09:11:33.402 DEBUG NotifyMsgHandler HasUnRead:false
25_06_13 09:11:33.561 VERBOSE ProviderUtil ContentValues: voice_assistant_is_support_scene=true  cv.size: 1
25_06_13 09:11:33.562 VERBOSE ProviderUtil Key: voice_assistant_is_support_scene, Value: true
25_06_13 09:11:33.563 VERBOSE ProviderUtil update uri: content://com.czur.starry.device.voiceassistant.provider.initProvider/getSPValue , contentValues Size: 1
25_06_13 09:11:33.683 VERBOSE ProviderUtil update result: 1
25_06_13 09:11:33.688 INFO VoiceAssistantHandler 更新成功, 更新缓存
25_06_13 09:11:33.698 INFO KeyCodeVM 读取导航栏状态:0
25_06_13 09:11:33.699 INFO CZBusinessAty page-start: LauncherMainActivity
25_06_13 09:11:33.706 DEBUG StatusBarFragment 刷新外设模式
25_06_13 09:11:33.707 VERBOSE ShareViewModel refreshGadgetState
25_06_13 09:11:33.709 VERBOSE SystemManagerProxy getGadgetMode Start
25_06_13 09:11:33.710 DEBUG SystemManagerProxy isGadgetMode Value: 0
25_06_13 09:11:33.710 VERBOSE ShareViewModel USB外设模式 = USB_GADGET_MODE_OFF
25_06_13 09:11:33.711 VERBOSE ShareViewModel peripheralModeRunningFlow byom = false, usb = false
25_06_13 09:11:33.712 VERBOSE StatusBarFragment byomAndUsbRunning:PeripheralMode(byom=false, usb=false)
25_06_13 09:11:33.714 VERBOSE ShareViewModel peripheralModeRunningFlow byom = false, usb = false
25_06_13 09:11:33.716 VERBOSE ProviderUtil ContentValues: voice_assistant_is_support_scene=true  cv.size: 1
25_06_13 09:11:33.718 VERBOSE ProviderUtil Key: voice_assistant_is_support_scene, Value: true
25_06_13 09:11:33.719 VERBOSE ProviderUtil update uri: content://com.czur.starry.device.voiceassistant.provider.initProvider/getSPValue , contentValues Size: 1
25_06_13 09:11:33.728 VERBOSE ProviderUtil update result: 1
25_06_13 09:11:33.730 INFO VoiceAssistantHandler 更新成功, 更新缓存
25_06_13 09:11:33.734 DEBUG LauncherActivity Launcher启动完成
25_06_13 09:11:33.740 DEBUG LauncherActivity Launcher doRepeatResume
25_06_13 09:11:33.741 DEBUG LauncherActivity 发送LauncherResume广播
25_06_13 09:11:33.761 VERBOSE OtherMeetingQuickBootAppManager 下一次自动刷新时间:120s 后
25_06_13 09:11:33.833 VERBOSE EShareUtil checkAndActiveEShare
25_06_13 09:11:33.840 DEBUG EShareUtil 没有获取到激活码
25_06_13 09:11:34.000 VERBOSE LauncherMainFragment 显示引导UI
25_06_13 09:11:34.002 DEBUG AppInfoViewModel 加载收藏应用列表
25_06_13 09:11:34.057 DEBUG ThemeNetStatusIcon 状态:NO_NET_WORK
25_06_13 09:11:34.248 DEBUG NoticeHandler 添加监听,监控生命周期StatusBarFragment-StatusBarFragment{9fe43b8} (e94718e5-f816-4bae-ab09-81718651f38a id=0x7f09019b tag=StatusBarFragment)
25_06_13 09:11:34.250 VERBOSE StatusBarFragment hasUnReadMsg:false
25_06_13 09:11:34.252 DEBUG NotifyMsgHandler 注册未读消息监听
25_06_13 09:11:34.254 VERBOSE ShareViewModel peripheralModeRunningFlow byom = false, usb = false
25_06_13 09:11:34.256 VERBOSE ProviderUtil ContentValues: voice_assistant_is_support_scene=true  cv.size: 1
25_06_13 09:11:34.257 VERBOSE ProviderUtil Key: voice_assistant_is_support_scene, Value: true
25_06_13 09:11:34.258 VERBOSE ProviderUtil update uri: content://com.czur.starry.device.voiceassistant.provider.initProvider/getSPValue , contentValues Size: 1
25_06_13 09:11:34.263 VERBOSE ProviderUtil update result: 1
25_06_13 09:11:34.264 INFO VoiceAssistantHandler 更新成功, 更新缓存
25_06_13 09:11:34.266 INFO StatusBarFragment AI互译字幕开启状态:false
25_06_13 09:11:34.267 VERBOSE ShareViewModel peripheralModeRunningFlow byom = false, usb = false
25_06_13 09:11:34.268 VERBOSE ProviderUtil ContentValues: voice_assistant_is_support_scene=true  cv.size: 1
25_06_13 09:11:34.269 VERBOSE ProviderUtil Key: voice_assistant_is_support_scene, Value: true
25_06_13 09:11:34.270 VERBOSE ProviderUtil update uri: content://com.czur.starry.device.voiceassistant.provider.initProvider/getSPValue , contentValues Size: 1
25_06_13 09:11:34.273 VERBOSE ProviderUtil update result: 1
25_06_13 09:11:34.274 INFO VoiceAssistantHandler 更新成功, 更新缓存
25_06_13 09:11:34.276 INFO KeyCodeVM 读取引导次数:0
25_06_13 09:11:34.279 DEBUG EShareUtil showDeviceNameAlertWindow:没有启用设备名称弹窗
25_06_13 09:11:34.280 DEBUG LauncherActivity ==enableEShareVoice===
25_06_13 09:11:34.280 DEBUG EShareUtil enableEShareVoice:true
25_06_13 09:11:34.281 DEBUG ShareViewModel Launcher启动, 打开EShare
25_06_13 09:11:34.301 VERBOSE QuickBootApp 新文件名字 = null
25_06_13 09:11:34.303 VERBOSE QuickBootApp 新校验码
25_06_13 09:11:34.304 VERBOSE QuickBootApp 新校验码
25_06_13 09:11:34.305 VERBOSE QuickBootApp 新校验码
25_06_13 09:11:34.306 VERBOSE QuickBootApp ota新版本:false , 触控板新版本:false
25_06_13 09:11:34.375 VERBOSE QuickBootApp ota新版本:false , 触控板新版本:false
25_06_13 09:11:34.385 VERBOSE QuickBootApp ota新版本:false , 触控板新版本:false
25_06_13 09:11:34.388 VERBOSE QuickBootApp StarryPad新文件名字 = 
25_06_13 09:11:34.402 VERBOSE NetAppDoubleCache 网络app缓存过期/缓存不存在
25_06_13 09:11:34.405 VERBOSE LauncherActivity 防止自动对焦服务挂掉
25_06_13 09:11:34.431 VERBOSE ActiveInterceptor 需要激活:/api/app/store/app/list
25_06_13 09:11:34.433 WARN SystemManagerProxy onSystemChangeListener is null,add default listener
25_06_13 09:11:34.446 VERBOSE CommonParamInterceptor Token为空
25_06_13 09:11:34.449 VERBOSE NetLogging 请求信息
25_06_13 09:11:34.449 VERBOSE NetLogging ╔══════════════════════════════
25_06_13 09:11:34.449 VERBOSE NetLogging ║ method:GET
25_06_13 09:11:34.449 VERBOSE NetLogging ║ host:test0927-starry.czur.cc
25_06_13 09:11:34.449 VERBOSE NetLogging ║ path:/api/starry/register/v2/active
25_06_13 09:11:34.449 VERBOSE NetLogging ║ -----
25_06_13 09:11:34.449 VERBOSE NetLogging ║ 请求头:
25_06_13 09:11:34.449 VERBOSE NetLogging ║ UDID: CBP02B2503000001
25_06_13 09:11:34.449 VERBOSE NetLogging ║ App-Key: com.czur.starry
25_06_13 09:11:34.449 VERBOSE NetLogging ║ Request-Timestamp: 1749777094445
25_06_13 09:11:34.449 VERBOSE NetLogging ║ 
25_06_13 09:11:34.449 VERBOSE NetLogging ║ url参数:
25_06_13 09:11:34.449 VERBOSE NetLogging ║ sn: CBP02B2503000001
25_06_13 09:11:34.449 VERBOSE NetLogging ║ secret: 1qaz2wsx3edc4rfv
25_06_13 09:11:34.449 VERBOSE NetLogging ║ voucher: C3190FB7E60F4F9689EA083680E3DCB7
25_06_13 09:11:34.449 VERBOSE NetLogging ║ mac: 9a:8e:5c:0b:11:26
25_06_13 09:11:34.449 VERBOSE NetLogging ║ udid: CBP02B2503000001
25_06_13 09:11:34.449 VERBOSE NetLogging ║ noncestr: 3030667814391850
25_06_13 09:11:34.449 VERBOSE NetLogging ║ timestamp: 1749777094446
25_06_13 09:11:34.449 VERBOSE NetLogging ║ sign: fc4caad726e76737cae6f57dca34ff613d1d02b1
25_06_13 09:11:34.449 VERBOSE NetLogging ╚══════════════════════════════
25_06_13 09:11:34.454 ERROR NetLogging 请求出错 path->/api/starry/register/v2/active
25_06_13 09:11:34.454 ERROR SyncHttpTask 请求出错:
25_06_13 09:11:34.456 ERROR NetResultUtil 网络错误
25_06_13 09:11:34.457 VERBOSE NetResultUtil 没有网络连接
25_06_13 09:11:34.464 VERBOSE OtherMeetingQuickBootAppManager 下一次自动刷新时间:120s 后
25_06_13 09:11:34.523 VERBOSE CZURAtyManager remove:LauncherActivity(onDestroy)
25_06_13 09:11:34.794 DEBUG LauncherActivity 发送Launcher启动消息
25_06_13 09:11:34.796 DEBUG LauncherActivity 发送Launcher启动消息
25_06_13 09:11:34.797 DEBUG NoticeHandler sendMessage: type:type=( MODULE_BOOT -[ Launcher ] )
25_06_13 09:11:34.799 VERBOSE LauncherActivity Launcher 开机第一次启动, 检查HDMI
25_06_13 09:11:34.800 VERBOSE LauncherActivity isEShareActive false
25_06_13 09:11:34.805 VERBOSE LauncherActivity hdmi state false
25_06_13 09:11:34.807 VERBOSE LauncherActivity 混投开关 true
25_06_13 09:11:34.936 DEBUG CZNotificationManager clearAllPkgNotification: com.czur.starry.device.noticecenter
25_06_13 09:11:50.520 DEBUG ToolSheetFragment 启动app:欢庆屏保
25_06_13 09:11:50.524 DEBUG BootUtil CheckBefore 启动app:com.czur.starry.device.wallpaperdisplay
25_06_13 09:11:51.394 INFO CZBusinessAty page-stop: LauncherMainActivity
25_06_13 09:11:51.407 DEBUG EShareUtil showDeviceNameAlertWindow:没有启用设备名称弹窗
25_06_13 09:11:55.792 VERBOSE ShareViewModel peripheralModeRunningFlow byom = false, usb = false
25_06_13 09:11:55.794 VERBOSE ProviderUtil ContentValues: voice_assistant_is_support_scene=true  cv.size: 1
25_06_13 09:11:55.795 VERBOSE ProviderUtil Key: voice_assistant_is_support_scene, Value: true
25_06_13 09:11:55.797 VERBOSE ProviderUtil update uri: content://com.czur.starry.device.voiceassistant.provider.initProvider/getSPValue , contentValues Size: 1
25_06_13 09:11:55.810 VERBOSE ProviderUtil update result: 1
25_06_13 09:11:55.811 INFO VoiceAssistantHandler 更新成功, 更新缓存
25_06_13 09:11:55.820 VERBOSE ShareViewModel peripheralModeRunningFlow byom = false, usb = false
25_06_13 09:11:55.821 VERBOSE ProviderUtil ContentValues: voice_assistant_is_support_scene=true  cv.size: 1
25_06_13 09:11:55.826 VERBOSE ProviderUtil Key: voice_assistant_is_support_scene, Value: true
25_06_13 09:11:55.827 VERBOSE ProviderUtil update uri: content://com.czur.starry.device.voiceassistant.provider.initProvider/getSPValue , contentValues Size: 1
25_06_13 09:11:55.839 VERBOSE ProviderUtil update result: 1
25_06_13 09:11:55.844 INFO VoiceAssistantHandler 更新成功, 更新缓存
25_06_13 09:11:55.847 VERBOSE ShareViewModel peripheralModeRunningFlow byom = false, usb = false
25_06_13 09:11:55.849 VERBOSE ProviderUtil ContentValues: voice_assistant_is_support_scene=true  cv.size: 1
25_06_13 09:11:55.852 VERBOSE ProviderUtil Key: voice_assistant_is_support_scene, Value: true
25_06_13 09:11:55.858 VERBOSE ProviderUtil update uri: content://com.czur.starry.device.voiceassistant.provider.initProvider/getSPValue , contentValues Size: 1
25_06_13 09:11:55.864 VERBOSE ProviderUtil update result: 1
25_06_13 09:11:55.865 INFO VoiceAssistantHandler 更新成功, 更新缓存
25_06_13 09:11:55.868 VERBOSE ShareViewModel peripheralModeRunningFlow byom = false, usb = false
25_06_13 09:11:55.869 VERBOSE ProviderUtil ContentValues: voice_assistant_is_support_scene=true  cv.size: 1
25_06_13 09:11:55.871 VERBOSE ProviderUtil Key: voice_assistant_is_support_scene, Value: true
25_06_13 09:11:55.872 VERBOSE ProviderUtil update uri: content://com.czur.starry.device.voiceassistant.provider.initProvider/getSPValue , contentValues Size: 1
25_06_13 09:11:55.876 VERBOSE ProviderUtil update result: 1
25_06_13 09:11:55.878 INFO VoiceAssistantHandler 更新成功, 更新缓存
25_06_13 09:11:55.882 INFO KeyCodeVM 读取导航栏状态:0
25_06_13 09:11:55.884 INFO CZBusinessAty page-start: LauncherMainActivity
25_06_13 09:11:55.886 DEBUG StatusBarFragment 刷新外设模式
25_06_13 09:11:55.887 VERBOSE ShareViewModel refreshGadgetState
25_06_13 09:11:55.888 VERBOSE SystemManagerProxy getGadgetMode Start
25_06_13 09:11:55.889 DEBUG SystemManagerProxy isGadgetMode Value: 0
25_06_13 09:11:55.890 VERBOSE ShareViewModel peripheralModeRunningFlow byom = false, usb = false
25_06_13 09:11:55.891 VERBOSE StatusBarFragment byomAndUsbRunning:PeripheralMode(byom=false, usb=false)
25_06_13 09:11:55.891 VERBOSE ShareViewModel peripheralModeRunningFlow byom = false, usb = false
25_06_13 09:11:55.892 VERBOSE ProviderUtil ContentValues: voice_assistant_is_support_scene=true  cv.size: 1
25_06_13 09:11:55.893 VERBOSE ProviderUtil Key: voice_assistant_is_support_scene, Value: true
25_06_13 09:11:55.894 VERBOSE ProviderUtil update uri: content://com.czur.starry.device.voiceassistant.provider.initProvider/getSPValue , contentValues Size: 1
25_06_13 09:11:55.894 VERBOSE ShareViewModel USB外设模式 = USB_GADGET_MODE_OFF
25_06_13 09:11:55.901 VERBOSE ProviderUtil update result: 1
25_06_13 09:11:55.902 INFO VoiceAssistantHandler 更新成功, 更新缓存
25_06_13 09:11:55.906 VERBOSE LauncherMainFragment 显示引导UI
25_06_13 09:11:55.909 DEBUG AppInfoViewModel 加载收藏应用列表
25_06_13 09:11:55.913 DEBUG LauncherActivity Launcher doRepeatResume
25_06_13 09:11:55.914 DEBUG LauncherActivity 发送LauncherResume广播
25_06_13 09:11:55.954 DEBUG EShareUtil showDeviceNameAlertWindow:没有启用设备名称弹窗
25_06_13 09:11:55.955 DEBUG LauncherActivity ==enableEShareVoice===
25_06_13 09:11:55.956 DEBUG EShareUtil enableEShareVoice:true
25_06_13 09:11:55.961 VERBOSE NetAppDoubleCache 网络app缓存过期/缓存不存在
25_06_13 09:11:55.962 VERBOSE LauncherActivity 防止自动对焦服务挂掉
25_06_13 09:11:55.983 VERBOSE ActiveInterceptor 需要激活:/api/app/store/app/list
25_06_13 09:11:55.988 VERBOSE CommonParamInterceptor Token为空
25_06_13 09:11:55.993 VERBOSE NetLogging 请求信息
25_06_13 09:11:55.993 VERBOSE NetLogging ╔══════════════════════════════
25_06_13 09:11:55.993 VERBOSE NetLogging ║ method:GET
25_06_13 09:11:55.993 VERBOSE NetLogging ║ host:test0927-starry.czur.cc
25_06_13 09:11:55.993 VERBOSE NetLogging ║ path:/api/starry/register/v2/active
25_06_13 09:11:55.993 VERBOSE NetLogging ║ -----
25_06_13 09:11:55.993 VERBOSE NetLogging ║ 请求头:
25_06_13 09:11:55.993 VERBOSE NetLogging ║ UDID: CBP02B2503000001
25_06_13 09:11:55.993 VERBOSE NetLogging ║ App-Key: com.czur.starry
25_06_13 09:11:55.993 VERBOSE NetLogging ║ Request-Timestamp: 1749777115988
25_06_13 09:11:55.993 VERBOSE NetLogging ║ 
25_06_13 09:11:55.993 VERBOSE NetLogging ║ url参数:
25_06_13 09:11:55.993 VERBOSE NetLogging ║ sn: CBP02B2503000001
25_06_13 09:11:55.993 VERBOSE NetLogging ║ secret: 1qaz2wsx3edc4rfv
25_06_13 09:11:55.993 VERBOSE NetLogging ║ voucher: C3190FB7E60F4F9689EA083680E3DCB7
25_06_13 09:11:55.993 VERBOSE NetLogging ║ mac: 9a:8e:5c:0b:11:26
25_06_13 09:11:55.993 VERBOSE NetLogging ║ udid: CBP02B2503000001
25_06_13 09:11:55.993 VERBOSE NetLogging ║ noncestr: 3030667814391850
25_06_13 09:11:55.993 VERBOSE NetLogging ║ timestamp: 1749777115988
25_06_13 09:11:55.993 VERBOSE NetLogging ║ sign: 3fe8d819ce5f2729e04551435e21926bba0608f1
25_06_13 09:11:55.993 VERBOSE NetLogging ╚══════════════════════════════
25_06_13 09:11:55.996 ERROR NetLogging 请求出错 path->/api/starry/register/v2/active
25_06_13 09:11:55.998 ERROR SyncHttpTask 请求出错:
25_06_13 09:11:55.999 ERROR NetResultUtil 网络错误
25_06_13 09:11:56.003 VERBOSE NetResultUtil 没有网络连接
25_06_13 09:11:56.011 VERBOSE OtherMeetingQuickBootAppManager 下一次自动刷新时间:120s 后
25_06_13 09:13:56.013 VERBOSE OtherMeetingQuickBootAppManager 刷新一次快速启动App
25_06_13 09:13:56.016 VERBOSE NetAppDoubleCache 网络app缓存过期/缓存不存在
25_06_13 09:13:56.028 VERBOSE ActiveInterceptor 需要激活:/api/app/store/app/list
25_06_13 09:13:56.038 VERBOSE CommonParamInterceptor Token为空
25_06_13 09:13:56.040 VERBOSE NetLogging 请求信息
25_06_13 09:13:56.040 VERBOSE NetLogging ╔══════════════════════════════
25_06_13 09:13:56.040 VERBOSE NetLogging ║ method:GET
25_06_13 09:13:56.040 VERBOSE NetLogging ║ host:test0927-starry.czur.cc
25_06_13 09:13:56.040 VERBOSE NetLogging ║ path:/api/starry/register/v2/active
25_06_13 09:13:56.040 VERBOSE NetLogging ║ -----
25_06_13 09:13:56.040 VERBOSE NetLogging ║ 请求头:
25_06_13 09:13:56.040 VERBOSE NetLogging ║ UDID: CBP02B2503000001
25_06_13 09:13:56.040 VERBOSE NetLogging ║ App-Key: com.czur.starry
25_06_13 09:13:56.040 VERBOSE NetLogging ║ Request-Timestamp: 1749777236035
25_06_13 09:13:56.040 VERBOSE NetLogging ║ 
25_06_13 09:13:56.040 VERBOSE NetLogging ║ url参数:
25_06_13 09:13:56.040 VERBOSE NetLogging ║ sn: CBP02B2503000001
25_06_13 09:13:56.040 VERBOSE NetLogging ║ secret: 1qaz2wsx3edc4rfv
25_06_13 09:13:56.040 VERBOSE NetLogging ║ voucher: C3190FB7E60F4F9689EA083680E3DCB7
25_06_13 09:13:56.040 VERBOSE NetLogging ║ mac: 9a:8e:5c:0b:11:26
25_06_13 09:13:56.040 VERBOSE NetLogging ║ udid: CBP02B2503000001
25_06_13 09:13:56.040 VERBOSE NetLogging ║ noncestr: 3030667814391850
25_06_13 09:13:56.040 VERBOSE NetLogging ║ timestamp: 1749777236035
25_06_13 09:13:56.040 VERBOSE NetLogging ║ sign: aed33d859905271e73a2503d278c0ebe09c4739a
25_06_13 09:13:56.040 VERBOSE NetLogging ╚══════════════════════════════
25_06_13 09:13:56.045 ERROR NetLogging 请求出错 path->/api/starry/register/v2/active
25_06_13 09:13:56.050 ERROR SyncHttpTask 请求出错:
25_06_13 09:13:56.052 ERROR NetResultUtil 网络错误
25_06_13 09:13:56.053 VERBOSE NetResultUtil 没有网络连接
25_06_13 09:13:56.057 VERBOSE OtherMeetingQuickBootAppManager 下一次自动刷新时间:120s 后
25_06_13 09:15:56.050 VERBOSE OtherMeetingQuickBootAppManager 刷新一次快速启动App
25_06_13 09:15:56.054 VERBOSE NetAppDoubleCache 网络app缓存过期/缓存不存在
25_06_13 09:15:56.072 VERBOSE ActiveInterceptor 需要激活:/api/app/store/app/list
25_06_13 09:15:56.074 VERBOSE CommonParamInterceptor Token为空
25_06_13 09:15:56.079 VERBOSE NetLogging 请求信息
25_06_13 09:15:56.079 VERBOSE NetLogging ╔══════════════════════════════
25_06_13 09:15:56.079 VERBOSE NetLogging ║ method:GET
25_06_13 09:15:56.079 VERBOSE NetLogging ║ host:test0927-starry.czur.cc
25_06_13 09:15:56.079 VERBOSE NetLogging ║ path:/api/starry/register/v2/active
25_06_13 09:15:56.079 VERBOSE NetLogging ║ -----
25_06_13 09:15:56.079 VERBOSE NetLogging ║ 请求头:
25_06_13 09:15:56.079 VERBOSE NetLogging ║ UDID: CBP02B2503000001
25_06_13 09:15:56.079 VERBOSE NetLogging ║ App-Key: com.czur.starry
25_06_13 09:15:56.079 VERBOSE NetLogging ║ Request-Timestamp: 1749777356073
25_06_13 09:15:56.079 VERBOSE NetLogging ║ 
25_06_13 09:15:56.079 VERBOSE NetLogging ║ url参数:
25_06_13 09:15:56.079 VERBOSE NetLogging ║ sn: CBP02B2503000001
25_06_13 09:15:56.079 VERBOSE NetLogging ║ secret: 1qaz2wsx3edc4rfv
25_06_13 09:15:56.079 VERBOSE NetLogging ║ voucher: C3190FB7E60F4F9689EA083680E3DCB7
25_06_13 09:15:56.079 VERBOSE NetLogging ║ mac: 9a:8e:5c:0b:11:26
25_06_13 09:15:56.079 VERBOSE NetLogging ║ udid: CBP02B2503000001
25_06_13 09:15:56.079 VERBOSE NetLogging ║ noncestr: 3030667814391850
25_06_13 09:15:56.079 VERBOSE NetLogging ║ timestamp: 1749777356073
25_06_13 09:15:56.079 VERBOSE NetLogging ║ sign: 3bc8de8ceed063d27614c8f69af523c876005e80
25_06_13 09:15:56.079 VERBOSE NetLogging ╚══════════════════════════════
25_06_13 09:15:56.085 ERROR NetLogging 请求出错 path->/api/starry/register/v2/active
25_06_13 09:15:56.087 ERROR SyncHttpTask 请求出错:
25_06_13 09:15:56.088 ERROR NetResultUtil 网络错误
25_06_13 09:15:56.090 VERBOSE NetResultUtil 没有网络连接
25_06_13 09:15:56.092 VERBOSE OtherMeetingQuickBootAppManager 下一次自动刷新时间:120s 后
25_06_13 09:17:56.090 VERBOSE OtherMeetingQuickBootAppManager 刷新一次快速启动App
25_06_13 09:17:56.094 VERBOSE NetAppDoubleCache 网络app缓存过期/缓存不存在
25_06_13 09:17:56.119 VERBOSE ActiveInterceptor 需要激活:/api/app/store/app/list
25_06_13 09:17:56.127 VERBOSE CommonParamInterceptor Token为空
25_06_13 09:17:56.136 VERBOSE NetLogging 请求信息
25_06_13 09:17:56.136 VERBOSE NetLogging ╔══════════════════════════════
25_06_13 09:17:56.136 VERBOSE NetLogging ║ method:GET
25_06_13 09:17:56.136 VERBOSE NetLogging ║ host:test0927-starry.czur.cc
25_06_13 09:17:56.136 VERBOSE NetLogging ║ path:/api/starry/register/v2/active
25_06_13 09:17:56.136 VERBOSE NetLogging ║ -----
25_06_13 09:17:56.136 VERBOSE NetLogging ║ 请求头:
25_06_13 09:17:56.136 VERBOSE NetLogging ║ UDID: CBP02B2503000001
25_06_13 09:17:56.136 VERBOSE NetLogging ║ App-Key: com.czur.starry
25_06_13 09:17:56.136 VERBOSE NetLogging ║ Request-Timestamp: 1749777476127
25_06_13 09:17:56.136 VERBOSE NetLogging ║ 
25_06_13 09:17:56.136 VERBOSE NetLogging ║ url参数:
25_06_13 09:17:56.136 VERBOSE NetLogging ║ sn: CBP02B2503000001
25_06_13 09:17:56.136 VERBOSE NetLogging ║ secret: 1qaz2wsx3edc4rfv
25_06_13 09:17:56.136 VERBOSE NetLogging ║ voucher: C3190FB7E60F4F9689EA083680E3DCB7
25_06_13 09:17:56.136 VERBOSE NetLogging ║ mac: 9a:8e:5c:0b:11:26
25_06_13 09:17:56.136 VERBOSE NetLogging ║ udid: CBP02B2503000001
25_06_13 09:17:56.136 VERBOSE NetLogging ║ noncestr: 3030667814391850
25_06_13 09:17:56.136 VERBOSE NetLogging ║ timestamp: 1749777476127
25_06_13 09:17:56.136 VERBOSE NetLogging ║ sign: 46a9ad5e4d03a6806feb512617f6c83e0f8a2fa5
25_06_13 09:17:56.136 VERBOSE NetLogging ╚══════════════════════════════
25_06_13 09:17:56.141 ERROR NetLogging 请求出错 path->/api/starry/register/v2/active
25_06_13 09:17:56.143 ERROR SyncHttpTask 请求出错:
25_06_13 09:17:56.144 ERROR NetResultUtil 网络错误
25_06_13 09:17:56.145 VERBOSE NetResultUtil 没有网络连接
25_06_13 09:17:56.146 VERBOSE OtherMeetingQuickBootAppManager 下一次自动刷新时间:120s 后
25_06_13 09:19:56.145 VERBOSE OtherMeetingQuickBootAppManager 刷新一次快速启动App
25_06_13 09:19:56.147 VERBOSE NetAppDoubleCache 网络app缓存过期/缓存不存在
25_06_13 09:19:56.155 VERBOSE ActiveInterceptor 需要激活:/api/app/store/app/list
25_06_13 09:19:56.158 VERBOSE CommonParamInterceptor Token为空
25_06_13 09:19:56.163 VERBOSE NetLogging 请求信息
25_06_13 09:19:56.163 VERBOSE NetLogging ╔══════════════════════════════
25_06_13 09:19:56.163 VERBOSE NetLogging ║ method:GET
25_06_13 09:19:56.163 VERBOSE NetLogging ║ host:test0927-starry.czur.cc
25_06_13 09:19:56.163 VERBOSE NetLogging ║ path:/api/starry/register/v2/active
25_06_13 09:19:56.163 VERBOSE NetLogging ║ -----
25_06_13 09:19:56.163 VERBOSE NetLogging ║ 请求头:
25_06_13 09:19:56.163 VERBOSE NetLogging ║ UDID: CBP02B2503000001
25_06_13 09:19:56.163 VERBOSE NetLogging ║ App-Key: com.czur.starry
25_06_13 09:19:56.163 VERBOSE NetLogging ║ Request-Timestamp: 1749777596157
25_06_13 09:19:56.163 VERBOSE NetLogging ║ 
25_06_13 09:19:56.163 VERBOSE NetLogging ║ url参数:
25_06_13 09:19:56.163 VERBOSE NetLogging ║ sn: CBP02B2503000001
25_06_13 09:19:56.163 VERBOSE NetLogging ║ secret: 1qaz2wsx3edc4rfv
25_06_13 09:19:56.163 VERBOSE NetLogging ║ voucher: C3190FB7E60F4F9689EA083680E3DCB7
25_06_13 09:19:56.163 VERBOSE NetLogging ║ mac: 9a:8e:5c:0b:11:26
25_06_13 09:19:56.163 VERBOSE NetLogging ║ udid: CBP02B2503000001
25_06_13 09:19:56.163 VERBOSE NetLogging ║ noncestr: 3030667814391850
25_06_13 09:19:56.163 VERBOSE NetLogging ║ timestamp: 1749777596158
25_06_13 09:19:56.163 VERBOSE NetLogging ║ sign: d086b4b3c738eaf575bf74ffebba17e6a44b11f0
25_06_13 09:19:56.163 VERBOSE NetLogging ╚══════════════════════════════
25_06_13 09:19:56.167 ERROR NetLogging 请求出错 path->/api/starry/register/v2/active
25_06_13 09:19:56.169 ERROR SyncHttpTask 请求出错:
25_06_13 09:19:56.171 ERROR NetResultUtil 网络错误
25_06_13 09:19:56.172 VERBOSE NetResultUtil 没有网络连接
25_06_13 09:19:56.174 VERBOSE OtherMeetingQuickBootAppManager 下一次自动刷新时间:120s 后
25_06_13 09:21:56.171 VERBOSE OtherMeetingQuickBootAppManager 刷新一次快速启动App
25_06_13 09:21:56.177 VERBOSE NetAppDoubleCache 网络app缓存过期/缓存不存在
25_06_13 09:21:56.213 VERBOSE ActiveInterceptor 需要激活:/api/app/store/app/list
25_06_13 09:21:56.220 VERBOSE CommonParamInterceptor Token为空
25_06_13 09:21:56.229 VERBOSE NetLogging 请求信息
25_06_13 09:21:56.229 VERBOSE NetLogging ╔══════════════════════════════
25_06_13 09:21:56.229 VERBOSE NetLogging ║ method:GET
25_06_13 09:21:56.229 VERBOSE NetLogging ║ host:test0927-starry.czur.cc
25_06_13 09:21:56.229 VERBOSE NetLogging ║ path:/api/starry/register/v2/active
25_06_13 09:21:56.229 VERBOSE NetLogging ║ -----
25_06_13 09:21:56.229 VERBOSE NetLogging ║ 请求头:
25_06_13 09:21:56.229 VERBOSE NetLogging ║ UDID: CBP02B2503000001
25_06_13 09:21:56.229 VERBOSE NetLogging ║ App-Key: com.czur.starry
25_06_13 09:21:56.229 VERBOSE NetLogging ║ Request-Timestamp: 1749777716220
25_06_13 09:21:56.229 VERBOSE NetLogging ║ 
25_06_13 09:21:56.229 VERBOSE NetLogging ║ url参数:
25_06_13 09:21:56.229 VERBOSE NetLogging ║ sn: CBP02B2503000001
25_06_13 09:21:56.229 VERBOSE NetLogging ║ secret: 1qaz2wsx3edc4rfv
25_06_13 09:21:56.229 VERBOSE NetLogging ║ voucher: C3190FB7E60F4F9689EA083680E3DCB7
25_06_13 09:21:56.229 VERBOSE NetLogging ║ mac: 9a:8e:5c:0b:11:26
25_06_13 09:21:56.229 VERBOSE NetLogging ║ udid: CBP02B2503000001
25_06_13 09:21:56.229 VERBOSE NetLogging ║ noncestr: 3030667814391850
25_06_13 09:21:56.229 VERBOSE NetLogging ║ timestamp: 1749777716224
25_06_13 09:21:56.229 VERBOSE NetLogging ║ sign: 392e9f0111ca829442f2b3dce5d66d0e98b0833a
25_06_13 09:21:56.229 VERBOSE NetLogging ╚══════════════════════════════
25_06_13 09:21:56.233 ERROR NetLogging 请求出错 path->/api/starry/register/v2/active
25_06_13 09:21:56.235 ERROR SyncHttpTask 请求出错:
25_06_13 09:21:56.237 ERROR NetResultUtil 网络错误
25_06_13 09:21:56.238 VERBOSE NetResultUtil 没有网络连接
25_06_13 09:21:56.239 VERBOSE OtherMeetingQuickBootAppManager 下一次自动刷新时间:120s 后
25_06_13 09:23:56.239 VERBOSE OtherMeetingQuickBootAppManager 刷新一次快速启动App
25_06_13 09:23:56.242 VERBOSE NetAppDoubleCache 网络app缓存过期/缓存不存在
25_06_13 09:23:56.263 VERBOSE ActiveInterceptor 需要激活:/api/app/store/app/list
25_06_13 09:23:56.264 VERBOSE CommonParamInterceptor Token为空
25_06_13 09:23:56.277 VERBOSE NetLogging 请求信息
25_06_13 09:23:56.277 VERBOSE NetLogging ╔══════════════════════════════
25_06_13 09:23:56.277 VERBOSE NetLogging ║ method:GET
25_06_13 09:23:56.277 VERBOSE NetLogging ║ host:test0927-starry.czur.cc
25_06_13 09:23:56.277 VERBOSE NetLogging ║ path:/api/starry/register/v2/active
25_06_13 09:23:56.277 VERBOSE NetLogging ║ -----
25_06_13 09:23:56.277 VERBOSE NetLogging ║ 请求头:
25_06_13 09:23:56.277 VERBOSE NetLogging ║ UDID: CBP02B2503000001
25_06_13 09:23:56.277 VERBOSE NetLogging ║ App-Key: com.czur.starry
25_06_13 09:23:56.277 VERBOSE NetLogging ║ Request-Timestamp: 1749777836264
25_06_13 09:23:56.277 VERBOSE NetLogging ║ 
25_06_13 09:23:56.277 VERBOSE NetLogging ║ url参数:
25_06_13 09:23:56.277 VERBOSE NetLogging ║ sn: CBP02B2503000001
25_06_13 09:23:56.277 VERBOSE NetLogging ║ secret: 1qaz2wsx3edc4rfv
25_06_13 09:23:56.277 VERBOSE NetLogging ║ voucher: C3190FB7E60F4F9689EA083680E3DCB7
25_06_13 09:23:56.277 VERBOSE NetLogging ║ mac: 9a:8e:5c:0b:11:26
25_06_13 09:23:56.277 VERBOSE NetLogging ║ udid: CBP02B2503000001
25_06_13 09:23:56.277 VERBOSE NetLogging ║ noncestr: 3030667814391850
25_06_13 09:23:56.277 VERBOSE NetLogging ║ timestamp: 1749777836264
25_06_13 09:23:56.277 VERBOSE NetLogging ║ sign: de1243729222771018d28eb7a65af73199bed2b0
25_06_13 09:23:56.277 VERBOSE NetLogging ╚══════════════════════════════
25_06_13 09:23:56.283 ERROR NetLogging 请求出错 path->/api/starry/register/v2/active
25_06_13 09:23:56.284 ERROR SyncHttpTask 请求出错:
25_06_13 09:23:56.286 ERROR NetResultUtil 网络错误
25_06_13 09:23:56.287 VERBOSE NetResultUtil 没有网络连接
25_06_13 09:23:56.289 VERBOSE OtherMeetingQuickBootAppManager 下一次自动刷新时间:120s 后
25_06_13 09:25:56.288 VERBOSE OtherMeetingQuickBootAppManager 刷新一次快速启动App
25_06_13 09:25:56.292 VERBOSE NetAppDoubleCache 网络app缓存过期/缓存不存在
25_06_13 09:25:56.300 VERBOSE ActiveInterceptor 需要激活:/api/app/store/app/list
25_06_13 09:25:56.306 VERBOSE CommonParamInterceptor Token为空
25_06_13 09:25:56.308 VERBOSE NetLogging 请求信息
25_06_13 09:25:56.308 VERBOSE NetLogging ╔══════════════════════════════
25_06_13 09:25:56.308 VERBOSE NetLogging ║ method:GET
25_06_13 09:25:56.308 VERBOSE NetLogging ║ host:test0927-starry.czur.cc
25_06_13 09:25:56.308 VERBOSE NetLogging ║ path:/api/starry/register/v2/active
25_06_13 09:25:56.308 VERBOSE NetLogging ║ -----
25_06_13 09:25:56.308 VERBOSE NetLogging ║ 请求头:
25_06_13 09:25:56.308 VERBOSE NetLogging ║ UDID: CBP02B2503000001
25_06_13 09:25:56.308 VERBOSE NetLogging ║ App-Key: com.czur.starry
25_06_13 09:25:56.308 VERBOSE NetLogging ║ Request-Timestamp: 1749777956303
25_06_13 09:25:56.308 VERBOSE NetLogging ║ 
25_06_13 09:25:56.308 VERBOSE NetLogging ║ url参数:
25_06_13 09:25:56.308 VERBOSE NetLogging ║ sn: CBP02B2503000001
25_06_13 09:25:56.308 VERBOSE NetLogging ║ secret: 1qaz2wsx3edc4rfv
25_06_13 09:25:56.308 VERBOSE NetLogging ║ voucher: C3190FB7E60F4F9689EA083680E3DCB7
25_06_13 09:25:56.308 VERBOSE NetLogging ║ mac: 9a:8e:5c:0b:11:26
25_06_13 09:25:56.308 VERBOSE NetLogging ║ udid: CBP02B2503000001
25_06_13 09:25:56.308 VERBOSE NetLogging ║ noncestr: 3030667814391850
25_06_13 09:25:56.308 VERBOSE NetLogging ║ timestamp: 1749777956303
25_06_13 09:25:56.308 VERBOSE NetLogging ║ sign: bc324b8af5ed080c7c62e273a23d4d964587b5fd
25_06_13 09:25:56.308 VERBOSE NetLogging ╚══════════════════════════════
25_06_13 09:25:56.313 ERROR NetLogging 请求出错 path->/api/starry/register/v2/active
25_06_13 09:25:56.314 ERROR SyncHttpTask 请求出错:
25_06_13 09:25:56.316 ERROR NetResultUtil 网络错误
25_06_13 09:25:56.317 VERBOSE NetResultUtil 没有网络连接
25_06_13 09:25:56.318 VERBOSE OtherMeetingQuickBootAppManager 下一次自动刷新时间:120s 后
25_06_13 09:28:48.347 DEBUG CZURAtyManager com.czur.starry.device.launcher.pages.view.launcher.LauncherActivity没有继承BaseActivity,不进行管理
25_06_13 09:28:48.421 VERBOSE WelcomeActivity 直接启动主画面
25_06_13 09:28:48.423 DEBUG WelcomeActivity 设置完毕, 发送广播
25_06_13 09:28:48.464 DEBUG WelcomeActivity completeUserSetupForSystem
25_06_13 09:28:48.465 DEBUG WelcomeActivity 启动小圆屏
25_06_13 09:28:48.474 DEBUG WelcomeActivity 找到了尺寸为 360x360 的屏幕
25_06_13 09:28:48.557 VERBOSE CZURAtyManager 添加:LauncherMainActivity(onCreate)
25_06_13 09:28:49.237 VERBOSE LauncherActivity 更新壁纸
25_06_13 09:28:49.298 DEBUG AppInfoViewModel 注册App安装广播
25_06_13 09:28:49.307 VERBOSE OtherQuickBootAppManager 加载tag:VideoMeeting
25_06_13 09:28:49.310 VERBOSE OtherMeetingQuickBootAppManager 下一次自动刷新时间:120s 后
25_06_13 09:28:49.311 INFO AppInfoViewModel 直接绑定AppStore Service
25_06_13 09:28:49.312 DEBUG AppInfoViewModel 绑定应用市场Service
25_06_13 09:28:49.516 DEBUG NoticeHandler 注册回调
25_06_13 09:28:49.517 DEBUG NoticeHandler 消息类型:type=( NOTIFY_MSG -[ 最新一条消息 ])第一次注册
25_06_13 09:28:49.519 DEBUG NoticeHandler 该owner没有之前添加过监听
25_06_13 09:28:49.531 DEBUG ShareViewModel peripheralUSBRunningFlow = false
25_06_13 09:28:49.532 VERBOSE ShareViewModel refreshGadgetState
25_06_13 09:28:49.563 DEBUG NoticeHandler 注册回调
25_06_13 09:28:49.565 VERBOSE SystemManagerProxy getGadgetMode Start
25_06_13 09:28:49.566 DEBUG NoticeHandler 消息类型:type=( SYNC - [ 外设USB模式变化 ] )第一次注册
25_06_13 09:28:49.568 VERBOSE ShareViewModel peripheralModeRunningFlow byom = false, usb = false
25_06_13 09:28:49.582 DEBUG SystemManagerProxy isGadgetMode Value: 0
25_06_13 09:28:49.585 VERBOSE ShareViewModel USB外设模式 = USB_GADGET_MODE_OFF
25_06_13 09:28:49.655 VERBOSE ProviderUtil ContentValues: voice_assistant_is_support_scene=true  cv.size: 1
25_06_13 09:28:49.656 VERBOSE ProviderUtil Key: voice_assistant_is_support_scene, Value: true
25_06_13 09:28:49.657 VERBOSE ProviderUtil update uri: content://com.czur.starry.device.voiceassistant.provider.initProvider/getSPValue , contentValues Size: 1
25_06_13 09:28:49.705 VERBOSE ProviderUtil update result: 1
25_06_13 09:28:49.706 INFO VoiceAssistantHandler 更新成功, 更新缓存
25_06_13 09:28:49.730 INFO KeyCodeVM 读取导航栏状态:0
25_06_13 09:28:49.731 INFO CZBusinessAty page-start: LauncherMainActivity
25_06_13 09:28:49.750 DEBUG StatusBarFragment 刷新外设模式
25_06_13 09:28:49.751 VERBOSE ShareViewModel refreshGadgetState
25_06_13 09:28:49.752 VERBOSE SystemManagerProxy getGadgetMode Start
25_06_13 09:28:49.781 DEBUG SystemManagerProxy isGadgetMode Value: 0
25_06_13 09:28:49.816 VERBOSE ShareViewModel USB外设模式 = USB_GADGET_MODE_OFF
25_06_13 09:28:49.835 VERBOSE ShareViewModel peripheralModeRunningFlow byom = false, usb = false
25_06_13 09:28:49.854 VERBOSE StatusBarFragment byomAndUsbRunning:PeripheralMode(byom=false, usb=false)
25_06_13 09:28:49.858 VERBOSE ShareViewModel peripheralModeRunningFlow byom = false, usb = false
25_06_13 09:28:49.865 VERBOSE ProviderUtil ContentValues: voice_assistant_is_support_scene=true  cv.size: 1
25_06_13 09:28:49.867 VERBOSE ProviderUtil Key: voice_assistant_is_support_scene, Value: true
25_06_13 09:28:49.875 VERBOSE ProviderUtil update uri: content://com.czur.starry.device.voiceassistant.provider.initProvider/getSPValue , contentValues Size: 1
25_06_13 09:28:49.878 VERBOSE ProviderUtil update result: 1
25_06_13 09:28:49.887 INFO VoiceAssistantHandler 更新成功, 更新缓存
25_06_13 09:28:49.889 DEBUG LauncherActivity Launcher启动完成
25_06_13 09:28:49.890 DEBUG LauncherActivity Launcher doRepeatResume
25_06_13 09:28:49.891 DEBUG LauncherActivity 发送LauncherResume广播
25_06_13 09:28:49.891 VERBOSE OtherMeetingQuickBootAppManager 下一次自动刷新时间:120s 后
25_06_13 09:28:49.892 DEBUG NotifyMsgHandler HasUnRead:false
25_06_13 09:28:49.893 VERBOSE EShareUtil checkAndActiveEShare
25_06_13 09:28:49.894 DEBUG EShareUtil 没有获取到激活码
25_06_13 09:28:50.078 VERBOSE LauncherMainFragment 显示引导UI
25_06_13 09:28:50.080 DEBUG AppInfoViewModel 加载收藏应用列表
25_06_13 09:28:50.148 DEBUG ThemeNetStatusIcon 状态:NO_NET_WORK
25_06_13 09:28:50.261 DEBUG NoticeHandler 添加监听,监控生命周期StatusBarFragment-StatusBarFragment{d0f6c29} (32f4282f-2c9e-4985-b4ad-8ba520026020 id=0x7f09019b tag=StatusBarFragment)
25_06_13 09:28:50.262 VERBOSE ShareViewModel peripheralModeRunningFlow byom = false, usb = false
25_06_13 09:28:50.263 VERBOSE ProviderUtil ContentValues: voice_assistant_is_support_scene=true  cv.size: 1
25_06_13 09:28:50.264 VERBOSE ProviderUtil Key: voice_assistant_is_support_scene, Value: true
25_06_13 09:28:50.265 VERBOSE ProviderUtil update uri: content://com.czur.starry.device.voiceassistant.provider.initProvider/getSPValue , contentValues Size: 1
25_06_13 09:28:50.308 VERBOSE ProviderUtil update result: 1
25_06_13 09:28:50.309 INFO VoiceAssistantHandler 更新成功, 更新缓存
25_06_13 09:28:50.310 VERBOSE ShareViewModel peripheralModeRunningFlow byom = false, usb = false
25_06_13 09:28:50.310 VERBOSE ProviderUtil ContentValues: voice_assistant_is_support_scene=true  cv.size: 1
25_06_13 09:28:50.312 VERBOSE ProviderUtil Key: voice_assistant_is_support_scene, Value: true
25_06_13 09:28:50.313 VERBOSE ProviderUtil update uri: content://com.czur.starry.device.voiceassistant.provider.initProvider/getSPValue , contentValues Size: 1
25_06_13 09:28:50.327 VERBOSE ProviderUtil update result: 1
25_06_13 09:28:50.328 INFO VoiceAssistantHandler 更新成功, 更新缓存
25_06_13 09:28:50.332 INFO StatusBarFragment AI互译字幕开启状态:false
25_06_13 09:28:50.339 DEBUG EShareUtil showDeviceNameAlertWindow:没有启用设备名称弹窗
25_06_13 09:28:50.340 DEBUG LauncherActivity ==enableEShareVoice===
25_06_13 09:28:50.341 DEBUG EShareUtil enableEShareVoice:true
25_06_13 09:28:50.342 DEBUG ShareViewModel Launcher启动, 打开EShare
25_06_13 09:28:50.354 INFO KeyCodeVM 读取引导次数:0
25_06_13 09:28:50.373 VERBOSE StatusBarFragment hasUnReadMsg:false
25_06_13 09:28:50.376 DEBUG NotifyMsgHandler 注册未读消息监听
25_06_13 09:28:50.378 WARN BackdropManager currentRegionDecoder为空
25_06_13 09:28:50.380 WARN BackdropManager 正在准备模糊图片, 请稍后
25_06_13 09:28:50.383 WARN BackdropManager 正在准备模糊图片, 请稍后
25_06_13 09:28:50.385 WARN BackdropManager 正在准备模糊图片, 请稍后
25_06_13 09:28:50.386 WARN BackdropManager 正在准备模糊图片, 请稍后
25_06_13 09:28:50.392 WARN BackdropManager 正在准备模糊图片, 请稍后
25_06_13 09:28:50.393 WARN BackdropManager 正在准备模糊图片, 请稍后
25_06_13 09:28:50.398 VERBOSE QuickBootApp 新校验码
25_06_13 09:28:50.400 VERBOSE QuickBootApp 新文件名字 = null
25_06_13 09:28:50.402 VERBOSE QuickBootApp 新校验码
25_06_13 09:28:50.403 VERBOSE QuickBootApp 新校验码
25_06_13 09:28:50.405 VERBOSE QuickBootApp ota新版本:false , 触控板新版本:false
25_06_13 09:28:50.408 VERBOSE QuickBootApp ota新版本:false , 触控板新版本:false
25_06_13 09:28:50.409 VERBOSE QuickBootApp ota新版本:false , 触控板新版本:false
25_06_13 09:28:50.421 VERBOSE LauncherActivity 防止自动对焦服务挂掉
25_06_13 09:28:50.425 VERBOSE NetAppDoubleCache 网络app缓存过期/缓存不存在
25_06_13 09:28:50.496 VERBOSE ActiveInterceptor 需要激活:/api/app/store/app/list
25_06_13 09:28:50.498 WARN SystemManagerProxy onSystemChangeListener is null,add default listener
25_06_13 09:28:50.511 DEBUG BackdropManager 主导色分析: RGB(40,72,192), 亮度=73.86080000000001
25_06_13 09:28:50.512 DEBUG BackdropManager 背景颜色分析结果: Dark, 选中样本颜色: #2848C0
25_06_13 09:28:50.514 DEBUG BackdropManager 背景颜色分析结果: Dark
25_06_13 09:28:50.547 VERBOSE CommonParamInterceptor Token为空
25_06_13 09:28:50.560 VERBOSE NetLogging 请求信息
25_06_13 09:28:50.560 VERBOSE NetLogging ╔══════════════════════════════
25_06_13 09:28:50.560 VERBOSE NetLogging ║ method:GET
25_06_13 09:28:50.560 VERBOSE NetLogging ║ host:test0927-starry.czur.cc
25_06_13 09:28:50.560 VERBOSE NetLogging ║ path:/api/starry/register/v2/active
25_06_13 09:28:50.560 VERBOSE NetLogging ║ -----
25_06_13 09:28:50.560 VERBOSE NetLogging ║ 请求头:
25_06_13 09:28:50.560 VERBOSE NetLogging ║ UDID: CBP02B2503000001
25_06_13 09:28:50.560 VERBOSE NetLogging ║ App-Key: com.czur.starry
25_06_13 09:28:50.560 VERBOSE NetLogging ║ Request-Timestamp: 1749778130532
25_06_13 09:28:50.560 VERBOSE NetLogging ║ 
25_06_13 09:28:50.560 VERBOSE NetLogging ║ url参数:
25_06_13 09:28:50.560 VERBOSE NetLogging ║ sn: CBP02B2503000001
25_06_13 09:28:50.560 VERBOSE NetLogging ║ secret: 1qaz2wsx3edc4rfv
25_06_13 09:28:50.560 VERBOSE NetLogging ║ voucher: C3190FB7E60F4F9689EA083680E3DCB7
25_06_13 09:28:50.560 VERBOSE NetLogging ║ mac: 9a:8e:5c:0b:11:26
25_06_13 09:28:50.560 VERBOSE NetLogging ║ udid: CBP02B2503000001
25_06_13 09:28:50.560 VERBOSE NetLogging ║ noncestr: 3030667814391850
25_06_13 09:28:50.560 VERBOSE NetLogging ║ timestamp: 1749778130541
25_06_13 09:28:50.560 VERBOSE NetLogging ║ sign: 85e6be2e2888dd926e1e297b430e269b5ca1dfb2
25_06_13 09:28:50.560 VERBOSE NetLogging ╚══════════════════════════════
25_06_13 09:28:50.653 WARN BackdropManager 正在准备模糊图片, 请稍后
25_06_13 09:28:50.654 WARN BackdropManager 正在准备模糊图片, 请稍后
25_06_13 09:28:50.655 WARN BackdropManager 正在准备模糊图片, 请稍后
25_06_13 09:28:50.656 WARN BackdropManager 正在准备模糊图片, 请稍后
25_06_13 09:28:50.656 WARN BackdropManager 正在准备模糊图片, 请稍后
25_06_13 09:28:50.657 WARN BackdropManager 正在准备模糊图片, 请稍后
25_06_13 09:28:50.668 ERROR NetLogging 请求出错 path->/api/starry/register/v2/active
25_06_13 09:28:50.718 ERROR SyncHttpTask 请求出错:
25_06_13 09:28:50.719 ERROR NetResultUtil 网络错误
25_06_13 09:28:50.720 VERBOSE NetResultUtil 没有网络连接
25_06_13 09:28:50.722 VERBOSE CZURAtyManager remove:LauncherActivity(onDestroy)
25_06_13 09:28:50.725 VERBOSE OtherMeetingQuickBootAppManager 下一次自动刷新时间:120s 后
25_06_13 09:28:50.727 VERBOSE QuickBootApp StarryPad新文件名字 = 
25_06_13 09:28:50.803 WARN BackdropManager 正在准备模糊图片, 请稍后
25_06_13 09:28:50.806 WARN BackdropManager 正在准备模糊图片, 请稍后
25_06_13 09:28:50.807 WARN BackdropManager 正在准备模糊图片, 请稍后
25_06_13 09:28:50.808 WARN BackdropManager 正在准备模糊图片, 请稍后
25_06_13 09:28:50.810 WARN BackdropManager 正在准备模糊图片, 请稍后
25_06_13 09:28:50.811 WARN BackdropManager 正在准备模糊图片, 请稍后
25_06_13 09:28:50.854 DEBUG LauncherActivity 发送Launcher启动消息
25_06_13 09:28:50.856 DEBUG LauncherActivity 发送Launcher启动消息
25_06_13 09:28:50.858 DEBUG NoticeHandler sendMessage: type:type=( MODULE_BOOT -[ Launcher ] )
25_06_13 09:28:50.948 DEBUG LauncherActivity 壁纸发生变化了,更新壁纸
25_06_13 09:28:50.956 VERBOSE LauncherActivity 更新壁纸
25_06_13 09:28:50.961 DEBUG LeftTimeFragment 壁纸发生变化了,重新获取模糊图
25_06_13 09:28:50.962 DEBUG ToolSheetFragment 壁纸发生变化了,重新获取模糊图
25_06_13 09:28:51.056 DEBUG CZNotificationManager clearAllPkgNotification: com.czur.starry.device.noticecenter
25_06_13 09:28:51.294 DEBUG ShareViewModel eShareCallback key = eshare_register_info
25_06_13 09:28:51.295 DEBUG ShareViewModel eShareCallback key = eshare_register_info
25_06_13 09:28:51.511 DEBUG CZNotificationManager clearAllPkgNotification: com.czur.starry.device.noticecenter
25_06_13 09:28:51.882 DEBUG ShareViewModel eShareCallback key = eshare_byom_running
25_06_13 09:28:51.884 DEBUG ShareViewModel eShareCallback isByomrunning = false
25_06_13 09:28:52.240 DEBUG ShareViewModel eShareCallback key = eshare_qrcode_info
25_06_13 09:28:52.242 DEBUG ShareViewModel eShareCallback key = eshare_qrcode_info
25_06_13 09:28:52.249 DEBUG ShareViewModel eShareCallback key = eshare_network_info
25_06_13 09:28:52.250 DEBUG ShareViewModel eShareCallback key = eshare_network_info
25_06_13 09:29:10.909 DEBUG ComplicatedQuickBootFragment 启动QuickBoot模块:设置
25_06_13 09:29:11.806 INFO CZBusinessAty page-stop: LauncherMainActivity
25_06_13 09:29:11.827 DEBUG EShareUtil showDeviceNameAlertWindow:没有启用设备名称弹窗
