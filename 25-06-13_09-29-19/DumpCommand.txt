
=========COMMAND START (cpuInfo)=========
Load: 2.28 / 3.77 / 3.03
CPU usage from 37274ms to 30933ms ago (2025-06-13 09:28:41.610 to 2025-06-13 09:28:47.952):
  92% 4276/system_server: 69% user + 22% kernel / faults: 29042 minor
  34% 551/surfaceflinger: 24% user + 9.7% kernel / faults: 399 minor
  16% 4205/zygote: 13% user + 2.1% kernel / faults: 13495 minor
  11% 508/android.hardware.graphics.composer3-service.rockchip: 6.4% user + 5% kernel / faults: 34 minor
  11% 4161/bootanimation: 7.6% user + 3.7% kernel / faults: 5555 minor
  4.4% 639/media.swcodec: 2.9% user + 1.4% kernel / faults: 387 minor
  4.4% 276/servicemanager: 2.2% user + 2.2% kernel / faults: 9 minor
  3.6% 292/vold: 1.7% user + 1.8% kernel / faults: 124 minor
  3.6% 274/logd: 1.8% user + 1.7% kernel / faults: 557 minor
  2.8% 506/android.hardware.wifi-service: 0% user + 2.8% kernel / faults: 14 minor
  2.4% 4206/zygote64: 0% user + 2.4% kernel / faults: 767 minor
  2% 1/init: 0.7% user + 1.2% kernel / faults: 744 minor
  1.7% 465/statsd: 0.6% user + 1% kernel / faults: 140 minor
  1.6% 205/ueventd: 1.2% user + 0.4% kernel
  1.4% 4227/android.hardware.audio.service: 0.6% user + 0.8% kernel / faults: 5414 minor
  1.4% 600/installd: 0.7% user + 0.6% kernel
  0.9% 318/android.system.suspend-service: 0.2% user + 0.6% kernel / faults: 4 minor
  0.9% 4209/mediaserver: 0.4% user + 0.5% kernel / faults: 147 minor
  0.8% 478/android.hardware.bluetooth@1.0-service: 0.1% user + 0.6% kernel / faults: 45 minor
  0.8% 601/media.extractor: 0.6% user + 0.1% kernel / faults: 22 minor
  0.6% 347/dhd_dpc_sdio: 0% user + 0.6% kernel
  0.6% 485/android.hardware.media.c2@1.1-service: 0.5% user + 0.1% kernel / faults: 22 minor
  0.7% 3892/kworker/u17:2-kbase_pm_poweroff_wait: 0% user + 0.7% kernel
  0.6% 4210/netd: 0.2% user + 0.4% kernel / faults: 26 minor
  0.5% 75/kconsole: 0% user + 0.5% kernel
  0.6% 174/kworker/u17:1-kbase_pm_poweroff_wait: 0% user + 0.6% kernel
  0.5% 277/hwservicemanager: 0.4% user + 0.1% kernel / faults: 2 minor
  0.4% 84/kworker/u16:3-ext4-rsv-conversion: 0% user + 0.4% kernel
  0.4% 609/media.metrics: 0.2% user + 0.1% kernel / faults: 241 minor
  0.2% 15/rcuog/0: 0% user + 0.2% kernel
  0.2% 53/rcuop/5: 0% user + 0.2% kernel
  0.3% 80/kworker/u16:2-events_unbound: 0% user + 0.3% kernel
  0.2% 314/jbd2/mmcblk0p15-8: 0% user + 0.2% kernel
  0.2% 483/android.hardware.graphics.allocator-V1-service: 0% user + 0.2% kernel / faults: 357 minor
  0.2% 4211/wificond: 0.1% user + 0.1% kernel / faults: 38 minor
  0.1% 14/rcu_preempt: 0% user + 0.1% kernel
  0.1% 16/rcuop/0: 0% user + 0.1% kernel
  0.1% 18/rcu_exp_gp_kthread_worker: 0% user + 0.1% kernel
  0.1% 24/migration/1: 0% user + 0.1% kernel
  0.1% 28/rcuop/1: 0% user + 0.1% kernel
  0.1% 30/migration/2: 0% user + 0.1% kernel
  0.1% 34/rcuop/2: 0% user + 0.1% kernel
  0.1% 36/migration/3: 0% user + 0.1% kernel
  0.1% 40/rcuop/3: 0% user + 0.1% kernel
  0.1% 46/rcuog/4: 0% user + 0.1% kernel
  0.1% 47/rcuop/4: 0% user + 0.1% kernel
  0.1% 59/rcuop/6: 0% user + 0.1% kernel
  0.1% 65/rcuop/7: 0% user + 0.1% kernel
  0.1% 108/kworker/4:2-events: 0% user + 0.1% kernel
  0.1% 197/kworker/5:1H-kblockd: 0% user + 0.1% kernel
  0.1% 275/lmkd: 0% user + 0.1% kernel
  0.1% 319/keystore2: 0.1% user + 0% kernel / faults: 164 minor
  0.1% 494/android.hardware.tv.hdmi.cec-service: 0% user + 0.1% kernel
  0.1% 501/android.hardware.tv.hdmi.connection-service: 0% user + 0.1% kernel
  0.1% 503/android.hardware.usb-service.rockchip: 0.1% user + 0% kernel / faults: 6 minor
  0.1% 504/android.hardware.usb.gadget-service.rockchip: 0% user + 0.1% kernel / faults: 4 minor
  0.1% 509/android.hardware.lights-service.rockchip: 0.1% user + 0% kernel
  0.1% 510/android.hardware.power-service.rockchip: 0% user + 0.1% kernel / faults: 1 minor
  0.1% 512/rockchip.hardware.outputmanager@1.0-service: 0% user + 0.1% kernel / faults: 9 minor
  0.1% 524/android.hardware.sensors-service.rockchip: 0% user + 0.1% kernel / faults: 40 minor
  0.1% 4340/idmap2d: 0% user + 0.1% kernel / faults: 19 minor
 +0% 4380/audioserver: 0% user + 0% kernel
 +0% 4475/kworker/3:0H: 0% user + 0% kernel
 +0% 4490/com.android.systemui: 0% user + 0% kernel
 +0% 4595/webview_zygote: 0% user + 0% kernel
 +0% 4606/com.android.networkstack.process: 0% user + 0% kernel
 +0% 4623/kworker/2:1H-kblockd: 0% user + 0% kernel
 +0% 4630/com.android.bluetooth: 0% user + 0% kernel
 +0% 4645/com.android.se: 0% user + 0% kernel
 +0% 4661/com.android.phone: 0% user + 0% kernel
 +0% 4666/com.android.settings: 0% user + 0% kernel
 +0% 4702/android.ext.services: 0% user + 0% kernel
 +0% 4768/kbase_event: 0% user + 0% kernel
 +0% 4789/com.android.smspush: 0% user + 0% kernel
 +0% 4790/irq/102-bt_default_wake_host_irq: 0% user + 0% kernel
 +0% 4814/dhd_eventd: 0% user + 0% kernel
 +0% 4818/kworker/5:0H: 0% user + 0% kernel
 +0% 4823/wpa_supplicant: 0% user + 0% kernel
 +0% 4855/hostapd: 0% user + 0% kernel
 +0% 4858/kworker/4:0-sock_diag_events: 0% user + 0% kernel
 +0% 4861/kbase_event: 0% user + 0% kernel
 +0% 4883/apexd: 0% user + 0% kernel
 +0% 4889/dhd_eventd: 0% user + 0% kernel
 +0% 4894/com.android.devicelockcontroller: 0% user + 0% kernel
 +0% 4901/kworker/7:0H: 0% user + 0% kernel
 +0% 4910/kworker/4:3-cgroup_destroy: 0% user + 0% kernel
 +0% 4916/dmesgd: 0% user + 0% kernel
 +0% 4923/dmesg: 0% user + 0% kernel
 +0% 4929/com.android.providers.media.module: 0% user + 0% kernel
 +0% 4944/com.czur.starry.device.launcher: 0% user + 0% kernel
 +0% 4949/irq/126-dwc3: 0% user + 0% kernel
 +0% 4957/com.czur.starry.device.memoryservice: 0% user + 0% kernel
 +0% 4988/com.czur.starry.device.smallroundscreen: 0% user + 0% kernel
 +0% 4995/kworker/7:1-events: 0% user + 0% kernel
40% TOTAL: 26% user + 12% kernel + 0.3% iowait + 1.3% irq + 0.1% softirq

=========COMMAND END (cpuInfo)=========
