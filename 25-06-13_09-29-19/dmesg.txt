
=========COMMAND START (dmesg)=========
[   11.091654] [  T287] type=1400 audit(1749776987.024:722): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_camera_native_prop:s0" dev="tmpfs" ino=141 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_camera_native_prop:s0 tclass=file permissive=1
[   11.091712] [  T287] type=1400 audit(1749776987.024:723): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_camera_native_prop:s0" dev="tmpfs" ino=141 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_camera_native_prop:s0 tclass=file permissive=1
[   11.091769] [  T287] type=1400 audit(1749776987.024:724): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_configuration_prop:s0" dev="tmpfs" ino=142 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_configuration_prop:s0 tclass=file permissive=1
[   11.091826] [  T287] type=1400 audit(1749776987.024:725): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_configuration_prop:s0" dev="tmpfs" ino=142 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_configuration_prop:s0 tclass=file permissive=1
[   11.091883] [  T287] type=1400 audit(1749776987.024:726): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_configuration_prop:s0" dev="tmpfs" ino=142 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_configuration_prop:s0 tclass=file permissive=1
[   11.091941] [  T287] type=1400 audit(1749776987.024:727): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_connectivity_prop:s0" dev="tmpfs" ino=143 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_connectivity_prop:s0 tclass=file permissive=1
[   11.091997] [  T287] type=1400 audit(1749776987.024:728): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_connectivity_prop:s0" dev="tmpfs" ino=143 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_connectivity_prop:s0 tclass=file permissive=1
[   11.092055] [  T287] type=1400 audit(1749776987.024:729): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_connectivity_prop:s0" dev="tmpfs" ino=143 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_connectivity_prop:s0 tclass=file permissive=1
[   11.092113] [  T287] type=1400 audit(1749776987.024:730): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_edgetpu_native_prop:s0" dev="tmpfs" ino=144 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_edgetpu_native_prop:s0 tclass=file permissive=1
[   11.092172] [  T287] type=1400 audit(1749776987.024:731): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_edgetpu_native_prop:s0" dev="tmpfs" ino=144 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_edgetpu_native_prop:s0 tclass=file permissive=1
[   11.092231] [  T287] type=1400 audit(1749776987.024:732): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_edgetpu_native_prop:s0" dev="tmpfs" ino=144 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_edgetpu_native_prop:s0 tclass=file permissive=1
[   11.092289] [  T287] type=1400 audit(1749776987.024:733): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_input_native_boot_prop:s0" dev="tmpfs" ino=145 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_input_native_boot_prop:s0 tclass=file permissive=1
[   11.092347] [  T287] type=1400 audit(1749776987.024:734): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_input_native_boot_prop:s0" dev="tmpfs" ino=145 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_input_native_boot_prop:s0 tclass=file permissive=1
[   11.092405] [  T287] type=1400 audit(1749776987.024:735): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_input_native_boot_prop:s0" dev="tmpfs" ino=145 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_input_native_boot_prop:s0 tclass=file permissive=1
[   11.092463] [  T287] type=1400 audit(1749776987.024:736): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_lmkd_native_prop:s0" dev="tmpfs" ino=146 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_lmkd_native_prop:s0 tclass=file permissive=1
[   11.092520] [  T287] type=1400 audit(1749776987.024:737): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_lmkd_native_prop:s0" dev="tmpfs" ino=146 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_lmkd_native_prop:s0 tclass=file permissive=1
[   11.092578] [  T287] type=1400 audit(1749776987.024:738): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_lmkd_native_prop:s0" dev="tmpfs" ino=146 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_lmkd_native_prop:s0 tclass=file permissive=1
[   11.092635] [  T287] type=1400 audit(1749776987.024:739): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_mglru_native_prop:s0" dev="tmpfs" ino=150 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_mglru_native_prop:s0 tclass=file permissive=1
[   11.092693] [  T287] type=1400 audit(1749776987.024:740): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_mglru_native_prop:s0" dev="tmpfs" ino=150 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_mglru_native_prop:s0 tclass=file permissive=1
[   11.092751] [  T287] type=1400 audit(1749776987.024:741): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_mglru_native_prop:s0" dev="tmpfs" ino=150 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_mglru_native_prop:s0 tclass=file permissive=1
[   11.092808] [  T287] type=1400 audit(1749776987.024:742): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_netd_native_prop:s0" dev="tmpfs" ino=151 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_netd_native_prop:s0 tclass=file permissive=1
[   11.092866] [  T287] type=1400 audit(1749776987.024:743): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_netd_native_prop:s0" dev="tmpfs" ino=151 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_netd_native_prop:s0 tclass=file permissive=1
[   11.092923] [  T287] type=1400 audit(1749776987.024:744): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_netd_native_prop:s0" dev="tmpfs" ino=151 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_netd_native_prop:s0 tclass=file permissive=1
[   11.093007] [  T287] type=1400 audit(1749776987.024:745): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_nnapi_native_prop:s0" dev="tmpfs" ino=152 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_nnapi_native_prop:s0 tclass=file permissive=1
[   11.093096] [  T287] type=1400 audit(1749776987.024:746): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_nnapi_native_prop:s0" dev="tmpfs" ino=152 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_nnapi_native_prop:s0 tclass=file permissive=1
[   11.093156] [  T287] type=1400 audit(1749776987.024:747): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_nnapi_native_prop:s0" dev="tmpfs" ino=152 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_nnapi_native_prop:s0 tclass=file permissive=1
[   11.093214] [  T287] type=1400 audit(1749776987.024:748): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_profcollect_native_boot_prop:s0" dev="tmpfs" ino=153 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_profcollect_native_boot_prop:s0 tclass=file permissive=1
[   11.093272] [  T287] type=1400 audit(1749776987.024:749): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_profcollect_native_boot_prop:s0" dev="tmpfs" ino=153 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_profcollect_native_boot_prop:s0 tclass=file permissive=1
[   11.093330] [  T287] type=1400 audit(1749776987.024:750): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_profcollect_native_boot_prop:s0" dev="tmpfs" ino=153 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_profcollect_native_boot_prop:s0 tclass=file permissive=1
[   11.093388] [  T287] type=1400 audit(1749776987.024:751): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_remote_key_provisioning_native_prop:s0" dev="tmpfs" ino=154 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_remote_key_provisioning_native_prop:s0 tclass=file permissive=1
[   11.093447] [  T287] type=1400 audit(1749776987.024:752): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_remote_key_provisioning_native_prop:s0" dev="tmpfs" ino=154 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_remote_key_provisioning_native_prop:s0 tclass=file permissive=1
[   11.093505] [  T287] type=1400 audit(1749776987.024:753): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_remote_key_provisioning_native_prop:s0" dev="tmpfs" ino=154 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_remote_key_provisioning_native_prop:s0 tclass=file permissive=1
[   11.093563] [  T287] type=1400 audit(1749776987.028:754): avc:  denied  { read } for  comm="odrefresh" name="u:object_r:device_config_reset_performed_prop:s0" dev="tmpfs" ino=155 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_reset_performed_prop:s0 tclass=file permissive=1
[   11.093621] [  T287] type=1400 audit(1749776987.028:755): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_reset_performed_prop:s0" dev="tmpfs" ino=155 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_reset_performed_prop:s0 tclass=file permissive=1
[   11.093679] [  T287] type=1400 audit(1749776987.028:756): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_reset_performed_prop:s0" dev="tmpfs" ino=155 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_reset_performed_prop:s0 tclass=file permissive=1
[   11.093736] [  T287] type=1400 audit(1749776987.028:757): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_reset_performed_prop:s0" dev="tmpfs" ino=155 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_reset_performed_prop:s0 tclass=file permissive=1
[   11.093794] [  T287] type=1400 audit(1749776987.028:758): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_statsd_native_boot_prop:s0" dev="tmpfs" ino=158 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_statsd_native_boot_prop:s0 tclass=file permissive=1
[   11.093852] [  T287] type=1400 audit(1749776987.028:759): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_statsd_native_boot_prop:s0" dev="tmpfs" ino=158 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_statsd_native_boot_prop:s0 tclass=file permissive=1
[   11.093910] [  T287] type=1400 audit(1749776987.028:760): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_statsd_native_boot_prop:s0" dev="tmpfs" ino=158 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_statsd_native_boot_prop:s0 tclass=file permissive=1
[   11.093968] [  T287] type=1400 audit(1749776987.028:761): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_statsd_native_prop:s0" dev="tmpfs" ino=159 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_statsd_native_prop:s0 tclass=file permissive=1
[   11.094026] [  T287] type=1400 audit(1749776987.028:762): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_statsd_native_prop:s0" dev="tmpfs" ino=159 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_statsd_native_prop:s0 tclass=file permissive=1
[   11.094083] [  T287] type=1400 audit(1749776987.028:763): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_statsd_native_prop:s0" dev="tmpfs" ino=159 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_statsd_native_prop:s0 tclass=file permissive=1
[   11.094141] [  T287] type=1400 audit(1749776987.028:764): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_storage_native_boot_prop:s0" dev="tmpfs" ino=160 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_storage_native_boot_prop:s0 tclass=file permissive=1
[   11.094199] [  T287] type=1400 audit(1749776987.028:765): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_storage_native_boot_prop:s0" dev="tmpfs" ino=160 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_storage_native_boot_prop:s0 tclass=file permissive=1
[   11.094257] [  T287] type=1400 audit(1749776987.028:766): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_storage_native_boot_prop:s0" dev="tmpfs" ino=160 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_storage_native_boot_prop:s0 tclass=file permissive=1
[   11.094315] [  T287] type=1400 audit(1749776987.028:767): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_surface_flinger_native_boot_prop:s0" dev="tmpfs" ino=161 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_surface_flinger_native_boot_prop:s0 tclass=file permissive=1
[   11.094373] [  T287] type=1400 audit(1749776987.028:768): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_surface_flinger_native_boot_prop:s0" dev="tmpfs" ino=161 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_surface_flinger_native_boot_prop:s0 tclass=file permissive=1
[   11.094431] [  T287] type=1400 audit(1749776987.028:769): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_surface_flinger_native_boot_prop:s0" dev="tmpfs" ino=161 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_surface_flinger_native_boot_prop:s0 tclass=file permissive=1
[   11.094490] [  T287] type=1400 audit(1749776987.028:770): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_swcodec_native_prop:s0" dev="tmpfs" ino=162 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_swcodec_native_prop:s0 tclass=file permissive=1
[   11.094548] [  T287] type=1400 audit(1749776987.028:771): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_swcodec_native_prop:s0" dev="tmpfs" ino=162 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_swcodec_native_prop:s0 tclass=file permissive=1
[   11.094606] [  T287] type=1400 audit(1749776987.028:772): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_swcodec_native_prop:s0" dev="tmpfs" ino=162 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_swcodec_native_prop:s0 tclass=file permissive=1
[   11.094664] [  T287] type=1400 audit(1749776987.028:773): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_sys_traced_prop:s0" dev="tmpfs" ino=163 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_sys_traced_prop:s0 tclass=file permissive=1
[   11.094722] [  T287] type=1400 audit(1749776987.028:774): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_sys_traced_prop:s0" dev="tmpfs" ino=163 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_sys_traced_prop:s0 tclass=file permissive=1
[   11.094780] [  T287] type=1400 audit(1749776987.028:775): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_sys_traced_prop:s0" dev="tmpfs" ino=163 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_sys_traced_prop:s0 tclass=file permissive=1
[   11.094838] [  T287] type=1400 audit(1749776987.028:776): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_tethering_u_or_later_native_prop:s0" dev="tmpfs" ino=164 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_tethering_u_or_later_native_prop:s0 tclass=file permissive=1
[   11.094896] [  T287] type=1400 audit(1749776987.028:777): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_tethering_u_or_later_native_prop:s0" dev="tmpfs" ino=164 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_tethering_u_or_later_native_prop:s0 tclass=file permissive=1
[   11.094955] [  T287] type=1400 audit(1749776987.028:778): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_tethering_u_or_later_native_prop:s0" dev="tmpfs" ino=164 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_tethering_u_or_later_native_prop:s0 tclass=file permissive=1
[   11.095014] [  T287] type=1400 audit(1749776987.028:779): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_vendor_system_native_boot_prop:s0" dev="tmpfs" ino=165 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_vendor_system_native_boot_prop:s0 tclass=file permissive=1
[   11.095072] [  T287] type=1400 audit(1749776987.028:780): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_vendor_system_native_boot_prop:s0" dev="tmpfs" ino=165 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_vendor_system_native_boot_prop:s0 tclass=file permissive=1
[   11.095130] [  T287] type=1400 audit(1749776987.028:781): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_vendor_system_native_boot_prop:s0" dev="tmpfs" ino=165 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_vendor_system_native_boot_prop:s0 tclass=file permissive=1
[   11.095189] [  T287] type=1400 audit(1749776987.028:782): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_vendor_system_native_prop:s0" dev="tmpfs" ino=166 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_vendor_system_native_prop:s0 tclass=file permissive=1
[   11.095247] [  T287] type=1400 audit(1749776987.028:783): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_vendor_system_native_prop:s0" dev="tmpfs" ino=166 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_vendor_system_native_prop:s0 tclass=file permissive=1
[   11.095305] [  T287] type=1400 audit(1749776987.028:784): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_vendor_system_native_prop:s0" dev="tmpfs" ino=166 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_vendor_system_native_prop:s0 tclass=file permissive=1
[   11.095363] [  T287] type=1400 audit(1749776987.028:785): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_virtualization_framework_native_prop:s0" dev="tmpfs" ino=167 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_virtualization_framework_native_prop:s0 tclass=file permissive=1
[   11.095421] [  T287] type=1400 audit(1749776987.028:786): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_virtualization_framework_native_prop:s0" dev="tmpfs" ino=167 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_virtualization_framework_native_prop:s0 tclass=file permissive=1
[   11.095480] [  T287] type=1400 audit(1749776987.028:787): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_virtualization_framework_native_prop:s0" dev="tmpfs" ino=167 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_virtualization_framework_native_prop:s0 tclass=file permissive=1
[   11.095538] [  T287] type=1400 audit(1749776987.028:788): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_window_manager_native_boot_prop:s0" dev="tmpfs" ino=168 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_window_manager_native_boot_prop:s0 tclass=file permissive=1
[   11.095596] [  T287] type=1400 audit(1749776987.028:789): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_window_manager_native_boot_prop:s0" dev="tmpfs" ino=168 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_window_manager_native_boot_prop:s0 tclass=file permissive=1
[   11.095654] [  T287] type=1400 audit(1749776987.028:790): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_window_manager_native_boot_prop:s0" dev="tmpfs" ino=168 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_window_manager_native_boot_prop:s0 tclass=file permissive=1
[   11.095712] [  T287] type=1400 audit(1749776987.028:791): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_logging_prop:s0" dev="tmpfs" ino=169 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_logging_prop:s0 tclass=file permissive=1
[   11.095769] [  T287] type=1400 audit(1749776987.028:792): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_logging_prop:s0" dev="tmpfs" ino=169 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_logging_prop:s0 tclass=file permissive=1
[   11.095824] [  T287] type=1400 audit(1749776987.028:793): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_logging_prop:s0" dev="tmpfs" ino=169 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_logging_prop:s0 tclass=file permissive=1
[   11.095881] [  T287] type=1400 audit(1749776987.028:794): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:dmesgd_start_prop:s0" dev="tmpfs" ino=171 scontext=u:r:odrefresh:s0 tcontext=u:object_r:dmesgd_start_prop:s0 tclass=file permissive=1
[   11.095937] [  T287] type=1400 audit(1749776987.028:795): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:dmesgd_start_prop:s0" dev="tmpfs" ino=171 scontext=u:r:odrefresh:s0 tcontext=u:object_r:dmesgd_start_prop:s0 tclass=file permissive=1
[   11.095994] [  T287] type=1400 audit(1749776987.028:796): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:dmesgd_start_prop:s0" dev="tmpfs" ino=171 scontext=u:r:odrefresh:s0 tcontext=u:object_r:dmesgd_start_prop:s0 tclass=file permissive=1
[   11.096052] [  T287] type=1400 audit(1749776987.028:797): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:drm_service_config_prop:s0" dev="tmpfs" ino=172 scontext=u:r:odrefresh:s0 tcontext=u:object_r:drm_service_config_prop:s0 tclass=file permissive=1
[   11.096110] [  T287] type=1400 audit(1749776987.028:798): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:drm_service_config_prop:s0" dev="tmpfs" ino=172 scontext=u:r:odrefresh:s0 tcontext=u:object_r:drm_service_config_prop:s0 tclass=file permissive=1
[   11.096166] [  T287] type=1400 audit(1749776987.028:799): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:drm_service_config_prop:s0" dev="tmpfs" ino=172 scontext=u:r:odrefresh:s0 tcontext=u:object_r:drm_service_config_prop:s0 tclass=file permissive=1
[   11.096223] [  T287] type=1400 audit(1749776987.028:800): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:dumpstate_options_prop:s0" dev="tmpfs" ino=173 scontext=u:r:odrefresh:s0 tcontext=u:object_r:dumpstate_options_prop:s0 tclass=file permissive=1
[   11.096280] [  T287] type=1400 audit(1749776987.028:801): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:dumpstate_options_prop:s0" dev="tmpfs" ino=173 scontext=u:r:odrefresh:s0 tcontext=u:object_r:dumpstate_options_prop:s0 tclass=file permissive=1
[   11.096337] [  T287] type=1400 audit(1749776987.028:802): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:dumpstate_options_prop:s0" dev="tmpfs" ino=173 scontext=u:r:odrefresh:s0 tcontext=u:object_r:dumpstate_options_prop:s0 tclass=file permissive=1
[   11.096394] [  T287] type=1400 audit(1749776987.028:803): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:dynamic_system_prop:s0" dev="tmpfs" ino=175 scontext=u:r:odrefresh:s0 tcontext=u:object_r:dynamic_system_prop:s0 tclass=file permissive=1
[   11.096450] [  T287] type=1400 audit(1749776987.028:804): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:dynamic_system_prop:s0" dev="tmpfs" ino=175 scontext=u:r:odrefresh:s0 tcontext=u:object_r:dynamic_system_prop:s0 tclass=file permissive=1
[   11.096506] [  T287] type=1400 audit(1749776987.028:805): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:dynamic_system_prop:s0" dev="tmpfs" ino=175 scontext=u:r:odrefresh:s0 tcontext=u:object_r:dynamic_system_prop:s0 tclass=file permissive=1
[   11.096563] [  T287] type=1400 audit(1749776987.028:806): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:exported_bluetooth_prop:s0" dev="tmpfs" ino=177 scontext=u:r:odrefresh:s0 tcontext=u:object_r:exported_bluetooth_prop:s0 tclass=file permissive=1
[   11.096621] [  T287] type=1400 audit(1749776987.028:807): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:exported_bluetooth_prop:s0" dev="tmpfs" ino=177 scontext=u:r:odrefresh:s0 tcontext=u:object_r:exported_bluetooth_prop:s0 tclass=file permissive=1
[   11.096678] [  T287] type=1400 audit(1749776987.028:808): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:exported_bluetooth_prop:s0" dev="tmpfs" ino=177 scontext=u:r:odrefresh:s0 tcontext=u:object_r:exported_bluetooth_prop:s0 tclass=file permissive=1
[   11.096735] [  T287] type=1400 audit(1749776987.028:809): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:exported_overlay_prop:s0" dev="tmpfs" ino=181 scontext=u:r:odrefresh:s0 tcontext=u:object_r:exported_overlay_prop:s0 tclass=file permissive=1
[   11.096791] [  T287] type=1400 audit(1749776987.028:810): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:exported_overlay_prop:s0" dev="tmpfs" ino=181 scontext=u:r:odrefresh:s0 tcontext=u:object_r:exported_overlay_prop:s0 tclass=file permissive=1
[   11.096847] [  T287] type=1400 audit(1749776987.028:811): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:exported_overlay_prop:s0" dev="tmpfs" ino=181 scontext=u:r:odrefresh:s0 tcontext=u:object_r:exported_overlay_prop:s0 tclass=file permissive=1
[   11.096904] [  T287] type=1400 audit(1749776987.028:812): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:fastbootd_protocol_prop:s0" dev="tmpfs" ino=184 scontext=u:r:odrefresh:s0 tcontext=u:object_r:fastbootd_protocol_prop:s0 tclass=file permissive=1
[   11.096981] [  T287] type=1400 audit(1749776987.028:813): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:fastbootd_protocol_prop:s0" dev="tmpfs" ino=184 scontext=u:r:odrefresh:s0 tcontext=u:object_r:fastbootd_protocol_prop:s0 tclass=file permissive=1
[   11.097047] [  T287] type=1400 audit(1749776987.028:814): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:fastbootd_protocol_prop:s0" dev="tmpfs" ino=184 scontext=u:r:odrefresh:s0 tcontext=u:object_r:fastbootd_protocol_prop:s0 tclass=file permissive=1
[   11.097105] [  T287] type=1400 audit(1749776987.028:815): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:ffs_control_prop:s0" dev="tmpfs" ino=186 scontext=u:r:odrefresh:s0 tcontext=u:object_r:ffs_control_prop:s0 tclass=file permissive=1
[   11.097162] [  T287] type=1400 audit(1749776987.028:816): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:ffs_control_prop:s0" dev="tmpfs" ino=186 scontext=u:r:odrefresh:s0 tcontext=u:object_r:ffs_control_prop:s0 tclass=file permissive=1
[   11.097218] [  T287] type=1400 audit(1749776987.028:817): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:ffs_control_prop:s0" dev="tmpfs" ino=186 scontext=u:r:odrefresh:s0 tcontext=u:object_r:ffs_control_prop:s0 tclass=file permissive=1
[   11.097274] [  T287] type=1400 audit(1749776987.028:818): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:firstboot_prop:s0" dev="tmpfs" ino=188 scontext=u:r:odrefresh:s0 tcontext=u:object_r:firstboot_prop:s0 tclass=file permissive=1
[   11.097330] [  T287] type=1400 audit(1749776987.028:819): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:firstboot_prop:s0" dev="tmpfs" ino=188 scontext=u:r:odrefresh:s0 tcontext=u:object_r:firstboot_prop:s0 tclass=file permissive=1
[   11.097386] [  T287] type=1400 audit(1749776987.028:820): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:firstboot_prop:s0" dev="tmpfs" ino=188 scontext=u:r:odrefresh:s0 tcontext=u:object_r:firstboot_prop:s0 tclass=file permissive=1
[   11.097442] [  T287] type=1400 audit(1749776987.028:821): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:framework_watchdog_config_prop:s0" dev="tmpfs" ino=190 scontext=u:r:odrefresh:s0 tcontext=u:object_r:framework_watchdog_config_prop:s0 tclass=file permissive=1
[   11.097500] [  T287] type=1400 audit(1749776987.028:822): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:framework_watchdog_config_prop:s0" dev="tmpfs" ino=190 scontext=u:r:odrefresh:s0 tcontext=u:object_r:framework_watchdog_config_prop:s0 tclass=file permissive=1
[   11.097557] [  T287] type=1400 audit(1749776987.028:823): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:framework_watchdog_config_prop:s0" dev="tmpfs" ino=190 scontext=u:r:odrefresh:s0 tcontext=u:object_r:framework_watchdog_config_prop:s0 tclass=file permissive=1
[   11.097613] [  T287] type=1400 audit(1749776987.028:824): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:future_pm_prop:s0" dev="tmpfs" ino=191 scontext=u:r:odrefresh:s0 tcontext=u:object_r:future_pm_prop:s0 tclass=file permissive=1
[   11.097669] [  T287] type=1400 audit(1749776987.028:825): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:future_pm_prop:s0" dev="tmpfs" ino=191 scontext=u:r:odrefresh:s0 tcontext=u:object_r:future_pm_prop:s0 tclass=file permissive=1
[   11.097725] [  T287] type=1400 audit(1749776987.028:826): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:future_pm_prop:s0" dev="tmpfs" ino=191 scontext=u:r:odrefresh:s0 tcontext=u:object_r:future_pm_prop:s0 tclass=file permissive=1
[   11.097780] [  T287] type=1400 audit(1749776987.028:827): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:gesture_prop:s0" dev="tmpfs" ino=192 scontext=u:r:odrefresh:s0 tcontext=u:object_r:gesture_prop:s0 tclass=file permissive=1
[   11.097836] [  T287] type=1400 audit(1749776987.028:828): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:gesture_prop:s0" dev="tmpfs" ino=192 scontext=u:r:odrefresh:s0 tcontext=u:object_r:gesture_prop:s0 tclass=file permissive=1
[   11.097891] [  T287] type=1400 audit(1749776987.028:829): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:gesture_prop:s0" dev="tmpfs" ino=192 scontext=u:r:odrefresh:s0 tcontext=u:object_r:gesture_prop:s0 tclass=file permissive=1
[   11.097946] [  T287] type=1400 audit(1749776987.028:830): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:gsid_prop:s0" dev="tmpfs" ino=195 scontext=u:r:odrefresh:s0 tcontext=u:object_r:gsid_prop:s0 tclass=file permissive=1
[   11.098002] [  T287] type=1400 audit(1749776987.028:831): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:gsid_prop:s0" dev="tmpfs" ino=195 scontext=u:r:odrefresh:s0 tcontext=u:object_r:gsid_prop:s0 tclass=file permissive=1
[   11.098066] [  T287] type=1400 audit(1749776987.028:832): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:gsid_prop:s0" dev="tmpfs" ino=195 scontext=u:r:odrefresh:s0 tcontext=u:object_r:gsid_prop:s0 tclass=file permissive=1
[   11.098130] [  T287] type=1400 audit(1749776987.028:833): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:hal_dumpstate_config_prop:s0" dev="tmpfs" ino=197 scontext=u:r:odrefresh:s0 tcontext=u:object_r:hal_dumpstate_config_prop:s0 tclass=file permissive=1
[   11.098193] [  T287] type=1400 audit(1749776987.028:834): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:hal_dumpstate_config_prop:s0" dev="tmpfs" ino=197 scontext=u:r:odrefresh:s0 tcontext=u:object_r:hal_dumpstate_config_prop:s0 tclass=file permissive=1
[   11.098256] [  T287] type=1400 audit(1749776987.028:835): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:hal_dumpstate_config_prop:s0" dev="tmpfs" ino=197 scontext=u:r:odrefresh:s0 tcontext=u:object_r:hal_dumpstate_config_prop:s0 tclass=file permissive=1
[   11.098318] [  T287] type=1400 audit(1749776987.028:836): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:heapprofd_enabled_prop:s0" dev="tmpfs" ino=200 scontext=u:r:odrefresh:s0 tcontext=u:object_r:heapprofd_enabled_prop:s0 tclass=file permissive=1
[   11.098379] [  T287] type=1400 audit(1749776987.028:837): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:heapprofd_enabled_prop:s0" dev="tmpfs" ino=200 scontext=u:r:odrefresh:s0 tcontext=u:object_r:heapprofd_enabled_prop:s0 tclass=file permissive=1
[   11.098441] [  T287] type=1400 audit(1749776987.028:838): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:heapprofd_enabled_prop:s0" dev="tmpfs" ino=200 scontext=u:r:odrefresh:s0 tcontext=u:object_r:heapprofd_enabled_prop:s0 tclass=file permissive=1
[   11.098503] [  T287] type=1400 audit(1749776987.028:839): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:hwservicemanager_prop:s0" dev="tmpfs" ino=203 scontext=u:r:odrefresh:s0 tcontext=u:object_r:hwservicemanager_prop:s0 tclass=file permissive=1
[   11.098564] [  T287] type=1400 audit(1749776987.028:840): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:hwservicemanager_prop:s0" dev="tmpfs" ino=203 scontext=u:r:odrefresh:s0 tcontext=u:object_r:hwservicemanager_prop:s0 tclass=file permissive=1
[   11.098625] [  T287] type=1400 audit(1749776987.028:841): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:hwservicemanager_prop:s0" dev="tmpfs" ino=203 scontext=u:r:odrefresh:s0 tcontext=u:object_r:hwservicemanager_prop:s0 tclass=file permissive=1
[   11.098687] [  T287] type=1400 audit(1749776987.028:842): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:hypervisor_prop:s0" dev="tmpfs" ino=204 scontext=u:r:odrefresh:s0 tcontext=u:object_r:hypervisor_prop:s0 tclass=file permissive=1
[   11.098749] [  T287] type=1400 audit(1749776987.028:843): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:hypervisor_prop:s0" dev="tmpfs" ino=204 scontext=u:r:odrefresh:s0 tcontext=u:object_r:hypervisor_prop:s0 tclass=file permissive=1
[   11.098810] [  T287] type=1400 audit(1749776987.028:844): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:hypervisor_prop:s0" dev="tmpfs" ino=204 scontext=u:r:odrefresh:s0 tcontext=u:object_r:hypervisor_prop:s0 tclass=file permissive=1
[   11.098872] [  T287] type=1400 audit(1749776987.028:845): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:hypervisor_restricted_prop:s0" dev="tmpfs" ino=205 scontext=u:r:odrefresh:s0 tcontext=u:object_r:hypervisor_restricted_prop:s0 tclass=file permissive=1
[   11.098935] [  T287] type=1400 audit(1749776987.028:846): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:hypervisor_restricted_prop:s0" dev="tmpfs" ino=205 scontext=u:r:odrefresh:s0 tcontext=u:object_r:hypervisor_restricted_prop:s0 tclass=file permissive=1
[   11.099000] [  T287] type=1400 audit(1749776987.028:847): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:hypervisor_restricted_prop:s0" dev="tmpfs" ino=205 scontext=u:r:odrefresh:s0 tcontext=u:object_r:hypervisor_restricted_prop:s0 tclass=file permissive=1
[   11.099062] [  T287] type=1400 audit(1749776987.028:848): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:incremental_prop:s0" dev="tmpfs" ino=206 scontext=u:r:odrefresh:s0 tcontext=u:object_r:incremental_prop:s0 tclass=file permissive=1
[   11.099124] [  T287] type=1400 audit(1749776987.028:849): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:incremental_prop:s0" dev="tmpfs" ino=206 scontext=u:r:odrefresh:s0 tcontext=u:object_r:incremental_prop:s0 tclass=file permissive=1
[   11.099185] [  T287] type=1400 audit(1749776987.028:850): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:incremental_prop:s0" dev="tmpfs" ino=206 scontext=u:r:odrefresh:s0 tcontext=u:object_r:incremental_prop:s0 tclass=file permissive=1
[   11.099247] [  T287] type=1400 audit(1749776987.028:851): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:init_perf_lsm_hooks_prop:s0" dev="tmpfs" ino=207 scontext=u:r:odrefresh:s0 tcontext=u:object_r:init_perf_lsm_hooks_prop:s0 tclass=file permissive=1
[   11.099310] [  T287] type=1400 audit(1749776987.028:852): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:init_perf_lsm_hooks_prop:s0" dev="tmpfs" ino=207 scontext=u:r:odrefresh:s0 tcontext=u:object_r:init_perf_lsm_hooks_prop:s0 tclass=file permissive=1
[   11.099372] [  T287] type=1400 audit(1749776987.028:853): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:init_perf_lsm_hooks_prop:s0" dev="tmpfs" ino=207 scontext=u:r:odrefresh:s0 tcontext=u:object_r:init_perf_lsm_hooks_prop:s0 tclass=file permissive=1
[   11.099436] [  T287] type=1400 audit(1749776987.028:854): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:init_storage_prop:s0" dev="tmpfs" ino=210 scontext=u:r:odrefresh:s0 tcontext=u:object_r:init_storage_prop:s0 tclass=file permissive=1
[   11.099498] [  T287] type=1400 audit(1749776987.028:855): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:init_storage_prop:s0" dev="tmpfs" ino=210 scontext=u:r:odrefresh:s0 tcontext=u:object_r:init_storage_prop:s0 tclass=file permissive=1
[   11.099559] [  T287] type=1400 audit(1749776987.028:856): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:init_storage_prop:s0" dev="tmpfs" ino=210 scontext=u:r:odrefresh:s0 tcontext=u:object_r:init_storage_prop:s0 tclass=file permissive=1
[   11.099620] [  T287] type=1400 audit(1749776987.028:857): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:init_svc_debug_prop:s0" dev="tmpfs" ino=211 scontext=u:r:odrefresh:s0 tcontext=u:object_r:init_svc_debug_prop:s0 tclass=file permissive=1
[   11.099682] [  T287] type=1400 audit(1749776987.028:858): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:init_svc_debug_prop:s0" dev="tmpfs" ino=211 scontext=u:r:odrefresh:s0 tcontext=u:object_r:init_svc_debug_prop:s0 tclass=file permissive=1
[   11.099744] [  T287] type=1400 audit(1749776987.028:859): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:init_svc_debug_prop:s0" dev="tmpfs" ino=211 scontext=u:r:odrefresh:s0 tcontext=u:object_r:init_svc_debug_prop:s0 tclass=file permissive=1
[   11.099805] [  T287] type=1400 audit(1749776987.028:860): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:keyguard_config_prop:s0" dev="tmpfs" ino=212 scontext=u:r:odrefresh:s0 tcontext=u:object_r:keyguard_config_prop:s0 tclass=file permissive=1
[   11.099866] [  T287] type=1400 audit(1749776987.028:861): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:keyguard_config_prop:s0" dev="tmpfs" ino=212 scontext=u:r:odrefresh:s0 tcontext=u:object_r:keyguard_config_prop:s0 tclass=file permissive=1
[   11.099928] [  T287] type=1400 audit(1749776987.028:862): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:keyguard_config_prop:s0" dev="tmpfs" ino=212 scontext=u:r:odrefresh:s0 tcontext=u:object_r:keyguard_config_prop:s0 tclass=file permissive=1
[   11.099989] [  T287] type=1400 audit(1749776987.028:863): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:keystore_config_prop:s0" dev="tmpfs" ino=213 scontext=u:r:odrefresh:s0 tcontext=u:object_r:keystore_config_prop:s0 tclass=file permissive=1
[   11.100051] [  T287] type=1400 audit(1749776987.028:864): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:keystore_config_prop:s0" dev="tmpfs" ino=213 scontext=u:r:odrefresh:s0 tcontext=u:object_r:keystore_config_prop:s0 tclass=file permissive=1
[   11.100112] [  T287] type=1400 audit(1749776987.032:865): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:keystore_config_prop:s0" dev="tmpfs" ino=213 scontext=u:r:odrefresh:s0 tcontext=u:object_r:keystore_config_prop:s0 tclass=file permissive=1
[   11.100173] [  T287] type=1400 audit(1749776987.032:866): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:keystore_crash_prop:s0" dev="tmpfs" ino=214 scontext=u:r:odrefresh:s0 tcontext=u:object_r:keystore_crash_prop:s0 tclass=file permissive=1
[   11.100211] [  T450] audit_log_start: 776 callbacks suppressed
[   11.100215] [  T450] audit: audit_backlog=65 > audit_backlog_limit=64
[   11.100226] [  T450] audit: audit_lost=257 audit_rate_limit=0 audit_backlog_limit=64
[   11.100231] [  T450] audit: backlog limit exceeded
[   11.100235] [  T287] type=1400 audit(1749776987.032:867): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:keystore_crash_prop:s0" dev="tmpfs" ino=214 scontext=u:r:odrefresh:s0 tcontext=u:object_r:keystore_crash_prop:s0 tclass=file permissive=1
[   11.100249] [  T450] audit: audit_backlog=65 > audit_backlog_limit=64
[   11.100255] [  T450] audit: audit_lost=258 audit_rate_limit=0 audit_backlog_limit=64
[   11.100260] [  T450] audit: backlog limit exceeded
[   11.100298] [  T287] type=1400 audit(1749776987.032:868): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:keystore_crash_prop:s0" dev="tmpfs" ino=214 scontext=u:r:odrefresh:s0 tcontext=u:object_r:keystore_crash_prop:s0 tclass=file permissive=1
[   11.100303] [  T450] audit: audit_backlog=65 > audit_backlog_limit=64
[   11.100306] [  T450] audit: audit_lost=259 audit_rate_limit=0 audit_backlog_limit=64
[   11.100308] [  T450] audit: backlog limit exceeded
[   11.100311] [  T450] audit: audit_backlog=65 > audit_backlog_limit=64
[   11.100380] [  T287] type=1400 audit(1749776987.032:869): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:keystore_listen_prop:s0" dev="tmpfs" ino=215 scontext=u:r:odrefresh:s0 tcontext=u:object_r:keystore_listen_prop:s0 tclass=file permissive=1
[   11.101063] [  T287] type=1400 audit(1749776987.032:870): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:keystore_listen_prop:s0" dev="tmpfs" ino=215 scontext=u:r:odrefresh:s0 tcontext=u:object_r:keystore_listen_prop:s0 tclass=file permissive=1
[   11.101151] [  T287] type=1400 audit(1749776987.032:871): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:keystore_listen_prop:s0" dev="tmpfs" ino=215 scontext=u:r:odrefresh:s0 tcontext=u:object_r:keystore_listen_prop:s0 tclass=file permissive=1
[   11.101214] [  T287] type=1400 audit(1749776987.032:872): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:last_boot_reason_prop:s0" dev="tmpfs" ino=216 scontext=u:r:odrefresh:s0 tcontext=u:object_r:last_boot_reason_prop:s0 tclass=file permissive=1
[   11.101278] [  T287] type=1400 audit(1749776987.032:873): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:last_boot_reason_prop:s0" dev="tmpfs" ino=216 scontext=u:r:odrefresh:s0 tcontext=u:object_r:last_boot_reason_prop:s0 tclass=file permissive=1
[   11.101340] [  T287] type=1400 audit(1749776987.032:874): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:last_boot_reason_prop:s0" dev="tmpfs" ino=216 scontext=u:r:odrefresh:s0 tcontext=u:object_r:last_boot_reason_prop:s0 tclass=file permissive=1
[   11.101402] [  T287] type=1400 audit(1749776987.032:875): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:llkd_prop:s0" dev="tmpfs" ino=218 scontext=u:r:odrefresh:s0 tcontext=u:object_r:llkd_prop:s0 tclass=file permissive=1
[   11.101464] [  T287] type=1400 audit(1749776987.032:876): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:llkd_prop:s0" dev="tmpfs" ino=218 scontext=u:r:odrefresh:s0 tcontext=u:object_r:llkd_prop:s0 tclass=file permissive=1
[   11.101525] [  T287] type=1400 audit(1749776987.032:877): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:llkd_prop:s0" dev="tmpfs" ino=218 scontext=u:r:odrefresh:s0 tcontext=u:object_r:llkd_prop:s0 tclass=file permissive=1
[   11.101586] [  T287] type=1400 audit(1749776987.032:878): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:lmkd_prop:s0" dev="tmpfs" ino=220 scontext=u:r:odrefresh:s0 tcontext=u:object_r:lmkd_prop:s0 tclass=file permissive=1
[   11.101647] [  T287] type=1400 audit(1749776987.032:879): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:lmkd_prop:s0" dev="tmpfs" ino=220 scontext=u:r:odrefresh:s0 tcontext=u:object_r:lmkd_prop:s0 tclass=file permissive=1
[   11.101708] [  T287] type=1400 audit(1749776987.032:880): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:lmkd_prop:s0" dev="tmpfs" ino=220 scontext=u:r:odrefresh:s0 tcontext=u:object_r:lmkd_prop:s0 tclass=file permissive=1
[   11.101771] [  T287] type=1400 audit(1749776987.032:881): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:logpersistd_logging_prop:s0" dev="tmpfs" ino=227 scontext=u:r:odrefresh:s0 tcontext=u:object_r:logpersistd_logging_prop:s0 tclass=file permissive=1
[   11.101834] [  T287] type=1400 audit(1749776987.032:882): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:logpersistd_logging_prop:s0" dev="tmpfs" ino=227 scontext=u:r:odrefresh:s0 tcontext=u:object_r:logpersistd_logging_prop:s0 tclass=file permissive=1
[   11.101898] [  T287] type=1400 audit(1749776987.032:883): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:logpersistd_logging_prop:s0" dev="tmpfs" ino=227 scontext=u:r:odrefresh:s0 tcontext=u:object_r:logpersistd_logging_prop:s0 tclass=file permissive=1
[   11.101961] [  T287] type=1400 audit(1749776987.032:884): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:lower_kptr_restrict_prop:s0" dev="tmpfs" ino=228 scontext=u:r:odrefresh:s0 tcontext=u:object_r:lower_kptr_restrict_prop:s0 tclass=file permissive=1
[   11.102024] [  T287] type=1400 audit(1749776987.032:885): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:lower_kptr_restrict_prop:s0" dev="tmpfs" ino=228 scontext=u:r:odrefresh:s0 tcontext=u:object_r:lower_kptr_restrict_prop:s0 tclass=file permissive=1
[   11.102086] [  T287] type=1400 audit(1749776987.032:886): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:lower_kptr_restrict_prop:s0" dev="tmpfs" ino=228 scontext=u:r:odrefresh:s0 tcontext=u:object_r:lower_kptr_restrict_prop:s0 tclass=file permissive=1
[   11.102148] [  T287] type=1400 audit(1749776987.032:887): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:lowpan_prop:s0" dev="tmpfs" ino=229 scontext=u:r:odrefresh:s0 tcontext=u:object_r:lowpan_prop:s0 tclass=file permissive=1
[   11.102210] [  T287] type=1400 audit(1749776987.032:888): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:lowpan_prop:s0" dev="tmpfs" ino=229 scontext=u:r:odrefresh:s0 tcontext=u:object_r:lowpan_prop:s0 tclass=file permissive=1
[   11.102271] [  T287] type=1400 audit(1749776987.032:889): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:lowpan_prop:s0" dev="tmpfs" ino=229 scontext=u:r:odrefresh:s0 tcontext=u:object_r:lowpan_prop:s0 tclass=file permissive=1
[   11.102332] [  T287] type=1400 audit(1749776987.032:890): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:lpdumpd_prop:s0" dev="tmpfs" ino=230 scontext=u:r:odrefresh:s0 tcontext=u:object_r:lpdumpd_prop:s0 tclass=file permissive=1
[   11.102394] [  T287] type=1400 audit(1749776987.032:891): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:lpdumpd_prop:s0" dev="tmpfs" ino=230 scontext=u:r:odrefresh:s0 tcontext=u:object_r:lpdumpd_prop:s0 tclass=file permissive=1
[   11.102455] [  T287] type=1400 audit(1749776987.032:892): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:lpdumpd_prop:s0" dev="tmpfs" ino=230 scontext=u:r:odrefresh:s0 tcontext=u:object_r:lpdumpd_prop:s0 tclass=file permissive=1
[   11.102516] [  T287] type=1400 audit(1749776987.032:893): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:media_config_prop:s0" dev="tmpfs" ino=231 scontext=u:r:odrefresh:s0 tcontext=u:object_r:media_config_prop:s0 tclass=file permissive=1
[   11.102579] [  T287] type=1400 audit(1749776987.032:894): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:media_config_prop:s0" dev="tmpfs" ino=231 scontext=u:r:odrefresh:s0 tcontext=u:object_r:media_config_prop:s0 tclass=file permissive=1
[   11.102642] [  T287] type=1400 audit(1749776987.032:895): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:media_config_prop:s0" dev="tmpfs" ino=231 scontext=u:r:odrefresh:s0 tcontext=u:object_r:media_config_prop:s0 tclass=file permissive=1
[   11.102705] [  T287] type=1400 audit(1749776987.032:896): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:media_variant_prop:s0" dev="tmpfs" ino=232 scontext=u:r:odrefresh:s0 tcontext=u:object_r:media_variant_prop:s0 tclass=file permissive=1
[   11.102767] [  T287] type=1400 audit(1749776987.032:897): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:media_variant_prop:s0" dev="tmpfs" ino=232 scontext=u:r:odrefresh:s0 tcontext=u:object_r:media_variant_prop:s0 tclass=file permissive=1
[   11.102828] [  T287] type=1400 audit(1749776987.032:898): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:media_variant_prop:s0" dev="tmpfs" ino=232 scontext=u:r:odrefresh:s0 tcontext=u:object_r:media_variant_prop:s0 tclass=file permissive=1
[   11.102890] [  T287] type=1400 audit(1749776987.032:899): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:mm_events_config_prop:s0" dev="tmpfs" ino=234 scontext=u:r:odrefresh:s0 tcontext=u:object_r:mm_events_config_prop:s0 tclass=file permissive=1
[   11.102952] [  T287] type=1400 audit(1749776987.032:900): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:mm_events_config_prop:s0" dev="tmpfs" ino=234 scontext=u:r:odrefresh:s0 tcontext=u:object_r:mm_events_config_prop:s0 tclass=file permissive=1
[   11.103015] [  T287] type=1400 audit(1749776987.032:901): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:mm_events_config_prop:s0" dev="tmpfs" ino=234 scontext=u:r:odrefresh:s0 tcontext=u:object_r:mm_events_config_prop:s0 tclass=file permissive=1
[   11.103076] [  T287] type=1400 audit(1749776987.032:902): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:mmc_prop:s0" dev="tmpfs" ino=235 scontext=u:r:odrefresh:s0 tcontext=u:object_r:mmc_prop:s0 tclass=file permissive=1
[   11.103138] [  T287] type=1400 audit(1749776987.032:903): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:mmc_prop:s0" dev="tmpfs" ino=235 scontext=u:r:odrefresh:s0 tcontext=u:object_r:mmc_prop:s0 tclass=file permissive=1
[   11.103199] [  T287] type=1400 audit(1749776987.032:904): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:mmc_prop:s0" dev="tmpfs" ino=235 scontext=u:r:odrefresh:s0 tcontext=u:object_r:mmc_prop:s0 tclass=file permissive=1
[   11.103260] [  T287] type=1400 audit(1749776987.032:905): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:mock_ota_prop:s0" dev="tmpfs" ino=236 scontext=u:r:odrefresh:s0 tcontext=u:object_r:mock_ota_prop:s0 tclass=file permissive=1
[   11.103322] [  T287] type=1400 audit(1749776987.032:906): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:mock_ota_prop:s0" dev="tmpfs" ino=236 scontext=u:r:odrefresh:s0 tcontext=u:object_r:mock_ota_prop:s0 tclass=file permissive=1
[   11.103383] [  T287] type=1400 audit(1749776987.032:907): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:mock_ota_prop:s0" dev="tmpfs" ino=236 scontext=u:r:odrefresh:s0 tcontext=u:object_r:mock_ota_prop:s0 tclass=file permissive=1
[   11.103445] [  T287] type=1400 audit(1749776987.032:908): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:net_464xlat_fromvendor_prop:s0" dev="tmpfs" ino=238 scontext=u:r:odrefresh:s0 tcontext=u:object_r:net_464xlat_fromvendor_prop:s0 tclass=file permissive=1
[   11.103509] [  T287] type=1400 audit(1749776987.032:909): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:net_464xlat_fromvendor_prop:s0" dev="tmpfs" ino=238 scontext=u:r:odrefresh:s0 tcontext=u:object_r:net_464xlat_fromvendor_prop:s0 tclass=file permissive=1
[   11.104143] [  T287] type=1400 audit(1749776987.032:910): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:net_464xlat_fromvendor_prop:s0" dev="tmpfs" ino=238 scontext=u:r:odrefresh:s0 tcontext=u:object_r:net_464xlat_fromvendor_prop:s0 tclass=file permissive=1
[   11.104230] [  T287] type=1400 audit(1749776987.032:911): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:net_connectivity_prop:s0" dev="tmpfs" ino=239 scontext=u:r:odrefresh:s0 tcontext=u:object_r:net_connectivity_prop:s0 tclass=file permissive=1
[   11.104293] [  T287] type=1400 audit(1749776987.032:912): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:net_connectivity_prop:s0" dev="tmpfs" ino=239 scontext=u:r:odrefresh:s0 tcontext=u:object_r:net_connectivity_prop:s0 tclass=file permissive=1
[   11.104355] [  T287] type=1400 audit(1749776987.032:913): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:net_connectivity_prop:s0" dev="tmpfs" ino=239 scontext=u:r:odrefresh:s0 tcontext=u:object_r:net_connectivity_prop:s0 tclass=file permissive=1
[   11.104419] [  T287] type=1400 audit(1749776987.032:914): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:net_dns_prop:s0" dev="tmpfs" ino=240 scontext=u:r:odrefresh:s0 tcontext=u:object_r:net_dns_prop:s0 tclass=file permissive=1
[   11.104483] [  T287] type=1400 audit(1749776987.032:915): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:net_dns_prop:s0" dev="tmpfs" ino=240 scontext=u:r:odrefresh:s0 tcontext=u:object_r:net_dns_prop:s0 tclass=file permissive=1
[   11.104546] [  T287] type=1400 audit(1749776987.032:916): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:net_dns_prop:s0" dev="tmpfs" ino=240 scontext=u:r:odrefresh:s0 tcontext=u:object_r:net_dns_prop:s0 tclass=file permissive=1
[   11.104609] [  T287] type=1400 audit(1749776987.032:917): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:netd_stable_secret_prop:s0" dev="tmpfs" ino=242 scontext=u:r:odrefresh:s0 tcontext=u:object_r:netd_stable_secret_prop:s0 tclass=file permissive=1
[   11.104673] [  T287] type=1400 audit(1749776987.032:918): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:netd_stable_secret_prop:s0" dev="tmpfs" ino=242 scontext=u:r:odrefresh:s0 tcontext=u:object_r:netd_stable_secret_prop:s0 tclass=file permissive=1
[   11.104736] [  T287] type=1400 audit(1749776987.032:919): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:netd_stable_secret_prop:s0" dev="tmpfs" ino=242 scontext=u:r:odrefresh:s0 tcontext=u:object_r:netd_stable_secret_prop:s0 tclass=file permissive=1
[   11.104799] [  T287] type=1400 audit(1749776987.032:920): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:nnapi_ext_deny_product_prop:s0" dev="tmpfs" ino=244 scontext=u:r:odrefresh:s0 tcontext=u:object_r:nnapi_ext_deny_product_prop:s0 tclass=file permissive=1
[   11.104863] [  T287] type=1400 audit(1749776987.032:921): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:nnapi_ext_deny_product_prop:s0" dev="tmpfs" ino=244 scontext=u:r:odrefresh:s0 tcontext=u:object_r:nnapi_ext_deny_product_prop:s0 tclass=file permissive=1
[   11.104926] [  T287] type=1400 audit(1749776987.032:922): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:nnapi_ext_deny_product_prop:s0" dev="tmpfs" ino=244 scontext=u:r:odrefresh:s0 tcontext=u:object_r:nnapi_ext_deny_product_prop:s0 tclass=file permissive=1
[   11.105020] [  T287] type=1400 audit(1749776987.032:923): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:odsign_prop:s0" dev="tmpfs" ino=245 scontext=u:r:odrefresh:s0 tcontext=u:object_r:odsign_prop:s0 tclass=file permissive=1
[   11.105079] [  T287] type=1400 audit(1749776987.032:924): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:odsign_prop:s0" dev="tmpfs" ino=245 scontext=u:r:odrefresh:s0 tcontext=u:object_r:odsign_prop:s0 tclass=file permissive=1
[   11.105135] [  T287] type=1400 audit(1749776987.032:925): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:odsign_prop:s0" dev="tmpfs" ino=245 scontext=u:r:odrefresh:s0 tcontext=u:object_r:odsign_prop:s0 tclass=file permissive=1
[   11.105191] [  T287] type=1400 audit(1749776987.032:926): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:oem_unlock_prop:s0" dev="tmpfs" ino=246 scontext=u:r:odrefresh:s0 tcontext=u:object_r:oem_unlock_prop:s0 tclass=file permissive=1
[   11.105248] [  T287] type=1400 audit(1749776987.032:927): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:oem_unlock_prop:s0" dev="tmpfs" ino=246 scontext=u:r:odrefresh:s0 tcontext=u:object_r:oem_unlock_prop:s0 tclass=file permissive=1
[   11.105310] [  T287] type=1400 audit(1749776987.032:928): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:oem_unlock_prop:s0" dev="tmpfs" ino=246 scontext=u:r:odrefresh:s0 tcontext=u:object_r:oem_unlock_prop:s0 tclass=file permissive=1
[   11.105372] [  T287] type=1400 audit(1749776987.032:929): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:overlay_prop:s0" dev="tmpfs" ino=248 scontext=u:r:odrefresh:s0 tcontext=u:object_r:overlay_prop:s0 tclass=file permissive=1
[   11.105434] [  T287] type=1400 audit(1749776987.032:930): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:overlay_prop:s0" dev="tmpfs" ino=248 scontext=u:r:odrefresh:s0 tcontext=u:object_r:overlay_prop:s0 tclass=file permissive=1
[   11.105496] [  T287] type=1400 audit(1749776987.032:931): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:overlay_prop:s0" dev="tmpfs" ino=248 scontext=u:r:odrefresh:s0 tcontext=u:object_r:overlay_prop:s0 tclass=file permissive=1
[   11.105558] [  T287] type=1400 audit(1749776987.032:932): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:packagemanager_config_prop:s0" dev="tmpfs" ino=249 scontext=u:r:odrefresh:s0 tcontext=u:object_r:packagemanager_config_prop:s0 tclass=file permissive=1
[   11.105621] [  T287] type=1400 audit(1749776987.032:933): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:packagemanager_config_prop:s0" dev="tmpfs" ino=249 scontext=u:r:odrefresh:s0 tcontext=u:object_r:packagemanager_config_prop:s0 tclass=file permissive=1
[   11.105684] [  T287] type=1400 audit(1749776987.032:934): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:packagemanager_config_prop:s0" dev="tmpfs" ino=249 scontext=u:r:odrefresh:s0 tcontext=u:object_r:packagemanager_config_prop:s0 tclass=file permissive=1
[   11.105746] [  T287] type=1400 audit(1749776987.032:935): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:perf_drop_caches_prop:s0" dev="tmpfs" ino=251 scontext=u:r:odrefresh:s0 tcontext=u:object_r:perf_drop_caches_prop:s0 tclass=file permissive=1
[   11.105803] [  T287] type=1400 audit(1749776987.032:936): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:perf_drop_caches_prop:s0" dev="tmpfs" ino=251 scontext=u:r:odrefresh:s0 tcontext=u:object_r:perf_drop_caches_prop:s0 tclass=file permissive=1
[   11.105858] [  T287] type=1400 audit(1749776987.032:937): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:perf_drop_caches_prop:s0" dev="tmpfs" ino=251 scontext=u:r:odrefresh:s0 tcontext=u:object_r:perf_drop_caches_prop:s0 tclass=file permissive=1
[   11.105916] [  T287] type=1400 audit(1749776987.032:938): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:persist_sysui_builder_extras_prop:s0" dev="tmpfs" ino=254 scontext=u:r:odrefresh:s0 tcontext=u:object_r:persist_sysui_builder_extras_prop:s0 tclass=file permissive=1
[   11.105975] [  T287] type=1400 audit(1749776987.032:939): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:persist_sysui_builder_extras_prop:s0" dev="tmpfs" ino=254 scontext=u:r:odrefresh:s0 tcontext=u:object_r:persist_sysui_builder_extras_prop:s0 tclass=file permissive=1
[   11.106033] [  T287] type=1400 audit(1749776987.032:940): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:persist_sysui_builder_extras_prop:s0" dev="tmpfs" ino=254 scontext=u:r:odrefresh:s0 tcontext=u:object_r:persist_sysui_builder_extras_prop:s0 tclass=file permissive=1
[   11.106090] [  T287] type=1400 audit(1749776987.032:941): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:persist_vendor_debug_wifi_prop:s0" dev="tmpfs" ino=255 scontext=u:r:odrefresh:s0 tcontext=u:object_r:persist_vendor_debug_wifi_prop:s0 tclass=file permissive=1
[   11.106151] [  T287] type=1400 audit(1749776987.032:942): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:persist_vendor_debug_wifi_prop:s0" dev="tmpfs" ino=255 scontext=u:r:odrefresh:s0 tcontext=u:object_r:persist_vendor_debug_wifi_prop:s0 tclass=file permissive=1
[   11.106208] [  T287] type=1400 audit(1749776987.032:943): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:persist_vendor_debug_wifi_prop:s0" dev="tmpfs" ino=255 scontext=u:r:odrefresh:s0 tcontext=u:object_r:persist_vendor_debug_wifi_prop:s0 tclass=file permissive=1
[   11.106265] [  T287] type=1400 audit(1749776987.032:944): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:persist_wm_debug_prop:s0" dev="tmpfs" ino=256 scontext=u:r:odrefresh:s0 tcontext=u:object_r:persist_wm_debug_prop:s0 tclass=file permissive=1
[   11.106321] [  T287] type=1400 audit(1749776987.032:945): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:persist_wm_debug_prop:s0" dev="tmpfs" ino=256 scontext=u:r:odrefresh:s0 tcontext=u:object_r:persist_wm_debug_prop:s0 tclass=file permissive=1
[   11.106382] [  T287] type=1400 audit(1749776987.032:946): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:persist_wm_debug_prop:s0" dev="tmpfs" ino=256 scontext=u:r:odrefresh:s0 tcontext=u:object_r:persist_wm_debug_prop:s0 tclass=file permissive=1
[   11.106444] [  T287] type=1400 audit(1749776987.032:947): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:persistent_properties_ready_prop:s0" dev="tmpfs" ino=257 scontext=u:r:odrefresh:s0 tcontext=u:object_r:persistent_properties_ready_prop:s0 tclass=file permissive=1
[   11.106503] [  T287] type=1400 audit(1749776987.032:948): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:persistent_properties_ready_prop:s0" dev="tmpfs" ino=257 scontext=u:r:odrefresh:s0 tcontext=u:object_r:persistent_properties_ready_prop:s0 tclass=file permissive=1
[   11.106560] [  T287] type=1400 audit(1749776987.032:949): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:persistent_properties_ready_prop:s0" dev="tmpfs" ino=257 scontext=u:r:odrefresh:s0 tcontext=u:object_r:persistent_properties_ready_prop:s0 tclass=file permissive=1
[   11.106617] [  T287] type=1400 audit(1749776987.032:950): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:power_debug_prop:s0" dev="tmpfs" ino=259 scontext=u:r:odrefresh:s0 tcontext=u:object_r:power_debug_prop:s0 tclass=file permissive=1
[   11.106673] [  T287] type=1400 audit(1749776987.032:951): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:power_debug_prop:s0" dev="tmpfs" ino=259 scontext=u:r:odrefresh:s0 tcontext=u:object_r:power_debug_prop:s0 tclass=file permissive=1
[   11.106728] [  T287] type=1400 audit(1749776987.032:952): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:power_debug_prop:s0" dev="tmpfs" ino=259 scontext=u:r:odrefresh:s0 tcontext=u:object_r:power_debug_prop:s0 tclass=file permissive=1
[   11.106785] [  T287] type=1400 audit(1749776987.032:953): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:profcollectd_node_id_prop:s0" dev="tmpfs" ino=261 scontext=u:r:odrefresh:s0 tcontext=u:object_r:profcollectd_node_id_prop:s0 tclass=file permissive=1
[   11.106842] [  T287] type=1400 audit(1749776987.032:954): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:profcollectd_node_id_prop:s0" dev="tmpfs" ino=261 scontext=u:r:odrefresh:s0 tcontext=u:object_r:profcollectd_node_id_prop:s0 tclass=file permissive=1
[   11.106899] [  T287] type=1400 audit(1749776987.032:955): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:profcollectd_node_id_prop:s0" dev="tmpfs" ino=261 scontext=u:r:odrefresh:s0 tcontext=u:object_r:profcollectd_node_id_prop:s0 tclass=file permissive=1
[   11.106955] [  T287] type=1400 audit(1749776987.032:956): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:provisioned_prop:s0" dev="tmpfs" ino=263 scontext=u:r:odrefresh:s0 tcontext=u:object_r:provisioned_prop:s0 tclass=file permissive=1
[   11.107011] [  T287] type=1400 audit(1749776987.032:957): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:provisioned_prop:s0" dev="tmpfs" ino=263 scontext=u:r:odrefresh:s0 tcontext=u:object_r:provisioned_prop:s0 tclass=file permissive=1
[   11.107067] [  T287] type=1400 audit(1749776987.032:958): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:provisioned_prop:s0" dev="tmpfs" ino=263 scontext=u:r:odrefresh:s0 tcontext=u:object_r:provisioned_prop:s0 tclass=file permissive=1
[   11.107122] [  T287] type=1400 audit(1749776987.032:959): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:qemu_hw_prop:s0" dev="tmpfs" ino=264 scontext=u:r:odrefresh:s0 tcontext=u:object_r:qemu_hw_prop:s0 tclass=file permissive=1
[   11.107178] [  T287] type=1400 audit(1749776987.032:960): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:qemu_hw_prop:s0" dev="tmpfs" ino=264 scontext=u:r:odrefresh:s0 tcontext=u:object_r:qemu_hw_prop:s0 tclass=file permissive=1
[   11.107234] [  T287] type=1400 audit(1749776987.032:961): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:qemu_hw_prop:s0" dev="tmpfs" ino=264 scontext=u:r:odrefresh:s0 tcontext=u:object_r:qemu_hw_prop:s0 tclass=file permissive=1
[   11.107298] [  T287] type=1400 audit(1749776987.036:962): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:qemu_sf_lcd_density_prop:s0" dev="tmpfs" ino=265 scontext=u:r:odrefresh:s0 tcontext=u:object_r:qemu_sf_lcd_density_prop:s0 tclass=file permissive=1
[   11.107361] [  T287] type=1400 audit(1749776987.036:963): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:qemu_sf_lcd_density_prop:s0" dev="tmpfs" ino=265 scontext=u:r:odrefresh:s0 tcontext=u:object_r:qemu_sf_lcd_density_prop:s0 tclass=file permissive=1
[   11.107418] [  T287] type=1400 audit(1749776987.036:964): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:qemu_sf_lcd_density_prop:s0" dev="tmpfs" ino=265 scontext=u:r:odrefresh:s0 tcontext=u:object_r:qemu_sf_lcd_density_prop:s0 tclass=file permissive=1
[   11.107475] [  T287] type=1400 audit(1749776987.036:965): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:quick_start_prop:s0" dev="tmpfs" ino=266 scontext=u:r:odrefresh:s0 tcontext=u:object_r:quick_start_prop:s0 tclass=file permissive=1
[   11.107531] [  T287] type=1400 audit(1749776987.036:966): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:quick_start_prop:s0" dev="tmpfs" ino=266 scontext=u:r:odrefresh:s0 tcontext=u:object_r:quick_start_prop:s0 tclass=file permissive=1
[   11.107587] [  T287] type=1400 audit(1749776987.036:967): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:quick_start_prop:s0" dev="tmpfs" ino=266 scontext=u:r:odrefresh:s0 tcontext=u:object_r:quick_start_prop:s0 tclass=file permissive=1
[   11.107642] [  T287] type=1400 audit(1749776987.036:968): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:radio_cdma_ecm_prop:s0" dev="tmpfs" ino=267 scontext=u:r:odrefresh:s0 tcontext=u:object_r:radio_cdma_ecm_prop:s0 tclass=file permissive=1
[   11.107698] [  T287] type=1400 audit(1749776987.036:969): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:radio_cdma_ecm_prop:s0" dev="tmpfs" ino=267 scontext=u:r:odrefresh:s0 tcontext=u:object_r:radio_cdma_ecm_prop:s0 tclass=file permissive=1
[   11.107754] [  T287] type=1400 audit(1749776987.036:970): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:radio_cdma_ecm_prop:s0" dev="tmpfs" ino=267 scontext=u:r:odrefresh:s0 tcontext=u:object_r:radio_cdma_ecm_prop:s0 tclass=file permissive=1
[   11.107810] [  T287] type=1400 audit(1749776987.036:971): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:rebootescrow_hal_prop:s0" dev="tmpfs" ino=270 scontext=u:r:odrefresh:s0 tcontext=u:object_r:rebootescrow_hal_prop:s0 tclass=file permissive=1
[   11.107866] [  T287] type=1400 audit(1749776987.036:972): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:rebootescrow_hal_prop:s0" dev="tmpfs" ino=270 scontext=u:r:odrefresh:s0 tcontext=u:object_r:rebootescrow_hal_prop:s0 tclass=file permissive=1
[   11.107922] [  T287] type=1400 audit(1749776987.036:973): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:rebootescrow_hal_prop:s0" dev="tmpfs" ino=270 scontext=u:r:odrefresh:s0 tcontext=u:object_r:rebootescrow_hal_prop:s0 tclass=file permissive=1
[   11.107977] [  T287] type=1400 audit(1749776987.036:974): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:recovery_config_prop:s0" dev="tmpfs" ino=271 scontext=u:r:odrefresh:s0 tcontext=u:object_r:recovery_config_prop:s0 tclass=file permissive=1
[   11.108033] [  T287] type=1400 audit(1749776987.036:975): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:recovery_config_prop:s0" dev="tmpfs" ino=271 scontext=u:r:odrefresh:s0 tcontext=u:object_r:recovery_config_prop:s0 tclass=file permissive=1
[   11.108089] [  T287] type=1400 audit(1749776987.036:976): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:recovery_config_prop:s0" dev="tmpfs" ino=271 scontext=u:r:odrefresh:s0 tcontext=u:object_r:recovery_config_prop:s0 tclass=file permissive=1
[   11.108145] [  T287] type=1400 audit(1749776987.036:977): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:recovery_usb_config_prop:s0" dev="tmpfs" ino=272 scontext=u:r:odrefresh:s0 tcontext=u:object_r:recovery_usb_config_prop:s0 tclass=file permissive=1
[   11.108202] [  T287] type=1400 audit(1749776987.036:978): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:recovery_usb_config_prop:s0" dev="tmpfs" ino=272 scontext=u:r:odrefresh:s0 tcontext=u:object_r:recovery_usb_config_prop:s0 tclass=file permissive=1
[   11.108259] [  T287] type=1400 audit(1749776987.036:979): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:recovery_usb_config_prop:s0" dev="tmpfs" ino=272 scontext=u:r:odrefresh:s0 tcontext=u:object_r:recovery_usb_config_prop:s0 tclass=file permissive=1
[   11.108315] [  T287] type=1400 audit(1749776987.036:980): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:remote_prov_prop:s0" dev="tmpfs" ino=273 scontext=u:r:odrefresh:s0 tcontext=u:object_r:remote_prov_prop:s0 tclass=file permissive=1
[   11.108371] [  T287] type=1400 audit(1749776987.036:981): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:remote_prov_prop:s0" dev="tmpfs" ino=273 scontext=u:r:odrefresh:s0 tcontext=u:object_r:remote_prov_prop:s0 tclass=file permissive=1
[   11.108426] [  T287] type=1400 audit(1749776987.036:982): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:remote_prov_prop:s0" dev="tmpfs" ino=273 scontext=u:r:odrefresh:s0 tcontext=u:object_r:remote_prov_prop:s0 tclass=file permissive=1
[   11.108482] [  T287] type=1400 audit(1749776987.036:983): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:retaildemo_prop:s0" dev="tmpfs" ino=275 scontext=u:r:odrefresh:s0 tcontext=u:object_r:retaildemo_prop:s0 tclass=file permissive=1
[   11.108538] [  T287] type=1400 audit(1749776987.036:984): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:retaildemo_prop:s0" dev="tmpfs" ino=275 scontext=u:r:odrefresh:s0 tcontext=u:object_r:retaildemo_prop:s0 tclass=file permissive=1
[   11.108593] [  T287] type=1400 audit(1749776987.036:985): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:retaildemo_prop:s0" dev="tmpfs" ino=275 scontext=u:r:odrefresh:s0 tcontext=u:object_r:retaildemo_prop:s0 tclass=file permissive=1
[   11.108648] [  T287] type=1400 audit(1749776987.036:986): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:safemode_prop:s0" dev="tmpfs" ino=277 scontext=u:r:odrefresh:s0 tcontext=u:object_r:safemode_prop:s0 tclass=file permissive=1
[   11.108704] [  T287] type=1400 audit(1749776987.036:987): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:safemode_prop:s0" dev="tmpfs" ino=277 scontext=u:r:odrefresh:s0 tcontext=u:object_r:safemode_prop:s0 tclass=file permissive=1
[   11.108760] [  T287] type=1400 audit(1749776987.036:988): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:safemode_prop:s0" dev="tmpfs" ino=277 scontext=u:r:odrefresh:s0 tcontext=u:object_r:safemode_prop:s0 tclass=file permissive=1
[   11.108816] [  T287] type=1400 audit(1749776987.036:989): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:sendbug_config_prop:s0" dev="tmpfs" ino=278 scontext=u:r:odrefresh:s0 tcontext=u:object_r:sendbug_config_prop:s0 tclass=file permissive=1
[   11.108872] [  T287] type=1400 audit(1749776987.036:990): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:sendbug_config_prop:s0" dev="tmpfs" ino=278 scontext=u:r:odrefresh:s0 tcontext=u:object_r:sendbug_config_prop:s0 tclass=file permissive=1
[   11.108927] [  T287] type=1400 audit(1749776987.036:991): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:sendbug_config_prop:s0" dev="tmpfs" ino=278 scontext=u:r:odrefresh:s0 tcontext=u:object_r:sendbug_config_prop:s0 tclass=file permissive=1
[   11.109136] [  T287] type=1400 audit(1749776987.036:992): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:serialno_prop:s0" dev="tmpfs" ino=279 scontext=u:r:odrefresh:s0 tcontext=u:object_r:serialno_prop:s0 tclass=file permissive=1
[   11.109222] [  T287] type=1400 audit(1749776987.036:993): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:serialno_prop:s0" dev="tmpfs" ino=279 scontext=u:r:odrefresh:s0 tcontext=u:object_r:serialno_prop:s0 tclass=file permissive=1
[   11.109281] [  T287] type=1400 audit(1749776987.036:994): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:serialno_prop:s0" dev="tmpfs" ino=279 scontext=u:r:odrefresh:s0 tcontext=u:object_r:serialno_prop:s0 tclass=file permissive=1
[   11.109342] [  T287] type=1400 audit(1749776987.036:995): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:smart_idle_maint_enabled_prop:s0" dev="tmpfs" ino=283 scontext=u:r:odrefresh:s0 tcontext=u:object_r:smart_idle_maint_enabled_prop:s0 tclass=file permissive=1
[   11.109401] [  T287] type=1400 audit(1749776987.036:996): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:smart_idle_maint_enabled_prop:s0" dev="tmpfs" ino=283 scontext=u:r:odrefresh:s0 tcontext=u:object_r:smart_idle_maint_enabled_prop:s0 tclass=file permissive=1
[   11.109460] [  T287] type=1400 audit(1749776987.036:997): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:smart_idle_maint_enabled_prop:s0" dev="tmpfs" ino=283 scontext=u:r:odrefresh:s0 tcontext=u:object_r:smart_idle_maint_enabled_prop:s0 tclass=file permissive=1
[   11.109518] [  T287] type=1400 audit(1749776987.036:998): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:snapuserd_prop:s0" dev="tmpfs" ino=284 scontext=u:r:odrefresh:s0 tcontext=u:object_r:snapuserd_prop:s0 tclass=file permissive=1
[   11.109574] [  T287] type=1400 audit(1749776987.036:999): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:snapuserd_prop:s0" dev="tmpfs" ino=284 scontext=u:r:odrefresh:s0 tcontext=u:object_r:snapuserd_prop:s0 tclass=file permissive=1
[   11.109631] [  T287] type=1400 audit(1749776987.036:1000): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:snapuserd_prop:s0" dev="tmpfs" ino=284 scontext=u:r:odrefresh:s0 tcontext=u:object_r:snapuserd_prop:s0 tclass=file permissive=1
[   11.109687] [  T287] type=1400 audit(1749776987.036:1001): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:storage_config_prop:s0" dev="tmpfs" ino=288 scontext=u:r:odrefresh:s0 tcontext=u:object_r:storage_config_prop:s0 tclass=file permissive=1
[   11.109744] [  T287] type=1400 audit(1749776987.036:1002): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:storage_config_prop:s0" dev="tmpfs" ino=288 scontext=u:r:odrefresh:s0 tcontext=u:object_r:storage_config_prop:s0 tclass=file permissive=1
[   11.109800] [  T287] type=1400 audit(1749776987.036:1003): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:storage_config_prop:s0" dev="tmpfs" ino=288 scontext=u:r:odrefresh:s0 tcontext=u:object_r:storage_config_prop:s0 tclass=file permissive=1
[   11.109858] [  T287] type=1400 audit(1749776987.036:1004): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:surfaceflinger_display_prop:s0" dev="tmpfs" ino=291 scontext=u:r:odrefresh:s0 tcontext=u:object_r:surfaceflinger_display_prop:s0 tclass=file permissive=1
[   11.109915] [  T287] type=1400 audit(1749776987.036:1005): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:surfaceflinger_display_prop:s0" dev="tmpfs" ino=291 scontext=u:r:odrefresh:s0 tcontext=u:object_r:surfaceflinger_display_prop:s0 tclass=file permissive=1
[   11.109973] [  T287] type=1400 audit(1749776987.036:1006): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:surfaceflinger_display_prop:s0" dev="tmpfs" ino=291 scontext=u:r:odrefresh:s0 tcontext=u:object_r:surfaceflinger_display_prop:s0 tclass=file permissive=1
[   11.110030] [  T287] type=1400 audit(1749776987.036:1007): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:suspend_prop:s0" dev="tmpfs" ino=293 scontext=u:r:odrefresh:s0 tcontext=u:object_r:suspend_prop:s0 tclass=file permissive=1
[   11.110087] [  T287] type=1400 audit(1749776987.036:1008): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:suspend_prop:s0" dev="tmpfs" ino=293 scontext=u:r:odrefresh:s0 tcontext=u:object_r:suspend_prop:s0 tclass=file permissive=1
[   11.110143] [  T287] type=1400 audit(1749776987.036:1009): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:suspend_prop:s0" dev="tmpfs" ino=293 scontext=u:r:odrefresh:s0 tcontext=u:object_r:suspend_prop:s0 tclass=file permissive=1
[   11.110221] [  T287] type=1400 audit(1749776987.036:1010): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:system_adbd_prop:s0" dev="tmpfs" ino=294 scontext=u:r:odrefresh:s0 tcontext=u:object_r:system_adbd_prop:s0 tclass=file permissive=1
[   11.110279] [  T287] type=1400 audit(1749776987.036:1011): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:system_adbd_prop:s0" dev="tmpfs" ino=294 scontext=u:r:odrefresh:s0 tcontext=u:object_r:system_adbd_prop:s0 tclass=file permissive=1
[   11.110337] [  T287] type=1400 audit(1749776987.036:1012): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:system_adbd_prop:s0" dev="tmpfs" ino=294 scontext=u:r:odrefresh:s0 tcontext=u:object_r:system_adbd_prop:s0 tclass=file permissive=1
[   11.110395] [  T287] type=1400 audit(1749776987.036:1013): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:system_boot_reason_prop:s0" dev="tmpfs" ino=295 scontext=u:r:odrefresh:s0 tcontext=u:object_r:system_boot_reason_prop:s0 tclass=file permissive=1
[   11.110454] [  T287] type=1400 audit(1749776987.036:1014): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:system_boot_reason_prop:s0" dev="tmpfs" ino=295 scontext=u:r:odrefresh:s0 tcontext=u:object_r:system_boot_reason_prop:s0 tclass=file permissive=1
[   11.110513] [  T287] type=1400 audit(1749776987.036:1015): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:system_boot_reason_prop:s0" dev="tmpfs" ino=295 scontext=u:r:odrefresh:s0 tcontext=u:object_r:system_boot_reason_prop:s0 tclass=file permissive=1
[   11.110572] [  T287] type=1400 audit(1749776987.036:1016): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:system_jvmti_agent_prop:s0" dev="tmpfs" ino=296 scontext=u:r:odrefresh:s0 tcontext=u:object_r:system_jvmti_agent_prop:s0 tclass=file permissive=1
[   11.110631] [  T287] type=1400 audit(1749776987.036:1017): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:system_jvmti_agent_prop:s0" dev="tmpfs" ino=296 scontext=u:r:odrefresh:s0 tcontext=u:object_r:system_jvmti_agent_prop:s0 tclass=file permissive=1
[   11.110690] [  T287] type=1400 audit(1749776987.036:1018): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:system_jvmti_agent_prop:s0" dev="tmpfs" ino=296 scontext=u:r:odrefresh:s0 tcontext=u:object_r:system_jvmti_agent_prop:s0 tclass=file permissive=1
[   11.110748] [  T287] type=1400 audit(1749776987.036:1019): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:system_lmk_prop:s0" dev="tmpfs" ino=297 scontext=u:r:odrefresh:s0 tcontext=u:object_r:system_lmk_prop:s0 tclass=file permissive=1
[   11.110806] [  T287] type=1400 audit(1749776987.036:1020): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:system_lmk_prop:s0" dev="tmpfs" ino=297 scontext=u:r:odrefresh:s0 tcontext=u:object_r:system_lmk_prop:s0 tclass=file permissive=1
[   11.110864] [  T287] type=1400 audit(1749776987.036:1021): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:system_lmk_prop:s0" dev="tmpfs" ino=297 scontext=u:r:odrefresh:s0 tcontext=u:object_r:system_lmk_prop:s0 tclass=file permissive=1
[   11.110921] [  T287] type=1400 audit(1749776987.036:1022): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:system_trace_prop:s0" dev="tmpfs" ino=299 scontext=u:r:odrefresh:s0 tcontext=u:object_r:system_trace_prop:s0 tclass=file permissive=1
[   11.110979] [  T287] type=1400 audit(1749776987.036:1023): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:system_trace_prop:s0" dev="tmpfs" ino=299 scontext=u:r:odrefresh:s0 tcontext=u:object_r:system_trace_prop:s0 tclass=file permissive=1
[   11.111036] [  T287] type=1400 audit(1749776987.036:1024): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:system_trace_prop:s0" dev="tmpfs" ino=299 scontext=u:r:odrefresh:s0 tcontext=u:object_r:system_trace_prop:s0 tclass=file permissive=1
[   11.111094] [  T287] type=1400 audit(1749776987.036:1025): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:system_user_mode_emulation_prop:s0" dev="tmpfs" ino=300 scontext=u:r:odrefresh:s0 tcontext=u:object_r:system_user_mode_emulation_prop:s0 tclass=file permissive=1
[   11.111153] [  T287] type=1400 audit(1749776987.036:1026): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:system_user_mode_emulation_prop:s0" dev="tmpfs" ino=300 scontext=u:r:odrefresh:s0 tcontext=u:object_r:system_user_mode_emulation_prop:s0 tclass=file permissive=1
[   11.111212] [  T287] type=1400 audit(1749776987.036:1027): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:system_user_mode_emulation_prop:s0" dev="tmpfs" ino=300 scontext=u:r:odrefresh:s0 tcontext=u:object_r:system_user_mode_emulation_prop:s0 tclass=file permissive=1
[   11.111270] [  T287] type=1400 audit(1749776987.036:1028): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:test_boot_reason_prop:s0" dev="tmpfs" ino=304 scontext=u:r:odrefresh:s0 tcontext=u:object_r:test_boot_reason_prop:s0 tclass=file permissive=1
[   11.111328] [  T287] type=1400 audit(1749776987.036:1029): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:test_boot_reason_prop:s0" dev="tmpfs" ino=304 scontext=u:r:odrefresh:s0 tcontext=u:object_r:test_boot_reason_prop:s0 tclass=file permissive=1
[   11.111386] [  T287] type=1400 audit(1749776987.036:1030): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:test_boot_reason_prop:s0" dev="tmpfs" ino=304 scontext=u:r:odrefresh:s0 tcontext=u:object_r:test_boot_reason_prop:s0 tclass=file permissive=1
[   11.111525] [  T287] type=1400 audit(1749776987.036:1031): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:test_harness_prop:s0" dev="tmpfs" ino=305 scontext=u:r:odrefresh:s0 tcontext=u:object_r:test_harness_prop:s0 tclass=file permissive=1
[   11.111586] [  T287] type=1400 audit(1749776987.036:1032): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:test_harness_prop:s0" dev="tmpfs" ino=305 scontext=u:r:odrefresh:s0 tcontext=u:object_r:test_harness_prop:s0 tclass=file permissive=1
[   11.111643] [  T287] type=1400 audit(1749776987.036:1033): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:test_harness_prop:s0" dev="tmpfs" ino=305 scontext=u:r:odrefresh:s0 tcontext=u:object_r:test_harness_prop:s0 tclass=file permissive=1
[   11.111699] [  T287] type=1400 audit(1749776987.036:1034): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:theme_prop:s0" dev="tmpfs" ino=306 scontext=u:r:odrefresh:s0 tcontext=u:object_r:theme_prop:s0 tclass=file permissive=1
[   11.111756] [  T287] type=1400 audit(1749776987.036:1035): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:theme_prop:s0" dev="tmpfs" ino=306 scontext=u:r:odrefresh:s0 tcontext=u:object_r:theme_prop:s0 tclass=file permissive=1
[   11.111812] [  T287] type=1400 audit(1749776987.036:1036): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:theme_prop:s0" dev="tmpfs" ino=306 scontext=u:r:odrefresh:s0 tcontext=u:object_r:theme_prop:s0 tclass=file permissive=1
[   11.111868] [  T287] type=1400 audit(1749776987.036:1037): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:time_prop:s0" dev="tmpfs" ino=307 scontext=u:r:odrefresh:s0 tcontext=u:object_r:time_prop:s0 tclass=file permissive=1
[   11.111923] [  T287] type=1400 audit(1749776987.036:1038): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:time_prop:s0" dev="tmpfs" ino=307 scontext=u:r:odrefresh:s0 tcontext=u:object_r:time_prop:s0 tclass=file permissive=1
[   11.111979] [  T287] type=1400 audit(1749776987.036:1039): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:time_prop:s0" dev="tmpfs" ino=307 scontext=u:r:odrefresh:s0 tcontext=u:object_r:time_prop:s0 tclass=file permissive=1
[   11.112034] [  T287] type=1400 audit(1749776987.036:1040): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:timezone_metadata_prop:s0" dev="tmpfs" ino=308 scontext=u:r:odrefresh:s0 tcontext=u:object_r:timezone_metadata_prop:s0 tclass=file permissive=1
[   11.112090] [  T287] type=1400 audit(1749776987.036:1041): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:timezone_metadata_prop:s0" dev="tmpfs" ino=308 scontext=u:r:odrefresh:s0 tcontext=u:object_r:timezone_metadata_prop:s0 tclass=file permissive=1
[   11.112146] [  T287] type=1400 audit(1749776987.036:1042): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:timezone_metadata_prop:s0" dev="tmpfs" ino=308 scontext=u:r:odrefresh:s0 tcontext=u:object_r:timezone_metadata_prop:s0 tclass=file permissive=1
[   11.112202] [  T287] type=1400 audit(1749776987.036:1043): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:tombstone_config_prop:s0" dev="tmpfs" ino=310 scontext=u:r:odrefresh:s0 tcontext=u:object_r:tombstone_config_prop:s0 tclass=file permissive=1
[   11.112259] [  T287] type=1400 audit(1749776987.036:1044): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:tombstone_config_prop:s0" dev="tmpfs" ino=310 scontext=u:r:odrefresh:s0 tcontext=u:object_r:tombstone_config_prop:s0 tclass=file permissive=1
[   11.112315] [  T287] type=1400 audit(1749776987.036:1045): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:tombstone_config_prop:s0" dev="tmpfs" ino=310 scontext=u:r:odrefresh:s0 tcontext=u:object_r:tombstone_config_prop:s0 tclass=file permissive=1
[   11.112371] [  T287] type=1400 audit(1749776987.036:1046): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:traced_enabled_prop:s0" dev="tmpfs" ino=311 scontext=u:r:odrefresh:s0 tcontext=u:object_r:traced_enabled_prop:s0 tclass=file permissive=1
[   11.112426] [  T287] type=1400 audit(1749776987.036:1047): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:traced_enabled_prop:s0" dev="tmpfs" ino=311 scontext=u:r:odrefresh:s0 tcontext=u:object_r:traced_enabled_prop:s0 tclass=file permissive=1
[   11.112482] [  T287] type=1400 audit(1749776987.036:1048): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:traced_enabled_prop:s0" dev="tmpfs" ino=311 scontext=u:r:odrefresh:s0 tcontext=u:object_r:traced_enabled_prop:s0 tclass=file permissive=1
[   11.112538] [  T287] type=1400 audit(1749776987.036:1049): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:traced_lazy_prop:s0" dev="tmpfs" ino=312 scontext=u:r:odrefresh:s0 tcontext=u:object_r:traced_lazy_prop:s0 tclass=file permissive=1
[   11.112594] [  T287] type=1400 audit(1749776987.036:1050): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:traced_lazy_prop:s0" dev="tmpfs" ino=312 scontext=u:r:odrefresh:s0 tcontext=u:object_r:traced_lazy_prop:s0 tclass=file permissive=1
[   11.112649] [  T287] type=1400 audit(1749776987.036:1051): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:traced_lazy_prop:s0" dev="tmpfs" ino=312 scontext=u:r:odrefresh:s0 tcontext=u:object_r:traced_lazy_prop:s0 tclass=file permissive=1
[   11.112707] [  T287] type=1400 audit(1749776987.036:1052): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:traced_oome_heap_session_count_prop:s0" dev="tmpfs" ino=313 scontext=u:r:odrefresh:s0 tcontext=u:object_r:traced_oome_heap_session_count_prop:s0 tclass=file permissive=1
[   11.112765] [  T287] type=1400 audit(1749776987.036:1053): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:traced_oome_heap_session_count_prop:s0" dev="tmpfs" ino=313 scontext=u:r:odrefresh:s0 tcontext=u:object_r:traced_oome_heap_session_count_prop:s0 tclass=file permissive=1
[   11.112822] [  T287] type=1400 audit(1749776987.036:1054): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:traced_oome_heap_session_count_prop:s0" dev="tmpfs" ino=313 scontext=u:r:odrefresh:s0 tcontext=u:object_r:traced_oome_heap_session_count_prop:s0 tclass=file permissive=1
[   11.112879] [  T287] type=1400 audit(1749776987.036:1055): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:traced_perf_enabled_prop:s0" dev="tmpfs" ino=314 scontext=u:r:odrefresh:s0 tcontext=u:object_r:traced_perf_enabled_prop:s0 tclass=file permissive=1
[   11.112937] [  T287] type=1400 audit(1749776987.036:1056): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:traced_perf_enabled_prop:s0" dev="tmpfs" ino=314 scontext=u:r:odrefresh:s0 tcontext=u:object_r:traced_perf_enabled_prop:s0 tclass=file permissive=1
[   11.113027] [  T287] type=1400 audit(1749776987.036:1057): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:traced_perf_enabled_prop:s0" dev="tmpfs" ino=314 scontext=u:r:odrefresh:s0 tcontext=u:object_r:traced_perf_enabled_prop:s0 tclass=file permissive=1
[   11.113142] [  T287] type=1400 audit(1749776987.036:1058): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:tuner_config_prop:s0" dev="tmpfs" ino=315 scontext=u:r:odrefresh:s0 tcontext=u:object_r:tuner_config_prop:s0 tclass=file permissive=1
[   11.113201] [  T287] type=1400 audit(1749776987.036:1059): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:tuner_config_prop:s0" dev="tmpfs" ino=315 scontext=u:r:odrefresh:s0 tcontext=u:object_r:tuner_config_prop:s0 tclass=file permissive=1
[   11.113256] [  T287] type=1400 audit(1749776987.036:1060): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:tuner_config_prop:s0" dev="tmpfs" ino=315 scontext=u:r:odrefresh:s0 tcontext=u:object_r:tuner_config_prop:s0 tclass=file permissive=1
[   11.113313] [  T287] type=1400 audit(1749776987.036:1061): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:tuner_server_ctl_prop:s0" dev="tmpfs" ino=316 scontext=u:r:odrefresh:s0 tcontext=u:object_r:tuner_server_ctl_prop:s0 tclass=file permissive=1
[   11.113369] [  T287] type=1400 audit(1749776987.036:1062): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:tuner_server_ctl_prop:s0" dev="tmpfs" ino=316 scontext=u:r:odrefresh:s0 tcontext=u:object_r:tuner_server_ctl_prop:s0 tclass=file permissive=1
[   11.113425] [  T287] type=1400 audit(1749776987.036:1063): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:tuner_server_ctl_prop:s0" dev="tmpfs" ino=316 scontext=u:r:odrefresh:s0 tcontext=u:object_r:tuner_server_ctl_prop:s0 tclass=file permissive=1
[   11.113481] [  T287] type=1400 audit(1749776987.036:1064): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:usb_uvc_enabled_prop:s0" dev="tmpfs" ino=320 scontext=u:r:odrefresh:s0 tcontext=u:object_r:usb_uvc_enabled_prop:s0 tclass=file permissive=1
[   11.113539] [  T287] type=1400 audit(1749776987.036:1065): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:usb_uvc_enabled_prop:s0" dev="tmpfs" ino=320 scontext=u:r:odrefresh:s0 tcontext=u:object_r:usb_uvc_enabled_prop:s0 tclass=file permissive=1
[   11.113595] [  T287] type=1400 audit(1749776987.036:1066): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:usb_uvc_enabled_prop:s0" dev="tmpfs" ino=320 scontext=u:r:odrefresh:s0 tcontext=u:object_r:usb_uvc_enabled_prop:s0 tclass=file permissive=1
[   11.113650] [  T287] type=1400 audit(1749776987.036:1067): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:vehicle_hal_prop:s0" dev="tmpfs" ino=327 scontext=u:r:odrefresh:s0 tcontext=u:object_r:vehicle_hal_prop:s0 tclass=file permissive=1
[   11.113706] [  T287] type=1400 audit(1749776987.036:1068): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:vehicle_hal_prop:s0" dev="tmpfs" ino=327 scontext=u:r:odrefresh:s0 tcontext=u:object_r:vehicle_hal_prop:s0 tclass=file permissive=1
[   11.113761] [  T287] type=1400 audit(1749776987.036:1069): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:vehicle_hal_prop:s0" dev="tmpfs" ino=327 scontext=u:r:odrefresh:s0 tcontext=u:object_r:vehicle_hal_prop:s0 tclass=file permissive=1
[   11.113817] [  T287] type=1400 audit(1749776987.036:1070): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:vendor_base_prop:s0" dev="tmpfs" ino=328 scontext=u:r:odrefresh:s0 tcontext=u:object_r:vendor_base_prop:s0 tclass=file permissive=1
[   11.113873] [  T287] type=1400 audit(1749776987.036:1071): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:vendor_base_prop:s0" dev="tmpfs" ino=328 scontext=u:r:odrefresh:s0 tcontext=u:object_r:vendor_base_prop:s0 tclass=file permissive=1
[   11.113928] [  T287] type=1400 audit(1749776987.036:1072): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:vendor_base_prop:s0" dev="tmpfs" ino=328 scontext=u:r:odrefresh:s0 tcontext=u:object_r:vendor_base_prop:s0 tclass=file permissive=1
[   11.113985] [  T287] type=1400 audit(1749776987.036:1073): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:vendor_boot_complete_prop:s0" dev="tmpfs" ino=329 scontext=u:r:odrefresh:s0 tcontext=u:object_r:vendor_boot_complete_prop:s0 tclass=file permissive=1
[   11.114042] [  T287] type=1400 audit(1749776987.036:1074): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:vendor_boot_complete_prop:s0" dev="tmpfs" ino=329 scontext=u:r:odrefresh:s0 tcontext=u:object_r:vendor_boot_complete_prop:s0 tclass=file permissive=1
[   11.114099] [  T287] type=1400 audit(1749776987.036:1075): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:vendor_boot_complete_prop:s0" dev="tmpfs" ino=329 scontext=u:r:odrefresh:s0 tcontext=u:object_r:vendor_boot_complete_prop:s0 tclass=file permissive=1
[   11.114155] [  T287] type=1400 audit(1749776987.036:1076): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:vendor_camera_prop:s0" dev="tmpfs" ino=330 scontext=u:r:odrefresh:s0 tcontext=u:object_r:vendor_camera_prop:s0 tclass=file permissive=1
[   11.114212] [  T287] type=1400 audit(1749776987.036:1077): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:vendor_camera_prop:s0" dev="tmpfs" ino=330 scontext=u:r:odrefresh:s0 tcontext=u:object_r:vendor_camera_prop:s0 tclass=file permissive=1
[   11.114267] [  T287] type=1400 audit(1749776987.036:1078): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:vendor_camera_prop:s0" dev="tmpfs" ino=330 scontext=u:r:odrefresh:s0 tcontext=u:object_r:vendor_camera_prop:s0 tclass=file permissive=1
[   11.114322] [  T287] type=1400 audit(1749776987.036:1079): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:vendor_cec_prop:s0" dev="tmpfs" ino=331 scontext=u:r:odrefresh:s0 tcontext=u:object_r:vendor_cec_prop:s0 tclass=file permissive=1
[   11.114378] [  T287] type=1400 audit(1749776987.036:1080): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:vendor_cec_prop:s0" dev="tmpfs" ino=331 scontext=u:r:odrefresh:s0 tcontext=u:object_r:vendor_cec_prop:s0 tclass=file permissive=1
[   11.114434] [  T287] type=1400 audit(1749776987.036:1081): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:vendor_cec_prop:s0" dev="tmpfs" ino=331 scontext=u:r:odrefresh:s0 tcontext=u:object_r:vendor_cec_prop:s0 tclass=file permissive=1
[   11.114489] [  T287] type=1400 audit(1749776987.036:1082): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:vendor_czur_prop:s0" dev="tmpfs" ino=332 scontext=u:r:odrefresh:s0 tcontext=u:object_r:vendor_czur_prop:s0 tclass=file permissive=1
[   11.114545] [  T287] type=1400 audit(1749776987.036:1083): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:vendor_czur_prop:s0" dev="tmpfs" ino=332 scontext=u:r:odrefresh:s0 tcontext=u:object_r:vendor_czur_prop:s0 tclass=file permissive=1
[   11.114600] [  T287] type=1400 audit(1749776987.036:1084): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:vendor_czur_prop:s0" dev="tmpfs" ino=332 scontext=u:r:odrefresh:s0 tcontext=u:object_r:vendor_czur_prop:s0 tclass=file permissive=1
[   11.114656] [  T287] type=1400 audit(1749776987.036:1085): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:vendor_default_prop:s0" dev="tmpfs" ino=333 scontext=u:r:odrefresh:s0 tcontext=u:object_r:vendor_default_prop:s0 tclass=file permissive=1
[   11.114712] [  T287] type=1400 audit(1749776987.036:1086): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:vendor_default_prop:s0" dev="tmpfs" ino=333 scontext=u:r:odrefresh:s0 tcontext=u:object_r:vendor_default_prop:s0 tclass=file permissive=1
[   11.114768] [  T287] type=1400 audit(1749776987.036:1087): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:vendor_default_prop:s0" dev="tmpfs" ino=333 scontext=u:r:odrefresh:s0 tcontext=u:object_r:vendor_default_prop:s0 tclass=file permissive=1
[   11.114823] [  T287] type=1400 audit(1749776987.036:1088): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:vendor_hwc_prop:s0" dev="tmpfs" ino=334 scontext=u:r:odrefresh:s0 tcontext=u:object_r:vendor_hwc_prop:s0 tclass=file permissive=1
[   11.114879] [  T287] type=1400 audit(1749776987.036:1089): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:vendor_hwc_prop:s0" dev="tmpfs" ino=334 scontext=u:r:odrefresh:s0 tcontext=u:object_r:vendor_hwc_prop:s0 tclass=file permissive=1
[   11.114934] [  T287] type=1400 audit(1749776987.036:1090): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:vendor_hwc_prop:s0" dev="tmpfs" ino=334 scontext=u:r:odrefresh:s0 tcontext=u:object_r:vendor_hwc_prop:s0 tclass=file permissive=1
[   11.114990] [  T287] type=1400 audit(1749776987.036:1091): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:vendor_omx_prop:s0" dev="tmpfs" ino=335 scontext=u:r:odrefresh:s0 tcontext=u:object_r:vendor_omx_prop:s0 tclass=file permissive=1
[   11.115046] [  T287] type=1400 audit(1749776987.036:1092): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:vendor_omx_prop:s0" dev="tmpfs" ino=335 scontext=u:r:odrefresh:s0 tcontext=u:object_r:vendor_omx_prop:s0 tclass=file permissive=1
[   11.115102] [  T287] type=1400 audit(1749776987.036:1093): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:vendor_omx_prop:s0" dev="tmpfs" ino=335 scontext=u:r:odrefresh:s0 tcontext=u:object_r:vendor_omx_prop:s0 tclass=file permissive=1
[   11.115157] [  T287] type=1400 audit(1749776987.036:1094): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:vendor_power_prop:s0" dev="tmpfs" ino=336 scontext=u:r:odrefresh:s0 tcontext=u:object_r:vendor_power_prop:s0 tclass=file permissive=1
[   11.115213] [  T287] type=1400 audit(1749776987.036:1095): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:vendor_power_prop:s0" dev="tmpfs" ino=336 scontext=u:r:odrefresh:s0 tcontext=u:object_r:vendor_power_prop:s0 tclass=file permissive=1
[   11.115268] [  T287] type=1400 audit(1749776987.036:1096): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:vendor_power_prop:s0" dev="tmpfs" ino=336 scontext=u:r:odrefresh:s0 tcontext=u:object_r:vendor_power_prop:s0 tclass=file permissive=1
[   11.115325] [  T287] type=1400 audit(1749776987.036:1097): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:vendor_security_patch_level_prop:s0" dev="tmpfs" ino=337 scontext=u:r:odrefresh:s0 tcontext=u:object_r:vendor_security_patch_level_prop:s0 tclass=file permissive=1
[   11.115382] [  T287] type=1400 audit(1749776987.036:1098): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:vendor_security_patch_level_prop:s0" dev="tmpfs" ino=337 scontext=u:r:odrefresh:s0 tcontext=u:object_r:vendor_security_patch_level_prop:s0 tclass=file permissive=1
[   11.115440] [  T287] type=1400 audit(1749776987.036:1099): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:vendor_security_patch_level_prop:s0" dev="tmpfs" ino=337 scontext=u:r:odrefresh:s0 tcontext=u:object_r:vendor_security_patch_level_prop:s0 tclass=file permissive=1
[   11.115497] [  T287] type=1400 audit(1749776987.036:1100): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:vendor_system_public_prop:s0" dev="tmpfs" ino=339 scontext=u:r:odrefresh:s0 tcontext=u:object_r:vendor_system_public_prop:s0 tclass=file permissive=1
[   11.115554] [  T287] type=1400 audit(1749776987.036:1101): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:vendor_tv_input_prop:s0" dev="tmpfs" ino=340 scontext=u:r:odrefresh:s0 tcontext=u:object_r:vendor_tv_input_prop:s0 tclass=file permissive=1
[   11.115609] [  T287] type=1400 audit(1749776987.036:1102): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:vendor_usb_prop:s0" dev="tmpfs" ino=342 scontext=u:r:odrefresh:s0 tcontext=u:object_r:vendor_usb_prop:s0 tclass=file permissive=1
[   11.130541] [  T287] type=1400 audit(1749776987.068:1103): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:apexd_prop:s0" dev="tmpfs" ino=66 scontext=u:r:odrefresh:s0 tcontext=u:object_r:apexd_prop:s0 tclass=file permissive=1
[   11.130646] [  T287] type=1400 audit(1749776987.068:1104): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:apexd_prop:s0" dev="tmpfs" ino=66 scontext=u:r:odrefresh:s0 tcontext=u:object_r:apexd_prop:s0 tclass=file permissive=1
[   11.130703] [  T287] type=1400 audit(1749776987.068:1105): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:apexd_prop:s0" dev="tmpfs" ino=66 scontext=u:r:odrefresh:s0 tcontext=u:object_r:apexd_prop:s0 tclass=file permissive=1
[   11.130759] [  T287] type=1400 audit(1749776987.068:1106): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:apk_verity_prop:s0" dev="tmpfs" ino=68 scontext=u:r:odrefresh:s0 tcontext=u:object_r:apk_verity_prop:s0 tclass=file permissive=1
[   11.130815] [  T287] type=1400 audit(1749776987.068:1107): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:apk_verity_prop:s0" dev="tmpfs" ino=68 scontext=u:r:odrefresh:s0 tcontext=u:object_r:apk_verity_prop:s0 tclass=file permissive=1
[   11.130871] [  T287] type=1400 audit(1749776987.068:1108): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:apk_verity_prop:s0" dev="tmpfs" ino=68 scontext=u:r:odrefresh:s0 tcontext=u:object_r:apk_verity_prop:s0 tclass=file permissive=1
[   11.130926] [  T287] type=1400 audit(1749776987.068:1109): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:audio_config_prop:s0" dev="tmpfs" ino=70 scontext=u:r:odrefresh:s0 tcontext=u:object_r:audio_config_prop:s0 tclass=file permissive=1
[   11.130982] [  T287] type=1400 audit(1749776987.068:1110): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:audio_config_prop:s0" dev="tmpfs" ino=70 scontext=u:r:odrefresh:s0 tcontext=u:object_r:audio_config_prop:s0 tclass=file permissive=1
[   11.131700] [  T287] type=1400 audit(1749776987.068:1111): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:audio_config_prop:s0" dev="tmpfs" ino=70 scontext=u:r:odrefresh:s0 tcontext=u:object_r:audio_config_prop:s0 tclass=file permissive=1
[   11.131769] [  T287] type=1400 audit(1749776987.068:1112): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:bootanim_config_prop:s0" dev="tmpfs" ino=80 scontext=u:r:odrefresh:s0 tcontext=u:object_r:bootanim_config_prop:s0 tclass=file permissive=1
[   11.131826] [  T287] type=1400 audit(1749776987.068:1113): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:bootanim_config_prop:s0" dev="tmpfs" ino=80 scontext=u:r:odrefresh:s0 tcontext=u:object_r:bootanim_config_prop:s0 tclass=file permissive=1
[   11.131883] [  T287] type=1400 audit(1749776987.068:1114): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:bootanim_config_prop:s0" dev="tmpfs" ino=80 scontext=u:r:odrefresh:s0 tcontext=u:object_r:bootanim_config_prop:s0 tclass=file permissive=1
[   11.131939] [  T287] type=1400 audit(1749776987.068:1115): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:ctl_odsign_prop:s0" dev="tmpfs" ino=123 scontext=u:r:odrefresh:s0 tcontext=u:object_r:ctl_odsign_prop:s0 tclass=file permissive=1
[   11.131995] [  T287] type=1400 audit(1749776987.068:1116): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:ctl_odsign_prop:s0" dev="tmpfs" ino=123 scontext=u:r:odrefresh:s0 tcontext=u:object_r:ctl_odsign_prop:s0 tclass=file permissive=1
[   11.132050] [  T287] type=1400 audit(1749776987.068:1117): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:ctl_odsign_prop:s0" dev="tmpfs" ino=123 scontext=u:r:odrefresh:s0 tcontext=u:object_r:ctl_odsign_prop:s0 tclass=file permissive=1
[   11.132105] [  T287] type=1400 audit(1749776987.068:1118): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:ctl_restart_prop:s0" dev="tmpfs" ino=124 scontext=u:r:odrefresh:s0 tcontext=u:object_r:ctl_restart_prop:s0 tclass=file permissive=1
[   11.132161] [  T287] type=1400 audit(1749776987.068:1119): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:ctl_restart_prop:s0" dev="tmpfs" ino=124 scontext=u:r:odrefresh:s0 tcontext=u:object_r:ctl_restart_prop:s0 tclass=file permissive=1
[   11.132217] [  T287] type=1400 audit(1749776987.068:1120): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:ctl_restart_prop:s0" dev="tmpfs" ino=124 scontext=u:r:odrefresh:s0 tcontext=u:object_r:ctl_restart_prop:s0 tclass=file permissive=1
[   11.132272] [  T287] type=1400 audit(1749776987.068:1121): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:ctl_rildaemon_prop:s0" dev="tmpfs" ino=125 scontext=u:r:odrefresh:s0 tcontext=u:object_r:ctl_rildaemon_prop:s0 tclass=file permissive=1
[   11.132327] [  T287] type=1400 audit(1749776987.068:1122): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:ctl_rildaemon_prop:s0" dev="tmpfs" ino=125 scontext=u:r:odrefresh:s0 tcontext=u:object_r:ctl_rildaemon_prop:s0 tclass=file permissive=1
[   11.132383] [  T287] type=1400 audit(1749776987.068:1123): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:ctl_rildaemon_prop:s0" dev="tmpfs" ino=125 scontext=u:r:odrefresh:s0 tcontext=u:object_r:ctl_rildaemon_prop:s0 tclass=file permissive=1
[   11.132438] [  T287] type=1400 audit(1749776987.068:1124): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:ctl_sigstop_prop:s0" dev="tmpfs" ino=126 scontext=u:r:odrefresh:s0 tcontext=u:object_r:ctl_sigstop_prop:s0 tclass=file permissive=1
[   11.132494] [  T287] type=1400 audit(1749776987.068:1125): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:ctl_sigstop_prop:s0" dev="tmpfs" ino=126 scontext=u:r:odrefresh:s0 tcontext=u:object_r:ctl_sigstop_prop:s0 tclass=file permissive=1
[   11.132550] [  T287] type=1400 audit(1749776987.068:1126): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:ctl_sigstop_prop:s0" dev="tmpfs" ino=126 scontext=u:r:odrefresh:s0 tcontext=u:object_r:ctl_sigstop_prop:s0 tclass=file permissive=1
[   11.132605] [  T287] type=1400 audit(1749776987.068:1127): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:ctl_snapuserd_prop:s0" dev="tmpfs" ino=127 scontext=u:r:odrefresh:s0 tcontext=u:object_r:ctl_snapuserd_prop:s0 tclass=file permissive=1
[   11.132661] [  T287] type=1400 audit(1749776987.068:1128): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:ctl_snapuserd_prop:s0" dev="tmpfs" ino=127 scontext=u:r:odrefresh:s0 tcontext=u:object_r:ctl_snapuserd_prop:s0 tclass=file permissive=1
[   11.132716] [  T287] type=1400 audit(1749776987.068:1129): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:ctl_snapuserd_prop:s0" dev="tmpfs" ino=127 scontext=u:r:odrefresh:s0 tcontext=u:object_r:ctl_snapuserd_prop:s0 tclass=file permissive=1
[   11.132773] [  T287] type=1400 audit(1749776987.068:1130): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_lmkd_native_prop:s0" dev="tmpfs" ino=146 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_lmkd_native_prop:s0 tclass=file permissive=1
[   11.132830] [  T287] type=1400 audit(1749776987.068:1131): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_lmkd_native_prop:s0" dev="tmpfs" ino=146 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_lmkd_native_prop:s0 tclass=file permissive=1
[   11.132887] [  T287] type=1400 audit(1749776987.068:1132): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_lmkd_native_prop:s0" dev="tmpfs" ino=146 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_lmkd_native_prop:s0 tclass=file permissive=1
[   11.132943] [  T287] type=1400 audit(1749776987.068:1133): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_mglru_native_prop:s0" dev="tmpfs" ino=150 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_mglru_native_prop:s0 tclass=file permissive=1
[   11.133029] [  T287] type=1400 audit(1749776987.068:1134): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_mglru_native_prop:s0" dev="tmpfs" ino=150 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_mglru_native_prop:s0 tclass=file permissive=1
[   11.133086] [  T287] type=1400 audit(1749776987.068:1135): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_mglru_native_prop:s0" dev="tmpfs" ino=150 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_mglru_native_prop:s0 tclass=file permissive=1
[   11.133143] [  T287] type=1400 audit(1749776987.068:1136): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_netd_native_prop:s0" dev="tmpfs" ino=151 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_netd_native_prop:s0 tclass=file permissive=1
[   11.133200] [  T287] type=1400 audit(1749776987.068:1137): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_netd_native_prop:s0" dev="tmpfs" ino=151 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_netd_native_prop:s0 tclass=file permissive=1
[   11.133822] [  T287] type=1400 audit(1749776987.068:1138): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_netd_native_prop:s0" dev="tmpfs" ino=151 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_netd_native_prop:s0 tclass=file permissive=1
[   11.133893] [  T287] type=1400 audit(1749776987.068:1139): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_nnapi_native_prop:s0" dev="tmpfs" ino=152 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_nnapi_native_prop:s0 tclass=file permissive=1
[   11.133952] [  T287] type=1400 audit(1749776987.068:1140): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_nnapi_native_prop:s0" dev="tmpfs" ino=152 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_nnapi_native_prop:s0 tclass=file permissive=1
[   11.134009] [  T287] type=1400 audit(1749776987.068:1141): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_nnapi_native_prop:s0" dev="tmpfs" ino=152 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_nnapi_native_prop:s0 tclass=file permissive=1
[   11.134067] [  T287] type=1400 audit(1749776987.068:1142): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_profcollect_native_boot_prop:s0" dev="tmpfs" ino=153 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_profcollect_native_boot_prop:s0 tclass=file permissive=1
[   11.134125] [  T287] type=1400 audit(1749776987.068:1143): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_profcollect_native_boot_prop:s0" dev="tmpfs" ino=153 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_profcollect_native_boot_prop:s0 tclass=file permissive=1
[   11.134183] [  T287] type=1400 audit(1749776987.068:1144): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_profcollect_native_boot_prop:s0" dev="tmpfs" ino=153 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_profcollect_native_boot_prop:s0 tclass=file permissive=1
[   11.134242] [  T287] type=1400 audit(1749776987.068:1145): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_remote_key_provisioning_native_prop:s0" dev="tmpfs" ino=154 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_remote_key_provisioning_native_prop:s0 tclass=file permissive=1
[   11.134300] [  T287] type=1400 audit(1749776987.068:1146): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_remote_key_provisioning_native_prop:s0" dev="tmpfs" ino=154 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_remote_key_provisioning_native_prop:s0 tclass=file permissive=1
[   11.134357] [  T287] type=1400 audit(1749776987.068:1147): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_remote_key_provisioning_native_prop:s0" dev="tmpfs" ino=154 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_remote_key_provisioning_native_prop:s0 tclass=file permissive=1
[   11.134415] [  T287] type=1400 audit(1749776987.068:1148): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_statsd_native_boot_prop:s0" dev="tmpfs" ino=158 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_statsd_native_boot_prop:s0 tclass=file permissive=1
[   11.134472] [  T287] type=1400 audit(1749776987.068:1149): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_statsd_native_boot_prop:s0" dev="tmpfs" ino=158 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_statsd_native_boot_prop:s0 tclass=file permissive=1
[   11.134529] [  T287] type=1400 audit(1749776987.068:1150): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_statsd_native_boot_prop:s0" dev="tmpfs" ino=158 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_statsd_native_boot_prop:s0 tclass=file permissive=1
[   11.134587] [  T287] type=1400 audit(1749776987.068:1151): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_statsd_native_prop:s0" dev="tmpfs" ino=159 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_statsd_native_prop:s0 tclass=file permissive=1
[   11.134644] [  T287] type=1400 audit(1749776987.068:1152): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_statsd_native_prop:s0" dev="tmpfs" ino=159 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_statsd_native_prop:s0 tclass=file permissive=1
[   11.134702] [  T287] type=1400 audit(1749776987.068:1153): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_statsd_native_prop:s0" dev="tmpfs" ino=159 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_statsd_native_prop:s0 tclass=file permissive=1
[   11.134758] [  T287] type=1400 audit(1749776987.068:1154): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_storage_native_boot_prop:s0" dev="tmpfs" ino=160 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_storage_native_boot_prop:s0 tclass=file permissive=1
[   11.134816] [  T287] type=1400 audit(1749776987.072:1155): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_storage_native_boot_prop:s0" dev="tmpfs" ino=160 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_storage_native_boot_prop:s0 tclass=file permissive=1
[   11.134874] [  T287] type=1400 audit(1749776987.072:1156): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_storage_native_boot_prop:s0" dev="tmpfs" ino=160 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_storage_native_boot_prop:s0 tclass=file permissive=1
[   11.134930] [  T287] type=1400 audit(1749776987.072:1157): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_surface_flinger_native_boot_prop:s0" dev="tmpfs" ino=161 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_surface_flinger_native_boot_prop:s0 tclass=file permissive=1
[   11.134988] [  T287] type=1400 audit(1749776987.072:1158): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_surface_flinger_native_boot_prop:s0" dev="tmpfs" ino=161 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_surface_flinger_native_boot_prop:s0 tclass=file permissive=1
[   11.135045] [  T287] type=1400 audit(1749776987.072:1159): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_surface_flinger_native_boot_prop:s0" dev="tmpfs" ino=161 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_surface_flinger_native_boot_prop:s0 tclass=file permissive=1
[   11.135103] [  T287] type=1400 audit(1749776987.072:1160): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_swcodec_native_prop:s0" dev="tmpfs" ino=162 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_swcodec_native_prop:s0 tclass=file permissive=1
[   11.135159] [  T287] type=1400 audit(1749776987.072:1161): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_swcodec_native_prop:s0" dev="tmpfs" ino=162 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_swcodec_native_prop:s0 tclass=file permissive=1
[   11.135217] [  T287] type=1400 audit(1749776987.072:1162): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_swcodec_native_prop:s0" dev="tmpfs" ino=162 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_swcodec_native_prop:s0 tclass=file permissive=1
[   11.135274] [  T287] type=1400 audit(1749776987.072:1163): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_tethering_u_or_later_native_prop:s0" dev="tmpfs" ino=164 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_tethering_u_or_later_native_prop:s0 tclass=file permissive=1
[   11.135333] [  T287] type=1400 audit(1749776987.072:1164): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_tethering_u_or_later_native_prop:s0" dev="tmpfs" ino=164 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_tethering_u_or_later_native_prop:s0 tclass=file permissive=1
[   11.135391] [  T287] type=1400 audit(1749776987.072:1165): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_tethering_u_or_later_native_prop:s0" dev="tmpfs" ino=164 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_tethering_u_or_later_native_prop:s0 tclass=file permissive=1
[   11.135448] [  T287] type=1400 audit(1749776987.072:1166): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_vendor_system_native_boot_prop:s0" dev="tmpfs" ino=165 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_vendor_system_native_boot_prop:s0 tclass=file permissive=1
[   11.135507] [  T287] type=1400 audit(1749776987.072:1167): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_vendor_system_native_boot_prop:s0" dev="tmpfs" ino=165 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_vendor_system_native_boot_prop:s0 tclass=file permissive=1
[   11.135564] [  T287] type=1400 audit(1749776987.072:1168): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_vendor_system_native_boot_prop:s0" dev="tmpfs" ino=165 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_vendor_system_native_boot_prop:s0 tclass=file permissive=1
[   11.135622] [  T287] type=1400 audit(1749776987.072:1169): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_virtualization_framework_native_prop:s0" dev="tmpfs" ino=167 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_virtualization_framework_native_prop:s0 tclass=file permissive=1
[   11.135681] [  T287] type=1400 audit(1749776987.072:1170): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_virtualization_framework_native_prop:s0" dev="tmpfs" ino=167 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_virtualization_framework_native_prop:s0 tclass=file permissive=1
[   11.135739] [  T287] type=1400 audit(1749776987.072:1171): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_virtualization_framework_native_prop:s0" dev="tmpfs" ino=167 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_virtualization_framework_native_prop:s0 tclass=file permissive=1
[   11.135797] [  T287] type=1400 audit(1749776987.072:1172): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_window_manager_native_boot_prop:s0" dev="tmpfs" ino=168 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_window_manager_native_boot_prop:s0 tclass=file permissive=1
[   11.135855] [  T287] type=1400 audit(1749776987.072:1173): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_window_manager_native_boot_prop:s0" dev="tmpfs" ino=168 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_window_manager_native_boot_prop:s0 tclass=file permissive=1
[   11.135912] [  T287] type=1400 audit(1749776987.072:1174): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_config_window_manager_native_boot_prop:s0" dev="tmpfs" ino=168 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_config_window_manager_native_boot_prop:s0 tclass=file permissive=1
[   11.135969] [  T287] type=1400 audit(1749776987.072:1175): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_logging_prop:s0" dev="tmpfs" ino=169 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_logging_prop:s0 tclass=file permissive=1
[   11.136024] [  T287] type=1400 audit(1749776987.072:1176): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_logging_prop:s0" dev="tmpfs" ino=169 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_logging_prop:s0 tclass=file permissive=1
[   11.136080] [  T287] type=1400 audit(1749776987.072:1177): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:device_logging_prop:s0" dev="tmpfs" ino=169 scontext=u:r:odrefresh:s0 tcontext=u:object_r:device_logging_prop:s0 tclass=file permissive=1
[   11.136135] [  T287] type=1400 audit(1749776987.072:1178): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:dmesgd_start_prop:s0" dev="tmpfs" ino=171 scontext=u:r:odrefresh:s0 tcontext=u:object_r:dmesgd_start_prop:s0 tclass=file permissive=1
[   11.136191] [  T287] type=1400 audit(1749776987.072:1179): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:dmesgd_start_prop:s0" dev="tmpfs" ino=171 scontext=u:r:odrefresh:s0 tcontext=u:object_r:dmesgd_start_prop:s0 tclass=file permissive=1
[   11.136246] [  T287] type=1400 audit(1749776987.072:1180): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:dmesgd_start_prop:s0" dev="tmpfs" ino=171 scontext=u:r:odrefresh:s0 tcontext=u:object_r:dmesgd_start_prop:s0 tclass=file permissive=1
[   11.136302] [  T287] type=1400 audit(1749776987.072:1181): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:drm_service_config_prop:s0" dev="tmpfs" ino=172 scontext=u:r:odrefresh:s0 tcontext=u:object_r:drm_service_config_prop:s0 tclass=file permissive=1
[   11.136359] [  T287] type=1400 audit(1749776987.072:1182): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:drm_service_config_prop:s0" dev="tmpfs" ino=172 scontext=u:r:odrefresh:s0 tcontext=u:object_r:drm_service_config_prop:s0 tclass=file permissive=1
[   11.136415] [  T287] type=1400 audit(1749776987.072:1183): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:drm_service_config_prop:s0" dev="tmpfs" ino=172 scontext=u:r:odrefresh:s0 tcontext=u:object_r:drm_service_config_prop:s0 tclass=file permissive=1
[   11.136472] [  T287] type=1400 audit(1749776987.072:1184): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:dumpstate_options_prop:s0" dev="tmpfs" ino=173 scontext=u:r:odrefresh:s0 tcontext=u:object_r:dumpstate_options_prop:s0 tclass=file permissive=1
[   11.136528] [  T287] type=1400 audit(1749776987.072:1185): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:dumpstate_options_prop:s0" dev="tmpfs" ino=173 scontext=u:r:odrefresh:s0 tcontext=u:object_r:dumpstate_options_prop:s0 tclass=file permissive=1
[   11.136584] [  T287] type=1400 audit(1749776987.072:1186): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:dumpstate_options_prop:s0" dev="tmpfs" ino=173 scontext=u:r:odrefresh:s0 tcontext=u:object_r:dumpstate_options_prop:s0 tclass=file permissive=1
[   11.136640] [  T287] type=1400 audit(1749776987.072:1187): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:dynamic_system_prop:s0" dev="tmpfs" ino=175 scontext=u:r:odrefresh:s0 tcontext=u:object_r:dynamic_system_prop:s0 tclass=file permissive=1
[   11.136696] [  T287] type=1400 audit(1749776987.072:1188): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:dynamic_system_prop:s0" dev="tmpfs" ino=175 scontext=u:r:odrefresh:s0 tcontext=u:object_r:dynamic_system_prop:s0 tclass=file permissive=1
[   11.136752] [  T287] type=1400 audit(1749776987.072:1189): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:dynamic_system_prop:s0" dev="tmpfs" ino=175 scontext=u:r:odrefresh:s0 tcontext=u:object_r:dynamic_system_prop:s0 tclass=file permissive=1
[   11.136809] [  T287] type=1400 audit(1749776987.072:1190): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:exported_bluetooth_prop:s0" dev="tmpfs" ino=177 scontext=u:r:odrefresh:s0 tcontext=u:object_r:exported_bluetooth_prop:s0 tclass=file permissive=1
[   11.136866] [  T287] type=1400 audit(1749776987.072:1191): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:exported_bluetooth_prop:s0" dev="tmpfs" ino=177 scontext=u:r:odrefresh:s0 tcontext=u:object_r:exported_bluetooth_prop:s0 tclass=file permissive=1
[   11.136923] [  T287] type=1400 audit(1749776987.072:1192): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:exported_bluetooth_prop:s0" dev="tmpfs" ino=177 scontext=u:r:odrefresh:s0 tcontext=u:object_r:exported_bluetooth_prop:s0 tclass=file permissive=1
[   11.137000] [  T287] type=1400 audit(1749776987.072:1193): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:netd_stable_secret_prop:s0" dev="tmpfs" ino=242 scontext=u:r:odrefresh:s0 tcontext=u:object_r:netd_stable_secret_prop:s0 tclass=file permissive=1
[   11.137058] [  T287] type=1400 audit(1749776987.072:1194): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:netd_stable_secret_prop:s0" dev="tmpfs" ino=242 scontext=u:r:odrefresh:s0 tcontext=u:object_r:netd_stable_secret_prop:s0 tclass=file permissive=1
[   11.137117] [  T287] type=1400 audit(1749776987.072:1195): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:netd_stable_secret_prop:s0" dev="tmpfs" ino=242 scontext=u:r:odrefresh:s0 tcontext=u:object_r:netd_stable_secret_prop:s0 tclass=file permissive=1
[   11.137174] [  T287] type=1400 audit(1749776987.072:1196): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:odsign_prop:s0" dev="tmpfs" ino=245 scontext=u:r:odrefresh:s0 tcontext=u:object_r:odsign_prop:s0 tclass=file permissive=1
[   11.137230] [  T287] type=1400 audit(1749776987.072:1197): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:odsign_prop:s0" dev="tmpfs" ino=245 scontext=u:r:odrefresh:s0 tcontext=u:object_r:odsign_prop:s0 tclass=file permissive=1
[   11.137286] [  T287] type=1400 audit(1749776987.072:1198): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:odsign_prop:s0" dev="tmpfs" ino=245 scontext=u:r:odrefresh:s0 tcontext=u:object_r:odsign_prop:s0 tclass=file permissive=1
[   11.137343] [  T287] type=1400 audit(1749776987.072:1199): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:perf_drop_caches_prop:s0" dev="tmpfs" ino=251 scontext=u:r:odrefresh:s0 tcontext=u:object_r:perf_drop_caches_prop:s0 tclass=file permissive=1
[   11.137399] [  T287] type=1400 audit(1749776987.072:1200): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:perf_drop_caches_prop:s0" dev="tmpfs" ino=251 scontext=u:r:odrefresh:s0 tcontext=u:object_r:perf_drop_caches_prop:s0 tclass=file permissive=1
[   11.137455] [  T287] type=1400 audit(1749776987.072:1201): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:perf_drop_caches_prop:s0" dev="tmpfs" ino=251 scontext=u:r:odrefresh:s0 tcontext=u:object_r:perf_drop_caches_prop:s0 tclass=file permissive=1
[   11.137514] [  T287] type=1400 audit(1749776987.072:1202): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:persist_sysui_builder_extras_prop:s0" dev="tmpfs" ino=254 scontext=u:r:odrefresh:s0 tcontext=u:object_r:persist_sysui_builder_extras_prop:s0 tclass=file permissive=1
[   11.137572] [  T287] type=1400 audit(1749776987.072:1203): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:persist_sysui_builder_extras_prop:s0" dev="tmpfs" ino=254 scontext=u:r:odrefresh:s0 tcontext=u:object_r:persist_sysui_builder_extras_prop:s0 tclass=file permissive=1
[   11.137630] [  T287] type=1400 audit(1749776987.072:1204): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:persist_sysui_builder_extras_prop:s0" dev="tmpfs" ino=254 scontext=u:r:odrefresh:s0 tcontext=u:object_r:persist_sysui_builder_extras_prop:s0 tclass=file permissive=1
[   11.137687] [  T287] type=1400 audit(1749776987.072:1205): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:persist_vendor_debug_wifi_prop:s0" dev="tmpfs" ino=255 scontext=u:r:odrefresh:s0 tcontext=u:object_r:persist_vendor_debug_wifi_prop:s0 tclass=file permissive=1
[   11.137744] [  T287] type=1400 audit(1749776987.072:1206): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:persist_vendor_debug_wifi_prop:s0" dev="tmpfs" ino=255 scontext=u:r:odrefresh:s0 tcontext=u:object_r:persist_vendor_debug_wifi_prop:s0 tclass=file permissive=1
[   11.137802] [  T287] type=1400 audit(1749776987.072:1207): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:persist_vendor_debug_wifi_prop:s0" dev="tmpfs" ino=255 scontext=u:r:odrefresh:s0 tcontext=u:object_r:persist_vendor_debug_wifi_prop:s0 tclass=file permissive=1
[   11.137858] [  T287] type=1400 audit(1749776987.072:1208): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:persist_wm_debug_prop:s0" dev="tmpfs" ino=256 scontext=u:r:odrefresh:s0 tcontext=u:object_r:persist_wm_debug_prop:s0 tclass=file permissive=1
[   11.137914] [  T287] type=1400 audit(1749776987.072:1209): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:persist_wm_debug_prop:s0" dev="tmpfs" ino=256 scontext=u:r:odrefresh:s0 tcontext=u:object_r:persist_wm_debug_prop:s0 tclass=file permissive=1
[   11.137969] [  T287] type=1400 audit(1749776987.072:1210): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:persist_wm_debug_prop:s0" dev="tmpfs" ino=256 scontext=u:r:odrefresh:s0 tcontext=u:object_r:persist_wm_debug_prop:s0 tclass=file permissive=1
[   11.138026] [  T287] type=1400 audit(1749776987.072:1211): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:persistent_properties_ready_prop:s0" dev="tmpfs" ino=257 scontext=u:r:odrefresh:s0 tcontext=u:object_r:persistent_properties_ready_prop:s0 tclass=file permissive=1
[   11.138084] [  T287] type=1400 audit(1749776987.072:1212): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:persistent_properties_ready_prop:s0" dev="tmpfs" ino=257 scontext=u:r:odrefresh:s0 tcontext=u:object_r:persistent_properties_ready_prop:s0 tclass=file permissive=1
[   11.138141] [  T287] type=1400 audit(1749776987.072:1213): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:persistent_properties_ready_prop:s0" dev="tmpfs" ino=257 scontext=u:r:odrefresh:s0 tcontext=u:object_r:persistent_properties_ready_prop:s0 tclass=file permissive=1
[   11.138197] [  T287] type=1400 audit(1749776987.072:1214): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:surfaceflinger_display_prop:s0" dev="tmpfs" ino=291 scontext=u:r:odrefresh:s0 tcontext=u:object_r:surfaceflinger_display_prop:s0 tclass=file permissive=1
[   11.138255] [  T287] type=1400 audit(1749776987.072:1215): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:surfaceflinger_display_prop:s0" dev="tmpfs" ino=291 scontext=u:r:odrefresh:s0 tcontext=u:object_r:surfaceflinger_display_prop:s0 tclass=file permissive=1
[   11.138311] [  T287] type=1400 audit(1749776987.072:1216): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:surfaceflinger_display_prop:s0" dev="tmpfs" ino=291 scontext=u:r:odrefresh:s0 tcontext=u:object_r:surfaceflinger_display_prop:s0 tclass=file permissive=1
[   11.138368] [  T287] type=1400 audit(1749776987.072:1217): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:suspend_prop:s0" dev="tmpfs" ino=293 scontext=u:r:odrefresh:s0 tcontext=u:object_r:suspend_prop:s0 tclass=file permissive=1
[   11.138424] [  T287] type=1400 audit(1749776987.072:1218): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:suspend_prop:s0" dev="tmpfs" ino=293 scontext=u:r:odrefresh:s0 tcontext=u:object_r:suspend_prop:s0 tclass=file permissive=1
[   11.138480] [  T287] type=1400 audit(1749776987.072:1219): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:suspend_prop:s0" dev="tmpfs" ino=293 scontext=u:r:odrefresh:s0 tcontext=u:object_r:suspend_prop:s0 tclass=file permissive=1
[   11.138535] [  T287] type=1400 audit(1749776987.072:1220): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:system_adbd_prop:s0" dev="tmpfs" ino=294 scontext=u:r:odrefresh:s0 tcontext=u:object_r:system_adbd_prop:s0 tclass=file permissive=1
[   11.138591] [  T287] type=1400 audit(1749776987.072:1221): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:system_adbd_prop:s0" dev="tmpfs" ino=294 scontext=u:r:odrefresh:s0 tcontext=u:object_r:system_adbd_prop:s0 tclass=file permissive=1
[   11.138646] [  T287] type=1400 audit(1749776987.072:1222): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:system_adbd_prop:s0" dev="tmpfs" ino=294 scontext=u:r:odrefresh:s0 tcontext=u:object_r:system_adbd_prop:s0 tclass=file permissive=1
[   11.138703] [  T287] type=1400 audit(1749776987.072:1223): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:system_boot_reason_prop:s0" dev="tmpfs" ino=295 scontext=u:r:odrefresh:s0 tcontext=u:object_r:system_boot_reason_prop:s0 tclass=file permissive=1
[   11.138761] [  T287] type=1400 audit(1749776987.072:1224): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:system_boot_reason_prop:s0" dev="tmpfs" ino=295 scontext=u:r:odrefresh:s0 tcontext=u:object_r:system_boot_reason_prop:s0 tclass=file permissive=1
[   11.138818] [  T287] type=1400 audit(1749776987.072:1225): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:system_boot_reason_prop:s0" dev="tmpfs" ino=295 scontext=u:r:odrefresh:s0 tcontext=u:object_r:system_boot_reason_prop:s0 tclass=file permissive=1
[   11.138874] [  T287] type=1400 audit(1749776987.072:1226): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:system_jvmti_agent_prop:s0" dev="tmpfs" ino=296 scontext=u:r:odrefresh:s0 tcontext=u:object_r:system_jvmti_agent_prop:s0 tclass=file permissive=1
[   11.138932] [  T287] type=1400 audit(1749776987.072:1227): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:system_jvmti_agent_prop:s0" dev="tmpfs" ino=296 scontext=u:r:odrefresh:s0 tcontext=u:object_r:system_jvmti_agent_prop:s0 tclass=file permissive=1
[   11.138989] [  T287] type=1400 audit(1749776987.072:1228): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:system_jvmti_agent_prop:s0" dev="tmpfs" ino=296 scontext=u:r:odrefresh:s0 tcontext=u:object_r:system_jvmti_agent_prop:s0 tclass=file permissive=1
[   11.139044] [  T287] type=1400 audit(1749776987.072:1229): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:system_lmk_prop:s0" dev="tmpfs" ino=297 scontext=u:r:odrefresh:s0 tcontext=u:object_r:system_lmk_prop:s0 tclass=file permissive=1
[   11.139100] [  T287] type=1400 audit(1749776987.072:1230): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:system_lmk_prop:s0" dev="tmpfs" ino=297 scontext=u:r:odrefresh:s0 tcontext=u:object_r:system_lmk_prop:s0 tclass=file permissive=1
[   11.139155] [  T287] type=1400 audit(1749776987.072:1231): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:system_lmk_prop:s0" dev="tmpfs" ino=297 scontext=u:r:odrefresh:s0 tcontext=u:object_r:system_lmk_prop:s0 tclass=file permissive=1
[   11.139210] [  T287] type=1400 audit(1749776987.076:1232): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:test_boot_reason_prop:s0" dev="tmpfs" ino=304 scontext=u:r:odrefresh:s0 tcontext=u:object_r:test_boot_reason_prop:s0 tclass=file permissive=1
[   11.139267] [  T287] type=1400 audit(1749776987.076:1233): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:test_boot_reason_prop:s0" dev="tmpfs" ino=304 scontext=u:r:odrefresh:s0 tcontext=u:object_r:test_boot_reason_prop:s0 tclass=file permissive=1
[   11.139322] [  T287] type=1400 audit(1749776987.076:1234): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:test_boot_reason_prop:s0" dev="tmpfs" ino=304 scontext=u:r:odrefresh:s0 tcontext=u:object_r:test_boot_reason_prop:s0 tclass=file permissive=1
[   11.139378] [  T287] type=1400 audit(1749776987.076:1235): avc:  denied  { open } for  comm="odrefresh" path="/dev/__properties__/u:object_r:test_harness_prop:s0" dev="tmpfs" ino=305 scontext=u:r:odrefresh:s0 tcontext=u:object_r:test_harness_prop:s0 tclass=file permissive=1
[   11.139434] [  T287] type=1400 audit(1749776987.076:1236): avc:  denied  { getattr } for  comm="odrefresh" path="/dev/__properties__/u:object_r:test_harness_prop:s0" dev="tmpfs" ino=305 scontext=u:r:odrefresh:s0 tcontext=u:object_r:test_harness_prop:s0 tclass=file permissive=1
[   11.139489] [  T287] type=1400 audit(1749776987.076:1237): avc:  denied  { map } for  comm="odrefresh" path="/dev/__properties__/u:object_r:test_harness_prop:s0" dev="tmpfs" ino=305 scontext=u:r:odrefresh:s0 tcontext=u:object_r:test_harness_prop:s0 tclass=file permissive=1
[   11.213318] [  T449] fs-verity: sha256 using implementation "sha256-ce"
[   11.224151] [  T203] init: Wait for property 'odsign.key.done=1' took 511ms
[   11.224533] [    T1] init: starting service 'exec 9 (/system/bin/fsverity_init --lock)'...
[   11.227231] [    T1] init: ... started service 'exec 9 (/system/bin/fsverity_init --lock)' has pid 452
[   11.227285] [    T1] init: SVC_EXEC service 'exec 9 (/system/bin/fsverity_init --lock)' pid 452 (uid 0 gid 0+0 context default) started; waiting...
[   11.227505] [    T1] init: Sending signal 9 to service 'odsign' (pid 449) process group...
[   11.232869] [    T1] libprocessgroup: Successfully killed process cgroup uid 0 pid 449 in 5ms
[   11.233472] [    T1] init: Control message: Processed ctl.stop for 'odsign' from pid: 449 (/system/bin/odsign)
[   11.233578] [    T1] init: Service 'odsign' (pid 449) received signal 9
[   11.239008] [    T1] init: Service 'exec 9 (/system/bin/fsverity_init --lock)' (pid 452) exited with status 0 waiting took 0.012000 seconds
[   11.239054] [    T1] init: Sending signal 9 to service 'exec 9 (/system/bin/fsverity_init --lock)' (pid 452) process group...
[   11.239197] [    T1] libprocessgroup: Successfully killed process cgroup uid 0 pid 452 in 0ms
[   11.240136] [    T1] init: starting service 'apexd-snapshotde'...
[   11.244468] [    T1] init: ... started service 'apexd-snapshotde' has pid 453
[   11.244549] [    T1] init: SVC_EXEC service 'apexd-snapshotde' pid 453 (uid 0 gid 1000+0 context default) started; waiting...
[   11.262259] [  T453] apexd: Snapshot DE subcommand detected
[   11.263731] [  T453] apexd-snapshotde: Marking APEXd as ready
[   11.266351] [    T1] init: Service 'apexd-snapshotde' (pid 453) exited with status 0 waiting took 0.023000 seconds
[   11.266400] [    T1] init: Sending signal 9 to service 'apexd-snapshotde' (pid 453) process group...
[   11.266546] [    T1] libprocessgroup: Successfully killed process cgroup uid 0 pid 453 in 0ms
[   11.279106] [    T1] init: Command 'verity_update_state' action=post-fs-data (/system/etc/init/hw/init.rc:1068) took 0ms and failed: fs_mgr_load_verity_state() failed
[   11.279184] [    T1] init: processing action (post-fs-data) from (/system/etc/init/hw/init.usb.rc:6)
[   11.281480] [    T1] init: Encryption policy of /data/adb set to f108e729abe705dc9c1118a9cab78588 v2 modes 1/4 flags 0x2
[   11.281576] [    T1] init: processing action (post-fs-data) from (/vendor/etc/init/hw/init.rk30board.rc:9)
[   11.287151] [    T1] init: processing action (post-fs-data) from (/system/etc/init/bootstat.rc:9)
[   11.288779] [    T1] init: processing action (post-fs-data) from (/system/etc/init/dumpstate.rc:40)
[   11.289577] [    T1] init: starting service 'dump_kernel_panic'...
[   11.290071] [    T1] init: Created socket '/dev/socket/dumpstate', mode 660, user 2000, group 1007
[   11.294950] [    T1] init: ... started service 'dump_kernel_panic' has pid 454
[   11.295046] [    T1] init: processing action (post-fs-data) from (/system/etc/init/gsid.rc:17)
[   11.295967] [    T1] init: Not setting encryption policy on: /data/gsi
[   11.299017] [    T1] init: processing action (post-fs-data) from (/system/etc/init/incidentd.rc:21)
[   11.299928] [    T1] init: processing action (post-fs-data) from (/system/etc/init/init.rockchip.rc:1)
[   11.300818] [    T1] init: Verified that /data/misc/cfg_rockchip has the encryption policy f108e729abe705dc9c1118a9cab78588 v2 modes 1/4 flags 0x2
[   11.301911] [    T1] init: Verified that /data/vendor/rkalgo has the encryption policy f108e729abe705dc9c1118a9cab78588 v2 modes 1/4 flags 0x2
[   11.301999] [    T1] init: processing action (post-fs-data) from (/system/etc/init/perfetto.rc:73)
[   11.302078] [    T1] init: Command 'rm /data/misc/perfetto-traces/.guardraildata' action=post-fs-data (/system/etc/init/perfetto.rc:74) took 0ms and failed: unlink() failed: No such file or directory
[   11.302109] [    T1] init: processing action (post-fs-data) from (/system/etc/init/recovery-persist.rc:1)
[   11.302475] [    T1] init: starting service 'exec 10 (/system/bin/recovery-persist)'...
[   11.306541] [    T1] init: ... started service 'exec 10 (/system/bin/recovery-persist)' has pid 455
[   11.306669] [    T1] init: processing action (post-fs-data) from (/system/etc/init/wifi.rc:18)
[   11.308218] [    T1] selinux: SELinux: Skipping restorecon on directory(/data/misc/apexdata/com.android.wifi)
[   11.308353] [    T1] init: processing action (post-fs-data) from (/vendor/etc/init/hostapd.android.rc:9)
[   11.311203] [    T1] init: processing action (sys.init.perf_lsm_hooks=1 && load_bpf_programs) from (/system/etc/init/hw/init.rc:1253)
[   11.311831] [    T1] init: processing action (load_bpf_programs) from (/system/etc/init/bpfloader.rc:17)
[   11.312157] [    T1] init: starting service 'bpfloader'...
[   11.317175] [    T1] init: ... started service 'bpfloader' has pid 456
[   11.317249] [    T1] init: SVC_EXEC service 'bpfloader' pid 456 (uid 0 gid 0+7 context default) started; waiting...
[   11.319280] [    T1] init: Service 'exec 10 (/system/bin/recovery-persist)' (pid 455) exited with status 0 oneshot service took 0.014000 seconds in background
[   11.319307] [    T1] init: Sending signal 9 to service 'exec 10 (/system/bin/recovery-persist)' (pid 455) process group...
[   11.319481] [    T1] libprocessgroup: Successfully killed process cgroup uid 1000 pid 455 in 0ms
[   11.334254] [    T1] init: Service 'dump_kernel_panic' (pid 454) exited with status 0 oneshot service took 0.041000 seconds in background
[   11.334307] [    T1] init: Sending signal 9 to service 'dump_kernel_panic' (pid 454) process group...
[   11.334439] [    T1] libprocessgroup: Successfully killed process cgroup uid 0 pid 454 in 0ms
[   11.335226] [  T456] WARNING: Unprivileged eBPF is enabled, data leaks possible via Spectre v2 BHB attacks!
[   11.336155] [  T456] LibBpfLoader: Loading optional ELF object /apex/com.android.tethering/etc/bpf/test.o with license Apache 2.0
[   11.336199] [  T456] LibBpfLoader: Section bpfloader_min_ver value is 2 [0x2]
[   11.336244] [  T456] LibBpfLoader: Section bpfloader_max_ver value is 25 [0x19]
[   11.336281] [  T456] LibBpfLoader: Couldn't find section bpfloader_min_required_ver (defaulting to 0 [0x0]).
[   11.336318] [  T456] LibBpfLoader: Section size_of_bpf_map_def value is 120 [0x78]
[   11.336359] [  T456] LibBpfLoader: Section size_of_bpf_prog_def value is 92 [0x5c]
[   11.336369] [  T456] LibBpfLoader: BpfLoader version 0x00026 ignoring ELF object /apex/com.android.tethering/etc/bpf/test.o with max ver 0x00019
[   11.336390] [  T456] bpfloader: Loaded object: /apex/com.android.tethering/etc/bpf/test.o
[   11.336956] [  T456] LibBpfLoader: Loading critical for Connectivity (Tethering) ELF object /apex/com.android.tethering/etc/bpf/offload.o with license Apache 2.0
[   11.337069] [  T456] LibBpfLoader: Section bpfloader_min_ver value is 2 [0x2]
[   11.337125] [  T456] LibBpfLoader: Section bpfloader_max_ver value is 25 [0x19]
[   11.337171] [  T456] LibBpfLoader: Couldn't find section bpfloader_min_required_ver (defaulting to 0 [0x0]).
[   11.337219] [  T456] LibBpfLoader: Section size_of_bpf_map_def value is 120 [0x78]
[   11.337270] [  T456] LibBpfLoader: Section size_of_bpf_prog_def value is 92 [0x5c]
[   11.337279] [  T456] LibBpfLoader: BpfLoader version 0x00026 ignoring ELF object /apex/com.android.tethering/etc/bpf/offload.o with max ver 0x00019
[   11.337304] [  T456] bpfloader: Loaded object: /apex/com.android.tethering/etc/bpf/offload.o
[   11.337509] [  T456] LibBpfLoader: Loading optional ELF object /apex/com.android.tethering/etc/bpf/test@btf.o with license Apache 2.0
[   11.337547] [  T456] LibBpfLoader: Section bpfloader_min_ver value is 25 [0x19]
[   11.337588] [  T456] LibBpfLoader: Section bpfloader_max_ver value is 65536 [0x10000]
[   11.337623] [  T456] LibBpfLoader: Couldn't find section bpfloader_min_required_ver (defaulting to 0 [0x0]).
[   11.337658] [  T456] LibBpfLoader: Section size_of_bpf_map_def value is 120 [0x78]
[   11.337697] [  T456] LibBpfLoader: Section size_of_bpf_prog_def value is 92 [0x5c]
[   11.337707] [  T456] LibBpfLoader: BpfLoader version 0x00026 processing ELF object /apex/com.android.tethering/etc/bpf/test@btf.o with ver [0x00019,0x10000)
[   11.338070] [  T456] LibBpfLoader: Loaded code section 3 (xdp_drop_ipv4_udp_ether)
[   11.338232] [  T456] LibBpfLoader: Adding section 3 to cs list
[   11.338671] [  T456] LibBpfLoader: Couldn't find section btf_min_bpfloader_ver (defaulting to 0 [0x0]).
[   11.338703] [  T456] LibBpfLoader: Couldn't find section btf_min_kernel_ver (defaulting to 0 [0x0]).
[   11.350442] [  T456] LibBpfLoader: bpf_create_map name tether_downstream6_map, ret: 6
[   11.350538] [  T456] LibBpfLoader: map /sys/fs/bpf/tethering/map_test_tether_downstream6_map id 1
[   11.350575] [  T456] LibBpfLoader: bpf_create_map name bitmap, ret: 8
[   11.350623] [  T456] LibBpfLoader: map /sys/fs/bpf/tethering/map_test_bitmap id 2
[   11.350648] [  T456] LibBpfLoader: map_fd found at 0 is 6 in /apex/com.android.tethering/etc/bpf/test@btf.o
[   11.350654] [  T456] LibBpfLoader: map_fd found at 1 is 8 in /apex/com.android.tethering/etc/bpf/test@btf.o
[   11.350845] [  T456] LibBpfLoader: cs[0].name:xdp_drop_ipv4_udp_ether min_kver:5090000 .max_kver:ffffffff (kvers:601004b)
[   11.350852] [  T456] LibBpfLoader: cs[0].name:xdp_drop_ipv4_udp_ether requires bpfloader version [0x00019,0x10000)
[   11.351840] [  T456] LibBpfLoader: bpf_prog_load lib call for /apex/com.android.tethering/etc/bpf/test@btf.o (xdp_drop_ipv4_udp_ether) returned fd: 7 (no error)
[   11.352054] [  T456] LibBpfLoader: prog /sys/fs/bpf/tethering/prog_test_xdp_drop_ipv4_udp_ether id 1
[   11.352114] [  T456] bpfloader: Loaded object: /apex/com.android.tethering/etc/bpf/test@btf.o
[   11.352691] [  T456] LibBpfLoader: Loading critical for Connectivity (Tethering) ELF object /apex/com.android.tethering/etc/bpf/offload@btf.o with license Apache 2.0
[   11.352739] [  T456] LibBpfLoader: Section bpfloader_min_ver value is 25 [0x19]
[   11.352787] [  T456] LibBpfLoader: Section bpfloader_max_ver value is 65536 [0x10000]
[   11.352830] [  T456] LibBpfLoader: Couldn't find section bpfloader_min_required_ver (defaulting to 0 [0x0]).
[   11.352874] [  T456] LibBpfLoader: Section size_of_bpf_map_def value is 120 [0x78]
[   11.352921] [  T456] LibBpfLoader: Section size_of_bpf_prog_def value is 92 [0x5c]
[   11.352931] [  T456] LibBpfLoader: BpfLoader version 0x00026 processing ELF object /apex/com.android.tethering/etc/bpf/offload@btf.o with ver [0x00019,0x10000)
[   11.355984] [  T456] LibBpfLoader: Loaded code section 3 (schedcls_tether_downstream6_ether)
[   11.356167] [  T456] LibBpfLoader: Loaded relo section 3 (.relschedcls/tether_downstream6_ether)
[   11.356174] [  T456] LibBpfLoader: Adding section 3 to cs list
[   11.356245] [  T456] LibBpfLoader: Loaded code section 5 (schedcls_tether_upstream6_ether)
[   11.356462] [  T456] LibBpfLoader: Loaded relo section 5 (.relschedcls/tether_upstream6_ether)
[   11.356468] [  T456] LibBpfLoader: Adding section 5 to cs list
[   11.356539] [  T456] LibBpfLoader: Loaded code section 7 (schedcls_tether_downstream6_rawip$4_14)
[   11.356794] [  T456] LibBpfLoader: Loaded relo section 7 (.relschedcls/tether_downstream6_rawip$4_14)
[   11.356806] [  T456] LibBpfLoader: Adding section 7 to cs list
[   11.356884] [  T456] LibBpfLoader: Loaded code section 9 (schedcls_tether_upstream6_rawip$4_14)
[   11.357194] [  T456] LibBpfLoader: Loaded relo section 9 (.relschedcls/tether_upstream6_rawip$4_14)
[   11.357199] [  T456] LibBpfLoader: Adding section 9 to cs list
[   11.357265] [  T456] LibBpfLoader: Loaded code section 11 (schedcls_tether_downstream6_rawip$stub)
[   11.357621] [  T456] LibBpfLoader: Adding section 11 to cs list
[   11.357668] [  T456] LibBpfLoader: Loaded code section 12 (schedcls_tether_upstream6_rawip$stub)
[   11.358008] [  T456] LibBpfLoader: Adding section 12 to cs list
[   11.358137] [  T456] LibBpfLoader: Loaded code section 13 (schedcls_tether_downstream4_rawip$5_8)
[   11.358543] [  T456] LibBpfLoader: Loaded relo section 13 (.relschedcls/tether_downstream4_rawip$5_8)
[   11.358549] [  T456] LibBpfLoader: Adding section 13 to cs list
[   11.358627] [  T456] LibBpfLoader: Loaded code section 15 (schedcls_tether_upstream4_rawip$5_8)
[   11.359047] [  T456] LibBpfLoader: Loaded relo section 15 (.relschedcls/tether_upstream4_rawip$5_8)
[   11.359052] [  T456] LibBpfLoader: Adding section 15 to cs list
[   11.359125] [  T456] LibBpfLoader: Loaded code section 17 (schedcls_tether_downstream4_ether$5_8)
[   11.359580] [  T456] LibBpfLoader: Loaded relo section 17 (.relschedcls/tether_downstream4_ether$5_8)
[   11.359587] [  T456] LibBpfLoader: Adding section 17 to cs list
[   11.359661] [  T456] LibBpfLoader: Loaded code section 19 (schedcls_tether_upstream4_ether$5_8)
[   11.360161] [  T456] LibBpfLoader: Loaded relo section 19 (.relschedcls/tether_upstream4_ether$5_8)
[   11.360167] [  T456] LibBpfLoader: Adding section 19 to cs list
[   11.360242] [  T456] LibBpfLoader: Loaded code section 21 (schedcls_tether_downstream4_rawip$opt)
[   11.360781] [  T456] LibBpfLoader: Loaded relo section 21 (.relschedcls/tether_downstream4_rawip$opt)
[   11.360786] [  T456] LibBpfLoader: Adding section 21 to cs list
[   11.360864] [  T456] LibBpfLoader: Loaded code section 23 (schedcls_tether_upstream4_rawip$opt)
[   11.361466] [  T456] LibBpfLoader: Loaded relo section 23 (.relschedcls/tether_upstream4_rawip$opt)
[   11.361471] [  T456] LibBpfLoader: Adding section 23 to cs list
[   11.361549] [  T456] LibBpfLoader: Loaded code section 25 (schedcls_tether_downstream4_ether$opt)
[   11.362167] [  T456] LibBpfLoader: Loaded relo section 25 (.relschedcls/tether_downstream4_ether$opt)
[   11.362173] [  T456] LibBpfLoader: Adding section 25 to cs list
[   11.362246] [  T456] LibBpfLoader: Loaded code section 27 (schedcls_tether_upstream4_ether$opt)
[   11.362899] [  T456] LibBpfLoader: Loaded relo section 27 (.relschedcls/tether_upstream4_ether$opt)
[   11.362905] [  T456] LibBpfLoader: Adding section 27 to cs list
[   11.362971] [  T456] LibBpfLoader: Loaded code section 29 (schedcls_tether_downstream4_rawip$5_4)
[   11.363668] [  T456] LibBpfLoader: Loaded relo section 29 (.relschedcls/tether_downstream4_rawip$5_4)
[   11.363674] [  T456] LibBpfLoader: Adding section 29 to cs list
[   11.363739] [  T456] LibBpfLoader: Loaded code section 31 (schedcls_tether_upstream4_rawip$5_4)
[   11.364482] [  T456] LibBpfLoader: Loaded relo section 31 (.relschedcls/tether_upstream4_rawip$5_4)
[   11.364487] [  T456] LibBpfLoader: Adding section 31 to cs list
[   11.364553] [  T456] LibBpfLoader: Loaded code section 33 (schedcls_tether_downstream4_rawip$4_14)
[   11.365350] [  T456] LibBpfLoader: Loaded relo section 33 (.relschedcls/tether_downstream4_rawip$4_14)
[   11.365366] [  T456] LibBpfLoader: Adding section 33 to cs list
[   11.365433] [  T456] LibBpfLoader: Loaded code section 35 (schedcls_tether_upstream4_rawip$4_14)
[   11.366252] [  T456] LibBpfLoader: Loaded relo section 35 (.relschedcls/tether_upstream4_rawip$4_14)
[   11.366257] [  T456] LibBpfLoader: Adding section 35 to cs list
[   11.366322] [  T456] LibBpfLoader: Loaded code section 37 (schedcls_tether_downstream4_ether$4_14)
[   11.367189] [  T456] LibBpfLoader: Loaded relo section 37 (.relschedcls/tether_downstream4_ether$4_14)
[   11.367194] [  T456] LibBpfLoader: Adding section 37 to cs list
[   11.367260] [  T456] LibBpfLoader: Loaded code section 39 (schedcls_tether_upstream4_ether$4_14)
[   11.368165] [  T456] LibBpfLoader: Loaded relo section 39 (.relschedcls/tether_upstream4_ether$4_14)
[   11.368170] [  T456] LibBpfLoader: Adding section 39 to cs list
[   11.368236] [  T456] LibBpfLoader: Loaded code section 41 (schedcls_tether_downstream4_rawip$stub)
[   11.369198] [  T456] LibBpfLoader: Adding section 41 to cs list
[   11.369245] [  T456] LibBpfLoader: Loaded code section 42 (schedcls_tether_upstream4_rawip$stub)
[   11.370180] [  T456] LibBpfLoader: Adding section 42 to cs list
[   11.370224] [  T456] LibBpfLoader: Loaded code section 43 (schedcls_tether_downstream4_ether$stub)
[   11.371179] [  T456] LibBpfLoader: Adding section 43 to cs list
[   11.371222] [  T456] LibBpfLoader: Loaded code section 44 (schedcls_tether_upstream4_ether$stub)
[   11.372196] [  T456] LibBpfLoader: Adding section 44 to cs list
[   11.372241] [  T456] LibBpfLoader: Loaded code section 45 (xdp_tether_downstream_ether)
[   11.373241] [  T456] LibBpfLoader: Adding section 45 to cs list
[   11.373286] [  T456] LibBpfLoader: Loaded code section 46 (xdp_tether_downstream_rawip)
[   11.374300] [  T456] LibBpfLoader: Adding section 46 to cs list
[   11.374344] [  T456] LibBpfLoader: Loaded code section 47 (xdp_tether_upstream_ether)
[   11.375378] [  T456] LibBpfLoader: Adding section 47 to cs list
[   11.375422] [  T456] LibBpfLoader: Loaded code section 48 (xdp_tether_upstream_rawip)
[   11.376475] [  T456] LibBpfLoader: Adding section 48 to cs list
[   11.378264] [  T456] LibBpfLoader: Couldn't find section btf_min_bpfloader_ver (defaulting to 0 [0x0]).
[   11.378305] [  T456] LibBpfLoader: Couldn't find section btf_min_kernel_ver (defaulting to 0 [0x0]).
[   11.389670] [  T456] LibBpfLoader: bpf_create_map name tether_error_map, ret: 6
[   11.389742] [  T456] LibBpfLoader: map /sys/fs/bpf/tethering/map_offload_tether_error_map id 3
[   11.389797] [  T456] LibBpfLoader: bpf_create_map name tether_stats_map, ret: 8
[   11.389839] [  T456] LibBpfLoader: map /sys/fs/bpf/tethering/map_offload_tether_stats_map id 4
[   11.389877] [  T456] LibBpfLoader: bpf_create_map name tether_limit_map, ret: 9
[   11.389918] [  T456] LibBpfLoader: map /sys/fs/bpf/tethering/map_offload_tether_limit_map id 5
[   11.389981] [  T456] LibBpfLoader: bpf_create_map name tether_downstream6_map, ret: 10
[   11.390032] [  T456] LibBpfLoader: map /sys/fs/bpf/tethering/map_offload_tether_downstream6_map id 6
[   11.390159] [  T456] LibBpfLoader: bpf_create_map name tether_downstream64_map, ret: 11
[   11.390211] [  T456] LibBpfLoader: map /sys/fs/bpf/tethering/map_offload_tether_downstream64_map id 7
[   11.390252] [  T456] LibBpfLoader: bpf_create_map name tether_upstream6_map, ret: 12
[   11.390303] [  T456] LibBpfLoader: map /sys/fs/bpf/tethering/map_offload_tether_upstream6_map id 8
[   11.390399] [  T456] LibBpfLoader: bpf_create_map name tether_downstream4_map, ret: 13
[   11.390451] [  T456] LibBpfLoader: map /sys/fs/bpf/tethering/map_offload_tether_downstream4_map id 9
[   11.390551] [  T456] LibBpfLoader: bpf_create_map name tether_upstream4_map, ret: 14
[   11.390599] [  T456] LibBpfLoader: map /sys/fs/bpf/tethering/map_offload_tether_upstream4_map id 10
[   11.405548] [  T456] LibBpfLoader: bpf_create_map name tether_dev_map, ret: 15
[   11.405630] [  T456] LibBpfLoader: map /sys/fs/bpf/tethering/map_offload_tether_dev_map id 11
[   11.405677] [  T456] LibBpfLoader: map_fd found at 0 is 6 in /apex/com.android.tethering/etc/bpf/offload@btf.o
[   11.405683] [  T456] LibBpfLoader: map_fd found at 1 is 8 in /apex/com.android.tethering/etc/bpf/offload@btf.o
[   11.405688] [  T456] LibBpfLoader: map_fd found at 2 is 9 in /apex/com.android.tethering/etc/bpf/offload@btf.o
[   11.405693] [  T456] LibBpfLoader: map_fd found at 3 is 10 in /apex/com.android.tethering/etc/bpf/offload@btf.o
[   11.405698] [  T456] LibBpfLoader: map_fd found at 4 is 11 in /apex/com.android.tethering/etc/bpf/offload@btf.o
[   11.405703] [  T456] LibBpfLoader: map_fd found at 5 is 12 in /apex/com.android.tethering/etc/bpf/offload@btf.o
[   11.405708] [  T456] LibBpfLoader: map_fd found at 6 is 13 in /apex/com.android.tethering/etc/bpf/offload@btf.o
[   11.405713] [  T456] LibBpfLoader: map_fd found at 7 is 14 in /apex/com.android.tethering/etc/bpf/offload@btf.o
[   11.405717] [  T456] LibBpfLoader: map_fd found at 8 is 15 in /apex/com.android.tethering/etc/bpf/offload@btf.o
[   11.421440] [  T456] LibBpfLoader: cs[0].name:schedcls_tether_downstream6_ether min_kver:0 .max_kver:ffffffff (kvers:601004b)
[   11.421450] [  T456] LibBpfLoader: cs[0].name:schedcls_tether_downstream6_ether requires bpfloader version [0x00019,0x10000)
[   11.423046] [  T456] LibBpfLoader: bpf_prog_load lib call for /apex/com.android.tethering/etc/bpf/offload@btf.o (schedcls_tether_downstream6_ether) returned fd: 7 (no error)
[   11.423266] [  T456] LibBpfLoader: prog /sys/fs/bpf/tethering/prog_offload_schedcls_tether_downstream6_ether id 2
[   11.423292] [  T456] LibBpfLoader: cs[1].name:schedcls_tether_upstream6_ether min_kver:0 .max_kver:ffffffff (kvers:601004b)
[   11.423299] [  T456] LibBpfLoader: cs[1].name:schedcls_tether_upstream6_ether requires bpfloader version [0x00019,0x10000)
[   11.424819] [  T456] LibBpfLoader: bpf_prog_load lib call for /apex/com.android.tethering/etc/bpf/offload@btf.o (schedcls_tether_upstream6_ether) returned fd: 16 (no error)
[   11.425053] [  T456] LibBpfLoader: prog /sys/fs/bpf/tethering/prog_offload_schedcls_tether_upstream6_ether id 3
[   11.425087] [  T456] LibBpfLoader: cs[2].name:schedcls_tether_downstream6_rawip$4_14 min_kver:40e0000 .max_kver:ffffffff (kvers:601004b)
[   11.425095] [  T456] LibBpfLoader: cs[2].name:schedcls_tether_downstream6_rawip$4_14 requires bpfloader version [0x00019,0x10000)
[   11.426466] [  T456] LibBpfLoader: bpf_prog_load lib call for /apex/com.android.tethering/etc/bpf/offload@btf.o (schedcls_tether_downstream6_rawip$4_14) returned fd: 17 (no error)
[   11.426657] [  T456] LibBpfLoader: prog /sys/fs/bpf/tethering/prog_offload_schedcls_tether_downstream6_rawip id 4
[   11.426676] [  T456] LibBpfLoader: cs[3].name:schedcls_tether_upstream6_rawip$4_14 min_kver:40e0000 .max_kver:ffffffff (kvers:601004b)
[   11.426683] [  T456] LibBpfLoader: cs[3].name:schedcls_tether_upstream6_rawip$4_14 requires bpfloader version [0x00019,0x10000)
[   11.428027] [  T456] LibBpfLoader: bpf_prog_load lib call for /apex/com.android.tethering/etc/bpf/offload@btf.o (schedcls_tether_upstream6_rawip$4_14) returned fd: 18 (no error)
[   11.428211] [  T456] LibBpfLoader: prog /sys/fs/bpf/tethering/prog_offload_schedcls_tether_upstream6_rawip id 5
[   11.428230] [  T456] LibBpfLoader: cs[4].name:schedcls_tether_downstream6_rawip$stub min_kver:0 .max_kver:40e0000 (kvers:601004b)
[   11.428236] [  T456] LibBpfLoader: cs[5].name:schedcls_tether_upstream6_rawip$stub min_kver:0 .max_kver:40e0000 (kvers:601004b)
[   11.428242] [  T456] LibBpfLoader: cs[6].name:schedcls_tether_downstream4_rawip$5_8 min_kver:5080000 .max_kver:ffffffff (kvers:601004b)
[   11.428249] [  T456] LibBpfLoader: cs[6].name:schedcls_tether_downstream4_rawip$5_8 requires bpfloader version [0x00019,0x10000)
[   11.430600] [  T456] LibBpfLoader: bpf_prog_load lib call for /apex/com.android.tethering/etc/bpf/offload@btf.o (schedcls_tether_downstream4_rawip$5_8) returned fd: 19 (no error)
[   11.430804] [  T456] LibBpfLoader: prog /sys/fs/bpf/tethering/prog_offload_schedcls_tether_downstream4_rawip id 6
[   11.430824] [  T456] LibBpfLoader: cs[7].name:schedcls_tether_upstream4_rawip$5_8 min_kver:5080000 .max_kver:ffffffff (kvers:601004b)
[   11.430832] [  T456] LibBpfLoader: cs[7].name:schedcls_tether_upstream4_rawip$5_8 requires bpfloader version [0x00019,0x10000)
[   11.433175] [  T456] LibBpfLoader: bpf_prog_load lib call for /apex/com.android.tethering/etc/bpf/offload@btf.o (schedcls_tether_upstream4_rawip$5_8) returned fd: 20 (no error)
[   11.433388] [  T456] LibBpfLoader: prog /sys/fs/bpf/tethering/prog_offload_schedcls_tether_upstream4_rawip id 7
[   11.433409] [  T456] LibBpfLoader: cs[8].name:schedcls_tether_downstream4_ether$5_8 min_kver:5080000 .max_kver:ffffffff (kvers:601004b)
[   11.433417] [  T456] LibBpfLoader: cs[8].name:schedcls_tether_downstream4_ether$5_8 requires bpfloader version [0x00019,0x10000)
[   11.435582] [  T456] LibBpfLoader: bpf_prog_load lib call for /apex/com.android.tethering/etc/bpf/offload@btf.o (schedcls_tether_downstream4_ether$5_8) returned fd: 21 (no error)
[   11.435784] [  T456] LibBpfLoader: prog /sys/fs/bpf/tethering/prog_offload_schedcls_tether_downstream4_ether id 8
[   11.435806] [  T456] LibBpfLoader: cs[9].name:schedcls_tether_upstream4_ether$5_8 min_kver:5080000 .max_kver:ffffffff (kvers:601004b)
[   11.435813] [  T456] LibBpfLoader: cs[9].name:schedcls_tether_upstream4_ether$5_8 requires bpfloader version [0x00019,0x10000)
[   11.437968] [  T456] LibBpfLoader: bpf_prog_load lib call for /apex/com.android.tethering/etc/bpf/offload@btf.o (schedcls_tether_upstream4_ether$5_8) returned fd: 22 (no error)
[   11.438187] [  T456] LibBpfLoader: prog /sys/fs/bpf/tethering/prog_offload_schedcls_tether_upstream4_ether id 9
[   11.438208] [  T456] LibBpfLoader: cs[10].name:schedcls_tether_downstream4_rawip$opt min_kver:40e0000 .max_kver:5080000 (kvers:601004b)
[   11.438214] [  T456] LibBpfLoader: cs[11].name:schedcls_tether_upstream4_rawip$opt min_kver:40e0000 .max_kver:5080000 (kvers:601004b)
[   11.438220] [  T456] LibBpfLoader: cs[12].name:schedcls_tether_downstream4_ether$opt min_kver:40e0000 .max_kver:5080000 (kvers:601004b)
[   11.438226] [  T456] LibBpfLoader: cs[13].name:schedcls_tether_upstream4_ether$opt min_kver:40e0000 .max_kver:5080000 (kvers:601004b)
[   11.438232] [  T456] LibBpfLoader: cs[14].name:schedcls_tether_downstream4_rawip$5_4 min_kver:5040000 .max_kver:5080000 (kvers:601004b)
[   11.438238] [  T456] LibBpfLoader: cs[15].name:schedcls_tether_upstream4_rawip$5_4 min_kver:5040000 .max_kver:5080000 (kvers:601004b)
[   11.438244] [  T456] LibBpfLoader: cs[16].name:schedcls_tether_downstream4_rawip$4_14 min_kver:40e0000 .max_kver:5040000 (kvers:601004b)
[   11.438250] [  T456] LibBpfLoader: cs[17].name:schedcls_tether_upstream4_rawip$4_14 min_kver:40e0000 .max_kver:5040000 (kvers:601004b)
[   11.438257] [  T456] LibBpfLoader: cs[18].name:schedcls_tether_downstream4_ether$4_14 min_kver:40e0000 .max_kver:5080000 (kvers:601004b)
[   11.438263] [  T456] LibBpfLoader: cs[19].name:schedcls_tether_upstream4_ether$4_14 min_kver:40e0000 .max_kver:5080000 (kvers:601004b)
[   11.438268] [  T456] LibBpfLoader: cs[20].name:schedcls_tether_downstream4_rawip$stub min_kver:0 .max_kver:5040000 (kvers:601004b)
[   11.438275] [  T456] LibBpfLoader: cs[21].name:schedcls_tether_upstream4_rawip$stub min_kver:0 .max_kver:5040000 (kvers:601004b)
[   11.438280] [  T456] LibBpfLoader: cs[22].name:schedcls_tether_downstream4_ether$stub min_kver:0 .max_kver:40e0000 (kvers:601004b)
[   11.438287] [  T456] LibBpfLoader: cs[23].name:schedcls_tether_upstream4_ether$stub min_kver:0 .max_kver:40e0000 (kvers:601004b)
[   11.438293] [  T456] LibBpfLoader: cs[24].name:xdp_tether_downstream_ether min_kver:5090000 .max_kver:ffffffff (kvers:601004b)
[   11.438300] [  T456] LibBpfLoader: cs[24].name:xdp_tether_downstream_ether requires bpfloader version [0x00019,0x10000)
[   11.439110] [  T456] LibBpfLoader: bpf_prog_load lib call for /apex/com.android.tethering/etc/bpf/offload@btf.o (xdp_tether_downstream_ether) returned fd: 23 (no error)
[   11.439284] [  T456] LibBpfLoader: prog /sys/fs/bpf/tethering/prog_offload_xdp_tether_downstream_ether id 10
[   11.439304] [  T456] LibBpfLoader: cs[25].name:xdp_tether_downstream_rawip min_kver:5090000 .max_kver:ffffffff (kvers:601004b)
[   11.439311] [  T456] LibBpfLoader: cs[25].name:xdp_tether_downstream_rawip requires bpfloader version [0x00019,0x10000)
[   11.440099] [  T456] LibBpfLoader: bpf_prog_load lib call for /apex/com.android.tethering/etc/bpf/offload@btf.o (xdp_tether_downstream_rawip) returned fd: 24 (no error)
[   11.440265] [  T456] LibBpfLoader: prog /sys/fs/bpf/tethering/prog_offload_xdp_tether_downstream_rawip id 11
[   11.440280] [  T456] LibBpfLoader: cs[26].name:xdp_tether_upstream_ether min_kver:5090000 .max_kver:ffffffff (kvers:601004b)
[   11.440287] [  T456] LibBpfLoader: cs[26].name:xdp_tether_upstream_ether requires bpfloader version [0x00019,0x10000)
[   11.441091] [  T456] LibBpfLoader: bpf_prog_load lib call for /apex/com.android.tethering/etc/bpf/offload@btf.o (xdp_tether_upstream_ether) returned fd: 25 (no error)
[   11.441302] [  T456] LibBpfLoader: prog /sys/fs/bpf/tethering/prog_offload_xdp_tether_upstream_ether id 12
[   11.441320] [  T456] LibBpfLoader: cs[27].name:xdp_tether_upstream_rawip min_kver:5090000 .max_kver:ffffffff (kvers:601004b)
[   11.441326] [  T456] LibBpfLoader: cs[27].name:xdp_tether_upstream_rawip requires bpfloader version [0x00019,0x10000)
[   11.442147] [  T456] LibBpfLoader: bpf_prog_load lib call for /apex/com.android.tethering/etc/bpf/offload@btf.o (xdp_tether_upstream_rawip) returned fd: 26 (no error)
[   11.442324] [  T456] LibBpfLoader: prog /sys/fs/bpf/tethering/prog_offload_xdp_tether_upstream_rawip id 13
[   11.442475] [  T456] bpfloader: Loaded object: /apex/com.android.tethering/etc/bpf/offload@btf.o
[   11.443295] [  T456] LibBpfLoader: Loading critical for Connectivity and netd ELF object /apex/com.android.tethering/etc/bpf/netd_shared/netd.o with license Apache 2.0
[   11.443339] [  T456] LibBpfLoader: Section bpfloader_min_ver value is 13 [0xd]
[   11.443384] [  T456] LibBpfLoader: Section bpfloader_max_ver value is 65536 [0x10000]
[   11.443423] [  T456] LibBpfLoader: Couldn't find section bpfloader_min_required_ver (defaulting to 0 [0x0]).
[   11.443462] [  T456] LibBpfLoader: Section size_of_bpf_map_def value is 120 [0x78]
[   11.443505] [  T456] LibBpfLoader: Section size_of_bpf_prog_def value is 92 [0x5c]
[   11.443515] [  T456] LibBpfLoader: BpfLoader version 0x00026 processing ELF object /apex/com.android.tethering/etc/bpf/netd_shared/netd.o with ver [0x0000d,0x10000)
[   11.444745] [  T456] LibBpfLoader: Loaded code section 3 (cgroupskb_ingress_stats$trace)
[   11.444904] [  T456] LibBpfLoader: Loaded relo section 3 (.relcgroupskb/ingress/stats$trace)
[   11.444910] [  T456] LibBpfLoader: Adding section 3 to cs list
[   11.444985] [  T456] LibBpfLoader: Loaded code section 5 (cgroupskb_ingress_stats$4_19)
[   11.445177] [  T456] LibBpfLoader: Loaded relo section 5 (.relcgroupskb/ingress/stats$4_19)
[   11.445183] [  T456] LibBpfLoader: Adding section 5 to cs list
[   11.445243] [  T456] LibBpfLoader: Loaded code section 7 (cgroupskb_ingress_stats$4_14)
[   11.445471] [  T456] LibBpfLoader: Loaded relo section 7 (.relcgroupskb/ingress/stats$4_14)
[   11.445481] [  T456] LibBpfLoader: Adding section 7 to cs list
[   11.445540] [  T456] LibBpfLoader: Loaded code section 9 (cgroupskb_egress_stats$trace)
[   11.445805] [  T456] LibBpfLoader: Loaded relo section 9 (.relcgroupskb/egress/stats$trace)
[   11.445810] [  T456] LibBpfLoader: Adding section 9 to cs list
[   11.445869] [  T456] LibBpfLoader: Loaded code section 11 (cgroupskb_egress_stats$4_19)
[   11.446169] [  T456] LibBpfLoader: Loaded relo section 11 (.relcgroupskb/egress/stats$4_19)
[   11.446176] [  T456] LibBpfLoader: Adding section 11 to cs list
[   11.446234] [  T456] LibBpfLoader: Loaded code section 13 (cgroupskb_egress_stats$4_14)
[   11.446576] [  T456] LibBpfLoader: Loaded relo section 13 (.relcgroupskb/egress/stats$4_14)
[   11.446581] [  T456] LibBpfLoader: Adding section 13 to cs list
[   11.446640] [  T456] LibBpfLoader: Loaded code section 15 (skfilter_egress_xtbpf)
[   11.447012] [  T456] LibBpfLoader: Loaded relo section 15 (.relskfilter/egress/xtbpf)
[   11.447017] [  T456] LibBpfLoader: Adding section 15 to cs list
[   11.447076] [  T456] LibBpfLoader: Loaded code section 17 (skfilter_ingress_xtbpf)
[   11.447483] [  T456] LibBpfLoader: Loaded relo section 17 (.relskfilter/ingress/xtbpf)
[   11.447489] [  T456] LibBpfLoader: Adding section 17 to cs list
[   11.447548] [  T456] LibBpfLoader: Loaded code section 19 (schedact_ingress_account)
[   11.447994] [  T456] LibBpfLoader: Loaded relo section 19 (.relschedact/ingress/account)
[   11.448001] [  T456] LibBpfLoader: Adding section 19 to cs list
[   11.448060] [  T456] LibBpfLoader: Loaded code section 21 (skfilter_allowlist_xtbpf)
[   11.448541] [  T456] LibBpfLoader: Loaded relo section 21 (.relskfilter/allowlist/xtbpf)
[   11.448546] [  T456] LibBpfLoader: Adding section 21 to cs list
[   11.448608] [  T456] LibBpfLoader: Loaded code section 23 (skfilter_denylist_xtbpf)
[   11.449168] [  T456] LibBpfLoader: Loaded relo section 23 (.relskfilter/denylist/xtbpf)
[   11.449174] [  T456] LibBpfLoader: Adding section 23 to cs list
[   11.449272] [  T456] LibBpfLoader: Loaded code section 25 (cgroupsock_inet_create)
[   11.449826] [  T456] LibBpfLoader: Loaded relo section 25 (.relcgroupsock/inet/create)
[   11.449831] [  T456] LibBpfLoader: Adding section 25 to cs list
[   11.451084] [  T456] LibBpfLoader: Couldn't find section btf_min_bpfloader_ver (defaulting to 0 [0x0]).
[   11.451119] [  T456] LibBpfLoader: Couldn't find section btf_min_kernel_ver (defaulting to 0 [0x0]).
[   11.461740] [  T456] LibBpfLoader: map configuration_map selinux_context [fs_bpf_netd_readonly            ] -> 5 -> 'fs_bpf_netd_readonly' (netd_readonly/)
[   11.461839] [  T456] LibBpfLoader: bpf_create_map name configuration_map, ret: 6
[   11.461956] [  T456] LibBpfLoader: map /sys/fs/bpf/netd_shared/map_netd_configuration_map id 12
[   11.462472] [  T456] LibBpfLoader: bpf_create_map name cookie_tag_map, ret: 8
[   11.462590] [  T456] LibBpfLoader: map /sys/fs/bpf/netd_shared/map_netd_cookie_tag_map id 13
[   11.462614] [  T456] LibBpfLoader: map uid_counterset_map selinux_context [fs_bpf_net_shared               ] -> 4 -> 'fs_bpf_net_shared' (net_shared/)
[   11.462858] [  T456] LibBpfLoader: bpf_create_map name uid_counterset_map, ret: 9
[   11.462954] [  T456] LibBpfLoader: map /sys/fs/bpf/netd_shared/map_netd_uid_counterset_map id 14
[   11.462969] [  T456] LibBpfLoader: map app_uid_stats_map selinux_context [fs_bpf_net_shared               ] -> 4 -> 'fs_bpf_net_shared' (net_shared/)
[   11.463557] [  T456] LibBpfLoader: bpf_create_map name app_uid_stats_map, ret: 10
[   11.463673] [  T456] LibBpfLoader: map /sys/fs/bpf/netd_shared/map_netd_app_uid_stats_map id 15
[   11.464027] [  T456] LibBpfLoader: bpf_create_map name stats_map_A, ret: 11
[   11.464083] [  T456] LibBpfLoader: map /sys/fs/bpf/netd_shared/map_netd_stats_map_A id 16
[   11.464102] [  T456] LibBpfLoader: map stats_map_B selinux_context [fs_bpf_netd_readonly            ] -> 5 -> 'fs_bpf_netd_readonly' (netd_readonly/)
[   11.464429] [  T456] LibBpfLoader: bpf_create_map name stats_map_B, ret: 12
[   11.464507] [  T456] LibBpfLoader: map /sys/fs/bpf/netd_shared/map_netd_stats_map_B id 17
[   11.464523] [  T456] LibBpfLoader: map iface_stats_map selinux_context [fs_bpf_net_shared               ] -> 4 -> 'fs_bpf_net_shared' (net_shared/)
[   11.464640] [  T456] LibBpfLoader: bpf_create_map name iface_stats_map, ret: 13
[   11.464720] [  T456] LibBpfLoader: map /sys/fs/bpf/netd_shared/map_netd_iface_stats_map id 18
[   11.464732] [  T456] LibBpfLoader: map uid_owner_map selinux_context [fs_bpf_net_shared               ] -> 4 -> 'fs_bpf_net_shared' (net_shared/)
[   11.464947] [  T456] LibBpfLoader: bpf_create_map name uid_owner_map, ret: 14
[   11.465043] [  T456] LibBpfLoader: map /sys/fs/bpf/netd_shared/map_netd_uid_owner_map id 19
[   11.465272] [  T456] LibBpfLoader: bpf_create_map name uid_permission_map, ret: 15
[   11.465333] [  T456] LibBpfLoader: map /sys/fs/bpf/netd_shared/map_netd_uid_permission_map id 20
[   11.465358] [  T456] LibBpfLoader: map iface_index_name_map selinux_context [fs_bpf_net_shared               ] -> 4 -> 'fs_bpf_net_shared' (net_shared/)
[   11.465453] [  T456] LibBpfLoader: bpf_create_map name iface_index_name_map, ret: 16
[   11.465526] [  T456] LibBpfLoader: map /sys/fs/bpf/netd_shared/map_netd_iface_index_name_map id 21
[   11.465537] [  T456] LibBpfLoader: skipping map packet_trace_enabled_map which is ignored on user builds
[   11.465546] [  T456] LibBpfLoader: skipping map packet_trace_ringbuf which is ignored on user builds
[   11.465578] [  T456] LibBpfLoader: map_fd found at 0 is 6 in /apex/com.android.tethering/etc/bpf/netd_shared/netd.o
[   11.465583] [  T456] LibBpfLoader: map_fd found at 1 is 8 in /apex/com.android.tethering/etc/bpf/netd_shared/netd.o
[   11.465588] [  T456] LibBpfLoader: map_fd found at 2 is 9 in /apex/com.android.tethering/etc/bpf/netd_shared/netd.o
[   11.465594] [  T456] LibBpfLoader: map_fd found at 3 is 10 in /apex/com.android.tethering/etc/bpf/netd_shared/netd.o
[   11.465598] [  T456] LibBpfLoader: map_fd found at 4 is 11 in /apex/com.android.tethering/etc/bpf/netd_shared/netd.o
[   11.465603] [  T456] LibBpfLoader: map_fd found at 5 is 12 in /apex/com.android.tethering/etc/bpf/netd_shared/netd.o
[   11.465608] [  T456] LibBpfLoader: map_fd found at 6 is 13 in /apex/com.android.tethering/etc/bpf/netd_shared/netd.o
[   11.465613] [  T456] LibBpfLoader: map_fd found at 7 is 14 in /apex/com.android.tethering/etc/bpf/netd_shared/netd.o
[   11.465618] [  T456] LibBpfLoader: map_fd found at 8 is 15 in /apex/com.android.tethering/etc/bpf/netd_shared/netd.o
[   11.465623] [  T456] LibBpfLoader: map_fd found at 9 is 16 in /apex/com.android.tethering/etc/bpf/netd_shared/netd.o
[   11.465628] [  T456] LibBpfLoader: map_fd found at 10 is -1 in /apex/com.android.tethering/etc/bpf/netd_shared/netd.o
[   11.465633] [  T456] LibBpfLoader: map_fd found at 11 is -1 in /apex/com.android.tethering/etc/bpf/netd_shared/netd.o
[   11.469869] [  T456] LibBpfLoader: cs[0].name:cgroupskb_ingress_stats$trace min_kver:5080000 .max_kver:ffffffff (kvers:601004b)
[   11.469879] [  T456] LibBpfLoader: cs[0].name:cgroupskb_ingress_stats$trace requires bpfloader version [0x00021,0x10000)
[   11.469885] [  T456] LibBpfLoader: cs[0].name:cgroupskb_ingress_stats$trace is ignored on user builds
[   11.469890] [  T456] LibBpfLoader: cs[1].name:cgroupskb_ingress_stats$4_19 min_kver:4130000 .max_kver:ffffffff (kvers:601004b)
[   11.469897] [  T456] LibBpfLoader: cs[1].name:cgroupskb_ingress_stats$4_19 requires bpfloader version [0x0000d,0x10000)
[   11.469902] [  T456] LibBpfLoader: prog cgroupskb_ingress_stats$4_19 selinux_context [fs_bpf_netd_readonly            ] -> 5 -> 'fs_bpf_netd_readonly' (netd_readonly/)
[   11.471629] [  T456] LibBpfLoader: bpf_prog_load lib call for /apex/com.android.tethering/etc/bpf/netd_shared/netd.o (cgroupskb_ingress_stats$4_19) returned fd: 7 (no error)
[   11.471867] [  T456] LibBpfLoader: prog /sys/fs/bpf/netd_shared/prog_netd_cgroupskb_ingress_stats id 14
[   11.471888] [  T456] LibBpfLoader: cs[2].name:cgroupskb_ingress_stats$4_14 min_kver:0 .max_kver:4130000 (kvers:601004b)
[   11.471895] [  T456] LibBpfLoader: cs[3].name:cgroupskb_egress_stats$trace min_kver:5080000 .max_kver:ffffffff (kvers:601004b)
[   11.471903] [  T456] LibBpfLoader: cs[3].name:cgroupskb_egress_stats$trace requires bpfloader version [0x00021,0x10000)
[   11.471908] [  T456] LibBpfLoader: cs[3].name:cgroupskb_egress_stats$trace is ignored on user builds
[   11.471914] [  T456] LibBpfLoader: cs[4].name:cgroupskb_egress_stats$4_19 min_kver:4130000 .max_kver:ffffffff (kvers:601004b)
[   11.471920] [  T456] LibBpfLoader: cs[4].name:cgroupskb_egress_stats$4_19 requires bpfloader version [0x0000d,0x10000)
[   11.471926] [  T456] LibBpfLoader: prog cgroupskb_egress_stats$4_19 selinux_context [fs_bpf_netd_readonly            ] -> 5 -> 'fs_bpf_netd_readonly' (netd_readonly/)
[   11.473440] [  T456] LibBpfLoader: bpf_prog_load lib call for /apex/com.android.tethering/etc/bpf/netd_shared/netd.o (cgroupskb_egress_stats$4_19) returned fd: 17 (no error)
[   11.473672] [  T456] LibBpfLoader: prog /sys/fs/bpf/netd_shared/prog_netd_cgroupskb_egress_stats id 15
[   11.473690] [  T456] LibBpfLoader: cs[5].name:cgroupskb_egress_stats$4_14 min_kver:0 .max_kver:4130000 (kvers:601004b)
[   11.473697] [  T456] LibBpfLoader: cs[6].name:skfilter_egress_xtbpf min_kver:0 .max_kver:ffffffff (kvers:601004b)
[   11.473703] [  T456] LibBpfLoader: cs[6].name:skfilter_egress_xtbpf requires bpfloader version [0x0000d,0x10000)
[   11.474654] [  T456] LibBpfLoader: bpf_prog_load lib call for /apex/com.android.tethering/etc/bpf/netd_shared/netd.o (skfilter_egress_xtbpf) returned fd: 18 (no error)
[   11.474823] [  T456] LibBpfLoader: prog /sys/fs/bpf/netd_shared/prog_netd_skfilter_egress_xtbpf id 16
[   11.474839] [  T456] LibBpfLoader: cs[7].name:skfilter_ingress_xtbpf min_kver:0 .max_kver:ffffffff (kvers:601004b)
[   11.474846] [  T456] LibBpfLoader: cs[7].name:skfilter_ingress_xtbpf requires bpfloader version [0x0000d,0x10000)
[   11.475737] [  T456] LibBpfLoader: bpf_prog_load lib call for /apex/com.android.tethering/etc/bpf/netd_shared/netd.o (skfilter_ingress_xtbpf) returned fd: 19 (no error)
[   11.475903] [  T456] LibBpfLoader: prog /sys/fs/bpf/netd_shared/prog_netd_skfilter_ingress_xtbpf id 17
[   11.475920] [  T456] LibBpfLoader: cs[8].name:schedact_ingress_account min_kver:0 .max_kver:ffffffff (kvers:601004b)
[   11.475927] [  T456] LibBpfLoader: cs[8].name:schedact_ingress_account requires bpfloader version [0x0000d,0x10000)
[   11.475933] [  T456] LibBpfLoader: prog schedact_ingress_account selinux_context [fs_bpf_net_shared               ] -> 4 -> 'fs_bpf_net_shared' (net_shared/)
[   11.476835] [  T456] LibBpfLoader: bpf_prog_load lib call for /apex/com.android.tethering/etc/bpf/netd_shared/netd.o (schedact_ingress_account) returned fd: 20 (no error)
[   11.477077] [  T456] LibBpfLoader: prog /sys/fs/bpf/netd_shared/prog_netd_schedact_ingress_account id 18
[   11.477096] [  T456] LibBpfLoader: cs[9].name:skfilter_allowlist_xtbpf min_kver:0 .max_kver:ffffffff (kvers:601004b)
[   11.477103] [  T456] LibBpfLoader: cs[9].name:skfilter_allowlist_xtbpf requires bpfloader version [0x0000d,0x10000)
[   11.477975] [  T456] LibBpfLoader: bpf_prog_load lib call for /apex/com.android.tethering/etc/bpf/netd_shared/netd.o (skfilter_allowlist_xtbpf) returned fd: 21 (no error)
[   11.478148] [  T456] LibBpfLoader: prog /sys/fs/bpf/netd_shared/prog_netd_skfilter_allowlist_xtbpf id 19
[   11.478165] [  T456] LibBpfLoader: cs[10].name:skfilter_denylist_xtbpf min_kver:0 .max_kver:ffffffff (kvers:601004b)
[   11.478172] [  T456] LibBpfLoader: cs[10].name:skfilter_denylist_xtbpf requires bpfloader version [0x0000d,0x10000)
[   11.478991] [  T456] LibBpfLoader: bpf_prog_load lib call for /apex/com.android.tethering/etc/bpf/netd_shared/netd.o (skfilter_denylist_xtbpf) returned fd: 22 (no error)
[   11.479154] [  T456] LibBpfLoader: prog /sys/fs/bpf/netd_shared/prog_netd_skfilter_denylist_xtbpf id 20
[   11.479170] [  T456] LibBpfLoader: cs[11].name:cgroupsock_inet_create min_kver:40e0000 .max_kver:ffffffff (kvers:601004b)
[   11.479177] [  T456] LibBpfLoader: cs[11].name:cgroupsock_inet_create requires bpfloader version [0x0000d,0x10000)
[   11.479183] [  T456] LibBpfLoader: prog cgroupsock_inet_create selinux_context [fs_bpf_netd_readonly            ] -> 5 -> 'fs_bpf_netd_readonly' (netd_readonly/)
[   11.480001] [  T456] LibBpfLoader: bpf_prog_load lib call for /apex/com.android.tethering/etc/bpf/netd_shared/netd.o (cgroupsock_inet_create) returned fd: 23 (no error)
[   11.480184] [  T456] LibBpfLoader: prog /sys/fs/bpf/netd_shared/prog_netd_cgroupsock_inet_create id 21
[   11.480308] [  T456] bpfloader: Loaded object: /apex/com.android.tethering/etc/bpf/netd_shared/netd.o
[   11.480775] [  T456] LibBpfLoader: Loading critical for Connectivity ELF object /apex/com.android.tethering/etc/bpf/net_shared/dscpPolicy.o with license Apache 2.0
[   11.480816] [  T456] LibBpfLoader: Section bpfloader_min_ver value is 13 [0xd]
[   11.480857] [  T456] LibBpfLoader: Section bpfloader_max_ver value is 65536 [0x10000]
[   11.480892] [  T456] LibBpfLoader: Couldn't find section bpfloader_min_required_ver (defaulting to 0 [0x0]).
[   11.480929] [  T456] LibBpfLoader: Section size_of_bpf_map_def value is 120 [0x78]
[   11.480982] [  T456] LibBpfLoader: Section size_of_bpf_prog_def value is 92 [0x5c]
[   11.480993] [  T456] LibBpfLoader: BpfLoader version 0x00026 processing ELF object /apex/com.android.tethering/etc/bpf/net_shared/dscpPolicy.o with ver [0x0000d,0x10000)
[   11.481329] [  T456] LibBpfLoader: Loaded code section 3 (schedcls_set_dscp_ether)
[   11.481465] [  T456] LibBpfLoader: Loaded relo section 3 (.relschedcls/set_dscp_ether)
[   11.481471] [  T456] LibBpfLoader: Adding section 3 to cs list
[   11.481953] [  T456] LibBpfLoader: Couldn't find section btf_min_bpfloader_ver (defaulting to 0 [0x0]).
[   11.481984] [  T456] LibBpfLoader: Couldn't find section btf_min_kernel_ver (defaulting to 0 [0x0]).
[   11.496976] [  T456] LibBpfLoader: bpf_create_map name socket_policy_cache_map, ret: 6
[   11.497059] [  T456] LibBpfLoader: map /sys/fs/bpf/net_shared/map_dscpPolicy_socket_policy_cache_map id 22
[   11.497102] [  T456] LibBpfLoader: bpf_create_map name ipv4_dscp_policies_map, ret: 8
[   11.497164] [  T456] LibBpfLoader: map /sys/fs/bpf/net_shared/map_dscpPolicy_ipv4_dscp_policies_map id 23
[   11.497196] [  T456] LibBpfLoader: bpf_create_map name ipv6_dscp_policies_map, ret: 9
[   11.497245] [  T456] LibBpfLoader: map /sys/fs/bpf/net_shared/map_dscpPolicy_ipv6_dscp_policies_map id 24
[   11.497270] [  T456] LibBpfLoader: map_fd found at 0 is 6 in /apex/com.android.tethering/etc/bpf/net_shared/dscpPolicy.o
[   11.497276] [  T456] LibBpfLoader: map_fd found at 1 is 8 in /apex/com.android.tethering/etc/bpf/net_shared/dscpPolicy.o
[   11.497281] [  T456] LibBpfLoader: map_fd found at 2 is 9 in /apex/com.android.tethering/etc/bpf/net_shared/dscpPolicy.o
[   11.497678] [  T456] LibBpfLoader: cs[0].name:schedcls_set_dscp_ether min_kver:50f0000 .max_kver:ffffffff (kvers:601004b)
[   11.497685] [  T456] LibBpfLoader: cs[0].name:schedcls_set_dscp_ether requires bpfloader version [0x0000d,0x10000)
[   11.519825] [  T456] LibBpfLoader: bpf_prog_load lib call for /apex/com.android.tethering/etc/bpf/net_shared/dscpPolicy.o (schedcls_set_dscp_ether) returned fd: 7 (no error)
[   11.520050] [  T456] LibBpfLoader: prog /sys/fs/bpf/net_shared/prog_dscpPolicy_schedcls_set_dscp_ether id 22
[   11.520128] [  T456] bpfloader: Loaded object: /apex/com.android.tethering/etc/bpf/net_shared/dscpPolicy.o
[   11.520502] [  T456] LibBpfLoader: Loading critical for ConnectivityNative ELF object /apex/com.android.tethering/etc/bpf/net_shared/block.o with license Apache 2.0
[   11.520542] [  T456] LibBpfLoader: Section bpfloader_min_ver value is 13 [0xd]
[   11.520582] [  T456] LibBpfLoader: Section bpfloader_max_ver value is 65536 [0x10000]
[   11.520617] [  T456] LibBpfLoader: Couldn't find section bpfloader_min_required_ver (defaulting to 0 [0x0]).
[   11.520653] [  T456] LibBpfLoader: Section size_of_bpf_map_def value is 120 [0x78]
[   11.520692] [  T456] LibBpfLoader: Section size_of_bpf_prog_def value is 92 [0x5c]
[   11.520702] [  T456] LibBpfLoader: BpfLoader version 0x00026 processing ELF object /apex/com.android.tethering/etc/bpf/net_shared/block.o with ver [0x0000d,0x10000)
[   11.521067] [  T456] LibBpfLoader: Loaded code section 3 (bind4_block_port)
[   11.521204] [  T456] LibBpfLoader: Loaded relo section 3 (.relbind4/block_port)
[   11.521211] [  T456] LibBpfLoader: Adding section 3 to cs list
[   11.521265] [  T456] LibBpfLoader: Loaded code section 5 (bind6_block_port)
[   11.521433] [  T456] LibBpfLoader: Loaded relo section 5 (.relbind6/block_port)
[   11.521439] [  T456] LibBpfLoader: Adding section 5 to cs list
[   11.521900] [  T456] LibBpfLoader: Couldn't find section btf_min_bpfloader_ver (defaulting to 0 [0x0]).
[   11.521932] [  T456] LibBpfLoader: Couldn't find section btf_min_kernel_ver (defaulting to 0 [0x0]).
[   11.531932] [  T456] LibBpfLoader: bpf_create_map name blocked_ports_map, ret: 6
[   11.532015] [  T456] LibBpfLoader: map /sys/fs/bpf/net_shared/map_block_blocked_ports_map id 25
[   11.532045] [  T456] LibBpfLoader: map_fd found at 0 is 6 in /apex/com.android.tethering/etc/bpf/net_shared/block.o
[   11.532332] [  T456] LibBpfLoader: cs[0].name:bind4_block_port min_kver:5040000 .max_kver:ffffffff (kvers:601004b)
[   11.532339] [  T456] LibBpfLoader: cs[0].name:bind4_block_port requires bpfloader version [0x0000d,0x10000)
[   11.533387] [  T456] LibBpfLoader: bpf_prog_load lib call for /apex/com.android.tethering/etc/bpf/net_shared/block.o (bind4_block_port) returned fd: 7 (no error)
[   11.533609] [  T456] LibBpfLoader: prog /sys/fs/bpf/net_shared/prog_block_bind4_block_port id 23
[   11.533629] [  T456] LibBpfLoader: cs[1].name:bind6_block_port min_kver:5040000 .max_kver:ffffffff (kvers:601004b)
[   11.533637] [  T456] LibBpfLoader: cs[1].name:bind6_block_port requires bpfloader version [0x0000d,0x10000)
[   11.534564] [  T456] LibBpfLoader: bpf_prog_load lib call for /apex/com.android.tethering/etc/bpf/net_shared/block.o (bind6_block_port) returned fd: 8 (no error)
[   11.534742] [  T456] LibBpfLoader: prog /sys/fs/bpf/net_shared/prog_block_bind6_block_port id 24
[   11.534805] [  T456] bpfloader: Loaded object: /apex/com.android.tethering/etc/bpf/net_shared/block.o
[   11.535380] [  T456] LibBpfLoader: Loading critical for Connectivity ELF object /apex/com.android.tethering/etc/bpf/net_shared/clatd.o with license Apache 2.0
[   11.535421] [  T456] LibBpfLoader: Section bpfloader_min_ver value is 13 [0xd]
[   11.535464] [  T456] LibBpfLoader: Section bpfloader_max_ver value is 65536 [0x10000]
[   11.535500] [  T456] LibBpfLoader: Couldn't find section bpfloader_min_required_ver (defaulting to 0 [0x0]).
[   11.535536] [  T456] LibBpfLoader: Section size_of_bpf_map_def value is 120 [0x78]
[   11.535576] [  T456] LibBpfLoader: Section size_of_bpf_prog_def value is 92 [0x5c]
[   11.535587] [  T456] LibBpfLoader: BpfLoader version 0x00026 processing ELF object /apex/com.android.tethering/etc/bpf/net_shared/clatd.o with ver [0x0000d,0x10000)
[   11.536115] [  T456] LibBpfLoader: Loaded code section 3 (schedcls_ingress6_clat_ether$4_14)
[   11.536259] [  T456] LibBpfLoader: Loaded relo section 3 (.relschedcls/ingress6/clat_ether$4_14)
[   11.536268] [  T456] LibBpfLoader: Adding section 3 to cs list
[   11.536327] [  T456] LibBpfLoader: Loaded code section 5 (schedcls_ingress6_clat_ether$4_9)
[   11.536502] [  T456] LibBpfLoader: Loaded relo section 5 (.relschedcls/ingress6/clat_ether$4_9)
[   11.536509] [  T456] LibBpfLoader: Adding section 5 to cs list
[   11.536568] [  T456] LibBpfLoader: Loaded code section 7 (schedcls_ingress6_clat_rawip$4_14)
[   11.536777] [  T456] LibBpfLoader: Loaded relo section 7 (.relschedcls/ingress6/clat_rawip$4_14)
[   11.536783] [  T456] LibBpfLoader: Adding section 7 to cs list
[   11.536838] [  T456] LibBpfLoader: Loaded code section 9 (schedcls_ingress6_clat_rawip$4_9)
[   11.537129] [  T456] LibBpfLoader: Loaded relo section 9 (.relschedcls/ingress6/clat_rawip$4_9)
[   11.537135] [  T456] LibBpfLoader: Adding section 9 to cs list
[   11.537192] [  T456] LibBpfLoader: Loaded code section 11 (schedcls_egress4_clat_rawip)
[   11.537469] [  T456] LibBpfLoader: Loaded relo section 11 (.relschedcls/egress4/clat_rawip)
[   11.537476] [  T456] LibBpfLoader: Adding section 11 to cs list
[   11.538070] [  T456] LibBpfLoader: Couldn't find section btf_min_bpfloader_ver (defaulting to 0 [0x0]).
[   11.538101] [  T456] LibBpfLoader: Couldn't find section btf_min_kernel_ver (defaulting to 0 [0x0]).
[   11.548425] [  T456] LibBpfLoader: bpf_create_map name clat_ingress6_map, ret: 6
[   11.548499] [  T456] LibBpfLoader: map /sys/fs/bpf/net_shared/map_clatd_clat_ingress6_map id 26
[   11.548547] [  T456] LibBpfLoader: bpf_create_map name clat_egress4_map, ret: 8
[   11.548589] [  T456] LibBpfLoader: map /sys/fs/bpf/net_shared/map_clatd_clat_egress4_map id 27
[   11.548613] [  T456] LibBpfLoader: map_fd found at 0 is 6 in /apex/com.android.tethering/etc/bpf/net_shared/clatd.o
[   11.548618] [  T456] LibBpfLoader: map_fd found at 1 is 8 in /apex/com.android.tethering/etc/bpf/net_shared/clatd.o
[   11.549174] [  T456] LibBpfLoader: cs[0].name:schedcls_ingress6_clat_ether$4_14 min_kver:40e0000 .max_kver:ffffffff (kvers:601004b)
[   11.549181] [  T456] LibBpfLoader: cs[0].name:schedcls_ingress6_clat_ether$4_14 requires bpfloader version [0x0000d,0x10000)
[   11.550603] [  T456] LibBpfLoader: bpf_prog_load lib call for /apex/com.android.tethering/etc/bpf/net_shared/clatd.o (schedcls_ingress6_clat_ether$4_14) returned fd: 7 (no error)
[   11.550805] [  T456] LibBpfLoader: prog /sys/fs/bpf/net_shared/prog_clatd_schedcls_ingress6_clat_ether id 25
[   11.550828] [  T456] LibBpfLoader: cs[1].name:schedcls_ingress6_clat_ether$4_9 min_kver:0 .max_kver:40e0000 (kvers:601004b)
[   11.550835] [  T456] LibBpfLoader: cs[2].name:schedcls_ingress6_clat_rawip$4_14 min_kver:40e0000 .max_kver:ffffffff (kvers:601004b)
[   11.550842] [  T456] LibBpfLoader: cs[2].name:schedcls_ingress6_clat_rawip$4_14 requires bpfloader version [0x0000d,0x10000)
[   11.552051] [  T456] LibBpfLoader: bpf_prog_load lib call for /apex/com.android.tethering/etc/bpf/net_shared/clatd.o (schedcls_ingress6_clat_rawip$4_14) returned fd: 9 (no error)
[   11.552230] [  T456] LibBpfLoader: prog /sys/fs/bpf/net_shared/prog_clatd_schedcls_ingress6_clat_rawip id 26
[   11.552249] [  T456] LibBpfLoader: cs[3].name:schedcls_ingress6_clat_rawip$4_9 min_kver:0 .max_kver:40e0000 (kvers:601004b)
[   11.552256] [  T456] LibBpfLoader: cs[4].name:schedcls_egress4_clat_rawip min_kver:0 .max_kver:ffffffff (kvers:601004b)
[   11.552262] [  T456] LibBpfLoader: cs[4].name:schedcls_egress4_clat_rawip requires bpfloader version [0x0000d,0x10000)
[   11.553388] [  T456] LibBpfLoader: bpf_prog_load lib call for /apex/com.android.tethering/etc/bpf/net_shared/clatd.o (schedcls_egress4_clat_rawip) returned fd: 10 (no error)
[   11.553571] [  T456] LibBpfLoader: prog /sys/fs/bpf/net_shared/prog_clatd_schedcls_egress4_clat_rawip id 27
[   11.553644] [  T456] bpfloader: Loaded object: /apex/com.android.tethering/etc/bpf/net_shared/clatd.o
[   11.557131] [  T456] LibBpfLoader: Loading optional ELF object /system/etc/bpf/timeInState.o with license GPL
[   11.557175] [  T456] LibBpfLoader: Section bpfloader_min_ver value is 28 [0x1c]
[   11.557221] [  T456] LibBpfLoader: Section bpfloader_max_ver value is 65536 [0x10000]
[   11.557259] [  T456] LibBpfLoader: Couldn't find section bpfloader_min_required_ver (defaulting to 0 [0x0]).
[   11.557299] [  T456] LibBpfLoader: Section size_of_bpf_map_def value is 120 [0x78]
[   11.557341] [  T456] LibBpfLoader: Section size_of_bpf_prog_def value is 92 [0x5c]
[   11.557351] [  T456] LibBpfLoader: BpfLoader version 0x00026 processing ELF object /system/etc/bpf/timeInState.o with ver [0x0001c,0x10000)
[   11.558363] [  T456] LibBpfLoader: Loaded code section 3 (tracepoint_sched_sched_switch)
[   11.558519] [  T456] LibBpfLoader: Loaded relo section 3 (.reltracepoint/sched/sched_switch)
[   11.558526] [  T456] LibBpfLoader: Adding section 3 to cs list
[   11.558586] [  T456] LibBpfLoader: Loaded code section 5 (tracepoint_power_cpu_frequency)
[   11.558777] [  T456] LibBpfLoader: Loaded relo section 5 (.reltracepoint/power/cpu_frequency)
[   11.558786] [  T456] LibBpfLoader: Adding section 5 to cs list
[   11.558846] [  T456] LibBpfLoader: Loaded code section 7 (tracepoint_sched_sched_process_free)
[   11.559076] [  T456] LibBpfLoader: Loaded relo section 7 (.reltracepoint/sched/sched_process_free)
[   11.559083] [  T456] LibBpfLoader: Adding section 7 to cs list
[   11.560105] [  T456] LibBpfLoader: Couldn't find section btf_min_bpfloader_ver (defaulting to 0 [0x0]).
[   11.560139] [  T456] LibBpfLoader: Couldn't find section btf_min_kernel_ver (defaulting to 0 [0x0]).
[   11.575582] [  T456] LibBpfLoader: bpf_create_map name total_time_in_state_map, ret: 6
[   11.575671] [  T456] LibBpfLoader: map /sys/fs/bpf/map_timeInState_total_time_in_state_map id 28
[   11.577319] [  T456] LibBpfLoader: bpf_create_map name uid_time_in_state_map, ret: 8
[   11.577426] [  T456] LibBpfLoader: map /sys/fs/bpf/map_timeInState_uid_time_in_state_map id 29
[   11.578644] [  T456] LibBpfLoader: bpf_create_map name uid_concurrent_times_map, ret: 9
[   11.578744] [  T456] LibBpfLoader: map /sys/fs/bpf/map_timeInState_uid_concurrent_times_map id 30
[   11.578875] [  T456] LibBpfLoader: bpf_create_map name uid_last_update_map, ret: 10
[   11.578922] [  T456] LibBpfLoader: map /sys/fs/bpf/map_timeInState_uid_last_update_map id 31
[   11.578955] [  T456] LibBpfLoader: bpf_create_map name cpu_last_update_map, ret: 11
[   11.579004] [  T456] LibBpfLoader: map /sys/fs/bpf/map_timeInState_cpu_last_update_map id 32
[   11.579036] [  T456] LibBpfLoader: bpf_create_map name cpu_last_pid_map, ret: 12
[   11.579083] [  T456] LibBpfLoader: map /sys/fs/bpf/map_timeInState_cpu_last_pid_map id 33
[   11.579113] [  T456] LibBpfLoader: bpf_create_map name cpu_policy_map, ret: 13
[   11.579159] [  T456] LibBpfLoader: map /sys/fs/bpf/map_timeInState_cpu_policy_map id 34
[   11.579189] [  T456] LibBpfLoader: bpf_create_map name policy_freq_idx_map, ret: 14
[   11.579235] [  T456] LibBpfLoader: map /sys/fs/bpf/map_timeInState_policy_freq_idx_map id 35
[   11.579362] [  T456] LibBpfLoader: bpf_create_map name freq_to_idx_map, ret: 15
[   11.579403] [  T456] LibBpfLoader: map /sys/fs/bpf/map_timeInState_freq_to_idx_map id 36
[   11.579443] [  T456] LibBpfLoader: bpf_create_map name nr_active_map, ret: 16
[   11.579482] [  T456] LibBpfLoader: map /sys/fs/bpf/map_timeInState_nr_active_map id 37
[   11.579512] [  T456] LibBpfLoader: bpf_create_map name policy_nr_active_map, ret: 17
[   11.579552] [  T456] LibBpfLoader: map /sys/fs/bpf/map_timeInState_policy_nr_active_map id 38
[   11.579597] [  T456] LibBpfLoader: bpf_create_map name pid_tracked_hash_map, ret: 18
[   11.579637] [  T456] LibBpfLoader: map /sys/fs/bpf/map_timeInState_pid_tracked_hash_map id 39
[   11.579666] [  T456] LibBpfLoader: bpf_create_map name pid_tracked_map, ret: 19
[   11.579704] [  T456] LibBpfLoader: map /sys/fs/bpf/map_timeInState_pid_tracked_map id 40
[   11.579788] [  T456] LibBpfLoader: bpf_create_map name pid_task_aggregation_map, ret: 20
[   11.579828] [  T456] LibBpfLoader: map /sys/fs/bpf/map_timeInState_pid_task_aggregation_map id 41
[   11.581308] [  T456] LibBpfLoader: bpf_create_map name pid_time_in_state_map, ret: 21
[   11.581402] [  T456] LibBpfLoader: map /sys/fs/bpf/map_timeInState_pid_time_in_state_map id 42
[   11.581453] [  T456] LibBpfLoader: map_fd found at 0 is 6 in /system/etc/bpf/timeInState.o
[   11.581458] [  T456] LibBpfLoader: map_fd found at 1 is 8 in /system/etc/bpf/timeInState.o
[   11.581463] [  T456] LibBpfLoader: map_fd found at 2 is 9 in /system/etc/bpf/timeInState.o
[   11.581468] [  T456] LibBpfLoader: map_fd found at 3 is 10 in /system/etc/bpf/timeInState.o
[   11.581473] [  T456] LibBpfLoader: map_fd found at 4 is 11 in /system/etc/bpf/timeInState.o
[   11.581478] [  T456] LibBpfLoader: map_fd found at 5 is 12 in /system/etc/bpf/timeInState.o
[   11.581482] [  T456] LibBpfLoader: map_fd found at 6 is 13 in /system/etc/bpf/timeInState.o
[   11.581487] [  T456] LibBpfLoader: map_fd found at 7 is 14 in /system/etc/bpf/timeInState.o
[   11.581492] [  T456] LibBpfLoader: map_fd found at 8 is 15 in /system/etc/bpf/timeInState.o
[   11.581496] [  T456] LibBpfLoader: map_fd found at 9 is 16 in /system/etc/bpf/timeInState.o
[   11.581501] [  T456] LibBpfLoader: map_fd found at 10 is 17 in /system/etc/bpf/timeInState.o
[   11.581506] [  T456] LibBpfLoader: map_fd found at 11 is 18 in /system/etc/bpf/timeInState.o
[   11.581510] [  T456] LibBpfLoader: map_fd found at 12 is 19 in /system/etc/bpf/timeInState.o
[   11.581515] [  T456] LibBpfLoader: map_fd found at 13 is 20 in /system/etc/bpf/timeInState.o
[   11.581520] [  T456] LibBpfLoader: map_fd found at 14 is 21 in /system/etc/bpf/timeInState.o
[   11.584173] [  T456] LibBpfLoader: cs[0].name:tracepoint_sched_sched_switch min_kver:0 .max_kver:ffffffff (kvers:601004b)
[   11.584181] [  T456] LibBpfLoader: cs[0].name:tracepoint_sched_sched_switch requires bpfloader version [0x0001c,0x10000)
[   11.588530] [  T456] LibBpfLoader: bpf_prog_load lib call for /system/etc/bpf/timeInState.o (tracepoint_sched_sched_switch) returned fd: 7 (no error)
[   11.588753] [  T456] LibBpfLoader: prog /sys/fs/bpf/prog_timeInState_tracepoint_sched_sched_switch id 28
[   11.588778] [  T456] LibBpfLoader: cs[1].name:tracepoint_power_cpu_frequency min_kver:0 .max_kver:ffffffff (kvers:601004b)
[   11.588786] [  T456] LibBpfLoader: cs[1].name:tracepoint_power_cpu_frequency requires bpfloader version [0x0001c,0x10000)
[   11.589725] [  T456] LibBpfLoader: bpf_prog_load lib call for /system/etc/bpf/timeInState.o (tracepoint_power_cpu_frequency) returned fd: 22 (no error)
[   11.589907] [  T456] LibBpfLoader: prog /sys/fs/bpf/prog_timeInState_tracepoint_power_cpu_frequency id 29
[   11.589925] [  T456] LibBpfLoader: cs[2].name:tracepoint_sched_sched_process_free min_kver:0 .max_kver:ffffffff (kvers:601004b)
[   11.589932] [  T456] LibBpfLoader: cs[2].name:tracepoint_sched_sched_process_free requires bpfloader version [0x0001c,0x10000)
[   11.591225] [  T456] LibBpfLoader: bpf_prog_load lib call for /system/etc/bpf/timeInState.o (tracepoint_sched_sched_process_free) returned fd: 23 (no error)
[   11.591403] [  T456] LibBpfLoader: prog /sys/fs/bpf/prog_timeInState_tracepoint_sched_sched_process_free id 30
[   11.591510] [  T456] bpfloader: Loaded object: /system/etc/bpf/timeInState.o
[   11.592232] [  T456] LibBpfLoader: Loading optional ELF object /system/etc/bpf/fuseMedia.o with license GPL
[   11.592271] [  T456] LibBpfLoader: Section bpfloader_min_ver value is 28 [0x1c]
[   11.592313] [  T456] LibBpfLoader: Section bpfloader_max_ver value is 65536 [0x10000]
[   11.592349] [  T456] LibBpfLoader: Couldn't find section bpfloader_min_required_ver (defaulting to 0 [0x0]).
[   11.592385] [  T456] LibBpfLoader: Section size_of_bpf_map_def value is 120 [0x78]
[   11.592424] [  T456] LibBpfLoader: Section size_of_bpf_prog_def value is 92 [0x5c]
[   11.592434] [  T456] LibBpfLoader: BpfLoader version 0x00026 processing ELF object /system/etc/bpf/fuseMedia.o with ver [0x0001c,0x10000)
[   11.592880] [  T456] LibBpfLoader: Loaded code section 3 (fuse_media)
[   11.593028] [  T456] LibBpfLoader: Adding section 3 to cs list
[   11.593456] [  T456] LibBpfLoader: No maps section could be found in elf object
[   11.593470] [  T456] LibBpfLoader: cs[0].name:fuse_media min_kver:0 .max_kver:ffffffff (kvers:601004b)
[   11.593476] [  T456] LibBpfLoader: cs[0].name:fuse_media requires bpfloader version [0x0001c,0x10000)
[   11.594780] [  T456] LibBpfLoader: bpf_prog_load lib call for /system/etc/bpf/fuseMedia.o (fuse_media) returned fd: 6 (no error)
[   11.594998] [  T456] LibBpfLoader: prog /sys/fs/bpf/prog_fuseMedia_fuse_media id 31
[   11.595047] [  T456] bpfloader: Loaded object: /system/etc/bpf/fuseMedia.o
[   11.595738] [  T456] LibBpfLoader: Loading optional ELF object /system/etc/bpf/gpuWork.o with license Apache 2.0
[   11.595779] [  T456] LibBpfLoader: Section bpfloader_min_ver value is 28 [0x1c]
[   11.595822] [  T456] LibBpfLoader: Section bpfloader_max_ver value is 65536 [0x10000]
[   11.595859] [  T456] LibBpfLoader: Couldn't find section bpfloader_min_required_ver (defaulting to 0 [0x0]).
[   11.595896] [  T456] LibBpfLoader: Section size_of_bpf_map_def value is 120 [0x78]
[   11.595937] [  T456] LibBpfLoader: Section size_of_bpf_prog_def value is 92 [0x5c]
[   11.595946] [  T456] LibBpfLoader: BpfLoader version 0x00026 processing ELF object /system/etc/bpf/gpuWork.o with ver [0x0001c,0x10000)
[   11.596298] [  T456] LibBpfLoader: Loaded code section 3 (tracepoint_power_gpu_work_period)
[   11.596448] [  T456] LibBpfLoader: Loaded relo section 3 (.reltracepoint/power/gpu_work_period)
[   11.596455] [  T456] LibBpfLoader: Adding section 3 to cs list
[   11.596911] [  T456] LibBpfLoader: Couldn't find section btf_min_bpfloader_ver (defaulting to 0 [0x0]).
[   11.596944] [  T456] LibBpfLoader: Couldn't find section btf_min_kernel_ver (defaulting to 0 [0x0]).
[   11.597131] [  T456] LibBpfLoader: bpf_create_map name gpu_work_map, ret: 6
[   11.597189] [  T456] LibBpfLoader: map /sys/fs/bpf/map_gpuWork_gpu_work_map id 43
[   11.597222] [  T456] LibBpfLoader: bpf_create_map name gpu_work_global_data, ret: 7
[   11.597264] [  T456] LibBpfLoader: map /sys/fs/bpf/map_gpuWork_gpu_work_global_data id 44
[   11.597275] [  T456] LibBpfLoader: map_fd found at 0 is 6 in /system/etc/bpf/gpuWork.o
[   11.597281] [  T456] LibBpfLoader: map_fd found at 1 is 7 in /system/etc/bpf/gpuWork.o
[   11.597586] [  T456] LibBpfLoader: cs[0].name:tracepoint_power_gpu_work_period min_kver:0 .max_kver:ffffffff (kvers:601004b)
[   11.597592] [  T456] LibBpfLoader: cs[0].name:tracepoint_power_gpu_work_period requires bpfloader version [0x0001c,0x10000)
[   11.598596] [  T456] LibBpfLoader: bpf_prog_load lib call for /system/etc/bpf/gpuWork.o (tracepoint_power_gpu_work_period) returned fd: 8 (no error)
[   11.598779] [  T456] LibBpfLoader: prog /sys/fs/bpf/prog_gpuWork_tracepoint_power_gpu_work_period id 32
[   11.598830] [  T456] bpfloader: Loaded object: /system/etc/bpf/gpuWork.o
[   11.599583] [  T456] LibBpfLoader: Loading optional ELF object /system/etc/bpf/gpuMem.o with license Apache 2.0
[   11.599621] [  T456] LibBpfLoader: Section bpfloader_min_ver value is 28 [0x1c]
[   11.599663] [  T456] LibBpfLoader: Section bpfloader_max_ver value is 65536 [0x10000]
[   11.599698] [  T456] LibBpfLoader: Couldn't find section bpfloader_min_required_ver (defaulting to 0 [0x0]).
[   11.599733] [  T456] LibBpfLoader: Section size_of_bpf_map_def value is 120 [0x78]
[   11.599772] [  T456] LibBpfLoader: Section size_of_bpf_prog_def value is 92 [0x5c]
[   11.599782] [  T456] LibBpfLoader: BpfLoader version 0x00026 processing ELF object /system/etc/bpf/gpuMem.o with ver [0x0001c,0x10000)
[   11.600082] [  T456] LibBpfLoader: Loaded code section 3 (tracepoint_gpu_mem_gpu_mem_total)
[   11.600220] [  T456] LibBpfLoader: Loaded relo section 3 (.reltracepoint/gpu_mem/gpu_mem_total)
[   11.600226] [  T456] LibBpfLoader: Adding section 3 to cs list
[   11.600633] [  T456] LibBpfLoader: Couldn't find section btf_min_bpfloader_ver (defaulting to 0 [0x0]).
[   11.600664] [  T456] LibBpfLoader: Couldn't find section btf_min_kernel_ver (defaulting to 0 [0x0]).
[   11.610780] [  T456] LibBpfLoader: bpf_create_map name gpu_mem_total_map, ret: 6
[   11.610856] [  T456] LibBpfLoader: map /sys/fs/bpf/map_gpuMem_gpu_mem_total_map id 45
[   11.610887] [  T456] LibBpfLoader: map_fd found at 0 is 6 in /system/etc/bpf/gpuMem.o
[   11.611162] [  T456] LibBpfLoader: cs[0].name:tracepoint_gpu_mem_gpu_mem_total min_kver:0 .max_kver:ffffffff (kvers:601004b)
[   11.611169] [  T456] LibBpfLoader: cs[0].name:tracepoint_gpu_mem_gpu_mem_total requires bpfloader version [0x0001c,0x10000)
[   11.612096] [  T456] LibBpfLoader: bpf_prog_load lib call for /system/etc/bpf/gpuMem.o (tracepoint_gpu_mem_gpu_mem_total) returned fd: 7 (no error)
[   11.612293] [  T456] LibBpfLoader: prog /sys/fs/bpf/prog_gpuMem_tracepoint_gpu_mem_gpu_mem_total id 33
[   11.612351] [  T456] bpfloader: Loaded object: /system/etc/bpf/gpuMem.o
[   11.614231] [    T1] init: Service 'bpfloader' (pid 456) exited with status 0 waiting took 0.299000 seconds
[   11.614277] [    T1] init: Sending signal 9 to service 'bpfloader' (pid 456) process group...
[   11.614437] [    T1] libprocessgroup: Successfully killed process cgroup uid 0 pid 456 in 0ms
[   11.615288] [    T1] init: processing action (ro.crypto.state=encrypted && ro.crypto.type=file && zygote-start) from (/system/etc/init/hw/init.rc:1090)
[   11.615343] [    T1] init: start_waiting_for_property("odsign.verification.done", "1"): already set
[   11.615441] [    T1] init: Command 'exec_start update_verifier_nonencrypted' action=ro.crypto.state=encrypted && ro.crypto.type=file && zygote-start (/system/etc/init/hw/init.rc:1093) took 0ms and failed: Service not found
[   11.616468] [    T1] init: starting service 'statsd'...
[   11.616954] [    T1] init: Created socket '/dev/socket/statsdw', mode 222, user 1066, group 1066
[   11.622568] [    T1] init: ... started service 'statsd' has pid 465
[   11.623040] [    T1] init: starting service 'netd'...
[   11.623626] [    T1] init: Created socket '/dev/socket/dnsproxyd', mode 660, user 0, group 3003
[   11.624099] [    T1] init: Created socket '/dev/socket/mdns', mode 660, user 0, group 1000
[   11.624534] [    T1] init: Created socket '/dev/socket/fwmarkd', mode 660, user 0, group 3003
[   11.629519] [    T1] init: ... started service 'netd' has pid 466
[   11.629948] [    T1] init: starting service 'zygote'...
[   11.630508] [    T1] init: Created socket '/dev/socket/zygote', mode 660, user 0, group 1000
[   11.630866] [    T1] init: Created socket '/dev/socket/usap_pool_primary', mode 660, user 0, group 1000
[   11.635478] [  T467] libprocessgroup: SetCgroup::ExecuteForProcess: failed to open /dev/stune/top-app/cgroup.procs: No such file or directory
[   11.635507] [  T467] libprocessgroup: Failed to apply MaxPerformance process profile
[   11.635520] [  T467] init: failed to set task profiles
[   11.635977] [    T1] init: ... started service 'zygote' has pid 467
[   11.636361] [    T1] init: starting service 'zygote_secondary'...
[   11.636842] [    T1] init: Created socket '/dev/socket/zygote_secondary', mode 660, user 0, group 1000
[   11.637227] [    T1] init: Created socket '/dev/socket/usap_pool_secondary', mode 660, user 0, group 1000
[   11.642131] [  T468] libprocessgroup: SetCgroup::ExecuteForProcess: failed to open /dev/stune/top-app/cgroup.procs: No such file or directory
[   11.642165] [  T468] libprocessgroup: Failed to apply MaxPerformance process profile
[   11.642179] [  T468] init: failed to set task profiles
[   11.642661] [    T1] init: ... started service 'zygote_secondary' has pid 468
[   11.642767] [    T1] init: processing action (zygote-start) from (/vendor/etc/init/hw/init.connectivity.rc:2)
[   11.650015] [  T287] type=1400 audit(1749776987.588:1238): avc:  denied  { setattr } for  comm="init" name="nfc" dev="mmcblk0p15" ino=1489489 scontext=u:r:vendor_init:s0 tcontext=u:object_r:system_data_file:s0 tclass=dir permissive=1
[   11.650168] [  T287] type=1400 audit(1749776987.588:1239): avc:  denied  { read } for  comm="init" name="nfc" dev="mmcblk0p15" ino=1489489 scontext=u:r:vendor_init:s0 tcontext=u:object_r:system_data_file:s0 tclass=dir permissive=1
[   11.650245] [  T287] type=1400 audit(1749776987.588:1240): avc:  denied  { open } for  comm="init" path="/data/nfc" dev="mmcblk0p15" ino=1489489 scontext=u:r:vendor_init:s0 tcontext=u:object_r:system_data_file:s0 tclass=dir permissive=1
[   11.650307] [  T287] type=1400 audit(1749776987.588:1241): avc:  denied  { ioctl } for  comm="init" path="/data/nfc" dev="mmcblk0p15" ino=1489489 ioctlcmd=0x6615 scontext=u:r:vendor_init:s0 tcontext=u:object_r:system_data_file:s0 tclass=dir permissive=1
[   11.650387] [  T204] init: Verified that /data/nfc has the encryption policy f108e729abe705dc9c1118a9cab78588 v2 modes 1/4 flags 0x2
[   11.650849] [  T204] init: Verified that /data/nfc/param has the encryption policy f108e729abe705dc9c1118a9cab78588 v2 modes 1/4 flags 0x2
[   11.651084] [    T1] init: processing action (firmware_mounts_complete) from (/system/etc/init/hw/init.rc:528)
[   11.651253] [    T1] init: processing action (early-boot) from (/vendor/etc/init/hw/init.rk30board.usb.rc:1)
[   11.659079] [  T204] file system registered
[   11.681236] [  T204] using random self ethernet address
[   11.681265] [  T204] using random host ethernet address
[   11.686377] [  T204] using random self ethernet address
[   11.686407] [  T204] using random host ethernet address
[   11.722749] [    T1] init: processing action (early-boot) from (/system/etc/init/installd.rc:7)
[   11.734770] [    T1] init: processing action (boot) from (/system/etc/init/hw/init.rc:1104)
[   11.740728] [  T287] type=1400 audit(1749776987.676:1242): avc:  denied  { add_name } for  comm="init" name="discard_max_bytes" scontext=u:r:init:s0 tcontext=u:object_r:sysfs_mmc:s0 tclass=dir permissive=1
[   11.742090] [    T1] init: starting service 'hidl_memory'...
[   11.746229] [    T1] init: ... started service 'hidl_memory' has pid 476
[   11.746546] [    T1] init: starting service 'vendor.audio-hal'...
[   11.750820] [  T477] libprocessgroup: SetCgroup::ExecuteForProcess: failed to open /dev/stune/foreground/cgroup.procs: No such file or directory
[   11.750858] [  T477] libprocessgroup: Failed to apply HighPerformance process profile
[   11.750872] [  T477] init: failed to set task profiles
[   11.751376] [    T1] init: ... started service 'vendor.audio-hal' has pid 477
[   11.751679] [    T1] init: starting service 'vendor.bluetooth-1-0'...
[   11.756086] [  T478] libprocessgroup: SetCgroup::ExecuteForProcess: failed to open /dev/stune/foreground/cgroup.procs: No such file or directory
[   11.756150] [  T478] libprocessgroup: Failed to apply HighPerformance process profile
[   11.756170] [  T478] init: failed to set task profiles
[   11.756491] [    T1] init: ... started service 'vendor.bluetooth-1-0' has pid 478
[   11.756943] [    T1] init: starting service 'vendor.camera.provider-ext'...
[   11.762014] [    T1] init: ... started service 'vendor.camera.provider-ext' has pid 479
[   11.762016] [  T479] libprocessgroup: SetCgroup::ExecuteForProcess: failed to open /dev/stune/top-app/cgroup.procs: No such file or directory
[   11.762044] [  T479] libprocessgroup: Failed to apply MaxPerformance process profile
[   11.762069] [  T479] init: failed to set task profiles
[   11.762280] [    T1] init: starting service 'vendor.camera-provider-rk'...
[   11.766194] [  T480] libprocessgroup: SetCgroup::ExecuteForProcess: failed to open /dev/stune/camera-daemon/cgroup.procs: No such file or directory
[   11.766271] [  T480] libprocessgroup: Failed to apply CameraServicePerformance process profile
[   11.766293] [  T480] init: failed to set task profiles
[   11.766294] [    T1] init: ... started service 'vendor.camera-provider-rk' has pid 480
[   11.766601] [    T1] init: starting service 'vendor.drm-clearkey-service'...
[   11.771796] [    T1] init: ... started service 'vendor.drm-clearkey-service' has pid 481
[   11.772099] [    T1] init: starting service 'vendor.gatekeeper_default'...
[   11.775801] [    T1] init: ... started service 'vendor.gatekeeper_default' has pid 482
[   11.776126] [    T1] init: starting service 'vendor.gralloc-v1'...
[   11.780201] [    T1] init: ... started service 'vendor.gralloc-v1' has pid 483
[   11.780486] [    T1] init: starting service 'vendor.health-rockchip'...
[   11.784327] [    T1] init: ... started service 'vendor.health-rockchip' has pid 484
[   11.784588] [    T1] init: starting service 'android-hardware-media-c2-hal-1-1'...
[   11.789609] [    T1] init: ... started service 'android-hardware-media-c2-hal-1-1' has pid 485
[   11.789900] [    T1] init: starting service 'vendor.radio-config-hal-1-0'...
[   11.793219] [    T1] init: ... started service 'vendor.radio-config-hal-1-0' has pid 486
[   11.793472] [    T1] init: starting service 'vendor.radio-1-2'...
[   11.797485] [    T1] init: ... started service 'vendor.radio-1-2' has pid 487
[   11.797878] [    T1] init: starting service 'vendor.thermal-rockchip'...
[   11.801854] [    T1] init: ... started service 'vendor.thermal-rockchip' has pid 493
[   11.802247] [    T1] init: starting service 'vendor.cec-default'...
[   11.806528] [    T1] init: ... started service 'vendor.cec-default' has pid 494
[   11.807012] [    T1] init: starting service 'vendor.hdmi-default'...
[   11.811488] [    T1] init: ... started service 'vendor.hdmi-default' has pid 501
[   11.812361] [    T1] init: starting service 'vendor.usb'...
[   11.814950] [  T276] servicemanager: getDeviceHalManifest: Reloading VINTF information.
[   11.815197] [  T276] servicemanager: getDeviceHalManifest: Reading VINTF information.
[   11.819011] [    T1] init: ... started service 'vendor.usb' has pid 503
[   11.819453] [    T1] init: starting service 'vendor.usb_gadget_default'...
[   11.821857] [  T276] servicemanager: getDeviceHalManifest: Successfully processed VINTF information
[   11.821948] [  T276] servicemanager: Found android.system.net.netd.INetd/default in framework VINTF manifest.
[   11.823309] [    T1] init: ... started service 'vendor.usb_gadget_default' has pid 504
[   11.823601] [    T1] init: starting service 'vendor.weaver_default'...
[   11.828561] [    T1] init: ... started service 'vendor.weaver_default' has pid 505
[   11.828854] [    T1] init: starting service 'vendor.wifi_hal_legacy'...
[   11.833526] [    T1] init: ... started service 'vendor.wifi_hal_legacy' has pid 506
[   11.833836] [    T1] init: starting service 'vendor.cas-default'...
[   11.837617] [    T1] init: ... started service 'vendor.cas-default' has pid 507
[   11.837917] [    T1] init: starting service 'hwcomposer-3'...
[   11.843628] [    T1] init: ... started service 'hwcomposer-3' has pid 508
[   11.844078] [    T1] init: starting service 'vendor.light-rockchip'...
[   11.849206] [    T1] init: ... started service 'vendor.light-rockchip' has pid 509
[   11.849531] [    T1] init: starting service 'vendor.power-aidl-rockchip'...
[   11.853837] [    T1] init: ... started service 'vendor.power-aidl-rockchip' has pid 510
[   11.854340] [    T1] init: starting service 'hdmi_server'...
[   11.858350] [    T1] init: ... started service 'hdmi_server' has pid 511
[   11.858648] [    T1] init: starting service 'vendor.outputmanager-1-0'...
[   11.862160] [    T1] init: ... started service 'vendor.outputmanager-1-0' has pid 512
[   11.862410] [    T1] init: starting service 'vendor.rockit-hal-1-0'...
[   11.873639] [  T276] servicemanager: Found android.hardware.thermal.IThermal/default in device VINTF manifest.
[   11.876167] [    T1] init: ... started service 'vendor.rockit-hal-1-0' has pid 515
[   11.876646] [    T1] init: starting service 'vendor.usbgadget-1-0'...
[   11.879519] [  T276] servicemanager: Found android.hardware.cas.IMediaCasService/default in device VINTF manifest.
[   11.881111] [    T1] init: ... started service 'vendor.usbgadget-1-0' has pid 516
[   11.881464] [    T1] init: starting service 'vendor.uvcgadget-1-0'...
[   11.885234] [    T1] init: ... started service 'vendor.uvcgadget-1-0' has pid 517
[   11.885575] [    T1] init: starting service 'vendor.vndsystemmanager-1-0'...
[   11.890145] [    T1] init: ... started service 'vendor.vndsystemmanager-1-0' has pid 518
[   11.898615] [  T484] healthd: BatteryCycleCountPath not found
[   11.898639] [  T484] healthd: batteryStateOfHealthPath not found
[   11.898647] [  T484] healthd: batteryHealthStatusPath not found
[   11.898655] [  T484] healthd: batteryManufacturingDatePath not found
[   11.898662] [  T484] healthd: batteryFirstUsageDatePath not found
[   11.898669] [  T484] healthd: chargingStatePath not found
[   11.898677] [  T484] healthd: chargingPolicyPath not found
[   11.901754] [  T276] servicemanager: Found android.hardware.health.IHealth/default in device VINTF manifest.
[   11.903549] [  T484] healthd: battery l=50 v=3300 t=2.6 h=2 st=3 c=-1600 fc=100 chg=au
[   11.913101] [  T276] servicemanager: Found android.hardware.tv.hdmi.connection.IHdmiConnection/default in device VINTF manifest.
[   11.915789] [    T1] init: starting service 'vendor.sensors-default'...
[   11.917463] [  T276] servicemanager: Found android.hardware.drm.IDrmFactory/clearkey in device VINTF manifest.
[   11.932011] [  T276] servicemanager: Found android.hardware.tv.hdmi.cec.IHdmiCec/default in device VINTF manifest.
[   11.936328] [  T276] servicemanager: Found android.hardware.usb.gadget.IUsbGadget/default in device VINTF manifest.
[   11.937071] [  T276] servicemanager: Found android.hardware.usb.IUsb/default in device VINTF manifest.
[   11.939939] [    T1] init: ... started service 'vendor.sensors-default' has pid 524
[   11.940014] [    T1] init: Command 'class_start hal' action=boot (/system/etc/init/hw/init.rc:1210) took 198ms and succeeded
[   11.940077] [    T1] init: service 'ueventd' requested start, but it is already running (flags: 2084)
[   11.940222] [    T1] init: Could not start service 'displayd' as part of class 'core': Cannot find '/system/bin/displayd': No such file or directory
[   11.940679] [    T1] init: service 'apexd' requested start, but it is already running (flags: 134)
[   11.942947] [    T1] init: starting service 'audioserver'...
[   11.943917] [  T276] servicemanager: Found android.hardware.graphics.allocator.IAllocator/default in device VINTF manifest.
[   11.946077] [  T276] servicemanager: Found android.hardware.power.IPower/default in device VINTF manifest.
[   11.947541] [  T276] servicemanager: Found android.hardware.light.ILights/default in device VINTF manifest.
[   11.948789] [  T539] libprocessgroup: SetCgroup::ExecuteForProcess: failed to open /dev/stune/foreground/cgroup.procs: No such file or directory
[   11.948897] [  T539] libprocessgroup: Failed to apply HighPerformance process profile
[   11.948922] [  T539] init: failed to set task profiles
[   11.949203] [    T1] init: ... started service 'audioserver' has pid 539
[   11.949614] [    T1] init: starting service 'credstore'...
[   11.953947] [  T276] servicemanager: Found android.hardware.wifi.IWifi/default in device VINTF manifest.
[   11.954829] [  T276] servicemanager: Found android.hardware.weaver.IWeaver/default in device VINTF manifest.
[   11.955085] [  T276] servicemanager: Found android.hardware.gatekeeper.IGatekeeper/default in device VINTF manifest.
[   11.955149] [    T1] init: ... started service 'credstore' has pid 541
[   11.955464] [    T1] init: starting service 'gpu'...
[   11.960485] [    T1] init: ... started service 'gpu' has pid 544
[   11.960555] [    T1] init: service 'lmkd' requested start, but it is already running (flags: 36)
[   11.960609] [    T1] init: service 'servicemanager' requested start, but it is already running (flags: 2084)
[   11.960939] [    T1] init: starting service 'surfaceflinger'...
[   11.961356] [    T1] init: Could not create socket 'pdx/system/vr/display/client': Failed to bind socket 'pdx/system/vr/display/client': No such file or directory
[   11.961577] [    T1] init: Could not create socket 'pdx/system/vr/display/manager': Failed to bind socket 'pdx/system/vr/display/manager': No such file or directory
[   11.961764] [    T1] init: Could not create socket 'pdx/system/vr/display/vsync': Failed to bind socket 'pdx/system/vr/display/vsync': No such file or directory
[   11.965318] [  T551] libprocessgroup: SetCgroup::ExecuteForProcess: failed to open /dev/stune/foreground/cgroup.procs: No such file or directory
[   11.965375] [  T551] libprocessgroup: Failed to apply HighPerformance process profile
[   11.965395] [  T551] init: failed to set task profiles
[   11.965417] [    T1] init: ... started service 'surfaceflinger' has pid 551
[   11.965465] [    T1] init: service 'vold' requested start, but it is already running (flags: 2052)
[   11.965515] [    T1] init: service 'tee-supplicant' requested start, but it is already running (flags: 4)
[   11.965569] [    T1] init: service 'vndservicemanager' requested start, but it is already running (flags: 2052)
[   11.965684] [    T1] init: processing action (persist.sys.usb.config=* && boot) from (/system/etc/init/hw/init.usb.rc:108)
[   11.965847] [    T1] init: processing action (boot) from (/vendor/etc/init/hw/init.rk30board.rc:35)
[   11.988116] [    T1] init: Command 'write /sys/module/pm_domains/parameters/keepon_startup 0' action=boot (/vendor/etc/init/hw/init.rk30board.rc:237) took 0ms and failed: Unable to write to file '/sys/module/pm_domains/parameters/keepon_startup': open() failed: Permission denied
[   11.988139] [  T287] type=1400 audit(1749776987.924:1243): avc:  denied  { add_name } for  comm="init" name="keepon_startup" scontext=u:r:vendor_init:s0 tcontext=u:object_r:sysfs:s0 tclass=dir permissive=1
[   11.988152] [    T1] init: processing action (boot) from (/vendor/etc/init/hw/init.rk30board.usb.rc:251)
[   11.988244] [  T287] type=1400 audit(1749776987.924:1244): avc:  denied  { create } for  comm="init" name="keepon_startup" scontext=u:r:vendor_init:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[   11.989388] [  T287] type=1400 audit(1749776987.928:1245): avc:  denied  { create } for  comm="init" name="initial_descriptor_timeout" scontext=u:r:vendor_init:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[   11.989720] [    T1] init: processing action (boot) from (/vendor/etc/init/hw/init.rk3576.rc:46)
[   11.997999] [    T1] init: processing action (boot) from (/system/etc/init/dumpstate.rc:1)
[   11.998102] [    T1] init: processing action (boot) from (/system/etc/init/gsid.rc:25)
[   11.998405] [    T1] init: starting service 'exec 11 (/system/bin/gsid run-startup-tasks)'...
[   12.001255] [    T1] init: ... started service 'exec 11 (/system/bin/gsid run-startup-tasks)' has pid 554
[   12.001382] [    T1] init: processing action (enable_property_trigger) from (<Builtin Action>:0)
[   12.001702] [    T1] init: processing action (apexd.status=ready && ro.product.cpu.abilist32=*) from (/system/etc/init/hw/init.rc:488)
[   12.002142] [    T1] init: starting service 'boringssl_self_test_apex32'...
[   12.006085] [    T1] init: ... started service 'boringssl_self_test_apex32' has pid 555
[   12.006160] [    T1] init: SVC_EXEC service 'boringssl_self_test_apex32' pid 555 (uid 9999 gid 0+0 context default) started; waiting...
[   12.022299] [  T276] servicemanager: Found android.hardware.sensors.ISensors/default in device VINTF manifest.
[   12.045885] [    T1] init: Service 'exec 11 (/system/bin/gsid run-startup-tasks)' (pid 554) exited with status 0 oneshot service took 0.045000 seconds in background
[   12.045919] [    T1] init: Sending signal 9 to service 'exec 11 (/system/bin/gsid run-startup-tasks)' (pid 554) process group...
[   12.046056] [    T1] libprocessgroup: Successfully killed process cgroup uid 0 pid 554 in 0ms
[   12.063520] [    T1] init: Service 'boringssl_self_test_apex32' (pid 555) exited with status 0 waiting took 0.059000 seconds
[   12.063566] [    T1] init: Sending signal 9 to service 'boringssl_self_test_apex32' (pid 555) process group...
[   12.063689] [    T1] libprocessgroup: Successfully killed process cgroup uid 9999 pid 555 in 0ms
[   12.064539] [    T1] init: processing action (apexd.status=ready && ro.product.cpu.abilist64=*) from (/system/etc/init/hw/init.rc:490)
[   12.065040] [    T1] init: starting service 'boringssl_self_test_apex64'...
[   12.069701] [    T1] init: ... started service 'boringssl_self_test_apex64' has pid 569
[   12.069783] [    T1] init: SVC_EXEC service 'boringssl_self_test_apex64' pid 569 (uid 9999 gid 0+0 context default) started; waiting...
[   12.113372] [    T1] init: Service 'boringssl_self_test_apex64' (pid 569) exited with status 0 waiting took 0.046000 seconds
[   12.113450] [    T1] init: Sending signal 9 to service 'boringssl_self_test_apex64' (pid 569) process group...
[   12.113712] [    T1] libprocessgroup: Successfully killed process cgroup uid 9999 pid 569 in 0ms
[   12.114660] [    T1] init: processing action (bootreceiver.enable=1 && ro.product.cpu.abilist64=*) from (/system/etc/init/hw/init.rc:668)
[   12.150292] [  T287] type=1107 audit(**********.088:1246): uid=0 auid=********** ses=********** subj=u:r:init:s0 msg='avc:  denied  { set } for property=vendor.vehicle.camera.count pid=479 uid=1047 gid=1005 scontext=u:r:hal_camera_default:s0 tcontext=u:object_r:vendor_default_prop:s0 tclass=property_service permissive=1'
[   12.237762] [  T287] type=1400 audit(**********.176:1247): avc:  denied  { read } for  comm="rockchip.hardwa" name="u:object_r:vendor_system_public_prop:s0" dev="tmpfs" ino=339 scontext=u:r:rk_uvcgadget_hal:s0 tcontext=u:object_r:vendor_system_public_prop:s0 tclass=file permissive=1
[   12.237946] [  T287] type=1400 audit(**********.176:1248): avc:  denied  { open } for  comm="rockchip.hardwa" path="/dev/__properties__/u:object_r:vendor_system_public_prop:s0" dev="tmpfs" ino=339 scontext=u:r:rk_uvcgadget_hal:s0 tcontext=u:object_r:vendor_system_public_prop:s0 tclass=file permissive=1
[   12.238032] [  T287] type=1400 audit(**********.176:1249): avc:  denied  { getattr } for  comm="rockchip.hardwa" path="/dev/__properties__/u:object_r:vendor_system_public_prop:s0" dev="tmpfs" ino=339 scontext=u:r:rk_uvcgadget_hal:s0 tcontext=u:object_r:vendor_system_public_prop:s0 tclass=file permissive=1
[   12.238095] [  T287] type=1400 audit(**********.176:1250): avc:  denied  { map } for  comm="rockchip.hardwa" path="/dev/__properties__/u:object_r:vendor_system_public_prop:s0" dev="tmpfs" ino=339 scontext=u:r:rk_uvcgadget_hal:s0 tcontext=u:object_r:vendor_system_public_prop:s0 tclass=file permissive=1
[   12.273023] [    T1] init: Command 'mkdir /sys/kernel/tracing/instances/bootreceiver 0700 system system' action=bootreceiver.enable=1 && ro.product.cpu.abilist64=* (/system/etc/init/hw/init.rc:672) took 158ms and succeeded
[   12.273486] [  T276] servicemanager: Found android.hardware.camera.provider.ICameraProvider/external/0 in device VINTF manifest.
[   12.274461] [  T276] servicemanager: Found android.hardware.bluetooth.audio.IBluetoothAudioProviderFactory/default in device VINTF manifest.
[   12.284064] [  T287] type=1400 audit(**********.220:1251): avc:  denied  { read } for  comm="android.hardwar" name="u:object_r:default_prop:s0" dev="tmpfs" ino=138 scontext=u:r:hal_graphics_composer_default:s0 tcontext=u:object_r:default_prop:s0 tclass=file permissive=1
[   12.284260] [  T287] type=1400 audit(**********.220:1252): avc:  denied  { open } for  comm="android.hardwar" path="/dev/__properties__/u:object_r:default_prop:s0" dev="tmpfs" ino=138 scontext=u:r:hal_graphics_composer_default:s0 tcontext=u:object_r:default_prop:s0 tclass=file permissive=1
[   12.284361] [  T287] type=1400 audit(**********.220:1253): avc:  denied  { getattr } for  comm="android.hardwar" path="/dev/__properties__/u:object_r:default_prop:s0" dev="tmpfs" ino=138 scontext=u:r:hal_graphics_composer_default:s0 tcontext=u:object_r:default_prop:s0 tclass=file permissive=1
[   12.284437] [  T287] type=1400 audit(**********.220:1254): avc:  denied  { map } for  comm="android.hardwar" path="/dev/__properties__/u:object_r:default_prop:s0" dev="tmpfs" ino=138 scontext=u:r:hal_graphics_composer_default:s0 tcontext=u:object_r:default_prop:s0 tclass=file permissive=1
[   12.286214] [    T1] init: processing action (net.tcp_def_init_rwnd=*) from (/system/etc/init/hw/init.rc:1242)
[   12.286538] [    T1] init: processing action (security.perf_harden=1) from (/system/etc/init/hw/init.rc:1267)
[   12.287273] [    T1] init: processing action (ro.debuggable=0) from (/system/etc/init/hw/init.rc:1304)
[   12.287535] [  T287] type=1400 audit(**********.224:1255): avc:  denied  { setattr } for  comm="init" name="otg_mode" dev="sysfs" ino=45437 scontext=u:r:init:s0 tcontext=u:object_r:sysfs_usb2_phy:s0 tclass=file permissive=1
[   12.287677] [  T287] type=1400 audit(**********.224:1256): avc:  denied  { write } for  comm="init" name="otg_mode" dev="sysfs" ino=45437 scontext=u:r:init:s0 tcontext=u:object_r:sysfs_usb2_phy:s0 tclass=file permissive=1
[   12.287760] [  T287] type=1400 audit(**********.224:1257): avc:  denied  { open } for  comm="init" path="/sys/devices/platform/2602e000.syscon/2602e000.syscon:usb2-phy@0/otg_mode" dev="sysfs" ino=45437 scontext=u:r:init:s0 tcontext=u:object_r:sysfs_usb2_phy:s0 tclass=file permissive=1
[   12.288458] [    T1] init: processing action (persist.sys.ext_ram=*) from (/vendor/etc/init/hw/init.rk30board.rc:246)
[   12.289278] [    T1] init: processing action (wlan.driver.status=ok) from (/vendor/etc/init/hw/init.connectivity.rc:60)
[   12.289657] [    T1] init: processing action (init.svc.audioserver=running) from (/system/etc/init/audioserver.rc:35)
[   12.289692] [    T1] init: service 'vendor.audio-hal' requested start, but it is already running (flags: 4)
[   12.289728] [    T1] init: Command 'start vendor.audio-hal-aidl' action=init.svc.audioserver=running (/system/etc/init/audioserver.rc:37) took 0ms and failed: service vendor.audio-hal-aidl not found
[   12.289760] [    T1] init: Command 'start vendor.audio-effect-hal-aidl' action=init.svc.audioserver=running (/system/etc/init/audioserver.rc:38) took 0ms and failed: service vendor.audio-effect-hal-aidl not found
[   12.289786] [    T1] init: Command 'start vendor.audio-hal-4-0-msd' action=init.svc.audioserver=running (/system/etc/init/audioserver.rc:39) took 0ms and failed: service vendor.audio-hal-4-0-msd not found
[   12.289811] [    T1] init: Command 'start audio_proxy_service' action=init.svc.audioserver=running (/system/etc/init/audioserver.rc:40) took 0ms and failed: service audio_proxy_service not found
[   12.289831] [    T1] init: processing action (ro.persistent_properties.ready=true) from (/system/etc/init/bootstat.rc:57)
[   12.290249] [    T1] init: processing action (ro.persistent_properties.ready=true) from (/system/etc/init/bootstat.rc:61)
[   12.291229] [    T1] init: starting service 'exec 12 (/system/bin/bootstat --set_system_boot_reason)'...
[   12.294478] [    T1] init: ... started service 'exec 12 (/system/bin/bootstat --set_system_boot_reason)' has pid 587
[   12.294693] [    T1] init: processing action (bootreceiver.enable=1 && ro.product.cpu.abilist64=*) from (/system/etc/init/dmesgd.rc:7)
[   12.295637] [    T1] init: Command 'rm /data/misc/dmesgd/sent_reports.txt' action=bootreceiver.enable=1 && ro.product.cpu.abilist64=* (/system/etc/init/dmesgd.rc:9) took 0ms and failed: unlink() failed: No such file or directory
[   12.295676] [    T1] init: processing action (drm.service.enabled=true) from (/system/etc/init/drmserver.rc:8)
[   12.295960] [    T1] init: starting service 'drm'...
[   12.300851] [    T1] init: ... started service 'drm' has pid 588
[   12.301003] [    T1] init: processing action (persist.heapprofd.enable= && traced.lazy.heapprofd=) from (/system/etc/init/heapprofd.rc:49)
[   12.301633] [    T1] init: processing action (ro.debuggable=*) from (/system/etc/init/llkd.rc:2)
[   12.302553] [    T1] init: processing action (debug.atrace.user_initiated= && persist.traced.enable=1) from (/system/etc/init/perfetto.rc:47)
[   12.302879] [    T1] init: starting service 'traced_probes'...
[   12.307183] [    T1] init: ... started service 'traced_probes' has pid 589
[   12.307327] [    T1] init: processing action (persist.traced.enable=1) from (/system/etc/init/perfetto.rc:50)
[   12.309507] [    T1] init: starting service 'traced'...
[   12.309876] [    T1] init: Created socket '/dev/socket/traced_consumer', mode 666, user 0, group 0
[   12.310100] [    T1] init: Created socket '/dev/socket/traced_producer', mode 666, user 0, group 0
[   12.314338] [    T1] init: ... started service 'traced' has pid 590
[   12.314508] [    T1] init: service 'traced_probes' requested start, but it is already running (flags: 132)
[   12.314569] [    T1] init: processing action (ro.persistent_properties.ready=true) from (/system/etc/init/perfetto.rc:116)
[   12.314643] [    T1] init: processing action (persist.traced_perf.enable= && sys.init.perf_lsm_hooks=1 && traced.lazy.traced_perf=) from (/system/etc/init/traced_perf.rc:43)
[   12.315324] [  T508] dwhdmi-rockchip 27da0000.hdmi: use tmds mode
[   12.316296] [    T1] init: processing action (vendor.usb.controller=*) from (/vendor/etc/init/init.rockchip_udc_detector.rc:11)
[   12.317103] [    T1] init: processing action (nonencrypted) from (/system/etc/init/hw/init.rc:1214)
[   12.317303] [    T1] init: Could not start service 'bplus_helper' as part of class 'main': Cannot find '/system/bin/bplus_helper': No such file or directory
[   12.317649] [    T1] init: Could not start service 'up_eth0' as part of class 'main': Cannot find '/system/bin/busybox': No such file or directory
[   12.317775] [    T1] init: Could not start service 'rk_store_keybox' as part of class 'main': Cannot find '/vendor/bin/rk_store_keybox': No such file or directory
[   12.317986] [    T1] init: service 'zygote_secondary' requested start, but it is already running (flags: 4)
[   12.318012] [    T1] init: service 'zygote' requested start, but it is already running (flags: 4)
[   12.318644] [    T1] init: starting service 'cameraserver'...
[   12.323356] [  T594] libprocessgroup: SetCgroup::ExecuteForProcess: failed to open /dev/stune/top-app/cgroup.procs: No such file or directory
[   12.323391] [  T594] libprocessgroup: Failed to apply MaxPerformance process profile
[   12.323406] [  T594] init: failed to set task profiles
[   12.324417] [    T1] init: ... started service 'cameraserver' has pid 594
[   12.324482] [    T1] init: service 'drm' requested start, but it is already running (flags: 132)
[   12.324818] [    T1] init: starting service 'idmap2d'...
[   12.329547] [    T1] init: ... started service 'idmap2d' has pid 598
[   12.329853] [    T1] init: starting service 'incidentd'...
[   12.337163] [    T1] init: ... started service 'incidentd' has pid 599
[   12.337548] [    T1] init: starting service 'installd'...
[   12.341549] [    T1] init: ... started service 'installd' has pid 600
[   12.341963] [    T1] init: starting service 'mediaextractor'...
[   12.355404] [    T1] init: ... started service 'mediaextractor' has pid 601
[   12.356051] [    T1] init: starting service 'mediametrics'...
[   12.361322] [  T276] servicemanager: Found android.hardware.graphics.composer3.IComposer/default in device VINTF manifest.
[   12.369964] [  T609] libprocessgroup: SetCgroup::ExecuteForProcess: failed to open /dev/stune/foreground/cgroup.procs: No such file or directory
[   12.370021] [  T609] libprocessgroup: Failed to apply HighPerformance process profile
[   12.370040] [  T609] init: failed to set task profiles
[   12.400073] [    T1] init: ... started service 'mediametrics' has pid 609
[   12.400362] [    T1] init: starting service 'media'...
[   12.403841] [  T621] libprocessgroup: SetCgroup::ExecuteForProcess: failed to open /dev/stune/foreground/cgroup.procs: No such file or directory
[   12.403896] [  T621] libprocessgroup: Failed to apply HighPerformance process profile
[   12.403915] [  T621] init: failed to set task profiles
[   12.432122] [  T287] type=1400 audit(**********.368:1258): avc:  denied  { write } for  comm="atrace" name="enable" dev="tracefs" ino=10541 scontext=u:r:atrace:s0 tcontext=u:object_r:debugfs_tracing_debug:s0 tclass=file permissive=1
[   12.484583] [    T1] init: ... started service 'media' has pid 621
[   12.484640] [    T1] init: service 'netd' requested start, but it is already running (flags: 4)
[   12.484937] [    T1] init: starting service 'storaged'...
[   12.485089] [    T1] init: Could not open file '/d/mmc0/mmc0:0001/ext_csd': Failed to open file '/d/mmc0/mmc0:0001/ext_csd': No such file or directory
[   12.488708] [    T1] init: ... started service 'storaged' has pid 633
[   12.488994] [    T1] init: starting service 'systemmanagerserver'...
[   12.492225] [  T634] libprocessgroup: SetCgroup::ExecuteForProcess: failed to open /dev/stune/foreground/cgroup.procs: No such file or directory
[   12.492294] [  T634] libprocessgroup: Failed to apply HighPerformance process profile
[   12.492314] [  T634] init: failed to set task profiles
[   12.492927] [    T1] init: ... started service 'systemmanagerserver' has pid 634
[   12.493245] [    T1] init: starting service 'wificond'...
[   12.497265] [    T1] init: ... started service 'wificond' has pid 635
[   12.497610] [    T1] init: starting service 'akmd'...
[   12.501563] [    T1] init: ... started service 'akmd' has pid 636
[   12.501823] [    T1] init: starting service 'vendor.ril-daemon'...
[   12.506558] [    T1] init: ... started service 'vendor.ril-daemon' has pid 637
[   12.506879] [    T1] init: starting service 'vendor_flash_recovery'...
[   12.510588] [    T1] init: ... started service 'vendor_flash_recovery' has pid 638
[   12.510974] [    T1] init: starting service 'media.swcodec'...
[   12.515238] [    T1] init: ... started service 'media.swcodec' has pid 639
[   12.515342] [    T1] init: service 'statsd' requested start, but it is already running (flags: 4)
[   12.515482] [    T1] init: Command 'class_start main' action=nonencrypted (/system/etc/init/hw/init.rc:1215) took 198ms and succeeded
[   12.515625] [    T1] init: Service 'exec 12 (/system/bin/bootstat --set_system_boot_reason)' (pid 587) exited with status 0 oneshot service took 0.222000 seconds in background
[   12.515651] [    T1] init: Sending signal 9 to service 'exec 12 (/system/bin/bootstat --set_system_boot_reason)' (pid 587) process group...
[   12.515895] [    T1] libprocessgroup: Successfully killed process cgroup uid 1000 pid 587 in 0ms
[   12.516674] [    T1] init: starting service 'gatekeeperd'...
[   12.520757] [    T1] init: ... started service 'gatekeeperd' has pid 640
[   12.520831] [    T1] init: service 'traced' requested start, but it is already running (flags: 132)
[   12.520848] [    T1] init: service 'traced_probes' requested start, but it is already running (flags: 132)
[   12.521249] [    T1] init: starting service 'usbd'...
[   12.527340] [    T1] init: ... started service 'usbd' has pid 642
[   12.527728] [    T1] init: starting service 'vendor_camera_threeaservice'...
[   12.533476] [    T1] init: ... started service 'vendor_camera_threeaservice' has pid 643
[   12.533649] [    T1] init: processing action (load_persist_props_action) from (/system/etc/init/flags_health_check.rc:1)
[   12.534751] [    T1] init: Encryption policy of /data/server_configurable_flags set to f108e729abe705dc9c1118a9cab78588 v2 modes 1/4 flags 0x2
[   12.535245] [    T1] init: starting service 'exec 13 (/system/bin/flags_health_check BOOT_FAILURE)'...
[   12.538672] [    T1] init: ... started service 'exec 13 (/system/bin/flags_health_check BOOT_FAILURE)' has pid 644
[   12.538790] [    T1] init: SVC_EXEC service 'exec 13 (/system/bin/flags_health_check BOOT_FAILURE)' pid 644 (uid 1000 gid 1000+0 context default) started; waiting...
[   12.539011] [    T1] init: Service 'akmd' (pid 636) exited with status 254 oneshot service took 0.039000 seconds in background
[   12.539050] [    T1] init: Sending signal 9 to service 'akmd' (pid 636) process group...
[   12.539226] [    T1] libprocessgroup: Successfully killed process cgroup uid 1000 pid 636 in 0ms
[   12.590221] [  T287] type=1400 audit(**********.528:1259): avc:  denied  { call } for  comm="systemmanagerse" scontext=u:r:systemmanagerserver:s0 tcontext=u:r:hal_hdmi_default:s0 tclass=binder permissive=1
[   12.590517] [  T287] type=1400 audit(**********.528:1260): avc:  denied  { read } for  comm="systemmanagerse" name="u:object_r:vendor_camera_prop:s0" dev="tmpfs" ino=330 scontext=u:r:systemmanagerserver:s0 tcontext=u:object_r:vendor_camera_prop:s0 tclass=file permissive=1
[   12.590642] [  T287] type=1400 audit(**********.528:1261): avc:  denied  { open } for  comm="systemmanagerse" path="/dev/__properties__/u:object_r:vendor_camera_prop:s0" dev="tmpfs" ino=330 scontext=u:r:systemmanagerserver:s0 tcontext=u:object_r:vendor_camera_prop:s0 tclass=file permissive=1
[   12.590737] [  T287] type=1400 audit(**********.528:1262): avc:  denied  { getattr } for  comm="systemmanagerse" path="/dev/__properties__/u:object_r:vendor_camera_prop:s0" dev="tmpfs" ino=330 scontext=u:r:systemmanagerserver:s0 tcontext=u:object_r:vendor_camera_prop:s0 tclass=file permissive=1
[   12.590814] [  T287] type=1400 audit(**********.528:1263): avc:  denied  { map } for  comm="systemmanagerse" path="/dev/__properties__/u:object_r:vendor_camera_prop:s0" dev="tmpfs" ino=330 scontext=u:r:systemmanagerserver:s0 tcontext=u:object_r:vendor_camera_prop:s0 tclass=file permissive=1
[   12.596630] [  T287] type=1400 audit(**********.532:1264): avc:  denied  { read write } for  comm="systemmanagerse" name="video5" dev="tmpfs" ino=428 scontext=u:r:systemmanagerserver:s0 tcontext=u:object_r:video_device:s0 tclass=chr_file permissive=1
[   12.596908] [  T287] type=1400 audit(**********.532:1265): avc:  denied  { open } for  comm="systemmanagerse" path="/dev/video5" dev="tmpfs" ino=428 scontext=u:r:systemmanagerserver:s0 tcontext=u:object_r:video_device:s0 tclass=chr_file permissive=1
[   12.597063] [  T287] type=1400 audit(**********.532:1266): avc:  denied  { ioctl } for  comm="systemmanagerse" path="/dev/video5" dev="tmpfs" ino=428 ioctlcmd=0x5600 scontext=u:r:systemmanagerserver:s0 tcontext=u:object_r:video_device:s0 tclass=chr_file permissive=1
[   12.611351] [  T276] servicemanager: Found android.hardware.usb.gadget.IUsbGadget/default in device VINTF manifest.
[   12.613453] [  T276] servicemanager: Found android.hardware.health.IHealth/default in device VINTF manifest.
[   12.617436] [    T1] init: Service 'exec 13 (/system/bin/flags_health_check BOOT_FAILURE)' (pid 644) exited with status 0 waiting took 0.080000 seconds
[   12.617478] [    T1] init: Sending signal 9 to service 'exec 13 (/system/bin/flags_health_check BOOT_FAILURE)' (pid 644) process group...
[   12.617601] [    T1] libprocessgroup: Successfully killed process cgroup uid 1000 pid 644 in 0ms
[   12.617668] [  T287] type=1400 audit(**********.552:1267): avc:  denied  { read } for  comm="android.hardwar" name="u:object_r:system_prop:s0" dev="tmpfs" ino=298 scontext=u:r:hal_usb_gadget_default:s0 tcontext=u:object_r:system_prop:s0 tclass=file permissive=1
[   12.617850] [  T287] type=1400 audit(**********.552:1268): avc:  denied  { open } for  comm="android.hardwar" path="/dev/__properties__/u:object_r:system_prop:s0" dev="tmpfs" ino=298 scontext=u:r:hal_usb_gadget_default:s0 tcontext=u:object_r:system_prop:s0 tclass=file permissive=1
[   12.617887] [    T1] init: Service 'usbd' (pid 642) exited with status 0 oneshot service took 0.094000 seconds in background
[   12.617909] [    T1] init: Sending signal 9 to service 'usbd' (pid 642) process group...
[   12.617953] [  T287] type=1400 audit(**********.552:1269): avc:  denied  { getattr } for  comm="android.hardwar" path="/dev/__properties__/u:object_r:system_prop:s0" dev="tmpfs" ino=298 scontext=u:r:hal_usb_gadget_default:s0 tcontext=u:object_r:system_prop:s0 tclass=file permissive=1
[   12.618032] [    T1] libprocessgroup: Successfully killed process cgroup uid 0 pid 642 in 0ms
[   12.618038] [  T287] type=1400 audit(**********.552:1270): avc:  denied  { map } for  comm="android.hardwar" path="/dev/__properties__/u:object_r:system_prop:s0" dev="tmpfs" ino=298 scontext=u:r:hal_usb_gadget_default:s0 tcontext=u:object_r:system_prop:s0 tclass=file permissive=1
[   12.618117] [  T287] type=1400 audit(**********.552:1271): avc:  denied  { read } for  comm="android.hardwar" name="u:object_r:serialno_prop:s0" dev="tmpfs" ino=279 scontext=u:r:hal_usb_gadget_default:s0 tcontext=u:object_r:serialno_prop:s0 tclass=file permissive=1
[   12.618189] [  T287] type=1400 audit(**********.552:1272): avc:  denied  { open } for  comm="android.hardwar" path="/dev/__properties__/u:object_r:serialno_prop:s0" dev="tmpfs" ino=279 scontext=u:r:hal_usb_gadget_default:s0 tcontext=u:object_r:serialno_prop:s0 tclass=file permissive=1
[   12.618250] [  T287] type=1400 audit(**********.552:1273): avc:  denied  { getattr } for  comm="android.hardwar" path="/dev/__properties__/u:object_r:serialno_prop:s0" dev="tmpfs" ino=279 scontext=u:r:hal_usb_gadget_default:s0 tcontext=u:object_r:serialno_prop:s0 tclass=file permissive=1
[   12.618311] [  T287] type=1400 audit(**********.552:1274): avc:  denied  { map } for  comm="android.hardwar" path="/dev/__properties__/u:object_r:serialno_prop:s0" dev="tmpfs" ino=279 scontext=u:r:hal_usb_gadget_default:s0 tcontext=u:object_r:serialno_prop:s0 tclass=file permissive=1
[   12.619373] [    T1] init: processing action (llk.enable=0) from (/system/etc/init/llkd.rc:12)
[   12.619917] [    T1] init: processing action (khungtask.enable=0) from (/system/etc/init/llkd.rc:21)
[   12.621047] [    T1] init: processing action (init.svc.media=*) from (/system/etc/init/mediaserver.rc:1)
[   12.622491] [    T1] init: processing action (khungtask.enable=false) from (/system/etc/init/llkd.rc:31)
[   12.622583] [  T287] type=1400 audit(**********.556:1275): avc:  denied  { read } for  comm="rild" name="/" dev="tmpfs" ino=1 scontext=u:r:rild:s0 tcontext=u:object_r:apex_mnt_dir:s0 tclass=dir permissive=1
[   12.622756] [  T287] type=1400 audit(**********.556:1276): avc:  denied  { open } for  comm="rild" path="/apex" dev="tmpfs" ino=1 scontext=u:r:rild:s0 tcontext=u:object_r:apex_mnt_dir:s0 tclass=dir permissive=1
[   12.633196] [  T276] servicemanager: Could not find android.hardware.net.nlinterceptor.IInterceptor/default in the VINTF manifest.
[   12.641441] [  T276] servicemanager: Found android.hardware.gatekeeper.IGatekeeper/default in device VINTF manifest.
[   12.677412] [  T287] type=1400 audit(**********.616:1277): avc:  denied  { ioctl } for  comm="systemmanagerse" path="/dev/video11" dev="tmpfs" ino=629 ioctlcmd=0x5600 scontext=u:r:systemmanagerserver:s0 tcontext=u:object_r:video_device:s0 tclass=chr_file permissive=1
[   12.678557] [  T287] type=1400 audit(**********.616:1278): avc:  denied  { read write } for  comm="systemmanagerse" name="video12" dev="tmpfs" ino=580 scontext=u:r:systemmanagerserver:s0 tcontext=u:object_r:video_device:s0 tclass=chr_file permissive=1
[   12.678706] [  T287] type=1400 audit(**********.616:1279): avc:  denied  { open } for  comm="systemmanagerse" path="/dev/video12" dev="tmpfs" ino=580 scontext=u:r:systemmanagerserver:s0 tcontext=u:object_r:video_device:s0 tclass=chr_file permissive=1
[   12.734815] [  T287] type=1400 audit(**********.672:1280): avc:  denied  { open } for  comm="surfaceflinger" path="/dev/__properties__/u:object_r:vendor_default_prop:s0" dev="tmpfs" ino=333 scontext=u:r:surfaceflinger:s0 tcontext=u:object_r:vendor_default_prop:s0 tclass=file permissive=1
[   12.735096] [  T287] type=1400 audit(**********.672:1281): avc:  denied  { getattr } for  comm="surfaceflinger" path="/dev/__properties__/u:object_r:vendor_default_prop:s0" dev="tmpfs" ino=333 scontext=u:r:surfaceflinger:s0 tcontext=u:object_r:vendor_default_prop:s0 tclass=file permissive=1
[   12.735219] [  T287] type=1400 audit(**********.672:1282): avc:  denied  { map } for  comm="surfaceflinger" path="/dev/__properties__/u:object_r:vendor_default_prop:s0" dev="tmpfs" ino=333 scontext=u:r:surfaceflinger:s0 tcontext=u:object_r:vendor_default_prop:s0 tclass=file permissive=1
[   12.744771] [  T276] servicemanager: Found android.hardware.graphics.allocator.IAllocator/default in device VINTF manifest.
[   12.759614] [  T276] servicemanager: Found android.hardware.graphics.allocator.IAllocator/default in device VINTF manifest.
[   12.937261] [  T480] imx415 4-0010: imx415_find_best_fit: cur_best_fit(7)
[   12.937335] [  T480] imx415 4-0010: set fmt: cur_mode: 1944x1097, hdr: 0, bpp: 12
[   12.938929] [  T480] imx415 4-0010: set vblank 0x814 vts 3165
[   12.938957] [  T480] imx415 4-0010: imx415_set_fmt: mode->mipi_freq_idx(0)
[   12.939001] [  T480] imx415 4-0010: imx415_find_best_fit: cur_best_fit(7)
[   12.939012] [  T480] imx415 4-0010: set fmt: cur_mode: 1944x1097, hdr: 0, bpp: 12
[   12.939026] [  T480] imx415 4-0010: imx415_set_fmt: mode->mipi_freq_idx(0)
[   12.939040] [  T480] imx415 4-0010: imx415_find_best_fit: cur_best_fit(4)
[   12.939046] [  T480] imx415 4-0010: set fmt: cur_mode: 3864x2192, hdr: 0, bpp: 12
[   12.939055] [  T480] imx415 4-0010: imx415_set_fmt: mode->mipi_freq_idx(1)
[   12.939065] [  T480] imx415 4-0010: imx415_find_best_fit: cur_best_fit(4)
[   12.939071] [  T480] imx415 4-0010: set fmt: cur_mode: 3864x2192, hdr: 0, bpp: 12
[   12.939078] [  T480] imx415 4-0010: imx415_set_fmt: mode->mipi_freq_idx(1)
[   12.939088] [  T480] imx415 4-0010: imx415_find_best_fit: cur_best_fit(4)
[   12.939094] [  T480] imx415 4-0010: set fmt: cur_mode: 3864x2192, hdr: 0, bpp: 12
[   12.939101] [  T480] imx415 4-0010: imx415_set_fmt: mode->mipi_freq_idx(1)
[   12.939111] [  T480] imx415 4-0010: imx415_find_best_fit: cur_best_fit(0)
[   12.939117] [  T480] imx415 4-0010: set fmt: cur_mode: 3864x2192, hdr: 0, bpp: 10
[   12.939124] [  T480] imx415 4-0010: imx415_set_fmt: mode->mipi_freq_idx(1)
[   12.939134] [  T480] imx415 4-0010: imx415_find_best_fit: cur_best_fit(0)
[   12.939139] [  T480] imx415 4-0010: set fmt: cur_mode: 3864x2192, hdr: 0, bpp: 10
[   12.939146] [  T480] imx415 4-0010: imx415_set_fmt: mode->mipi_freq_idx(1)
[   12.939156] [  T480] imx415 4-0010: imx415_find_best_fit: cur_best_fit(0)
[   12.939162] [  T480] imx415 4-0010: set fmt: cur_mode: 3864x2192, hdr: 0, bpp: 10
[   12.939169] [  T480] imx415 4-0010: imx415_set_fmt: mode->mipi_freq_idx(1)
[   12.939178] [  T480] imx415 4-0010: imx415_find_best_fit: cur_best_fit(0)
[   12.939184] [  T480] imx415 4-0010: set fmt: cur_mode: 3864x2192, hdr: 0, bpp: 10
[   12.939191] [  T480] imx415 4-0010: imx415_set_fmt: mode->mipi_freq_idx(1)
[   12.939248] [  T480] imx415 4-0010: imx415_find_best_fit: cur_best_fit(0)
[   12.939257] [  T480] imx415 4-0010: set fmt: cur_mode: 3864x2192, hdr: 0, bpp: 10
[   12.939264] [  T480] imx415 4-0010: imx415_set_fmt: mode->mipi_freq_idx(1)
[   12.965089] [  T480] m02_b_LT6911UXE 5-002b: enable_stream: disable
[   12.992998] [  T480] m02_b_LT6911UXE 5-002b: enable_stream: disable
[   12.995841] [  T287] type=1107 audit(**********.932:1283): uid=0 auid=********** ses=********** subj=u:r:init:s0 msg='avc:  denied  { set } for property=vendor.hwc.compose_policy pid=551 uid=1000 gid=1003 scontext=u:r:surfaceflinger:s0 tcontext=u:object_r:vendor_hwc_prop:s0 tclass=property_service permissive=1'
[   12.997208] [  T276] servicemanager: Found android.hardware.graphics.composer3.IComposer/default in device VINTF manifest.
[   13.021017] [  T480] m02_b_LT6911UXE 5-002b: enable_stream: disable
[   13.049069] [  T480] m02_b_LT6911UXE 5-002b: enable_stream: disable
[   13.076980] [  T480] m02_b_LT6911UXE 5-002b: enable_stream: disable
[   13.109018] [  T480] m02_b_LT6911UXE 5-002b: enable_stream: disable
[   13.137073] [  T480] m02_b_LT6911UXE 5-002b: enable_stream: disable
[   13.165002] [  T480] m02_b_LT6911UXE 5-002b: enable_stream: disable
[   13.193003] [  T480] m02_b_LT6911UXE 5-002b: enable_stream: disable
[   13.221016] [  T480] m02_b_LT6911UXE 5-002b: enable_stream: disable
[   13.249012] [  T480] m02_b_LT6911UXE 5-002b: enable_stream: disable
[   13.277015] [  T480] m02_b_LT6911UXE 5-002b: enable_stream: disable
[   13.305040] [  T480] m02_b_LT6911UXE 5-002b: enable_stream: disable
[   13.308438] [  T287] type=1400 audit(**********.244:1284): avc:  denied  { read } for  comm="systemmanagerse" name="u:object_r:serialno_prop:s0" dev="tmpfs" ino=279 scontext=u:r:systemmanagerserver:s0 tcontext=u:object_r:serialno_prop:s0 tclass=file permissive=1
[   13.308617] [  T287] type=1400 audit(**********.244:1285): avc:  denied  { open } for  comm="systemmanagerse" path="/dev/__properties__/u:object_r:serialno_prop:s0" dev="tmpfs" ino=279 scontext=u:r:systemmanagerserver:s0 tcontext=u:object_r:serialno_prop:s0 tclass=file permissive=1
[   13.308713] [  T287] type=1400 audit(**********.244:1286): avc:  denied  { getattr } for  comm="systemmanagerse" path="/dev/__properties__/u:object_r:serialno_prop:s0" dev="tmpfs" ino=279 scontext=u:r:systemmanagerserver:s0 tcontext=u:object_r:serialno_prop:s0 tclass=file permissive=1
[   13.308783] [  T287] type=1400 audit(**********.244:1287): avc:  denied  { map } for  comm="systemmanagerse" path="/dev/__properties__/u:object_r:serialno_prop:s0" dev="tmpfs" ino=279 scontext=u:r:systemmanagerserver:s0 tcontext=u:object_r:serialno_prop:s0 tclass=file permissive=1
[   13.309175] [  T287] type=1107 audit(**********.248:1288): uid=0 auid=********** ses=********** subj=u:r:init:s0 msg='avc:  denied  { set } for property=persist.camera.subvideo pid=634 uid=1000 gid=1005 scontext=u:r:systemmanagerserver:s0 tcontext=u:object_r:default_prop:s0 tclass=property_service permissive=1'
[   13.337005] [  T480] m02_b_LT6911UXE 5-002b: enable_stream: disable
[   13.365003] [  T480] m02_b_LT6911UXE 5-002b: enable_stream: disable
[   13.393002] [  T480] m02_b_LT6911UXE 5-002b: enable_stream: disable
[   13.420996] [  T480] m02_b_LT6911UXE 5-002b: enable_stream: disable
[   13.449007] [  T480] m02_b_LT6911UXE 5-002b: enable_stream: disable
[   13.462625] [  T681] imx415 4-0010: set hdr cfg, set mode to 1
[   13.462645] [  T681] imx415 4-0010: set fmt: cur_mode: 3864x2192, hdr: 5, bpp: 10
[   13.463976] [  T681] imx415 4-0010: set vblank 0x968 vts 2300
[   13.467294] [  T681] imx415 4-0010: imx415 is not streaming, save hdr ae!
[   13.481046] [  T480] m02_b_LT6911UXE 5-002b: enable_stream: disable
[   13.483200] [  T681] rkisp rkisp-vir0: first params buf queue
[   13.497547] [  T287] type=1400 audit(**********.436:1289): avc:  denied  { read } for  comm="rkaiq_multi_cam" name="u:object_r:default_prop:s0" dev="tmpfs" ino=138 scontext=u:r:vendor_camera_threeaservice:s0 tcontext=u:object_r:default_prop:s0 tclass=file permissive=1
[   13.497709] [  T287] type=1400 audit(**********.436:1290): avc:  denied  { open } for  comm="rkaiq_multi_cam" path="/dev/__properties__/u:object_r:default_prop:s0" dev="tmpfs" ino=138 scontext=u:r:vendor_camera_threeaservice:s0 tcontext=u:object_r:default_prop:s0 tclass=file permissive=1
[   13.497779] [  T287] type=1400 audit(**********.436:1291): avc:  denied  { getattr } for  comm="rkaiq_multi_cam" path="/dev/__properties__/u:object_r:default_prop:s0" dev="tmpfs" ino=138 scontext=u:r:vendor_camera_threeaservice:s0 tcontext=u:object_r:default_prop:s0 tclass=file permissive=1
[   13.497840] [  T287] type=1400 audit(**********.436:1292): avc:  denied  { map } for  comm="rkaiq_multi_cam" path="/dev/__properties__/u:object_r:default_prop:s0" dev="tmpfs" ino=138 scontext=u:r:vendor_camera_threeaservice:s0 tcontext=u:object_r:default_prop:s0 tclass=file permissive=1
[   13.508983] [  T480] m02_b_LT6911UXE 5-002b: enable_stream: disable
[   13.537032] [  T480] m02_b_LT6911UXE 5-002b: enable_stream: disable
[   13.565053] [  T480] m02_b_LT6911UXE 5-002b: enable_stream: disable
[   13.593010] [  T480] m02_b_LT6911UXE 5-002b: enable_stream: disable
[   13.621075] [  T480] m02_b_LT6911UXE 5-002b: enable_stream: disable
[   13.649020] [  T480] m02_b_LT6911UXE 5-002b: enable_stream: disable
[   13.653266] [  T287] type=1400 audit(**********.588:1293): avc:  denied  { read } for  comm="main" name="u:object_r:vendor_default_prop:s0" dev="tmpfs" ino=333 scontext=u:r:zygote:s0 tcontext=u:object_r:vendor_default_prop:s0 tclass=file permissive=1
[   13.653533] [  T287] type=1400 audit(**********.588:1294): avc:  denied  { open } for  comm="main" path="/dev/__properties__/u:object_r:vendor_default_prop:s0" dev="tmpfs" ino=333 scontext=u:r:zygote:s0 tcontext=u:object_r:vendor_default_prop:s0 tclass=file permissive=1
[   13.653754] [  T287] type=1400 audit(**********.588:1295): avc:  denied  { getattr } for  comm="main" path="/dev/__properties__/u:object_r:vendor_default_prop:s0" dev="tmpfs" ino=333 scontext=u:r:zygote:s0 tcontext=u:object_r:vendor_default_prop:s0 tclass=file permissive=1
[   13.653883] [  T287] type=1400 audit(**********.588:1296): avc:  denied  { map } for  comm="main" path="/dev/__properties__/u:object_r:vendor_default_prop:s0" dev="tmpfs" ino=333 scontext=u:r:zygote:s0 tcontext=u:object_r:vendor_default_prop:s0 tclass=file permissive=1
[   13.714930] [  T703] imx415-1 4-001a: set hdr cfg, set mode to 1
[   13.715007] [  T703] imx415-1 4-001a: set fmt: cur_mode: 3864x2192, hdr: 5, bpp: 10
[   13.716572] [  T703] imx415-1 4-001a: set vblank 0x968 vts 2300
[   13.720111] [  T703] imx415-1 4-001a: imx415 is not streaming, save hdr ae!
[   13.740932] [  T703] rkisp rkisp-vir1: first params buf queue
[   13.756334] [  T287] type=1107 audit(**********.692:1297): uid=0 auid=********** ses=********** subj=u:r:init:s0 msg='avc:  denied  { set } for property=vendor.camera.3a_ready pid=643 uid=1006 gid=1005 scontext=u:r:vendor_camera_threeaservice:s0 tcontext=u:object_r:vendor_default_prop:s0 tclass=property_service permissive=1'
[   13.770428] [  T276] servicemanager: Found android.hardware.camera.provider.ICameraProvider/internal/0 in device VINTF manifest.
[   13.801795] [    T1] init: Service 'vendor_flash_recovery' (pid 638) exited with status 0 oneshot service took 1.293000 seconds in background
[   13.801837] [    T1] init: Sending signal 9 to service 'vendor_flash_recovery' (pid 638) process group...
[   13.801996] [    T1] libprocessgroup: Successfully killed process cgroup uid 0 pid 638 in 0ms
[   13.901680] [  T276] servicemanager: Found android.frameworks.cameraservice.service.ICameraService/default in framework VINTF manifest.
[   13.902092] [  T276] servicemanager: Found android.frameworks.cameraservice.service.ICameraService/default in framework VINTF manifest.
[   14.442911] [    T1] init: starting service 'bootanim'...
[   14.447402] [  T736] libprocessgroup: SetCgroup::ExecuteForProcess: failed to open /dev/stune/top-app/cgroup.procs: No such file or directory
[   14.447461] [  T736] libprocessgroup: Failed to apply MaxPerformance process profile
[   14.447480] [  T736] init: failed to set task profiles
[   14.447535] [    T1] init: ... started service 'bootanim' has pid 736
[   14.447581] [    T1] init: Control message: Processed ctl.start for 'bootanim' from pid: 551 (/system/bin/surfaceflinger)
[   14.452120] [  T611] dwhdmi-rockchip 27da0000.hdmi: use tmds mode
[   14.753397] [  T287] type=1400 audit(1749776990.688:1298): avc:  denied  { open } for  comm="BootAnimation" path="/dev/__properties__/u:object_r:vendor_default_prop:s0" dev="tmpfs" ino=333 scontext=u:r:bootanim:s0 tcontext=u:object_r:vendor_default_prop:s0 tclass=file permissive=1
[   14.753557] [  T287] type=1400 audit(1749776990.688:1299): avc:  denied  { getattr } for  comm="BootAnimation" path="/dev/__properties__/u:object_r:vendor_default_prop:s0" dev="tmpfs" ino=333 scontext=u:r:bootanim:s0 tcontext=u:object_r:vendor_default_prop:s0 tclass=file permissive=1
[   14.753620] [  T287] type=1400 audit(1749776990.688:1300): avc:  denied  { map } for  comm="BootAnimation" path="/dev/__properties__/u:object_r:vendor_default_prop:s0" dev="tmpfs" ino=333 scontext=u:r:bootanim:s0 tcontext=u:object_r:vendor_default_prop:s0 tclass=file permissive=1
[   14.754775] [  T287] type=1107 audit(1749776990.692:1301): uid=0 auid=********** ses=********** subj=u:r:init:s0 msg='avc:  denied  { set } for property=sys.gmali.version pid=736 uid=1003 gid=1003 scontext=u:r:bootanim:s0 tcontext=u:object_r:system_prop:s0 tclass=property_service permissive=1'
[   14.816358] [  T276] servicemanager: Found android.hardware.graphics.allocator.IAllocator/default in device VINTF manifest.
[   14.821792] [  T276] servicemanager: Found android.hardware.graphics.allocator.IAllocator/default in device VINTF manifest.
[   14.844331] [  T276] servicemanager: Found android.hardware.power.IPower/default in device VINTF manifest.
[   15.462088] [  T287] type=1400 audit(1749776991.400:1302): avc:  denied  { dac_read_search } for  comm="system_server" capability=2  scontext=u:r:system_server:s0 tcontext=u:r:system_server:s0 tclass=capability permissive=1 bug=b/228030183
[   15.898132] [   T21] Freeing drm_logo memory: 8608K
[   16.159054] [  T276] servicemanager: Notifying apexservice they do (previously: don't) have clients when service is guaranteed to be in use
[   16.159232] [  T441] AidlLazyServiceRegistrar: Process has 1 (of 1 available) client(s) in use after notification apexservice has clients: 1
[   16.159250] [  T441] AidlLazyServiceRegistrar: Shutdown prevented by forcePersist override flag.
[   16.159532] [  T441] apexd: getActivePackages received by ApexService
[   16.191683] [  T276] servicemanager: Could not find android.hardware.power.stats.IPowerStats/default in the VINTF manifest.
[   16.195543] [  T276] servicemanager: Found android.frameworks.stats.IStats/default in framework VINTF manifest.
[   16.196129] [  T276] servicemanager: Could not find android.hardware.memtrack.IMemtrack/default in the VINTF manifest.
[   16.300996] [  T287] type=1400 audit(1749776992.236:1303): avc:  denied  { read } for  comm="binder:318_3" name="wakeup4" dev="sysfs" ino=39988 scontext=u:r:system_suspend:s0 tcontext=u:object_r:sysfs:s0 tclass=dir permissive=1
[   16.301146] [  T287] type=1400 audit(1749776992.236:1304): avc:  denied  { open } for  comm="binder:318_3" path="/sys/devices/virtual/power_supply/test_ac/wakeup4" dev="sysfs" ino=39988 scontext=u:r:system_suspend:s0 tcontext=u:object_r:sysfs:s0 tclass=dir permissive=1
[   16.301306] [  T287] type=1400 audit(1749776992.236:1305): avc:  denied  { read } for  comm="binder:318_3" name="event_count" dev="sysfs" ino=39995 scontext=u:r:system_suspend:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[   16.301366] [  T287] type=1400 audit(1749776992.236:1306): avc:  denied  { open } for  comm="binder:318_3" path="/sys/devices/virtual/power_supply/test_ac/wakeup4/event_count" dev="sysfs" ino=39995 scontext=u:r:system_suspend:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[   16.301423] [  T287] type=1400 audit(1749776992.236:1307): avc:  denied  { getattr } for  comm="binder:318_3" path="/sys/devices/virtual/power_supply/test_ac/wakeup4/event_count" dev="sysfs" ino=39995 scontext=u:r:system_suspend:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[   16.359931] [  T276] servicemanager: Found android.hardware.power.IPower/default in device VINTF manifest.
[   16.367375] [  T276] servicemanager: Found android.hardware.light.ILights/default in device VINTF manifest.
[   16.466665] [  T441] apexd: getAllPackages received by ApexService
[   17.604180] [  T287] type=1400 audit(1749776993.540:1308): avc:  denied  { read } for  comm="PerfMonitorThre" name="u:object_r:vendor_default_prop:s0" dev="tmpfs" ino=333 scontext=u:r:systemmanagerserver:s0 tcontext=u:object_r:vendor_default_prop:s0 tclass=file permissive=1
[   17.604374] [  T287] type=1400 audit(1749776993.540:1309): avc:  denied  { open } for  comm="PerfMonitorThre" path="/dev/__properties__/u:object_r:vendor_default_prop:s0" dev="tmpfs" ino=333 scontext=u:r:systemmanagerserver:s0 tcontext=u:object_r:vendor_default_prop:s0 tclass=file permissive=1
[   17.604443] [  T287] type=1400 audit(1749776993.540:1310): avc:  denied  { getattr } for  comm="PerfMonitorThre" path="/dev/__properties__/u:object_r:vendor_default_prop:s0" dev="tmpfs" ino=333 scontext=u:r:systemmanagerserver:s0 tcontext=u:object_r:vendor_default_prop:s0 tclass=file permissive=1
[   17.604502] [  T287] type=1400 audit(1749776993.540:1311): avc:  denied  { map } for  comm="PerfMonitorThre" path="/dev/__properties__/u:object_r:vendor_default_prop:s0" dev="tmpfs" ino=333 scontext=u:r:systemmanagerserver:s0 tcontext=u:object_r:vendor_default_prop:s0 tclass=file permissive=1
[   21.594090] [    T1] init: service 'idmap2d' requested start, but it is already running (flags: 4)
[   21.594168] [    T1] init: Control message: Processed ctl.start for 'idmap2d' from pid: 763 (system_server)
[   21.697210] [  T276] servicemanager: Found android.hardware.health.IHealth/default in device VINTF manifest.
[   21.699640] [  T276] servicemanager: Found android.hardware.sensors.ISensors/default in device VINTF manifest.
[   21.701122] [  T484] healthd: battery l=50 v=3300 t=2.6 h=2 st=3 c=-1600 fc=100 chg=au
[   21.990326] [  T276] servicemanager: Could not find android.hardware.vibrator.IVibratorManager/default in the VINTF manifest.
[   21.990765] [  T276] servicemanager: Could not find android.hardware.vibrator.IVibrator/default in the VINTF manifest.
[   22.148730] [  T287] type=1400 audit(**********.084:1312): avc:  denied  { read } for  comm="android.ui" name="name" dev="sysfs" ino=26811 scontext=u:r:system_server:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[   22.148900] [  T287] type=1400 audit(**********.084:1313): avc:  denied  { open } for  comm="android.ui" path="/sys/devices/platform/2602e000.syscon/2602e000.syscon:usb2-phy@2000/extcon/extcon0/cable.3/name" dev="sysfs" ino=26811 scontext=u:r:system_server:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[   22.149003] [  T287] type=1400 audit(**********.084:1314): avc:  denied  { getattr } for  comm="android.ui" path="/sys/devices/platform/2602e000.syscon/2602e000.syscon:usb2-phy@2000/extcon/extcon0/cable.3/name" dev="sysfs" ino=26811 scontext=u:r:system_server:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[   22.164712] [  T287] type=1107 audit(**********.100:1315): uid=0 auid=********** ses=********** subj=u:r:init:s0 msg='avc:  denied  { set } for property=vendor.czur.hdmi.out pid=634 uid=1000 gid=1005 scontext=u:r:systemmanagerserver:s0 tcontext=u:object_r:vendor_default_prop:s0 tclass=property_service permissive=1'
[   22.170488] [  T287] type=1400 audit(**********.108:1316): avc:  denied  { dac_read_search } for  comm="InputReader" capability=2  scontext=u:r:system_server:s0 tcontext=u:r:system_server:s0 tclass=capability permissive=1 bug=b/228030183
[   22.173035] [  T276] servicemanager: Found android.frameworks.sensorservice.ISensorManager/default in framework VINTF manifest.
[   23.238878] [    T1] init: processing action (sys.sysctl.extra_free_kbytes=*) from (/system/etc/init/hw/init.rc:1233)
[   23.239534] [    T1] init: starting service 'exec 14 (/system/bin/extra_free_kbytes.sh 24300)'...
[   23.245308] [    T1] init: ... started service 'exec 14 (/system/bin/extra_free_kbytes.sh 24300)' has pid 875
[   23.245396] [    T1] init: SVC_EXEC service 'exec 14 (/system/bin/extra_free_kbytes.sh 24300)' pid 875 (uid 0 gid 0+0 context default) started; waiting...
[   23.294144] [  T276] servicemanager: Since 'artd' could not be found, trying to start it as a lazy AIDL service. (if it's not configured to be a lazy service, it may be stuck starting or still starting).
[   23.295744] [    T1] init: starting service 'artd'...
[   23.302497] [    T1] init: ... started service 'artd' has pid 881
[   23.302598] [    T1] init: Control message: Processed ctl.interface_start for 'aidl/artd' from pid: 276 (/system/bin/servicemanager)
[   23.334805] [  T276] BpBinder: onLastStrongRef automatically unlinking death recipients: 
[   23.344369] [  T276] servicemanager: Notifying artd they do (previously: don't) have clients when service is guaranteed to be in use
[   23.537925] [    T1] init: Service 'exec 14 (/system/bin/extra_free_kbytes.sh 24300)' (pid 875) exited with status 0 waiting took 0.295000 seconds
[   23.537993] [    T1] init: Sending signal 9 to service 'exec 14 (/system/bin/extra_free_kbytes.sh 24300)' (pid 875) process group...
[   23.538222] [    T1] libprocessgroup: Successfully killed process cgroup uid 0 pid 875 in 0ms
[   24.913889] [  T276] servicemanager: Notifying apexservice they don't (previously: do) have clients when we now have no record of a client
[   24.914124] [  T441] AidlLazyServiceRegistrar: Process has 0 (of 1 available) client(s) in use after notification apexservice has clients: 0
[   24.914143] [  T441] AidlLazyServiceRegistrar: Shutdown prevented by forcePersist override flag.
[   31.666803] [    T1] init: Sending signal 9 to service 'idmap2d' (pid 598) process group...
[   31.672298] [    T1] libprocessgroup: Successfully killed process cgroup uid 1000 pid 598 in 5ms
[   31.672850] [    T1] init: Control message: Processed ctl.stop for 'idmap2d' from pid: 763 (system_server)
[   31.673163] [    T1] init: Service 'idmap2d' (pid 598) received signal 9
[   60.375454] [  T276] servicemanager: Could not find android.hardware.oemlock.IOemLock/default in the VINTF manifest.
[   60.652107] [  T276] servicemanager: Found android.hardware.wifi.IWifi/default in device VINTF manifest.
[   60.654519] [  T276] servicemanager: Found android.hardware.wifi.supplicant.ISupplicant/default in device VINTF manifest.
[   60.660028] [  T276] servicemanager: Found android.hardware.wifi.supplicant.ISupplicant/default in device VINTF manifest.
[   60.904351] [  T276] servicemanager: Found android.frameworks.location.altitude.IAltitudeService/default in framework VINTF manifest.
[   60.947472] [  T276] servicemanager: Could not find android.hardware.soundtrigger3.ISoundTriggerHw/default in the VINTF manifest.
[   60.966078] [  T276] servicemanager: Found android.hardware.thermal.IThermal/default in device VINTF manifest.
[   60.966736] [  T287] type=1400 audit(1749777036.904:1317): avc:  denied  { call } for  comm="system-server-i" scontext=u:r:system_server:s0 tcontext=u:r:hal_hdmi_default:s0 tclass=binder permissive=1
[   60.968621] [  T287] type=1400 audit(1749777036.904:1318): avc:  denied  { transfer } for  comm="system-server-i" scontext=u:r:system_server:s0 tcontext=u:r:hal_hdmi_default:s0 tclass=binder permissive=1
[   60.968825] [  T276] servicemanager: Found android.hardware.usb.gadget.IUsbGadget/default in device VINTF manifest.
[   60.977558] [  T276] servicemanager: Found android.hardware.usb.IUsb/default in device VINTF manifest.
[   61.096028] [  T276] SELinux: avc:  denied  { add } for pid=763 uid=1000 name=uacgadget scontext=u:r:system_server:s0 tcontext=u:object_r:default_android_service:s0 tclass=service_manager permissive=1
[   61.104139] [ T1208] dwc3 23000000.usb: device reset
[   61.152737] [   T21] android_work: sent uevent USB_STATE=CONNECTED
[   61.153095] [   T21] rk_send_wakeup_key: KEY_WAKEUP event sent
[   61.401480] [  T276] servicemanager: Could not find android.hardware.authsecret.IAuthSecret/default in the VINTF manifest.
[   61.577476] [  T502] rk_gmac-dwmac 2a220000.ethernet eth0: Register MEM_TYPE_PAGE_POOL RxQ-0
[   61.580859] [  T502] rk_gmac-dwmac 2a220000.ethernet eth0: PHY [stmmac-0:01] driver [Generic PHY] (irq=POLL)
[   61.581441] [  T502] dwmac4: Master AXI performs any burst length
[   61.581484] [  T502] rk_gmac-dwmac 2a220000.ethernet eth0: No Safety Features support found
[   61.581512] [  T502] rk_gmac-dwmac 2a220000.ethernet eth0: IEEE 1588-2008 Advanced Timestamp supported
[   61.582094] [  T502] rk_gmac-dwmac 2a220000.ethernet eth0: registered PTP clock
[   61.585690] [  T502] rk_gmac-dwmac 2a220000.ethernet eth0: FPE workqueue start
[   61.585762] [  T502] rk_gmac-dwmac 2a220000.ethernet eth0: configuring for phy/rgmii-rxid link mode
[   61.591949] [  T276] servicemanager: Found android.hardware.wifi.IWifi/default in device VINTF manifest.
[   61.610804] [ T1234] android_work: sent uevent USB_STATE=CONFIGURED
[   61.621123] [  T276] servicemanager: Found android.hardware.wifi.IWifi/default in device VINTF manifest.
[   61.622519] [    T1] init: processing action (wlan.driver.status=ok) from (/vendor/etc/init/hw/init.connectivity.rc:60)
[   61.641321] [    T1] init: processing action (vendor.wifi.direct.interface=p2p-dev-wlan0) from (/vendor/etc/init/hw/init.connectivity.rc:67)
[   61.643360] [  T276] servicemanager: Found android.hardware.wifi.IWifi/default in device VINTF manifest.
[   61.648148] [    T1] init: processing action (vendor.wifi.direct.interface=p2p-dev-wlan0) from (/vendor/etc/init/hw/init.connectivity.rc:67)
[   61.650514] [  T543] [dhd-pcie-0] [wlan0] dhd_open : Primary net_device is already up
[   61.651199] [    T1] init: processing action (wlan.driver.status=ok) from (/vendor/etc/init/hw/init.connectivity.rc:60)
[   61.664123] [    T1] init: processing action (vendor.wifi.direct.interface=p2p-dev-wlan0) from (/vendor/etc/init/hw/init.connectivity.rc:67)
[   61.678214] [  T276] servicemanager: Found android.hardware.wifi.IWifi/default in device VINTF manifest.
[   61.689001] [    T1] init: processing action (vendor.wifi.direct.interface=p2p-dev-wlan0) from (/vendor/etc/init/hw/init.connectivity.rc:67)
[   61.698745] [  T543] netlink: 'binder:506_3': attribute type 15 has an invalid length.
[   61.720191] [  T287] type=1400 audit(**********.656:1319): avc:  denied  { read } for  comm="system_server" name="btb_state" dev="sysfs" ino=48300 scontext=u:r:system_server:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[   61.720364] [  T287] type=1400 audit(**********.656:1320): avc:  denied  { open } for  comm="system_server" path="/sys/devices/platform/bluetooch_det/btb_state" dev="sysfs" ino=48300 scontext=u:r:system_server:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[   61.720464] [  T287] type=1400 audit(**********.656:1321): avc:  denied  { getattr } for  comm="system_server" path="/sys/devices/platform/bluetooch_det/btb_state" dev="sysfs" ino=48300 scontext=u:r:system_server:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[   61.721348] [  T287] type=1107 audit(**********.660:1322): uid=0 auid=********** ses=********** subj=u:r:init:s0 msg='avc:  denied  { set } for property=vendor.czur.btbdocked pid=763 uid=1000 gid=1000 scontext=u:r:system_server:s0 tcontext=u:object_r:vendor_default_prop:s0 tclass=property_service permissive=1'
[   61.722116] [  T276] servicemanager: Found android.hardware.health.IHealth/default in device VINTF manifest.
[   61.725159] [  T276] servicemanager: Found android.hardware.wifi.IWifi/default in device VINTF manifest.
[   61.725761] [  T276] servicemanager: Found android.hardware.wifi.supplicant.ISupplicant/default in device VINTF manifest.
[   61.726405] [  T276] servicemanager: Found android.hardware.wifi.supplicant.ISupplicant/default in device VINTF manifest.
[   61.726793] [  T276] servicemanager: Since 'android.hardware.wifi.supplicant.ISupplicant/default' could not be found, trying to start it as a lazy AIDL service. (if it's not configured to be a lazy service, it may be stuck starting or still starting).
[   61.728070] [    T1] init: starting service 'wpa_supplicant'...
[   61.728651] [    T1] init: Created socket '/dev/socket/wpa_wlan0', mode 660, user 1010, group 1010
[   61.732613] [    T1] init: ... started service 'wpa_supplicant' has pid 1242
[   61.732685] [    T1] init: Control message: Processed ctl.interface_start for 'aidl/android.hardware.wifi.supplicant.ISupplicant/default' from pid: 276 (/system/bin/servicemanager)
[   61.821174] [ T1242] capability: warning: `wpa_supplicant' uses 32-bit capabilities (legacy support in use)
[   61.823089] [  T276] servicemanager: Found android.hardware.wifi.supplicant.ISupplicant/default in device VINTF manifest.
[   61.823515] [  T276] BpBinder: onLastStrongRef automatically unlinking death recipients: 
[   61.848415] [ T1242] [dhd-sdio-1] CFG80211-ERROR) wl_cfg80211_netdev_notifier_call : wrong cfg ptr (000000006385d3e5)
[   61.848912] [ T1242] [dhd-sdio-1] CFG80211-ERROR) wl_cfg80211_netdev_notifier_call : wrong cfg ptr (000000006385d3e5)
[   61.872940] [  T276] servicemanager: Found android.hardware.wifi.IWifi/default in device VINTF manifest.
[   61.901749] [ T1242] [dhd-pcie-0] wl_android_priv_cmd: Android private cmd "COUNTRY CN" on wlan0
[   61.903837] [ T1242] [dhd-pcie-0] dhd_conf_same_country : country code = CN/0 is already configured
[   61.906707] [ T1242] [dhd-pcie-0] wl_android_priv_cmd: Android private cmd "SETSUSPENDMODE 1" on wlan0
[   62.018723] [  T276] servicemanager: Notifying apexservice they do (previously: don't) have clients when service is guaranteed to be in use
[   62.018909] [  T441] AidlLazyServiceRegistrar: Process has 1 (of 1 available) client(s) in use after notification apexservice has clients: 1
[   62.018925] [  T441] AidlLazyServiceRegistrar: Shutdown prevented by forcePersist override flag.
[   62.019127] [  T441] apexd: getSessions() received by ApexService
[   62.214044] [  T276] servicemanager: Found android.hardware.thermal.IThermal/default in device VINTF manifest.
[   62.359542] [  T276] servicemanager: Found android.hardware.graphics.allocator.IAllocator/default in device VINTF manifest.
[   62.383947] [  T276] servicemanager: Found android.hardware.graphics.allocator.IAllocator/default in device VINTF manifest.
[   62.670235] [  T287] type=1400 audit(1749777038.608:1323): avc:  denied  { dac_read_search } for  comm="SoundDecoder_1" capability=2  scontext=u:r:system_server:s0 tcontext=u:r:system_server:s0 tclass=capability permissive=1 bug=b/228030183
[   62.774679] [  T287] type=1400 audit(1749777038.712:1324): avc:  denied  { read } for  comm=5573625365727669636520686F7374 name="u:object_r:vendor_usb_prop:s0" dev="tmpfs" ino=342 scontext=u:r:system_server:s0 tcontext=u:object_r:vendor_usb_prop:s0 tclass=file permissive=1
[   62.774889] [  T287] type=1400 audit(1749777038.712:1325): avc:  denied  { open } for  comm=5573625365727669636520686F7374 path="/dev/__properties__/u:object_r:vendor_usb_prop:s0" dev="tmpfs" ino=342 scontext=u:r:system_server:s0 tcontext=u:object_r:vendor_usb_prop:s0 tclass=file permissive=1
[   62.774992] [  T287] type=1400 audit(1749777038.712:1326): avc:  denied  { getattr } for  comm=5573625365727669636520686F7374 path="/dev/__properties__/u:object_r:vendor_usb_prop:s0" dev="tmpfs" ino=342 scontext=u:r:system_server:s0 tcontext=u:object_r:vendor_usb_prop:s0 tclass=file permissive=1
[   62.775057] [  T287] type=1400 audit(1749777038.712:1327): avc:  denied  { map } for  comm=5573625365727669636520686F7374 path="/dev/__properties__/u:object_r:vendor_usb_prop:s0" dev="tmpfs" ino=342 scontext=u:r:system_server:s0 tcontext=u:object_r:vendor_usb_prop:s0 tclass=file permissive=1
[   64.059910] [  T276] servicemanager: Could not find android.hardware.gnss.IGnss/default in the VINTF manifest.
[   64.122427] [  T276] servicemanager: Could not find android.hardware.input.processor.IInputProcessor/default in the VINTF manifest.
[   64.186206] [    T1] init: starting service 'idmap2d'...
[   64.194686] [    T1] init: ... started service 'idmap2d' has pid 1435
[   64.194783] [    T1] init: Control message: Processed ctl.start for 'idmap2d' from pid: 763 (system_server)
[   64.649065] [  T276] servicemanager: Found android.hardware.weaver.IWeaver/default in device VINTF manifest.
[   64.687291] [  T276] servicemanager: Could not find android.hardware.secure_element.ISecureElement/eSE1 in the VINTF manifest.
[   64.691028] [  T276] servicemanager: Could not find android.hardware.secure_element.ISecureElement/SIM1 in the VINTF manifest.
[   64.914019] [  T276] servicemanager: Notifying apexservice they don't (previously: do) have clients when we now have no record of a client
[   64.914217] [  T276] servicemanager: Notifying artd they don't (previously: do) have clients when we now have no record of a client
[   64.914234] [  T441] AidlLazyServiceRegistrar: Process has 0 (of 1 available) client(s) in use after notification apexservice has clients: 0
[   64.914245] [  T441] AidlLazyServiceRegistrar: Shutdown prevented by forcePersist override flag.
[   64.915105] [  T276] servicemanager: Unregistering artd
[   64.915164] [  T276] BpBinder: onLastStrongRef automatically unlinking death recipients: 
[   64.921861] [    T1] init: Service 'artd' (pid 881) exited with status 0 oneshot service took 41.623001 seconds in background
[   64.921922] [    T1] init: Sending signal 9 to service 'artd' (pid 881) process group...
[   64.922719] [    T1] libprocessgroup: Successfully killed process cgroup uid 1082 pid 881 in 0ms
[   64.979833] [  T276] servicemanager: Found android.hardware.graphics.allocator.IAllocator/default in device VINTF manifest.
[   64.983813] [  T276] servicemanager: Found android.hardware.graphics.allocator.IAllocator/default in device VINTF manifest.
[   65.006263] [  T287] type=1400 audit(1749777040.944:1328): avc:  denied  { dac_read_search } for  comm="android.fg" capability=2  scontext=u:r:system_server:s0 tcontext=u:r:system_server:s0 tclass=capability permissive=1 bug=b/228030183
[   65.021681] [    T1] init: processing action (vendor.wifi.direct.interface=p2p-dev-wlan0) from (/vendor/etc/init/hw/init.connectivity.rc:67)
[   65.069659] [  T276] servicemanager: Found android.hardware.wifi.hostapd.IHostapd/default in device VINTF manifest.
[   65.070467] [  T276] servicemanager: Found android.hardware.wifi.hostapd.IHostapd/default in device VINTF manifest.
[   65.070889] [  T276] servicemanager: Found android.hardware.wifi.hostapd.IHostapd/default in device VINTF manifest.
[   65.071146] [  T276] servicemanager: Since 'android.hardware.wifi.hostapd.IHostapd/default' could not be found, trying to start it as a lazy AIDL service. (if it's not configured to be a lazy service, it may be stuck starting or still starting).
[   65.072877] [    T1] init: starting service 'hostapd'...
[   65.079618] [    T1] init: ... started service 'hostapd' has pid 1544
[   65.079736] [    T1] init: Control message: Processed ctl.interface_start for 'aidl/android.hardware.wifi.hostapd.IHostapd/default' from pid: 276 (/system/bin/servicemanager)
[   65.114614] [  T276] servicemanager: Found android.hardware.wifi.hostapd.IHostapd/default in device VINTF manifest.
[   65.115554] [  T276] BpBinder: onLastStrongRef automatically unlinking death recipients: 
[   65.118308] [  T276] servicemanager: Found android.hardware.wifi.IWifi/default in device VINTF manifest.
[   65.124824] [    T1] init: processing action (vendor.wifi.direct.interface=p2p-dev-wlan0) from (/vendor/etc/init/hw/init.connectivity.rc:67)
[   65.132562] [  T276] servicemanager: Found android.hardware.wifi.IWifi/default in device VINTF manifest.
[   65.145108] [  T543] [dhd-sdio-1] dhd_get_fw_capabilities: Get Capability failed (error=-19)
[   65.145162] [  T543] [dhd-sdio-1] Capabilities rechecking fail
[   65.145175] [  T543] [dhd-sdio-1] NULL POINTER : dhd_is_pno_supported
[   65.145395] [  T543] [dhd-pcie-0] CFG80211-ERROR) wl_cfg80211_netdev_notifier_call : wrong cfg ptr (000000003686c2b9)
[   65.145430] [  T543] [dhd-sdio-1] [wlan1] dhd_set_mac_address : macaddr = 9c:b8:b4:8e:35:e4
[   65.145441] [  T543] [dhd-sdio-1] CFG80211-ERROR) wl_cfg80211_macaddr_sync_reqd : no macthing if type
[   65.145570] [  T543] [dhd-pcie-0] CFG80211-ERROR) wl_cfg80211_netdev_notifier_call : wrong cfg ptr (000000003686c2b9)
[   65.145689] [  T543] [dhd-pcie-0] CFG80211-ERROR) wl_cfg80211_netdev_notifier_call : wrong cfg ptr (000000003686c2b9)
[   65.145707] [  T543] [dhd-sdio-1] dhd_pri_open : no mutex held
[   65.145715] [  T543] [dhd-sdio-1] dhd_pri_open : set mutex lock
[   65.145723] [  T543] [dhd-sdio-1] [wlan1] dhd_open : Enter
[   65.145731] [  T543] [dhd-sdio-1] Dongle Host Driver, version 101.10.591.91.39 (20241101-1)(fe3ee42)
[   65.145731] [  T543] /mnt/data1/android_workspace/rk3576_android14/external/wifi_driver/bcmdhd compiled on Jun 12 2025 at 20:41:00
[   65.145731] [  T543] 
[   65.145744] [  T543] [dhd-sdio-1] ANDROID_VERSION = 14
[   65.145756] [  T543] [dhd-sdio-1] dhd_open: ######### called for ifidx=0 #########
[   65.145773] [  T543] [dhd-sdio-1] [wlan1] wl_android_wifi_on : in g_wifi_on=0
[   65.145782] [  T543] [dhd-sdio-1] wifi_platform_set_power = 1, delay: 200 msec
[   65.145793] [  T543] [dhd-sdio-1] ======== PULL WL_REG_ON(-1) HIGH! ========
[   65.145801] [  T543] [WLAN_RFKILL-1]: rockchip_wifi_power1: 1
[   65.145809] [  T543] [WLAN_RFKILL-1]: rockchip_wifi_power1: toggle = false
[   65.145817] [  T543] [WLAN_RFKILL-1]: wifi turn on power [GPIO-1-0]
[   65.249646] [  T276] servicemanager: VINTF HALs require names in the format type/instance (e.g. some.package.foo.IFoo/default) but got: suspend_control
[   65.250288] [  T276] servicemanager: Could not find android.hardware.bluetooth.IBluetoothHci/default in the VINTF manifest.
[   65.263060] [  T287] type=1400 audit(1749777041.200:1329): avc:  denied  { search } for  comm="bluetooth@1.0-s" name="bluetooth" dev="mmcblk0p15" ino=1424025 scontext=u:r:hal_bluetooth_default:s0 tcontext=u:object_r:bluetooth_data_file:s0 tclass=dir permissive=1
[   65.293807] [  T478] [BT_RFKILL]: bt shut off power
[   65.294007] [  T478] [BT_RFKILL]: rfkill_rk_set_power: set bt wake_host high!
[   65.294157] [  T287] type=1400 audit(1749777041.232:1330): avc:  denied  { read } for  comm="bluetooth@1.0-s" name="u:object_r:default_prop:s0" dev="tmpfs" ino=138 scontext=u:r:hal_bluetooth_default:s0 tcontext=u:object_r:default_prop:s0 tclass=file permissive=1
[   65.294286] [  T287] type=1400 audit(1749777041.232:1331): avc:  denied  { open } for  comm="bluetooth@1.0-s" path="/dev/__properties__/u:object_r:default_prop:s0" dev="tmpfs" ino=138 scontext=u:r:hal_bluetooth_default:s0 tcontext=u:object_r:default_prop:s0 tclass=file permissive=1
[   65.294357] [  T287] type=1400 audit(1749777041.232:1332): avc:  denied  { getattr } for  comm="bluetooth@1.0-s" path="/dev/__properties__/u:object_r:default_prop:s0" dev="tmpfs" ino=138 scontext=u:r:hal_bluetooth_default:s0 tcontext=u:object_r:default_prop:s0 tclass=file permissive=1
[   65.294418] [  T287] type=1400 audit(1749777041.232:1333): avc:  denied  { map } for  comm="bluetooth@1.0-s" path="/dev/__properties__/u:object_r:default_prop:s0" dev="tmpfs" ino=138 scontext=u:r:hal_bluetooth_default:s0 tcontext=u:object_r:default_prop:s0 tclass=file permissive=1
[   65.349047] [  T478] [BT_RFKILL]: rfkill_rk_set_power: set bt wake_host input!
[   65.349108] [  T478] [BT_RFKILL]: ENABLE UART_RTS
[   65.352997] [  T543] [dhd-sdio-1] wifi_platform_set_power = 1, sleep done: 200 msec
[   65.353057] [  T543] [dhd-sdio-1] sdio_sw_reset: call mmc_hw_reset
[   65.456998] [  T478] [BT_RFKILL]: DISABLE UART_RTS
[   65.457070] [  T478] [BT_RFKILL]: bt turn on power
[   65.457164] [  T478] [BT_RFKILL]: Request irq for bt wakeup host
[   65.457525] [  T478] [BT_RFKILL]: ** disable irq
[   65.458293] [  T478] of_dma_request_slave_channel: dma-names property of node '/serial@2ad70000' missing or empty
[   65.458324] [  T478] dw-apb-uart 2ad70000.serial: failed to request DMA, use interrupt mode
[   65.573043] [  T543] mmc_host mmc1: Bus speed (slot 0) = 400000Hz (slot req 400000Hz, actual 400000HZ div = 0)
[   65.618663] [  T287] type=1400 audit(1749777041.556:1334): avc:  denied  { read } for  comm="system_server" name="state" dev="sysfs" ino=48102 scontext=u:r:system_server:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[   65.618818] [  T287] type=1400 audit(1749777041.556:1335): avc:  denied  { open } for  comm="system_server" path="/sys/devices/platform/ad82129-sound/extcon/extcon5/state" dev="sysfs" ino=48102 scontext=u:r:system_server:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[   65.618889] [  T287] type=1400 audit(1749777041.556:1336): avc:  denied  { getattr } for  comm="system_server" path="/sys/devices/platform/ad82129-sound/extcon/extcon5/state" dev="sysfs" ino=48102 scontext=u:r:system_server:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[   65.689754] [  T543] mmc_host mmc1: Bus speed (slot 0) = 100000000Hz (slot req 100000000Hz, actual 100000000HZ div = 0)
[   65.689862] [  T543] dwmmc_rockchip 2a320000.mmc: Successfully tuned phase to 90
[   65.689936] [  T543] [dhd-sdio-1] sdioh_start: set sd_f2_blocksize 256
[   65.690641] [  T543] [dhd-sdio-1] dhd_bus_devreset: == Power ON ==
[   65.690674] [  T543] [dhd-sdio-1] dhd_bus_devreset: set si_wd FALSE
[   65.690781] [  T543] [dhd-sdio-1] F1 signature read @0x18000000=0x15294345
[   65.693937] [  T543] [dhd-sdio-1] F1 signature OK, socitype:0x1 chip:0x4345 rev:0x9 pkg:0x2
[   65.694345] [  T543] [dhd-sdio-1] DHD: dongle ram size is set to 819200(orig 819200) at 0x198000
[   65.694399] [  T543] [dhd-sdio-1] dhd_bus_devreset: making DHD_BUS_DOWN
[   65.694426] [  T543] [dhd-sdio-1] dhdsdio_probe_init: making DHD_BUS_DOWN
[   65.694512] [  T543] [dhd-sdio-1] dhd_conf_set_path_params : Final fw_path=/fw_bcm43456c5_ag.bin
[   65.694521] [  T543] [dhd-sdio-1] dhd_conf_set_path_params : Final nv_path=/nvram_ap6256.txt
[   65.694529] [  T543] [dhd-sdio-1] dhd_conf_set_path_params : Final clm_path=/clm_bcm43456c5_ag.blob
[   65.694536] [  T543] [dhd-sdio-1] dhd_conf_set_path_params : Final conf_path=/config_bcm43456c5_ag.txt
[   65.694741] [  T543] bcmsdh_sdmmc mmc1:0001:2: Direct firmware load for /config_bcm43456c5_ag.txt failed with error -2
[   65.694769] [  T543] [dhd-sdio-1] dhd_os_get_img_fwreq: request_firmware /config_bcm43456c5_ag.txt err: -2
[   65.694783] [  T543] [dhd-sdio-1] dhd_os_get_img(Request Firmware API) error : -30
[   65.694793] [  T543] [dhd-sdio-1] dhd_conf_read_config : Ignore config file /config_bcm43456c5_ag.txt
[   65.698705] [  T543] [dhd-sdio-1] dhd_os_get_img_fwreq: /fw_bcm43456c5_ag.bin (629343 bytes) open success
[   65.734247] [  T276] servicemanager: Found android.hardware.graphics.allocator.IAllocator/default in device VINTF manifest.
[   65.739193] [  T276] servicemanager: Found android.hardware.graphics.allocator.IAllocator/default in device VINTF manifest.
[   65.804860] [  T543] [dhd-sdio-1] dhd_os_get_img_fwreq: /nvram_ap6256.txt (2732 bytes) open success
[   65.804984] [  T543] [dhd-sdio-1] #AP6256_NVRAM_V1.4_06112021
[   65.805976] [  T543] [dhd-sdio-1] dhdsdio_write_vars: Download, Upload and compare of NVRAM succeeded.
[   65.883997] [  T276] servicemanager: Could not find android.hardware.input.processor.IInputProcessor/default in the VINTF manifest.
[   65.896131] [  T543] [dhd-sdio-1] dhd_bus_init: enable 0x06, ready 0x06 (waited 0us)
[   65.896439] [  T543] [dhd-sdio-1] bcmsdh_oob_intr_register: HW_OOB irq=131 flags=0x4
[   65.896679] [  T543] [dhd-sdio-1] dhd_get_memdump_info: MEMDUMP ENABLED = 3
[   65.898022] [  T543] [dhd-sdio-1] wlc_ver_major 4, wlc_ver_minor 1
[   65.898057] [  T543] [dhd-sdio-1] dhd_tcpack_suppress_set: TCP ACK Suppress mode 2 -> mode 1
[   65.898066] [  T543] [dhd-sdio-1] dhd_tcpack_suppress_set: TCPACK_INFO_MAXNUM=40, TCPDATA_INFO_MAXNUM=40
[   65.898392] [  T543] [dhd-sdio-1] dhd_conf_custom_mac : set custom MAC address 9c:b8:b4:8e:35:e4 from SIOCSIFHWADDR
[   65.899171] [  T543] [dhd-sdio-1] dhd_legacy_preinit_ioctls: use firmware generated mac_address 9c:b8:b4:8e:35:e4
[   65.899273] [  T543] bcmsdh_sdmmc mmc1:0001:2: Direct firmware load for /clm_bcm43456c5_ag.blob failed with error -2
[   65.899288] [  T543] [dhd-sdio-1] dhd_os_get_img_fwreq: request_firmware /clm_bcm43456c5_ag.blob err: -2
[   65.899297] [  T543] [dhd-sdio-1] dhd_os_get_img(Request Firmware API) error : -30
[   65.899304] [  T543] [dhd-sdio-1] dhd_apply_default_clm: Ignore clm file /clm_bcm43456c5_ag.blob
[   65.901120] [  T543] [dhd-sdio-1] Firmware up: op_mode=0x0005, MAC=9c:b8:b4:8e:35:e4
[   65.907482] [  T543] [dhd-sdio-1] dhd_legacy_preinit_ioctls: event_log_max_sets: 40 ret: -23
[   65.910275] [  T543] [dhd-sdio-1] dhd_legacy_preinit_ioctls set event_log_tag_control fail -23
[   65.911877] [  T543] [dhd-sdio-1] arp_enable:1 arp_ol:0
[   65.913700] [  T543] [dhd-sdio-1]   Driver: 101.10.591.91.39 (20241101-1)
[   65.913700] [  T543] [dhd-sdio-1]   Firmware: wl0: Nov 20 2023 17:22:11 version *********** (g0fffcc23) FWID 01-fbd67456 es7.c5.n4.a3
[   65.913700] [  T543] [dhd-sdio-1]   CLM: 9.2.9 (2016-02-03 04:34:31) 
[   65.914021] [  T543] [dhd-sdio-1] dhd_txglom_enable: enable 1
[   65.914049] [  T543] [dhd-sdio-1] dhd_conf_set_txglom_params : txglom_mode=copy
[   65.914058] [  T543] [dhd-sdio-1] dhd_conf_set_txglom_params : txglomsize=36, deferred_tx_len=0
[   65.914067] [  T543] [dhd-sdio-1] dhd_conf_set_txglom_params : txinrx_thres=128, dhd_txminmax=-1
[   65.914075] [  T543] [dhd-sdio-1] dhd_conf_set_txglom_params : tx_max_offset=0, txctl_tmo_fix=300
[   65.914086] [  T543] [dhd-sdio-1] dhd_conf_get_disable_proptx : fw_proptx=1, disable_proptx=-1
[   65.916272] [  T543] [dhd-sdio-1] dhd_wlfc_hostreorder_init(): successful bdcv2 tlv signaling, 64
[   65.918288] [  T543] [dhd-sdio-1] dhd_pno_init: Support Android Location Service
[   65.918870] [  T543] [dhd-sdio-1] rtt_do_get_ioctl: failed to send getbuf proxd iovar (CMD ID : 1), status=-4
[   65.918893] [  T543] [dhd-sdio-1] dhd_rtt_init : FTM is not supported
[   65.918900] [  T543] [dhd-sdio-1] dhd_rtt_init EXIT, err = 0
[   65.957169] [  T543] [dhd-sdio-1] dhd_legacy_preinit_ioctls: Failed to get preserve log # !
[   65.957785] [  T543] [dhd-sdio-1] dhd_legacy_preinit_ioctls: d3_hostwake_delay IOVAR not present, proceed
[   65.958281] [  T543] [dhd-sdio-1] dhd_bus_check_srmemsize : srmem_size no need to change.
[   65.980468] [  T543] [dhd-sdio-1] dhd_conf_set_country : set country CN, revision 38
[   65.983299] [  T543] [dhd-sdio-1] dhd_conf_set_country : Country code: CN (CN/38)
[   65.989900] [  T543] [dhd-sdio-1] [wlan1] wl_android_wifi_on : Success
[   66.050867] [  T543] [dhd-sdio-1] CFG80211-ERROR) init_roam_cache : roamscan_mode iovar failed. -23
[   66.050892] [  T543] [dhd-sdio-1] CFG80211-ERROR) wl_cfg80211_up : Failed to enable RCC.
[   66.053200] [  T543] [dhd-sdio-1] [wlan1] dhd_open : Exit ret=0
[   66.053225] [  T543] [dhd-sdio-1] [wlan1] dhd_pri_open : tx queue started
[   66.053232] [  T543] [dhd-sdio-1] dhd_pri_open : mutex is released.
[   66.053415] [  T543] [dhd-pcie-0] CFG80211-ERROR) wl_cfg80211_netdev_notifier_call : wrong cfg ptr (000000003686c2b9)
[   66.055065] [   T38] [dhd-sdio-1] [wlan1] _dhd_set_mac_address : close dev for mac changing
[   66.055131] [   T38] [dhd-pcie-0] CFG80211-ERROR) wl_cfg80211_netdev_notifier_call : wrong cfg ptr (000000003686c2b9)
[   66.055420] [   T38] [dhd-sdio-1] [wlan1] dhd_pri_stop : tx queue stopped
[   66.055452] [   T38] [dhd-sdio-1] [wlan1] dhd_stop : Enter
[   66.055462] [   T38] [dhd-sdio-1] dhd_stop: ######### called for ifidx=0 #########
[   66.055475] [   T38] [dhd-sdio-1] [wlan1] dhd_stop : skip chip reset.
[   66.055648] [   T38] [dhd-sdio-1] [wlan1] dhd_stop : Exit
[   66.056030] [   T38] [dhd-pcie-0] CFG80211-ERROR) wl_cfg80211_netdev_notifier_call : wrong cfg ptr (000000003686c2b9)
[   66.056686] [   T38] [dhd-sdio-1] [wlan1] _dhd_set_mac_address : MACID 9c:b8:b4:8e:35:e4 is overwritten
[   66.056743] [   T38] [dhd-pcie-0] CFG80211-ERROR) wl_cfg80211_netdev_notifier_call : wrong cfg ptr (000000003686c2b9)
[   66.056763] [   T38] [dhd-sdio-1] dhd_pri_open : no mutex held
[   66.056771] [   T38] [dhd-sdio-1] dhd_pri_open : set mutex lock
[   66.056779] [   T38] [dhd-sdio-1] [wlan1] dhd_open : Primary net_device is already up
[   66.056795] [   T38] [dhd-sdio-1] [wlan1] dhd_pri_open : tx queue started
[   66.056803] [   T38] [dhd-sdio-1] dhd_pri_open : mutex is released.
[   66.057209] [   T38] [dhd-pcie-0] CFG80211-ERROR) wl_cfg80211_netdev_notifier_call : wrong cfg ptr (000000003686c2b9)
[   66.057259] [   T38] [dhd-sdio-1] [wlan1] _dhd_set_mac_address : notify mac changed done
[   66.136046] [ T1544] [dhd-sdio-1] wl_android_priv_cmd: Android private cmd "SET_AP_WPS_P2P_IE 1" on wlan1
[   66.136173] [ T1544] [dhd-sdio-1] wl_android_priv_cmd: Android private cmd "SET_AP_WPS_P2P_IE 2" on wlan1
[   66.136193] [ T1544] [dhd-sdio-1] wl_android_priv_cmd: Android private cmd "SET_AP_WPS_P2P_IE 4" on wlan1
[   66.136741] [  T287] type=1400 audit(1749777042.072:1337): avc:  denied  { read } for  comm="hostapd" name="devices" dev="sysfs" ino=22410 scontext=u:r:hal_wifi_hostapd_default:s0 tcontext=u:object_r:sysfs:s0 tclass=dir permissive=1
[   66.136956] [  T287] type=1400 audit(1749777042.072:1338): avc:  denied  { open } for  comm="hostapd" path="/sys/bus/sdio/devices" dev="sysfs" ino=22410 scontext=u:r:hal_wifi_hostapd_default:s0 tcontext=u:object_r:sysfs:s0 tclass=dir permissive=1
[   66.137133] [  T287] type=1400 audit(1749777042.072:1339): avc:  denied  { read } for  comm="hostapd" name="uevent" dev="sysfs" ino=44326 scontext=u:r:hal_wifi_hostapd_default:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[   66.137239] [  T287] type=1400 audit(1749777042.072:1340): avc:  denied  { open } for  comm="hostapd" path="/sys/devices/platform/2a320000.mmc/mmc_host/mmc1/mmc1:0001/mmc1:0001:3/uevent" dev="sysfs" ino=44326 scontext=u:r:hal_wifi_hostapd_default:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[   66.137313] [  T287] type=1400 audit(1749777042.072:1341): avc:  denied  { getattr } for  comm="hostapd" path="/sys/devices/platform/2a320000.mmc/mmc_host/mmc1/mmc1:0001/mmc1:0001:3/uevent" dev="sysfs" ino=44326 scontext=u:r:hal_wifi_hostapd_default:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[   66.140049] [  T347] [dhd] STATIC-MSG) dhd_wlan_mem_prealloc : bus_type 3, index 1, section 8, size 44584
[   66.140098] [  T347] [dhd-sdio-1] dhd_wlfc_enable: proptx=1, ptxmode=2, ret=0
[   66.140461] [ T1544] [dhd-sdio-1] [wlan1] wl_cfg80211_set_channel : netdev_ifidx(11) target channel(5g-36 20MHz)
[   66.141052] [ T1544] [dhd-sdio-1] [wlan1] wl_cfg80211_set_channel : hostapd bw(20MHz) => chip bw(80MHz)
[   66.171628] [  T347] [dhd-sdio-1] _dhd_wlfc_mac_entry_update():2047, entry(32)
[   66.172148] [ T1544] [dhd-sdio-1] CFG80211-ERROR) wl_cfg80211_bcn_bringup_ap : failed to disable uapsd, error=-5
[   66.172474] [  T347] [dhd-sdio-1] _dhd_wlfc_mac_entry_update():2047, entry(32)
[   66.175187] [ T1544] [dhd-sdio-1] [wlan1] wl_cfg80211_bcn_bringup_ap : Creating AP with sec=wpa2/psk/mfpn/aes
[   66.244859] [  T347] [dhd-sdio-1] WLC_E_LINK: idx:0, action:UP, iftype:STA, [9c:b8:b4:8e:35:e4]
[   66.244975] [   T92] [dhd-sdio-1] [wlan1] wl_iw_event : Link UP with 9c:b8:b4:8e:35:e4
[   66.245032] [   T92] [dhd-sdio-1] [wlan1] wl_ext_iapsta_link : Link up w/o creating? (etype=16)
[   66.247230] [  T174] [dhd-sdio-1] [wlan1] wl_notify_connect_status_ap : AP/GO Link up (5g-36 80MHz)
[   66.258519] [ T1544] [dhd-sdio-1] [wlan1] wl_cfg80211_del_station : Disconnect STA : ff:ff:ff:ff:ff:ff scb_val.val 3
[   66.258922] [ T1544] [dhd-pcie-0] CFG80211-ERROR) wl_cfg80211_netdev_notifier_call : wrong cfg ptr (000000003686c2b9)
[   66.265369] [  T543] [dhd-pcie-0] dhd_os_start_logging , ring_id : 7 log_level : 0, time_intval : 0, threshod 0 Bytes
[   66.265658] [  T543] [dhd-pcie-0] dhd_os_start_logging , ring_id : 7 log_level : 1, time_intval : 3600, threshod 16384 Bytes
[   66.272205] [ T1242] [dhd-pcie-0] wl_android_priv_cmd: Android private cmd "BTCOEXSCAN-STOP" on wlan0
[   66.272515] [ T1242] [dhd-pcie-0] wl_android_priv_cmd: Android private cmd "RXFILTER-STOP" on wlan0
[   66.272846] [ T1242] [dhd-pcie-0] wl_android_priv_cmd: Android private cmd "RXFILTER-ADD 2" on wlan0
[   66.275814] [ T1242] [dhd-pcie-0] wl_android_priv_cmd: Android private cmd "RXFILTER-START" on wlan0
[   66.276061] [ T1242] [dhd-pcie-0] wl_android_priv_cmd: Android private cmd "RXFILTER-STOP" on wlan0
[   66.276272] [ T1242] [dhd-pcie-0] wl_android_priv_cmd: Android private cmd "RXFILTER-ADD 3" on wlan0
[   66.276448] [ T1242] [dhd-pcie-0] wl_android_priv_cmd: Android private cmd "RXFILTER-START" on wlan0
[   66.276649] [ T1242] [dhd-pcie-0] wl_android_priv_cmd: Android private cmd "SETSUSPENDMODE 1" on wlan0
[   66.298233] [ T1242] [dhd-pcie-0] wl_android_priv_cmd: Android private cmd "SETSUSPENDMODE 0" on wlan0
[   66.310894] [  T276] servicemanager: Could not find android.hardware.tetheroffload.IOffload/default in the VINTF manifest.
[   66.791896] [    T1] init: Control message: Processed ctl.stop for 'adbd' from pid: 763 (system_server)
[   66.792211] [    T1] init: processing action (init.svc.adbd=stopped) from (/vendor/etc/init/hw/init.rk30board.usb.rc:258)
[   66.792684] [  T276] servicemanager: Found android.hardware.wifi.IWifi/default in device VINTF manifest.
[   66.793671] [    T1] init: processing action (init.svc.adbd=stopped) from (/system/etc/init/hw/init.usb.configfs.rc:14)
[   66.793928] [  T504] dwc3 23000000.usb: request 0000000000000000 was not queued to ep0out
[   66.794205] [  T166] android_work: sent uevent USB_STATE=DISCONNECTED
[   66.802910] [  T276] servicemanager: Found android.hardware.wifi.IWifi/default in device VINTF manifest.
[   66.810120] [  T276] servicemanager: Found android.hardware.wifi.IWifi/default in device VINTF manifest.
[   66.816657] [  T276] servicemanager: Found android.hardware.wifi.IWifi/default in device VINTF manifest.
[   66.829816] [  T276] servicemanager: Found android.hardware.wifi.IWifi/default in device VINTF manifest.
[   66.836641] [  T276] servicemanager: Found android.hardware.wifi.IWifi/default in device VINTF manifest.
[   66.837506] [ T1672] es8156_set_switch_hp(507), es8156->muted = 1
[   66.840196] [ T1672] ad82129_startup(241), -----------
[   66.840262] [ T1672] enter into es8156_set_dai_sysclk, freq = 4096000
[   66.844637] [  T276] servicemanager: Found android.hardware.wifi.IWifi/default in device VINTF manifest.
[   66.846106] [  T276] servicemanager: Notifying apexservice they do (previously: don't) have clients when service is guaranteed to be in use
[   66.846341] [  T441] AidlLazyServiceRegistrar: Process has 1 (of 1 available) client(s) in use after notification apexservice has clients: 1
[   66.846376] [  T441] AidlLazyServiceRegistrar: Shutdown prevented by forcePersist override flag.
[   66.846534] [    T8] enter into es8156_set_bias_level, level = 1
[   66.846709] [  T441] apexd: markBootCompleted() received by ApexService
[   66.851216] [  T276] servicemanager: Found android.hardware.wifi.IWifi/default in device VINTF manifest.
[   66.855983] [    T1] init: processing action (sys.boot_completed=1) from (/system/etc/init/hw/init.rc:1224)
[   66.856480] [    T1] init: starting service 'exec 15 (/bin/rm -rf /data/per_boot)'...
[   66.858173] [  T276] servicemanager: Found android.hardware.wifi.IWifi/default in device VINTF manifest.
[   66.860470] [    T1] init: ... started service 'exec 15 (/bin/rm -rf /data/per_boot)' has pid 1674
[   66.860554] [    T1] init: SVC_EXEC service 'exec 15 (/bin/rm -rf /data/per_boot)' pid 1674 (uid 1000 gid 1000+0 context default) started; waiting...
[   66.864950] [  T276] servicemanager: Found android.hardware.wifi.IWifi/default in device VINTF manifest.
[   66.871578] [  T276] servicemanager: Found android.hardware.wifi.IWifi/default in device VINTF manifest.
[   66.878447] [  T276] servicemanager: Found android.hardware.wifi.IWifi/default in device VINTF manifest.
[   66.890242] [    T1] init: Service 'exec 15 (/bin/rm -rf /data/per_boot)' (pid 1674) exited with status 0 waiting took 0.031000 seconds
[   66.890290] [    T1] init: Sending signal 9 to service 'exec 15 (/bin/rm -rf /data/per_boot)' (pid 1674) process group...
[   66.890521] [    T1] libprocessgroup: Successfully killed process cgroup uid 1000 pid 1674 in 0ms
[   66.891955] [    T1] init: Encryption policy of /data/per_boot set to aeeddf890fe65226063f067741e1970d v2 modes 1/4 flags 0x2
[   66.892041] [    T1] init: processing action (sys.boot_completed=1) from (/vendor/etc/init/hw/init.rk30board.rc:240)
[   66.893522] [    T1] init: processing action (sys.boot_completed=1) from (/vendor/etc/init/hw/init.rk3576.rc:2)
[   66.901247] [  T287] type=1400 audit(1749777042.836:1342): avc:  denied  { create } for  comm="init" name="vehicle" scontext=u:r:vendor_init:s0 tcontext=u:object_r:device:s0 tclass=file permissive=1
[   66.901434] [  T287] type=1400 audit(1749777042.836:1343): avc:  denied  { write } for  comm="init" path="/dev/vehicle" dev="tmpfs" ino=784 scontext=u:r:vendor_init:s0 tcontext=u:object_r:device:s0 tclass=file permissive=1
[   66.901557] [  T204] init: Top-level directory needs encryption action, eg mkdir /data/tof <mode> <uid> <gid> encryption=Require
[   66.902672] [  T287] type=1400 audit(1749777042.840:1344): avc:  denied  { create } for  comm="init" name="tof" scontext=u:r:vendor_init:s0 tcontext=u:object_r:system_data_file:s0 tclass=dir permissive=1
[   66.903329] [  T287] type=1400 audit(1749777042.840:1345): avc:  denied  { read } for  comm="init" name="tof" dev="mmcblk0p15" ino=973897 scontext=u:r:vendor_init:s0 tcontext=u:object_r:system_data_file:s0 tclass=dir permissive=1
[   66.903362] [  T204] init: Encryption policy of /data/tof set to f108e729abe705dc9c1118a9cab78588 v2 modes 1/4 flags 0x2
[   66.903479] [  T287] type=1400 audit(1749777042.840:1346): avc:  denied  { open } for  comm="init" path="/data/tof" dev="mmcblk0p15" ino=973897 scontext=u:r:vendor_init:s0 tcontext=u:object_r:system_data_file:s0 tclass=dir permissive=1
[   66.903559] [  T287] type=1400 audit(1749777042.840:1347): avc:  denied  { ioctl } for  comm="init" path="/data/tof" dev="mmcblk0p15" ino=973897 ioctlcmd=0x6615 scontext=u:r:vendor_init:s0 tcontext=u:object_r:system_data_file:s0 tclass=dir permissive=1
[   66.903686] [  T204] init: Top-level directory needs encryption action, eg mkdir /data/hysd <mode> <uid> <gid> encryption=Require
[   66.905102] [  T204] init: Encryption policy of /data/hysd set to f108e729abe705dc9c1118a9cab78588 v2 modes 1/4 flags 0x2
[   66.906362] [    T1] init: processing action (sys.boot_completed=1 && sys.bootstat.first_boot_completed=0) from (/system/etc/init/bootstat.rc:67)
[   66.907507] [  T287] type=1400 audit(1749777042.844:1348): avc:  denied  { setattr } for  comm="init" name="tof" dev="mmcblk0p15" ino=973897 scontext=u:r:vendor_init:s0 tcontext=u:object_r:system_data_file:s0 tclass=dir permissive=1
[   66.908803] [    T1] init: starting service 'exec 16 (/system/bin/bootstat --record_boot_complete --record_boot_reason --record_time_since_factory_reset -l)'...
[   66.914978] [    T1] init: ... started service 'exec 16 (/system/bin/bootstat --record_boot_complete --record_boot_reason --record_time_since_factory_reset -l)' has pid 1694
[   66.916100] [    T1] init: processing action (sys.boot_completed=1) from (/system/etc/init/dmesgd.rc:4)
[   66.917668] [    T1] init: processing action (sys.boot_completed=1) from (/system/etc/init/flags_health_check.rc:7)
[   66.935329] [    T1] init: processing action (sys.boot_completed=1) from (/system/etc/init/logd.rc:34)
[   66.935906] [    T1] init: starting service 'logd-auditctl'...
[   66.952226] [    T1] init: ... started service 'logd-auditctl' has pid 1699
[   66.952537] [    T1] init: processing action (sys.boot_completed=1) from (/system/etc/init/perfetto.rc:130)
[   66.953324] [    T1] init: starting service 'exec 17 (/system/bin/perfetto -c /data/misc/perfetto-configs/stopboottracetrigger.pbtxt --txt)'...
[   66.959036] [    T1] init: ... started service 'exec 17 (/system/bin/perfetto -c /data/misc/perfetto-configs/stopboottracetrigger.pbtxt --txt)' has pid 1700
[   66.959231] [    T1] init: SVC_EXEC service 'exec 17 (/system/bin/perfetto -c /data/misc/perfetto-configs/stopboottracetrigger.pbtxt --txt)' pid 1700 (uid 0 gid 0+0 context default) started; waiting...
[   66.960488] [    T1] init: Service 'exec 16 (/system/bin/bootstat --record_boot_complete --record_boot_reason --record_time_since_factory_reset -l)' (pid 1694) exited with status 0 oneshot service took 0.048000 seconds in background
[   66.960528] [    T1] init: Sending signal 9 to service 'exec 16 (/system/bin/bootstat --record_boot_complete --record_boot_reason --record_time_since_factory_reset -l)' (pid 1694) process group...
[   66.960771] [    T1] libprocessgroup: Successfully killed process cgroup uid 1000 pid 1694 in 0ms
[   66.966584] [    T1] init: Service 'logd-auditctl' (pid 1699) exited with status 0 oneshot service took 0.027000 seconds in background
[   66.966656] [    T1] init: Sending signal 9 to service 'logd-auditctl' (pid 1699) process group...
[   66.966896] [    T1] libprocessgroup: Successfully killed process cgroup uid 1036 pid 1699 in 0ms
[   66.979748] [  T635] [dhd-pcie-0] [wlan0] wl_run_escan : LEGACY_SCAN sync ID: 0, bssidx: 0
[   66.981407] [    T1] init: Service 'exec 17 (/system/bin/perfetto -c /data/misc/perfetto-configs/stopboottracetrigger.pbtxt --txt)' (pid 1700) exited with status 1 waiting took 0.025000 seconds
[   66.981446] [    T1] init: Sending signal 9 to service 'exec 17 (/system/bin/perfetto -c /data/misc/perfetto-configs/stopboottracetrigger.pbtxt --txt)' (pid 1700) process group...
[   66.981610] [    T1] libprocessgroup: Successfully killed process cgroup uid 0 pid 1700 in 0ms
[   66.981959] [    T1] init: processing action (sys.boot_completed=1 && sys.wifitracing.started=0) from (/system/etc/init/wifi.rc:28)
[   66.982403] [    T1] selinux: SELinux: Could not get canonical path for /sys/kernel/debug/tracing/instances/wifi restorecon: No such file or directory.
[   67.048998] [    T8] enter into es8156_set_bias_level, level = 2
[   67.050791] [   T84] enter into es8156_set_bias_level, level = 3
[   67.116165] [    T1] init: Command 'mkdir /sys/kernel/tracing/instances/wifi 711' action=sys.boot_completed=1 && sys.wifitracing.started=0 (/system/etc/init/wifi.rc:36) took 132ms and succeeded
[   67.132022] [    T1] init: processing action (sys.boot_completed=1) from (/vendor/etc/init/init.tune_io.rc:12)
[   67.136478] [    T1] init: processing action (persist.sys.zram_enabled=1 && sys-boot-completed-set) from (/vendor/etc/init/hw/init.rk30board.rc:253)
[   67.142425] [    T1] zram0: detected capacity change from 0 to 6043200
[   67.164144] [    T1] mkswap: Swapspace size: 3021596k, UUID=2df8d538-aa8d-4564-92bc-51c263b35b49
[   67.166756] [    T1] Adding 3021596k swap on /dev/block/zram0.  Priority:-2 extents:1 across:3021596k SS
[   67.168175] [    T1] init: processing action (bootreceiver.enable=1 && dmesgd.start=1 && ro.product.cpu.abilist64=*) from (/system/etc/init/dmesgd.rc:12)
[   67.169093] [    T1] init: starting service 'dmesgd'...
[   67.177095] [    T1] init: ... started service 'dmesgd' has pid 1721
[   67.178042] [    T1] init: processing action (sys.user.0.ce_available=true) from (/system/etc/init/wifi.rc:21)
[   67.179971] [    T1] init: processing action (sys.boot_completed=1 && sys.wifitracing.started=1 && wifi.interface=*) from (/system/etc/init/wifi.rc:98)
[   67.250649] [  T478] [BT_RFKILL]: bt shut off power
[   67.369154] [  T441] apexd: destroyCeSnapshotsNotSpecified() received by ApexService user_id : 0 retain_rollback_ids : []
[   67.391552] [  T287] type=1400 audit(1749777043.324:1349): avc:  denied  { dac_read_search } for  comm="backup" capability=2  scontext=u:r:system_server:s0 tcontext=u:r:system_server:s0 tclass=capability permissive=1 bug=b/228030183
[   67.418933] [  T287] type=1400 audit(1749777043.356:1350): avc:  denied  { read } for  comm="binder:318_2" name="wakeup4" dev="sysfs" ino=39988 scontext=u:r:system_suspend:s0 tcontext=u:object_r:sysfs:s0 tclass=dir permissive=1
[   67.419127] [  T287] type=1400 audit(1749777043.356:1351): avc:  denied  { open } for  comm="binder:318_2" path="/sys/devices/virtual/power_supply/test_ac/wakeup4" dev="sysfs" ino=39988 scontext=u:r:system_suspend:s0 tcontext=u:object_r:sysfs:s0 tclass=dir permissive=1
[   67.419228] [  T287] type=1400 audit(1749777043.356:1352): avc:  denied  { read } for  comm="binder:318_2" name="event_count" dev="sysfs" ino=39995 scontext=u:r:system_suspend:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[   67.419300] [  T287] type=1400 audit(1749777043.356:1353): avc:  denied  { open } for  comm="binder:318_2" path="/sys/devices/virtual/power_supply/test_ac/wakeup4/event_count" dev="sysfs" ino=39995 scontext=u:r:system_suspend:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[   67.419366] [  T287] type=1400 audit(1749777043.356:1354): avc:  denied  { getattr } for  comm="binder:318_2" path="/sys/devices/virtual/power_supply/test_ac/wakeup4/event_count" dev="sysfs" ino=39995 scontext=u:r:system_suspend:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[   67.422269] [  T287] type=1400 audit(1749777043.360:1355): avc:  denied  { dac_read_search } for  comm="pool-42-thread-" capability=2  scontext=u:r:system_server:s0 tcontext=u:r:system_server:s0 tclass=capability permissive=1 bug=b/228030183
[   67.429803] [  T322] audit_log_lost: 134 callbacks suppressed
[   67.429808] [  T322] audit: audit_lost=305 audit_rate_limit=5 audit_backlog_limit=64
[   67.429827] [  T322] audit: rate limit exceeded
[   67.430056] [  T287] type=1400 audit(1749777043.368:1356): avc:  denied  { read } for  comm="binder:318_2" name="event_count" dev="sysfs" ino=40070 scontext=u:r:system_suspend:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[   67.430238] [  T287] type=1400 audit(1749777043.368:1357): avc:  denied  { open } for  comm="binder:318_2" path="/sys/devices/virtual/power_supply/test_battery/wakeup5/event_count" dev="sysfs" ino=40070 scontext=u:r:system_suspend:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[   67.683059] [    T1] init: Service 'dmesgd' (pid 1721) exited with status 0 oneshot service took 0.510000 seconds in background
[   67.683125] [    T1] init: Sending signal 9 to service 'dmesgd' (pid 1721) process group...
[   67.683387] [    T1] libprocessgroup: Successfully killed process cgroup uid 1086 pid 1721 in 0ms
[   67.838025] [ T1739] dwc3 23000000.usb: device reset
[   67.889750] [ T1234] android_work: sent uevent USB_STATE=CONNECTED
[   67.892661] [ T1234] rk_send_wakeup_key: KEY_WAKEUP event sent
[   68.071593] [ T1234] android_work: sent uevent USB_STATE=CONFIGURED
[   69.310386] [  T276] servicemanager: Found android.hardware.graphics.allocator.IAllocator/default in device VINTF manifest.
[   69.313587] [  T287] type=1400 audit(1749777045.252:1366): avc:  denied  { dac_read_search } for  comm="binder:763_3" capability=2  scontext=u:r:system_server:s0 tcontext=u:r:system_server:s0 tclass=capability permissive=1 bug=b/228030183
[   69.314972] [  T276] servicemanager: Found android.hardware.graphics.allocator.IAllocator/default in device VINTF manifest.
[   69.404877] [  T276] servicemanager: Found android.hardware.graphics.allocator.IAllocator/default in device VINTF manifest.
[   69.410295] [  T276] servicemanager: Found android.hardware.graphics.allocator.IAllocator/default in device VINTF manifest.
[   69.573791] [  T287] type=1400 audit(1749777045.508:1367): avc:  denied  { add_name } for  comm="HeapTaskDaemon" name="oat_primary" scontext=u:r:secure_element:s0:c44,c260,c512,c768 tcontext=u:object_r:system_data_file:s0 tclass=dir permissive=1
[   69.574104] [  T287] type=1400 audit(1749777045.508:1368): avc:  denied  { create } for  comm="HeapTaskDaemon" name="oat_primary" scontext=u:r:secure_element:s0:c44,c260,c512,c768 tcontext=u:object_r:system_data_file:s0:c44,c260,c512,c768 tclass=dir permissive=1
[   69.590117] [  T287] type=1400 audit(1749777045.528:1369): avc:  denied  { add_name } for  comm="HeapTaskDaemon" name="arm64" scontext=u:r:secure_element:s0:c44,c260,c512,c768 tcontext=u:object_r:system_data_file:s0:c44,c260,c512,c768 tclass=dir permissive=1
[   69.590572] [  T287] type=1400 audit(1749777045.528:1370): avc:  denied  { create } for  comm="HeapTaskDaemon" name="SecureElement.1462.tmp" scontext=u:r:secure_element:s0:c44,c260,c512,c768 tcontext=u:object_r:system_data_file:s0:c44,c260,c512,c768 tclass=file permissive=1
[   69.596896] [ T1472] audit: audit_lost=313 audit_rate_limit=5 audit_backlog_limit=64
[   69.596996] [ T1472] audit: rate limit exceeded
[   69.645825] [  T276] servicemanager: Found android.hardware.graphics.allocator.IAllocator/default in device VINTF manifest.
[   69.653199] [  T276] servicemanager: Found android.hardware.graphics.allocator.IAllocator/default in device VINTF manifest.
[   69.828074] [  T276] servicemanager: Found android.hardware.graphics.allocator.IAllocator/default in device VINTF manifest.
[   69.835793] [   T84] enter into es8156_set_bias_level, level = 2
[   69.837720] [   T84] enter into es8156_set_bias_level, level = 1
[   69.841697] [  T276] servicemanager: Found android.hardware.graphics.allocator.IAllocator/default in device VINTF manifest.
[   69.843819] [   T84] enter into es8156_set_bias_level, level = 0
[   69.862209] [  T276] servicemanager: Found android.hardware.graphics.allocator.IAllocator/default in device VINTF manifest.
[   69.863061] [  T276] servicemanager: Found android.hardware.graphics.allocator.IAllocator/default in device VINTF manifest.
[   69.913991] [  T276] servicemanager: Notifying apexservice they don't (previously: do) have clients when we now have no record of a client
[   69.915034] [  T441] AidlLazyServiceRegistrar: Process has 0 (of 1 available) client(s) in use after notification apexservice has clients: 0
[   69.915063] [  T441] AidlLazyServiceRegistrar: Shutdown prevented by forcePersist override flag.
[   70.254683] [  T339] apexd: Deleting unused dm device com.android.apex.cts.shim
[   70.254858] [  T339] apexd: Didn't generate uevent for [com.android.apex.cts.shim] removal
[   70.254885] [  T339] apexd: Failed to delete dm-device com.android.apex.cts.shim
[   70.254930] [  T339] apexd: Deleting unused dm device com.android.btservices
[   70.255068] [  T339] apexd: Didn't generate uevent for [com.android.btservices] removal
[   70.255092] [  T339] apexd: Failed to delete dm-device com.android.btservices
[   70.255130] [  T339] apexd: Deleting unused dm device com.android.devicelock
[   70.255267] [  T339] apexd: Didn't generate uevent for [com.android.devicelock] removal
[   70.255296] [  T339] apexd: Failed to delete dm-device com.android.devicelock
[   70.255331] [  T339] apexd: Deleting unused dm device com.android.healthfitness
[   70.255467] [  T339] apexd: Didn't generate uevent for [com.android.healthfitness] removal
[   70.255494] [  T339] apexd: Failed to delete dm-device com.android.healthfitness
[   70.255523] [  T339] apexd: Deleting unused dm device com.android.i18n
[   70.255684] [  T339] apexd: Didn't generate uevent for [com.android.i18n] removal
[   70.255710] [  T339] apexd: Failed to delete dm-device com.android.i18n
[   70.255768] [  T339] apexd: Deleting unused dm device com.android.os.statsd
[   70.255911] [  T339] apexd: Didn't generate uevent for [com.android.os.statsd] removal
[   70.255939] [  T339] apexd: Failed to delete dm-device com.android.os.statsd
[   70.255980] [  T339] apexd: Deleting unused dm device com.android.rkpd
[   70.256105] [  T339] apexd: Didn't generate uevent for [com.android.rkpd] removal
[   70.256132] [  T339] apexd: Failed to delete dm-device com.android.rkpd
[   70.256160] [  T339] apexd: Deleting unused dm device com.android.runtime
[   70.256347] [  T339] apexd: Didn't generate uevent for [com.android.runtime] removal
[   70.256374] [  T339] apexd: Failed to delete dm-device com.android.runtime
[   70.256416] [  T339] apexd: Deleting unused dm device com.android.sdkext
[   70.256557] [  T339] apexd: Didn't generate uevent for [com.android.sdkext] removal
[   70.256582] [  T339] apexd: Failed to delete dm-device com.android.sdkext
[   70.256615] [  T339] apexd: Deleting unused dm device com.android.tzdata
[   70.256763] [  T339] apexd: Didn't generate uevent for [com.android.tzdata] removal
[   70.256790] [  T339] apexd: Failed to delete dm-device com.android.tzdata
[   70.256826] [  T339] apexd: Deleting unused dm device com.android.virt
[   70.257041] [  T339] apexd: Didn't generate uevent for [com.android.virt] removal
[   70.257067] [  T339] apexd: Failed to delete dm-device com.android.virt
[   70.257106] [  T339] apexd: Deleting unused dm device com.android.vndk.v34
[   70.257363] [  T339] apexd: Didn't generate uevent for [com.android.vndk.v34] removal
[   70.257392] [  T339] apexd: Failed to delete dm-device com.android.vndk.v34
[   70.257431] [  T339] apexd: Deleting unused dm device com.rockchip.hardware.sensors
[   70.257607] [  T339] apexd: Didn't generate uevent for [com.rockchip.hardware.sensors] removal
[   70.257634] [  T339] apexd: Failed to delete dm-device com.rockchip.hardware.sensors
[   70.257703] [  T339] AidlLazyServiceRegistrar: Trying to shut down the service. No clients in use for any service in process.
[   70.258151] [  T276] servicemanager: Unregistering apexservice
[   70.258237] [  T276] BpBinder: onLastStrongRef automatically unlinking death recipients: 
[   70.258414] [  T339] AidlLazyServiceRegistrar: Unregistered all clients and exiting
[   70.264542] [    T1] init: Service 'apexd' (pid 339) exited with status 0 oneshot service took 64.366997 seconds in background
[   70.264587] [    T1] init: Sending signal 9 to service 'apexd' (pid 339) process group...
[   70.264847] [    T1] libprocessgroup: Successfully killed process cgroup uid 0 pid 339 in 0ms
[   70.362107] [  T679] ad82129_shutdown(257), -----------
[   70.377254] [  T287] type=1107 audit(1749777046.312:1376): uid=0 auid=********** ses=********** subj=u:r:init:s0 msg='avc:  denied  { set } for property=vendor.czur.trackname pid=621 uid=1013 gid=1005 scontext=u:r:mediaserver:s0 tcontext=u:object_r:vendor_default_prop:s0 tclass=property_service permissive=1'
[   70.379153] [ T1672] es8156_set_switch_hp(507), es8156->muted = 1
[   70.381667] [ T1672] ad82129_startup(241), -----------
[   70.381751] [ T1672] enter into es8156_set_dai_sysclk, freq = 4096000
[   70.388915] [    T8] enter into es8156_set_bias_level, level = 1
[   70.594493] [    T8] enter into es8156_set_bias_level, level = 2
[   70.596376] [   T84] enter into es8156_set_bias_level, level = 3
[   70.662780] [  T287] type=1107 audit(1749777046.600:1377): uid=0 auid=********** ses=********** subj=u:r:init:s0 msg='avc:  denied  { set } for property=system.device.touchBoardVersion pid=2431 uid=1000 gid=1000 scontext=u:r:system_app:s0 tcontext=u:object_r:default_prop:s0 tclass=property_service permissive=1'
[   70.922990] [  T276] servicemanager: VINTF HALs require names in the format type/instance (e.g. some.package.foo.IFoo/default) but got: suspend_control
[   70.935080] [  T287] type=1400 audit(1749777046.872:1378): avc:  denied  { dac_read_search } for  comm="binder:763_7" capability=2  scontext=u:r:system_server:s0 tcontext=u:r:system_server:s0 tclass=capability permissive=1 bug=b/228030183
[   70.938169] [  T276] servicemanager: Could not find android.hardware.bluetooth.IBluetoothHci/default in the VINTF manifest.
[   70.955406] [  T478] [BT_RFKILL]: bt shut off power
[   70.955583] [  T287] type=1400 audit(1749777046.892:1379): avc:  denied  { search } for  comm="bluetooth@1.0-s" name="bluetooth" dev="mmcblk0p15" ino=1424025 scontext=u:r:hal_bluetooth_default:s0 tcontext=u:object_r:bluetooth_data_file:s0 tclass=dir permissive=1
[   70.955616] [  T478] [BT_RFKILL]: rfkill_rk_set_power: set bt wake_host high!
[   71.009034] [  T478] [BT_RFKILL]: rfkill_rk_set_power: set bt wake_host input!
[   71.009115] [  T478] [BT_RFKILL]: ENABLE UART_RTS
[   71.117022] [  T478] [BT_RFKILL]: DISABLE UART_RTS
[   71.117108] [  T478] [BT_RFKILL]: bt turn on power
[   71.117211] [  T478] [BT_RFKILL]: Request irq for bt wakeup host
[   71.117544] [  T478] [BT_RFKILL]: ** disable irq
[   71.896934] [  T107] binder: undelivered transaction 95379, process died.
[   71.897002] [  T107] binder: undelivered transaction 95367, process died.
[   71.897524] [    T1] init: Service 'bootanim' (pid 736) exited with status 0 oneshot service took 57.452000 seconds in background
[   71.897558] [    T1] init: Sending signal 9 to service 'bootanim' (pid 736) process group...
[   71.897750] [    T1] libprocessgroup: Successfully killed process cgroup uid 1003 pid 736 in 0ms
[   71.904506] [  T484] healthd: battery l=50 v=3300 t=2.6 h=2 st=3 c=-1600 fc=100 chg=au
[   72.425466] [  T287] type=1400 audit(**********.360:1380): avc:  denied  { ioctl } for  comm="m.ecloud.emedia" path="socket:[45949]" dev="sockfs" ino=45949 ioctlcmd=0x8927 scontext=u:r:platform_app:s0:c512,c768 tcontext=u:r:platform_app:s0:c512,c768 tclass=udp_socket permissive=1 app=com.ecloud.emedia
[   72.425675] [ T2666] logd: start watching /data/system/packages.list ...
[   72.426900] [ T2666] logd: ReadPackageList, total packages: 101
[   72.558964] [  T276] servicemanager: Could not find android.hardware.radio.data.IRadioData/slot1 in the VINTF manifest.
[   72.559651] [  T276] servicemanager: Could not find android.hardware.radio.messaging.IRadioMessaging/slot1 in the VINTF manifest.
[   72.560372] [  T276] servicemanager: Could not find android.hardware.radio.modem.IRadioModem/slot1 in the VINTF manifest.
[   72.560991] [  T276] servicemanager: Could not find android.hardware.radio.network.IRadioNetwork/slot1 in the VINTF manifest.
[   72.561434] [  T276] servicemanager: Could not find android.hardware.radio.sim.IRadioSim/slot1 in the VINTF manifest.
[   72.561748] [  T276] servicemanager: Could not find android.hardware.radio.voice.IRadioVoice/slot1 in the VINTF manifest.
[   72.562093] [  T276] servicemanager: Could not find android.hardware.radio.ims.IRadioIms/slot1 in the VINTF manifest.
[   72.565393] [  T276] servicemanager: Could not find android.hardware.radio.data.IRadioData/slot1 in the VINTF manifest.
[   72.565865] [  T276] servicemanager: Could not find android.hardware.radio.messaging.IRadioMessaging/slot1 in the VINTF manifest.
[   72.566186] [  T276] servicemanager: Could not find android.hardware.radio.modem.IRadioModem/slot1 in the VINTF manifest.
[   72.566431] [  T276] servicemanager: Could not find android.hardware.radio.network.IRadioNetwork/slot1 in the VINTF manifest.
[   72.566576] [  T276] servicemanager: Could not find android.hardware.radio.sim.IRadioSim/slot1 in the VINTF manifest.
[   72.566703] [  T276] servicemanager: Could not find android.hardware.radio.voice.IRadioVoice/slot1 in the VINTF manifest.
[   72.566834] [  T276] servicemanager: Could not find android.hardware.radio.ims.IRadioIms/slot1 in the VINTF manifest.
[   72.569415] [  T276] servicemanager: Could not find android.hardware.radio.ims.IRadioIms/slot1 in the VINTF manifest.
[   73.172302] [  T287] type=1400 audit(**********.108:1381): avc:  denied  { dac_read_search } for  comm="binder:763_8" capability=2  scontext=u:r:system_server:s0 tcontext=u:r:system_server:s0 tclass=capability permissive=1 bug=b/228030183
[   73.439524] [ T1242] [dhd-pcie-0] wl_android_priv_cmd: Android private cmd "BTCOEXSCAN-STOP" on wlan0
[   73.517557] [  T276] servicemanager: Found android.hardware.bluetooth.audio.IBluetoothAudioProviderFactory/default in device VINTF manifest.
[   73.518123] [  T276] servicemanager: Found android.hardware.bluetooth.audio.IBluetoothAudioProviderFactory/default in device VINTF manifest.
[   73.604176] [ T1242] [dhd-pcie-0] wl_android_priv_cmd: Android private cmd "BTCOEXSCAN-STOP" on wlan0
[   74.257492] [    T1] init: Sending signal 9 to service 'idmap2d' (pid 1435) process group...
[   74.269723] [    T1] libprocessgroup: Successfully killed process cgroup uid 1000 pid 1435 in 12ms
[   74.270707] [    T1] init: Control message: Processed ctl.stop for 'idmap2d' from pid: 763 (system_server)
[   74.271083] [    T1] init: Service 'idmap2d' (pid 1435) received signal 9
[   76.423962] [  T287] type=1400 audit(**********.360:1382): avc:  denied  { dac_read_search } for  comm="binder:763_2" capability=2  scontext=u:r:system_server:s0 tcontext=u:r:system_server:s0 tclass=capability permissive=1 bug=b/228030183
[   78.092714] [  T287] type=1400 audit(**********.028:1383): avc:  denied  { read } for  comm="hdmi@1.0-servic" name="audio_present" dev="sysfs" ino=44688 scontext=u:r:hal_hdmi_default:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[   78.092899] [  T287] type=1400 audit(**********.028:1384): avc:  denied  { open } for  comm="hdmi@1.0-servic" path="/sys/devices/platform/2ac80000.i2c/i2c-5/5-002b/audio_present" dev="sysfs" ino=44688 scontext=u:r:hal_hdmi_default:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[   78.242154] [  T287] type=1400 audit(**********.180:1385): avc:  denied  { dac_read_search } for  comm="binder:763_E" capability=2  scontext=u:r:system_server:s0 tcontext=u:r:system_server:s0 tclass=capability permissive=1 bug=b/228030183
[   78.268251] [  T287] type=1400 audit(**********.204:1386): avc:  denied  { read } for  comm="systemmanagerse" name="address" dev="sysfs" ino=34757 scontext=u:r:systemmanagerserver:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[   78.268400] [  T287] type=1400 audit(**********.204:1387): avc:  denied  { open } for  comm="systemmanagerse" path="/sys/devices/platform/2a220000.ethernet/net/eth0/address" dev="sysfs" ino=34757 scontext=u:r:systemmanagerserver:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[   78.268472] [  T287] type=1400 audit(**********.204:1388): avc:  denied  { getattr } for  comm="systemmanagerse" path="/sys/devices/platform/2a220000.ethernet/net/eth0/address" dev="sysfs" ino=34757 scontext=u:r:systemmanagerserver:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[   78.427044] [  T287] type=1400 audit(**********.364:1389): avc:  denied  { dac_read_search } for  comm="binder:763_E" capability=2  scontext=u:r:system_server:s0 tcontext=u:r:system_server:s0 tclass=capability permissive=1 bug=b/228030183
[   78.806598] [  T287] type=1400 audit(**********.744:1390): avc:  denied  { ioctl } for  comm="m.ecloud.emedia" path="socket:[47531]" dev="sockfs" ino=47531 ioctlcmd=0x8927 scontext=u:r:platform_app:s0:c512,c768 tcontext=u:r:platform_app:s0:c512,c768 tclass=udp_socket permissive=1 app=com.ecloud.emedia
[   81.693239] [    T1] init: starting service 'simple_bugreportd'...
[   81.694659] [    T1] init: Created socket '/dev/socket/dumpstate', mode 660, user 2000, group 1007
[   81.706014] [    T1] init: ... started service 'simple_bugreportd' has pid 2951
[   81.706070] [    T1] init: Control message: Processed ctl.start for 'simple_bugreportd' from pid: 763 (system_server)
[   81.821190] [  T284] logd: logdr: UID=0 GID=0 PID=2954 n tail=0 logMask=99 pid=0 start=0ns deadline=0ns
[   82.113763] [  T287] type=1400 audit(1749777058.052:1391): avc:  denied  { ioctl } for  comm="m.ecloud.emedia" path="socket:[48529]" dev="sockfs" ino=48529 ioctlcmd=0x8927 scontext=u:r:platform_app:s0:c512,c768 tcontext=u:r:platform_app:s0:c512,c768 tclass=udp_socket permissive=1 app=com.ecloud.emedia
[   82.164728] [  T284] logd: logdr: UID=0 GID=0 PID=2956 n tail=0 logMask=4 pid=0 start=0ns deadline=0ns
[   82.414570] [  T287] type=1400 audit(1749777058.352:1392): avc:  denied  { getattr } for  comm="mount" path="/data_mirror/cur_profiles" dev="mmcblk0p15" ino=1424061 scontext=u:r:dumpstate:s0 tcontext=u:object_r:user_profile_root_file:s0 tclass=dir permissive=1
[   82.414719] [  T287] type=1400 audit(1749777058.352:1393): avc:  denied  { getattr } for  comm="mount" path="/data_mirror/ref_profiles" dev="mmcblk0p15" ino=1424062 scontext=u:r:dumpstate:s0 tcontext=u:object_r:user_profile_data_file:s0 tclass=dir permissive=1
[   82.416351] [  T287] type=1400 audit(1749777058.352:1394): avc:  denied  { search } for  comm="mount" name="pass_through" dev="tmpfs" ino=16 scontext=u:r:dumpstate:s0 tcontext=u:object_r:mnt_pass_through_file:s0 tclass=dir permissive=1
[   82.416473] [  T287] type=1400 audit(1749777058.352:1395): avc:  denied  { getattr } for  comm="mount" path="/mnt/pass_through/0/emulated" dev="mmcblk0p15" ino=728377 scontext=u:r:dumpstate:s0 tcontext=u:object_r:media_userdir_file:s0 tclass=dir permissive=1
[   85.417931] [  T287] type=1400 audit(1749777061.356:1396): avc:  denied  { ioctl } for  comm="m.ecloud.emedia" path="socket:[47574]" dev="sockfs" ino=47574 ioctlcmd=0x8927 scontext=u:r:platform_app:s0:c512,c768 tcontext=u:r:platform_app:s0:c512,c768 tclass=udp_socket permissive=1 app=com.ecloud.emedia
[   85.534117] [    T1] init: Service 'simple_bugreportd' (pid 2951) exited with status 0 oneshot service took 3.831000 seconds in background
[   85.534186] [    T1] init: Sending signal 9 to service 'simple_bugreportd' (pid 2951) process group...
[   85.534455] [    T1] libprocessgroup: Successfully killed process cgroup uid 0 pid 2951 in 0ms
[   87.011740] [  T635] [dhd-pcie-0] [wlan0] wl_run_escan : LEGACY_SCAN sync ID: 1, bssidx: 0
[  105.049167] [  T276] servicemanager: Found android.hardware.graphics.allocator.IAllocator/default in device VINTF manifest.
[  105.052649] [  T276] servicemanager: Found android.hardware.graphics.allocator.IAllocator/default in device VINTF manifest.
[  105.131598] [  T287] type=1400 audit(1749777081.060:1397): avc:  denied  { dac_read_search } for  comm="android.fg" capability=2  scontext=u:r:system_server:s0 tcontext=u:r:system_server:s0 tclass=capability permissive=1 bug=b/228030183
[  105.314146] [  T287] type=1400 audit(1749777081.252:1398): avc:  denied  { ioctl } for  comm="m.ecloud.emedia" path="socket:[47639]" dev="sockfs" ino=47639 ioctlcmd=0x8927 scontext=u:r:platform_app:s0:c512,c768 tcontext=u:r:platform_app:s0:c512,c768 tclass=udp_socket permissive=1 app=com.ecloud.emedia
[  107.209074] [  T635] [dhd-pcie-0] [wlan0] wl_run_escan : LEGACY_SCAN sync ID: 2, bssidx: 0
[  110.561819] [  T287] type=1400 audit(1749777086.496:1399): avc:  denied  { add_name } for  comm="binder:466_3" name="globalAlert" scontext=u:r:netd:s0 tcontext=u:object_r:proc_net:s0 tclass=dir permissive=1
[  110.562033] [  T287] type=1400 audit(1749777086.496:1400): avc:  denied  { create } for  comm="binder:466_3" name="globalAlert" scontext=u:r:netd:s0 tcontext=u:object_r:proc_net:s0 tclass=file permissive=1
[  110.733848] [  T287] type=1400 audit(1749777086.668:1401): avc:  denied  { ioctl } for  comm="m.ecloud.emedia" path="socket:[48611]" dev="sockfs" ino=48611 ioctlcmd=0x8927 scontext=u:r:platform_app:s0:c512,c768 tcontext=u:r:platform_app:s0:c512,c768 tclass=udp_socket permissive=1 app=com.ecloud.emedia
[  110.782147] [  T287] type=1400 audit(1749777086.720:1402): avc:  denied  { dac_read_search } for  comm="binder:763_4" capability=2  scontext=u:r:system_server:s0 tcontext=u:r:system_server:s0 tclass=capability permissive=1 bug=b/228030183
[  110.872073] [  T287] type=1400 audit(1749777086.808:1403): avc:  denied  { ioctl } for  comm="d.eshare.server" path="socket:[60764]" dev="sockfs" ino=60764 ioctlcmd=0x8927 scontext=u:r:system_app:s0 tcontext=u:r:system_app:s0 tclass=udp_socket permissive=1
[  111.517247] [  T287] type=1400 audit(1749777087.452:1404): avc:  denied  { dac_read_search } for  comm="binder:763_A" capability=2  scontext=u:r:system_server:s0 tcontext=u:r:system_server:s0 tclass=capability permissive=1 bug=b/228030183
[  111.677731] [ T3149] audit: audit_lost=318 audit_rate_limit=5 audit_backlog_limit=64
[  111.677901] [ T3149] audit: rate limit exceeded
[  111.678355] [  T287] type=1400 audit(1749777087.616:1405): avc:  denied  { open } for  comm="ecloud.eairplay" path="/dev/__properties__/u:object_r:serialno_prop:s0" dev="tmpfs" ino=279 scontext=u:r:system_app:s0 tcontext=u:object_r:serialno_prop:s0 tclass=file permissive=1
[  111.837998] [  T287] type=1400 audit(1749777087.772:1408): avc:  denied  { dac_read_search } for  comm="binder:763_B" capability=2  scontext=u:r:system_server:s0 tcontext=u:r:system_server:s0 tclass=capability permissive=1 bug=b/228030183
[  111.878347] [  T287] type=1400 audit(1749777087.816:1409): avc:  denied  { ioctl } for  comm="ecloud.eairplay" path="socket:[62675]" dev="sockfs" ino=62675 ioctlcmd=0x8927 scontext=u:r:system_app:s0 tcontext=u:r:system_app:s0 tclass=udp_socket permissive=1
[  114.946914] [  T287] type=1400 audit(1749777090.884:1410): avc:  denied  { ioctl } for  comm="m.ecloud.emedia" path="socket:[82274]" dev="sockfs" ino=82274 ioctlcmd=0x8927 scontext=u:r:platform_app:s0:c512,c768 tcontext=u:r:platform_app:s0:c512,c768 tclass=udp_socket permissive=1 app=com.ecloud.emedia
[  115.889182] [  T287] type=1400 audit(1749777091.824:1411): avc:  denied  { ioctl } for  comm="ecloud.eairplay" path="socket:[82300]" dev="sockfs" ino=82300 ioctlcmd=0x8927 scontext=u:r:system_app:s0 tcontext=u:r:system_app:s0 tclass=udp_socket permissive=1
[  117.667022] [  T287] type=1400 audit(1749777093.600:1412): avc:  denied  { dac_read_search } for  comm="binder:763_D" capability=2  scontext=u:r:system_server:s0 tcontext=u:r:system_server:s0 tclass=capability permissive=1 bug=b/228030183
[  117.886556] [  T276] servicemanager: Found android.hardware.graphics.allocator.IAllocator/default in device VINTF manifest.
[  117.889940] [  T276] servicemanager: Found android.hardware.graphics.allocator.IAllocator/default in device VINTF manifest.
[  117.896105] [  T287] type=1400 audit(1749777093.832:1413): avc:  denied  { ioctl } for  comm="ecloud.eairplay" path="socket:[62774]" dev="sockfs" ino=62774 ioctlcmd=0x8927 scontext=u:r:system_app:s0 tcontext=u:r:system_app:s0 tclass=udp_socket permissive=1
[  118.490088] [  T287] type=1400 audit(1749777094.428:1414): avc:  denied  { read } for  comm="systemmanagerse" name="address" dev="sysfs" ino=34757 scontext=u:r:systemmanagerserver:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[  118.492464] [  T287] type=1400 audit(1749777094.428:1415): avc:  denied  { open } for  comm="systemmanagerse" path="/sys/devices/platform/2a220000.ethernet/net/eth0/address" dev="sysfs" ino=34757 scontext=u:r:systemmanagerserver:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[  118.492649] [  T287] type=1400 audit(1749777094.428:1416): avc:  denied  { getattr } for  comm="systemmanagerse" path="/sys/devices/platform/2a220000.ethernet/net/eth0/address" dev="sysfs" ino=34757 scontext=u:r:systemmanagerserver:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[  118.890592] [  T287] type=1107 audit(1749777094.828:1417): uid=0 auid=********** ses=********** subj=u:r:init:s0 msg='avc:  denied  { set } for property=vendor.czur.touchpad.update pid=2534 uid=1000 gid=1000 scontext=u:r:system_app:s0 tcontext=u:object_r:vendor_default_prop:s0 tclass=property_service permissive=1'
[  119.305672] [  T287] type=1400 audit(1749777095.236:1418): avc:  denied  { dac_read_search } for  comm="binder:763_4" capability=2  scontext=u:r:system_server:s0 tcontext=u:r:system_server:s0 tclass=capability permissive=1 bug=b/228030183
[  119.579170] [  T287] type=1400 audit(1749777095.516:1419): avc:  denied  { read } for  comm="systemmanagerse" name="address" dev="sysfs" ino=34757 scontext=u:r:systemmanagerserver:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[  119.579299] [  T287] type=1400 audit(1749777095.516:1420): avc:  denied  { open } for  comm="systemmanagerse" path="/sys/devices/platform/2a220000.ethernet/net/eth0/address" dev="sysfs" ino=34757 scontext=u:r:systemmanagerserver:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[  119.579365] [  T287] type=1400 audit(1749777095.516:1421): avc:  denied  { getattr } for  comm="systemmanagerse" path="/sys/devices/platform/2a220000.ethernet/net/eth0/address" dev="sysfs" ino=34757 scontext=u:r:systemmanagerserver:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[  119.904048] [  T287] type=1400 audit(1749777095.840:1422): avc:  denied  { ioctl } for  comm="ecloud.eairplay" path="socket:[60928]" dev="sockfs" ino=60928 ioctlcmd=0x8927 scontext=u:r:system_app:s0 tcontext=u:r:system_app:s0 tclass=udp_socket permissive=1
[  121.346492] [  T287] type=1400 audit(1749777097.284:1423): avc:  denied  { ioctl } for  comm="m.ecloud.emedia" path="socket:[48831]" dev="sockfs" ino=48831 ioctlcmd=0x8927 scontext=u:r:platform_app:s0:c512,c768 tcontext=u:r:platform_app:s0:c512,c768 tclass=udp_socket permissive=1 app=com.ecloud.emedia
[  124.651534] [  T287] type=1400 audit(1749777100.588:1424): avc:  denied  { ioctl } for  comm="m.ecloud.emedia" path="socket:[48841]" dev="sockfs" ino=48841 ioctlcmd=0x8927 scontext=u:r:platform_app:s0:c512,c768 tcontext=u:r:platform_app:s0:c512,c768 tclass=udp_socket permissive=1 app=com.ecloud.emedia
[  127.016755] [  T635] [dhd-pcie-0] [wlan0] wl_run_escan : LEGACY_SCAN sync ID: 3, bssidx: 0
[  130.637867] [  T287] type=1400 audit(**********.576:1425): avc:  denied  { ioctl } for  comm="d.eshare.server" path="socket:[83083]" dev="sockfs" ino=83083 ioctlcmd=0x8927 scontext=u:r:system_app:s0 tcontext=u:r:system_app:s0 tclass=udp_socket permissive=1
[  131.906738] [  T484] healthd: battery l=50 v=3300 t=2.6 h=2 st=3 c=-1600 fc=100 chg=au
[  134.778427] [  T287] type=1400 audit(**********.716:1426): avc:  denied  { dac_read_search } for  comm="binder:763_10" capability=2  scontext=u:r:system_server:s0 tcontext=u:r:system_server:s0 tclass=capability permissive=1 bug=b/228030183
[  135.031203] [  T276] servicemanager: Found android.hardware.graphics.allocator.IAllocator/default in device VINTF manifest.
[  135.034524] [  T276] servicemanager: Found android.hardware.graphics.allocator.IAllocator/default in device VINTF manifest.
[  137.896638] [  T287] type=1400 audit(**********.832:1427): avc:  denied  { ioctl } for  comm="m.ecloud.emedia" path="socket:[85335]" dev="sockfs" ino=85335 ioctlcmd=0x8927 scontext=u:r:platform_app:s0:c512,c768 tcontext=u:r:platform_app:s0:c512,c768 tclass=udp_socket permissive=1 app=com.ecloud.emedia
[  138.975269] [  T287] type=1400 audit(**********.912:1428): avc:  denied  { ioctl } for  comm="ecloud.eairplay" path="socket:[85343]" dev="sockfs" ino=85343 ioctlcmd=0x8927 scontext=u:r:system_app:s0 tcontext=u:r:system_app:s0 tclass=udp_socket permissive=1
[  139.941184] [  T276] servicemanager: Found android.hardware.graphics.allocator.IAllocator/default in device VINTF manifest.
[  139.946456] [  T276] servicemanager: Found android.hardware.graphics.allocator.IAllocator/default in device VINTF manifest.
[  140.047758] [  T287] type=1400 audit(**********.980:1429): avc:  denied  { read } for  comm="systemmanagerse" name="address" dev="sysfs" ino=34757 scontext=u:r:systemmanagerserver:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[  140.047958] [  T287] type=1400 audit(**********.980:1430): avc:  denied  { open } for  comm="systemmanagerse" path="/sys/devices/platform/2a220000.ethernet/net/eth0/address" dev="sysfs" ino=34757 scontext=u:r:systemmanagerserver:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[  140.048037] [  T287] type=1400 audit(**********.980:1431): avc:  denied  { getattr } for  comm="systemmanagerse" path="/sys/devices/platform/2a220000.ethernet/net/eth0/address" dev="sysfs" ino=34757 scontext=u:r:systemmanagerserver:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[  141.990050] [ T1234] usb 1-1.3: USB disconnect, device number 3
[  191.909050] [  T484] healthd: battery l=50 v=3300 t=2.6 h=2 st=3 c=-1600 fc=100 chg=au
[  207.003216] [  T635] [dhd-pcie-0] [wlan0] wl_run_escan : LEGACY_SCAN sync ID: 4, bssidx: 0
[  238.942742] [ T1544] [dhd-sdio-1] CFG80211-ERROR) wl_cfg80211_abort_action_frame : actframe_abort unsupported. ret:-23
[  239.391124] [ T1544] [dhd-sdio-1] CFG80211-ERROR) wl_cfg80211_abort_action_frame : actframe_abort unsupported. ret:-23
[  239.843714] [ T1544] [dhd-sdio-1] CFG80211-ERROR) wl_cfg80211_abort_action_frame : actframe_abort unsupported. ret:-23
[  240.293939] [ T1544] [dhd-sdio-1] CFG80211-ERROR) wl_cfg80211_abort_action_frame : actframe_abort unsupported. ret:-23
[  240.776652] [ T1544] [dhd-sdio-1] CFG80211-ERROR) wl_cfg80211_abort_action_frame : actframe_abort unsupported. ret:-23
[  241.246658] [ T1544] [dhd-sdio-1] CFG80211-ERROR) wl_cfg80211_abort_action_frame : actframe_abort unsupported. ret:-23
[  241.263487] [ T1544] [dhd-sdio-1] CFG80211-ERROR) wl_cfg80211_send_action_frame : Failed to send Action Frame(retry 6)
[  241.265121] [ T1544] [dhd-sdio-1] CFG80211-ERROR) wl_cfg80211_send_action_frame : -- Action Frame Tx failed, listen chan: 0
[  251.905435] [  T484] healthd: battery l=50 v=3300 t=2.6 h=2 st=3 c=-1600 fc=100 chg=au
[  260.087317] [  T287] type=1400 audit(**********.024:1432): avc:  denied  { read } for  comm="systemmanagerse" name="address" dev="sysfs" ino=34757 scontext=u:r:systemmanagerserver:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[  260.087532] [  T287] type=1400 audit(**********.024:1433): avc:  denied  { open } for  comm="systemmanagerse" path="/sys/devices/platform/2a220000.ethernet/net/eth0/address" dev="sysfs" ino=34757 scontext=u:r:systemmanagerserver:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[  260.087945] [  T287] type=1400 audit(**********.024:1434): avc:  denied  { getattr } for  comm="systemmanagerse" path="/sys/devices/platform/2a220000.ethernet/net/eth0/address" dev="sysfs" ino=34757 scontext=u:r:systemmanagerserver:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[  311.922852] [  T484] healthd: battery l=50 v=3300 t=2.6 h=2 st=3 c=-1600 fc=100 chg=au
[  367.018106] [  T635] [dhd-pcie-0] [wlan0] wl_run_escan : LEGACY_SCAN sync ID: 5, bssidx: 0
[  371.904625] [  T484] healthd: battery l=50 v=3300 t=2.6 h=2 st=3 c=-1600 fc=100 chg=au
[  431.906692] [  T484] healthd: battery l=50 v=3300 t=2.6 h=2 st=3 c=-1600 fc=100 chg=au
[  491.905450] [  T484] healthd: battery l=50 v=3300 t=2.6 h=2 st=3 c=-1600 fc=100 chg=au
[  527.022712] [  T635] [dhd-pcie-0] [wlan0] wl_run_escan : LEGACY_SCAN sync ID: 6, bssidx: 0
[  551.907143] [  T484] healthd: battery l=50 v=3300 t=2.6 h=2 st=3 c=-1600 fc=100 chg=au
[  611.906564] [  T484] healthd: battery l=50 v=3300 t=2.6 h=2 st=3 c=-1600 fc=100 chg=au
[  671.903883] [  T484] healthd: battery l=50 v=3300 t=2.6 h=2 st=3 c=-1600 fc=100 chg=au
[  687.028024] [  T635] [dhd-pcie-0] [wlan0] wl_run_escan : LEGACY_SCAN sync ID: 7, bssidx: 0
[  731.905091] [  T484] healthd: battery l=50 v=3300 t=2.6 h=2 st=3 c=-1600 fc=100 chg=au
[  741.327035] [  T287] type=1400 audit(**********.264:1435): avc:  denied  { ioctl } for  comm="m.ecloud.emedia" path="socket:[129085]" dev="sockfs" ino=129085 ioctlcmd=0x8927 scontext=u:r:platform_app:s0:c512,c768 tcontext=u:r:platform_app:s0:c512,c768 tclass=udp_socket permissive=1 app=com.ecloud.emedia
[  791.904493] [  T484] healthd: battery l=50 v=3300 t=2.6 h=2 st=3 c=-1600 fc=100 chg=au
[  847.006004] [  T635] [dhd-pcie-0] [wlan0] wl_run_escan : LEGACY_SCAN sync ID: 8, bssidx: 0
[  851.906193] [  T484] healthd: battery l=50 v=3300 t=2.6 h=2 st=3 c=-1600 fc=100 chg=au
[  861.434401] [ T1544] [dhd-sdio-1] CFG80211-ERROR) wl_cfg80211_abort_action_frame : actframe_abort unsupported. ret:-23
[  861.883057] [ T1544] [dhd-sdio-1] CFG80211-ERROR) wl_cfg80211_abort_action_frame : actframe_abort unsupported. ret:-23
[  862.336272] [ T1544] [dhd-sdio-1] CFG80211-ERROR) wl_cfg80211_abort_action_frame : actframe_abort unsupported. ret:-23
[  862.782515] [ T1544] [dhd-sdio-1] CFG80211-ERROR) wl_cfg80211_abort_action_frame : actframe_abort unsupported. ret:-23
[  863.231332] [ T1544] [dhd-sdio-1] CFG80211-ERROR) wl_cfg80211_abort_action_frame : actframe_abort unsupported. ret:-23
[  863.713835] [ T1544] [dhd-sdio-1] CFG80211-ERROR) wl_cfg80211_abort_action_frame : actframe_abort unsupported. ret:-23
[  863.717407] [ T1544] [dhd-sdio-1] CFG80211-ERROR) wl_cfg80211_send_action_frame : Failed to send Action Frame(retry 6)
[  863.717499] [ T1544] [dhd-sdio-1] CFG80211-ERROR) wl_cfg80211_send_action_frame : -- Action Frame Tx failed, listen chan: 0
[  911.903868] [  T484] healthd: battery l=50 v=3300 t=2.6 h=2 st=3 c=-1600 fc=100 chg=au
[  971.906529] [  T484] healthd: battery l=50 v=3300 t=2.6 h=2 st=3 c=-1600 fc=100 chg=au
[ 1007.032569] [  T635] [dhd-pcie-0] [wlan0] wl_run_escan : LEGACY_SCAN sync ID: 9, bssidx: 0
[ 1031.905157] [  T484] healthd: battery l=50 v=3300 t=2.6 h=2 st=3 c=-1600 fc=100 chg=au
[ 1032.967399] [  T287] type=1400 audit(**********.904:1436): avc:  denied  { ioctl } for  comm="ecloud.eairplay" path="socket:[148805]" dev="sockfs" ino=148805 ioctlcmd=0x8927 scontext=u:r:system_app:s0 tcontext=u:r:system_app:s0 tclass=udp_socket permissive=1
[ 1091.903555] [  T484] healthd: battery l=50 v=3300 t=2.6 h=2 st=3 c=-1600 fc=100 chg=au
[ 1097.189107] [    T1] init: Untracked pid 3918 exited with status 0
[ 1097.189164] [    T1] init: Untracked pid 3918 did not have an associated service entry and will not be reaped
[ 1097.189686] [    T1] init: Untracked pid 3920 exited with status 0
[ 1097.189717] [    T1] init: Untracked pid 3920 did not have an associated service entry and will not be reaped
[ 1097.229274] [    T1] init: Untracked pid 3923 exited with status 0
[ 1097.229331] [    T1] init: Untracked pid 3923 did not have an associated service entry and will not be reaped
[ 1097.230871] [    T1] init: Untracked pid 3925 exited with status 0
[ 1097.230926] [    T1] init: Untracked pid 3925 did not have an associated service entry and will not be reaped
[ 1097.288008] [    T1] init: Untracked pid 3928 exited with status 0
[ 1097.288037] [    T1] init: Untracked pid 3928 did not have an associated service entry and will not be reaped
[ 1097.289512] [    T1] init: Untracked pid 3930 exited with status 0
[ 1097.289551] [    T1] init: Untracked pid 3930 did not have an associated service entry and will not be reaped
[ 1097.337566] [    T1] init: Untracked pid 3933 exited with status 0
[ 1097.337637] [    T1] init: Untracked pid 3933 did not have an associated service entry and will not be reaped
[ 1097.340721] [    T1] init: Untracked pid 3935 exited with status 0
[ 1097.341552] [    T1] init: Untracked pid 3935 did not have an associated service entry and will not be reaped
[ 1097.384305] [    T1] init: Untracked pid 3938 exited with status 0
[ 1097.384370] [    T1] init: Untracked pid 3938 did not have an associated service entry and will not be reaped
[ 1097.387228] [    T1] init: Untracked pid 3940 exited with status 0
[ 1097.387286] [    T1] init: Untracked pid 3940 did not have an associated service entry and will not be reaped
[ 1097.572135] [    T1] init: Untracked pid 3943 exited with status 0
[ 1097.572190] [    T1] init: Untracked pid 3943 did not have an associated service entry and will not be reaped
[ 1097.580878] [    T1] init: Untracked pid 3945 exited with status 0
[ 1097.580950] [    T1] init: Untracked pid 3945 did not have an associated service entry and will not be reaped
[ 1097.731035] [    T1] init: Untracked pid 3948 exited with status 0
[ 1097.731093] [    T1] init: Untracked pid 3948 did not have an associated service entry and will not be reaped
[ 1097.736336] [    T1] init: Untracked pid 3950 exited with status 0
[ 1097.736405] [    T1] init: Untracked pid 3950 did not have an associated service entry and will not be reaped
[ 1097.782301] [    T1] init: Untracked pid 3953 exited with status 0
[ 1097.782358] [    T1] init: Untracked pid 3953 did not have an associated service entry and will not be reaped
[ 1097.784636] [    T1] init: Untracked pid 3955 exited with status 0
[ 1097.784700] [    T1] init: Untracked pid 3955 did not have an associated service entry and will not be reaped
[ 1097.816148] [    T1] init: Untracked pid 3959 exited with status 0
[ 1097.816207] [    T1] init: Untracked pid 3959 did not have an associated service entry and will not be reaped
[ 1097.817547] [    T1] init: Untracked pid 3961 exited with status 0
[ 1097.817588] [    T1] init: Untracked pid 3961 did not have an associated service entry and will not be reaped
[ 1097.844409] [    T1] init: Untracked pid 3964 exited with status 0
[ 1097.844510] [    T1] init: Untracked pid 3964 did not have an associated service entry and will not be reaped
[ 1097.846308] [    T1] init: Untracked pid 3966 exited with status 0
[ 1097.846366] [    T1] init: Untracked pid 3966 did not have an associated service entry and will not be reaped
[ 1097.881070] [    T1] init: Untracked pid 3969 exited with status 0
[ 1097.881141] [    T1] init: Untracked pid 3969 did not have an associated service entry and will not be reaped
[ 1097.882904] [    T1] init: Untracked pid 3971 exited with status 0
[ 1097.882950] [    T1] init: Untracked pid 3971 did not have an associated service entry and will not be reaped
[ 1097.914090] [    T1] init: Untracked pid 3974 exited with status 0
[ 1097.914154] [    T1] init: Untracked pid 3974 did not have an associated service entry and will not be reaped
[ 1097.915100] [    T1] init: Untracked pid 3976 exited with status 0
[ 1097.915128] [    T1] init: Untracked pid 3976 did not have an associated service entry and will not be reaped
[ 1097.939627] [    T1] init: Untracked pid 3979 exited with status 0
[ 1097.939693] [    T1] init: Untracked pid 3979 did not have an associated service entry and will not be reaped
[ 1097.945191] [    T1] init: Untracked pid 3981 exited with status 0
[ 1097.945257] [    T1] init: Untracked pid 3981 did not have an associated service entry and will not be reaped
[ 1097.967171] [    T1] init: Untracked pid 3984 exited with status 0
[ 1097.967233] [    T1] init: Untracked pid 3984 did not have an associated service entry and will not be reaped
[ 1097.968285] [    T1] init: Untracked pid 3986 exited with status 0
[ 1097.968323] [    T1] init: Untracked pid 3986 did not have an associated service entry and will not be reaped
[ 1097.988333] [  T287] type=1400 audit(1749778073.924:1437): avc:  denied  { ptrace } for  comm="crash_dump64" scontext=u:r:crash_dump:s0 tcontext=u:r:vold:s0 tclass=process permissive=1
[ 1098.007835] [    T1] init: Untracked pid 3989 exited with status 0
[ 1098.007899] [    T1] init: Untracked pid 3989 did not have an associated service entry and will not be reaped
[ 1098.013873] [    T1] init: Untracked pid 3991 exited with status 0
[ 1098.013945] [    T1] init: Untracked pid 3991 did not have an associated service entry and will not be reaped
[ 1098.240927] [  T287] type=1400 audit(1749778074.176:1438): avc:  denied  { ioctl } for  comm="ecloud.eairplay" path="socket:[151970]" dev="sockfs" ino=151970 ioctlcmd=0x8927 scontext=u:r:system_app:s0 tcontext=u:r:system_app:s0 tclass=udp_socket permissive=1
[ 1098.391232] [    T1] init: Untracked pid 3994 exited with status 0
[ 1098.391268] [    T1] init: Untracked pid 3994 did not have an associated service entry and will not be reaped
[ 1098.398321] [    T1] init: Untracked pid 3996 exited with status 0
[ 1098.398379] [    T1] init: Untracked pid 3996 did not have an associated service entry and will not be reaped
[ 1098.481567] [    T1] init: Untracked pid 3999 exited with status 0
[ 1098.481623] [    T1] init: Untracked pid 3999 did not have an associated service entry and will not be reaped
[ 1098.488357] [    T1] init: Untracked pid 4001 exited with status 0
[ 1098.488392] [    T1] init: Untracked pid 4001 did not have an associated service entry and will not be reaped
[ 1098.570778] [    T1] init: Untracked pid 4007 exited with status 0
[ 1098.570812] [    T1] init: Untracked pid 4007 did not have an associated service entry and will not be reaped
[ 1098.574298] [    T1] init: Untracked pid 4009 exited with status 0
[ 1098.574352] [    T1] init: Untracked pid 4009 did not have an associated service entry and will not be reaped
[ 1098.594622] [    T1] init: Untracked pid 4012 exited with status 0
[ 1098.594688] [    T1] init: Untracked pid 4012 did not have an associated service entry and will not be reaped
[ 1098.595594] [    T1] init: Untracked pid 4014 exited with status 0
[ 1098.595640] [    T1] init: Untracked pid 4014 did not have an associated service entry and will not be reaped
[ 1098.619329] [    T1] init: Untracked pid 4017 exited with status 0
[ 1098.619388] [    T1] init: Untracked pid 4017 did not have an associated service entry and will not be reaped
[ 1098.619829] [    T1] init: Untracked pid 4019 exited with status 0
[ 1098.619850] [    T1] init: Untracked pid 4019 did not have an associated service entry and will not be reaped
[ 1098.649772] [    T1] init: Untracked pid 4022 exited with status 0
[ 1098.649846] [    T1] init: Untracked pid 4022 did not have an associated service entry and will not be reaped
[ 1098.650544] [    T1] init: Untracked pid 4024 exited with status 0
[ 1098.650599] [    T1] init: Untracked pid 4024 did not have an associated service entry and will not be reaped
[ 1098.830545] [  T287] type=1400 audit(1749778074.768:1439): avc:  denied  { ptrace } for  comm="crash_dump64" scontext=u:r:crash_dump:s0 tcontext=u:r:keystore:s0 tclass=process permissive=1
[ 1098.848834] [    T1] init: Untracked pid 4027 exited with status 0
[ 1098.848899] [    T1] init: Untracked pid 4027 did not have an associated service entry and will not be reaped
[ 1098.851810] [    T1] init: Untracked pid 4029 exited with status 0
[ 1098.851867] [    T1] init: Untracked pid 4029 did not have an associated service entry and will not be reaped
[ 1099.125661] [  T287] type=1400 audit(1749778075.064:1440): avc:  denied  { ioctl } for  comm="m.ecloud.emedia" path="socket:[149492]" dev="sockfs" ino=149492 ioctlcmd=0x8927 scontext=u:r:platform_app:s0:c512,c768 tcontext=u:r:platform_app:s0:c512,c768 tclass=udp_socket permissive=1 app=com.ecloud.emedia
[ 1104.254924] [  T287] type=1400 audit(1749778080.192:1441): avc:  denied  { ioctl } for  comm="ecloud.eairplay" path="socket:[155663]" dev="sockfs" ino=155663 ioctlcmd=0x8927 scontext=u:r:system_app:s0 tcontext=u:r:system_app:s0 tclass=udp_socket permissive=1
[ 1105.733616] [  T287] type=1400 audit(1749778081.672:1442): avc:  denied  { ioctl } for  comm="m.ecloud.emedia" path="socket:[155667]" dev="sockfs" ino=155667 ioctlcmd=0x8927 scontext=u:r:platform_app:s0:c512,c768 tcontext=u:r:platform_app:s0:c512,c768 tclass=udp_socket permissive=1 app=com.ecloud.emedia
[ 1134.865166] [    T1] init: Untracked pid 4039 exited with status 0
[ 1134.865228] [    T1] init: Untracked pid 4039 did not have an associated service entry and will not be reaped
[ 1134.866583] [    T1] init: Untracked pid 4041 exited with status 0
[ 1134.866626] [    T1] init: Untracked pid 4041 did not have an associated service entry and will not be reaped
[ 1134.899300] [    T1] init: Untracked pid 4044 exited with status 0
[ 1134.899358] [    T1] init: Untracked pid 4044 did not have an associated service entry and will not be reaped
[ 1134.900674] [    T1] init: Untracked pid 4046 exited with status 0
[ 1134.900719] [    T1] init: Untracked pid 4046 did not have an associated service entry and will not be reaped
[ 1134.960805] [    T1] init: Untracked pid 4049 exited with status 0
[ 1134.960845] [    T1] init: Untracked pid 4049 did not have an associated service entry and will not be reaped
[ 1134.963151] [    T1] init: Untracked pid 4051 exited with status 0
[ 1134.963213] [    T1] init: Untracked pid 4051 did not have an associated service entry and will not be reaped
[ 1135.004635] [    T1] init: Untracked pid 4054 exited with status 0
[ 1135.004697] [    T1] init: Untracked pid 4054 did not have an associated service entry and will not be reaped
[ 1135.005702] [    T1] init: Untracked pid 4056 exited with status 0
[ 1135.005730] [    T1] init: Untracked pid 4056 did not have an associated service entry and will not be reaped
[ 1135.047653] [    T1] init: Untracked pid 4060 exited with status 0
[ 1135.047689] [    T1] init: Untracked pid 4060 did not have an associated service entry and will not be reaped
[ 1135.053951] [    T1] init: Untracked pid 4062 exited with status 0
[ 1135.054010] [    T1] init: Untracked pid 4062 did not have an associated service entry and will not be reaped
[ 1135.230568] [    T1] init: Untracked pid 4065 exited with status 0
[ 1135.230622] [    T1] init: Untracked pid 4065 did not have an associated service entry and will not be reaped
[ 1135.238443] [    T1] init: Untracked pid 4067 exited with status 0
[ 1135.238509] [    T1] init: Untracked pid 4067 did not have an associated service entry and will not be reaped
[ 1135.382209] [    T1] init: Untracked pid 4071 exited with status 0
[ 1135.382271] [    T1] init: Untracked pid 4071 did not have an associated service entry and will not be reaped
[ 1135.384902] [    T1] init: Untracked pid 4073 exited with status 0
[ 1135.384958] [    T1] init: Untracked pid 4073 did not have an associated service entry and will not be reaped
[ 1135.434933] [    T1] init: Untracked pid 4077 exited with status 0
[ 1135.434991] [    T1] init: Untracked pid 4077 did not have an associated service entry and will not be reaped
[ 1135.439248] [    T1] init: Untracked pid 4079 exited with status 0
[ 1135.439359] [    T1] init: Untracked pid 4079 did not have an associated service entry and will not be reaped
[ 1135.463203] [    T1] init: Untracked pid 4082 exited with status 0
[ 1135.463267] [    T1] init: Untracked pid 4082 did not have an associated service entry and will not be reaped
[ 1135.464309] [    T1] init: Untracked pid 4084 exited with status 0
[ 1135.464350] [    T1] init: Untracked pid 4084 did not have an associated service entry and will not be reaped
[ 1135.494635] [    T1] init: Untracked pid 4087 exited with status 0
[ 1135.494702] [    T1] init: Untracked pid 4087 did not have an associated service entry and will not be reaped
[ 1135.497163] [    T1] init: Untracked pid 4089 exited with status 0
[ 1135.497216] [    T1] init: Untracked pid 4089 did not have an associated service entry and will not be reaped
[ 1135.537439] [    T1] init: Untracked pid 4092 exited with status 0
[ 1135.537502] [    T1] init: Untracked pid 4092 did not have an associated service entry and will not be reaped
[ 1135.539958] [    T1] init: Untracked pid 4094 exited with status 0
[ 1135.540016] [    T1] init: Untracked pid 4094 did not have an associated service entry and will not be reaped
[ 1135.570432] [    T1] init: Untracked pid 4097 exited with status 0
[ 1135.570489] [    T1] init: Untracked pid 4097 did not have an associated service entry and will not be reaped
[ 1135.573229] [    T1] init: Untracked pid 4099 exited with status 0
[ 1135.573284] [    T1] init: Untracked pid 4099 did not have an associated service entry and will not be reaped
[ 1135.596627] [    T1] init: Untracked pid 4102 exited with status 0
[ 1135.596682] [    T1] init: Untracked pid 4102 did not have an associated service entry and will not be reaped
[ 1135.598908] [    T1] init: Untracked pid 4104 exited with status 0
[ 1135.598968] [    T1] init: Untracked pid 4104 did not have an associated service entry and will not be reaped
[ 1135.626179] [    T1] init: Untracked pid 4107 exited with status 0
[ 1135.626298] [    T1] init: Untracked pid 4107 did not have an associated service entry and will not be reaped
[ 1135.626843] [    T1] init: Untracked pid 4109 exited with status 0
[ 1135.626898] [    T1] init: Untracked pid 4109 did not have an associated service entry and will not be reaped
[ 1135.645680] [  T287] type=1400 audit(1749778111.584:1443): avc:  denied  { ptrace } for  comm="crash_dump64" scontext=u:r:crash_dump:s0 tcontext=u:r:vold:s0 tclass=process permissive=1
[ 1135.661996] [    T1] init: Untracked pid 4112 exited with status 0
[ 1135.662093] [    T1] init: Untracked pid 4112 did not have an associated service entry and will not be reaped
[ 1135.662615] [    T1] init: Untracked pid 4114 exited with status 0
[ 1135.662634] [    T1] init: Untracked pid 4114 did not have an associated service entry and will not be reaped
[ 1136.051595] [    T1] init: Untracked pid 4117 exited with status 0
[ 1136.051628] [    T1] init: Untracked pid 4117 did not have an associated service entry and will not be reaped
[ 1136.059045] [    T1] init: Untracked pid 4119 exited with status 0
[ 1136.059109] [    T1] init: Untracked pid 4119 did not have an associated service entry and will not be reaped
[ 1136.134186] [    T1] init: Untracked pid 4122 exited with status 0
[ 1136.134248] [    T1] init: Untracked pid 4122 did not have an associated service entry and will not be reaped
[ 1136.137970] [    T1] init: Untracked pid 4124 exited with status 0
[ 1136.138030] [    T1] init: Untracked pid 4124 did not have an associated service entry and will not be reaped
[ 1136.216581] [    T1] init: Untracked pid 4128 exited with status 0
[ 1136.216612] [    T1] init: Untracked pid 4128 did not have an associated service entry and will not be reaped
[ 1136.220461] [    T1] init: Untracked pid 4130 exited with status 0
[ 1136.220515] [    T1] init: Untracked pid 4130 did not have an associated service entry and will not be reaped
[ 1136.252629] [    T1] init: Untracked pid 4133 exited with status 0
[ 1136.252697] [    T1] init: Untracked pid 4133 did not have an associated service entry and will not be reaped
[ 1136.254219] [    T1] init: Untracked pid 4135 exited with status 0
[ 1136.254265] [    T1] init: Untracked pid 4135 did not have an associated service entry and will not be reaped
[ 1136.278125] [    T1] init: Untracked pid 4138 exited with status 0
[ 1136.278189] [    T1] init: Untracked pid 4138 did not have an associated service entry and will not be reaped
[ 1136.278733] [    T1] init: Untracked pid 4140 exited with status 0
[ 1136.278778] [    T1] init: Untracked pid 4140 did not have an associated service entry and will not be reaped
[ 1136.307925] [    T1] init: Untracked pid 4143 exited with status 0
[ 1136.307992] [    T1] init: Untracked pid 4143 did not have an associated service entry and will not be reaped
[ 1136.309362] [    T1] init: Untracked pid 4145 exited with status 0
[ 1136.309411] [    T1] init: Untracked pid 4145 did not have an associated service entry and will not be reaped
[ 1136.320517] [  T287] type=1400 audit(1749778112.256:1444): avc:  denied  { ioctl } for  comm="ecloud.eairplay" path="socket:[155813]" dev="sockfs" ino=155813 ioctlcmd=0x8927 scontext=u:r:system_app:s0 tcontext=u:r:system_app:s0 tclass=udp_socket permissive=1
[ 1136.480282] [  T287] type=1400 audit(1749778112.416:1445): avc:  denied  { ptrace } for  comm="crash_dump64" scontext=u:r:crash_dump:s0 tcontext=u:r:keystore:s0 tclass=process permissive=1
[ 1136.497733] [    T1] init: Untracked pid 4148 exited with status 0
[ 1136.497796] [    T1] init: Untracked pid 4148 did not have an associated service entry and will not be reaped
[ 1136.500190] [    T1] init: Untracked pid 4150 exited with status 0
[ 1136.500246] [    T1] init: Untracked pid 4150 did not have an associated service entry and will not be reaped
[ 1138.799017] [  T287] type=1400 audit(1749778114.736:1446): avc:  denied  { ioctl } for  comm="m.ecloud.emedia" path="socket:[153111]" dev="sockfs" ino=153111 ioctlcmd=0x8927 scontext=u:r:platform_app:s0:c512,c768 tcontext=u:r:platform_app:s0:c512,c768 tclass=udp_socket permissive=1 app=com.ecloud.emedia
[ 1141.522460] [  T785] sysrq: Show Blocked State
[ 1141.529187] [  T785] sysrq: Show backtrace of all active CPUs
[ 1141.529234] [  T785] sysrq: CPU3:
[ 1141.529242] [  T785] Call trace:
[ 1141.529249] [  T785]  dump_backtrace+0xf4/0x114
[ 1141.529268] [  T785]  show_stack+0x18/0x24
[ 1141.529278] [  T785]  sysrq_handle_showallcpus+0x84/0xcc
[ 1141.529290] [  T785]  __handle_sysrq+0xcc/0x17c
[ 1141.529299] [  T785]  write_sysrq_trigger+0x100/0x188
[ 1141.529309] [  T785]  proc_reg_write+0xc4/0x134
[ 1141.529320] [  T785]  vfs_write+0xf0/0x294
[ 1141.529331] [  T785]  ksys_write+0x78/0xe4
[ 1141.529340] [  T785]  __arm64_sys_write+0x1c/0x28
[ 1141.529350] [  T785]  invoke_syscall+0x40/0x104
[ 1141.529360] [  T785]  el0_svc_common+0xbc/0x168
[ 1141.529370] [  T785]  do_el0_svc+0x1c/0x28
[ 1141.529379] [  T785]  el0_svc+0x1c/0x68
[ 1141.529390] [  T785]  el0t_64_sync_handler+0x68/0xb4
[ 1141.529398] [  T785]  el0t_64_sync+0x164/0x168
[ 1141.529433] [    C2] sysrq: CPU2: backtrace skipped as idling
[ 1141.529435] [    C0] sysrq: CPU0: backtrace skipped as idling
[ 1141.529484] [    C4] sysrq: CPU4: backtrace skipped as idling
[ 1141.529497] [    C1] sysrq: CPU1: backtrace skipped as idling
[ 1141.529503] [    C6] sysrq: CPU6: backtrace skipped as idling
[ 1141.529510] [    C7] sysrq: CPU7: backtrace skipped as idling
[ 1141.529518] [    C5] sysrq: CPU5: backtrace skipped as idling
[ 1141.545524] [    T1] init: starting service 'simple_bugreportd'...
[ 1141.546191] [    T1] init: Created socket '/dev/socket/dumpstate', mode 660, user 2000, group 1007
[ 1141.551678] [    T1] init: ... started service 'simple_bugreportd' has pid 4152
[ 1141.551747] [    T1] init: Control message: Processed ctl.start for 'simple_bugreportd' from pid: 763 (system_server)
[ 1141.800846] [ T3275] binder_alloc: 763: binder_alloc_buf, no vma
[ 1141.800883] [ T3275] binder: cannot allocate buffer: vma cleared, target dead or dying
[ 1141.800890] [ T3275] binder: 3149:3275 transaction call to 763:0 failed 1545422/29189/-3, size 88-0 line 3465
[ 1141.801364] [ T3275] binder_alloc: 763: binder_alloc_buf, no vma
[ 1141.801379] [ T3275] binder: cannot allocate buffer: vma cleared, target dead or dying
[ 1141.801385] [ T3275] binder: 3149:3275 transaction async to 763:0 failed 1545423/29189/-3, size 92-0 line 3465
[ 1141.806641] [ T3893] binder: release 763:776 transaction 1431777 in, still active
[ 1141.806689] [ T3893] binder: send failed reply for transaction 1431777 to 1746:1746
[ 1141.806820] [ T3893] binder: release 763:826 transaction 1431797 in, still active
[ 1141.806835] [ T3893] binder: send failed reply for transaction 1431797 to 1260:1332
[ 1141.807007] [ T3893] binder: release 763:1233 transaction 1431731 in, still active
[ 1141.807025] [ T3893] binder: send failed reply for transaction 1431731 to 3633:3633
[ 1141.807059] [ T1746] binder_alloc: 763: binder_alloc_buf, no vma
[ 1141.807396] [ T3633] binder_alloc: 763: binder_alloc_buf, no vma
[ 1141.807420] [ T1284] binder_alloc: 763: binder_alloc_buf, no vma
[ 1141.817438] [ T1544] [dhd-sdio-1] [wlan1] wl_cfg80211_del_station : Disconnect STA : ff:ff:ff:ff:ff:ff scb_val.val 3
[ 1141.819858] [ T1544] [dhd-sdio-1] [wlan1] wl_cfg80211_stop_ap : stopping AP operation
[ 1141.822028] [ T3892] [dhd-sdio-1] [wlan1] wl_iw_event : Link Down with 9c:b8:b4:8e:35:e4, reason=4
[ 1141.822074] [ T3892] [dhd-sdio-1] [wlan1] wl_ext_iapsta_link : Link down, reason=4
[ 1141.823417] [  T347] [dhd-sdio-1] _dhd_wlfc_mac_entry_update():2047, entry(32)
[ 1141.824091] [ T1242] [dhd-sdio-1] CFG80211-ERROR) wl_cfg80211_netdev_notifier_call : wrong cfg ptr (000000006385d3e5)
[ 1141.824216] [ T1242] [dhd-sdio-1] CFG80211-ERROR) wl_cfg80211_netdev_notifier_call : wrong cfg ptr (000000006385d3e5)
[ 1141.832369] [ T1544] [dhd-sdio-1] dhd_wlfc_deinit():4093, maintain HOST RXRERODER flag in tvl
[ 1141.832869] [    T1] init: Service 'wpa_supplicant' (pid 1242) exited with status 0 oneshot service took 1080.102051 seconds in background
[ 1141.832903] [    T1] init: Sending signal 9 to service 'wpa_supplicant' (pid 1242) process group...
[ 1141.833110] [    T1] libprocessgroup: Successfully killed process cgroup uid 0 pid 1242 in 0ms
[ 1141.834403] [ T1544] [dhd-sdio-1] [wlan1] wl_cfg80211_add_del_bss : wl bss 2 bssidx:0
[ 1141.835779] [    T1] init: starting service 'bootanim'...
[ 1141.840067] [ T4161] libprocessgroup: SetCgroup::ExecuteForProcess: failed to open /dev/stune/top-app/cgroup.procs: No such file or directory
[ 1141.840119] [ T4161] libprocessgroup: Failed to apply MaxPerformance process profile
[ 1141.840137] [ T4161] init: failed to set task profiles
[ 1141.840377] [    T1] init: ... started service 'bootanim' has pid 4161
[ 1141.840453] [    T1] init: Control message: Processed ctl.start for 'bootanim' from pid: 551 (/system/bin/surfaceflinger)
[ 1141.843574] [    T1] init: Service 'hostapd' (pid 1544) exited with status 0 oneshot service took 1076.767944 seconds in background
[ 1141.843615] [    T1] init: Sending signal 9 to service 'hostapd' (pid 1544) process group...
[ 1141.843836] [    T1] libprocessgroup: Successfully killed process cgroup uid 1010 pid 1544 in 0ms
[ 1141.871715] [    T1] init: Service 'zygote' (pid 467) received signal 9
[ 1141.871777] [    T1] init: Sending signal 9 to service 'zygote' (pid 467) process group...
[ 1141.924951] [    T1] libprocessgroup: Successfully killed process cgroup uid 0 pid 467 in 53ms
[ 1142.059222] [    T1] init: starting service 'exec 18 (/system/bin/vdc volume abort_fuse)'...
[ 1142.076955] [    T1] init: ... started service 'exec 18 (/system/bin/vdc volume abort_fuse)' has pid 4176
[ 1142.077608] [    T1] init: Command 'write /sys/power/state on' action=onrestart (<Service 'zygote' onrestart>:2) took 0ms and failed: Unable to write to file '/sys/power/state': Unable to write file contents: Invalid argument
[ 1142.093929] [    T1] init: Sending signal 9 to service 'audioserver' (pid 539) process group...
[ 1142.097275] [  T478] [BT_RFKILL]: bt shut off power
[ 1142.100328] [    T1] libprocessgroup: Successfully killed process cgroup uid 1041 pid 539 in 5ms
[ 1142.103656] [  T287] type=1400 audit(**********.040:1447): avc:  denied  { ioctl } for  comm="m.ecloud.emedia" path="socket:[153130]" dev="sockfs" ino=153130 ioctlcmd=0x8927 scontext=u:r:platform_app:s0:c512,c768 tcontext=u:r:platform_app:s0:c512,c768 tclass=udp_socket permissive=1 app=com.ecloud.emedia
[ 1142.110122] [   T80] enter into es8156_set_bias_level, level = 2
[ 1142.112075] [   T84] enter into es8156_set_bias_level, level = 1
[ 1142.114645] [   T84] enter into es8156_set_bias_level, level = 0
[ 1142.127435] [    T1] init: Sending signal 9 to service 'cameraserver' (pid 594) process group...
[ 1142.133051] [    T1] libprocessgroup: Successfully killed process cgroup uid 1047 pid 594 in 5ms
[ 1142.162324] [    T1] init: Sending signal 9 to service 'media' (pid 621) process group...
[ 1142.168958] [    T1] libprocessgroup: Successfully killed process cgroup uid 1013 pid 621 in 6ms
[ 1142.200958] [    T1] init: Command 'restart media.tuner' action=onrestart (<Service 'zygote' onrestart>:7) took 0ms and failed: service media.tuner not found
[ 1142.201095] [    T1] init: Sending signal 9 to service 'netd' (pid 466) process group...
[ 1142.206970] [    T1] libprocessgroup: Successfully killed process cgroup uid 0 pid 466 in 5ms
[ 1142.213064] [  T284] logd: logdr: UID=0 GID=0 PID=4183 n tail=0 logMask=99 pid=0 start=0ns deadline=0ns
[ 1142.213560] [    T1] init: Sending signal 9 to service 'wificond' (pid 635) process group...
[ 1142.219180] [    T1] libprocessgroup: Successfully killed process cgroup uid 1010 pid 635 in 5ms
[ 1142.219925] [    T1] init: Command 'restart vendor.input-default' action=onrestart (<Service 'zygote' onrestart>:10) took 0ms and failed: service vendor.input-default not found
[ 1142.221417] [    T1] init: Service 'netd' (pid 466) received signal 9
[ 1142.221641] [    T1] init: Sending signal 9 to service 'zygote_secondary' (pid 468) process group...
[ 1142.243680] [    T1] libprocessgroup: Successfully killed process cgroup uid 0 pid 468 in 21ms
[ 1142.245015] [    T1] init: Service 'zygote_secondary' (pid 468) received signal 9
[ 1142.246197] [    T1] init: Service 'audioserver' (pid 539) received signal 9
[ 1142.246254] [    T1] init: Sending signal 9 to service 'vendor.audio-hal' (pid 477) process group...
[ 1142.261240] [  T287] type=1400 audit(**********.196:1448): avc:  denied  { open } for  comm="BootAnimation" path="/dev/__properties__/u:object_r:vendor_default_prop:s0" dev="tmpfs" ino=333 scontext=u:r:bootanim:s0 tcontext=u:object_r:vendor_default_prop:s0 tclass=file permissive=1
[ 1142.261399] [  T287] type=1400 audit(**********.196:1449): avc:  denied  { getattr } for  comm="BootAnimation" path="/dev/__properties__/u:object_r:vendor_default_prop:s0" dev="tmpfs" ino=333 scontext=u:r:bootanim:s0 tcontext=u:object_r:vendor_default_prop:s0 tclass=file permissive=1
[ 1142.261459] [  T287] type=1400 audit(**********.196:1450): avc:  denied  { map } for  comm="BootAnimation" path="/dev/__properties__/u:object_r:vendor_default_prop:s0" dev="tmpfs" ino=333 scontext=u:r:bootanim:s0 tcontext=u:object_r:vendor_default_prop:s0 tclass=file permissive=1
[ 1142.261516] [  T287] type=1400 audit(**********.196:1451): avc:  denied  { search } for  comm="BootAnimation" name="data" dev="mmcblk0p15" ino=8185 scontext=u:r:bootanim:s0 tcontext=u:object_r:system_data_file:s0:c512,c768 tclass=dir permissive=1
[ 1142.307948] [  T276] servicemanager: Found android.hardware.graphics.allocator.IAllocator/default in device VINTF manifest.
[ 1142.314200] [  T276] servicemanager: Found android.hardware.graphics.allocator.IAllocator/default in device VINTF manifest.
[ 1142.455273] [    T1] libprocessgroup: Failed to kill process cgroup uid 1041 pid 477 in 208ms, 1 processes remain
[ 1142.455591] [    T1] init: Command 'restart vendor.audio-hal' action=onrestart (<Service 'audioserver' onrestart>:1) took 209ms and succeeded
[ 1142.455630] [    T1] init: Command 'restart vendor.audio-hal-aidl' action=onrestart (<Service 'audioserver' onrestart>:2) took 0ms and failed: service vendor.audio-hal-aidl not found
[ 1142.455658] [    T1] init: Command 'restart vendor.audio-effect-hal-aidl' action=onrestart (<Service 'audioserver' onrestart>:3) took 0ms and failed: service vendor.audio-effect-hal-aidl not found
[ 1142.455681] [    T1] init: Command 'restart vendor.audio-hal-4-0-msd' action=onrestart (<Service 'audioserver' onrestart>:4) took 0ms and failed: service vendor.audio-hal-4-0-msd not found
[ 1142.455701] [    T1] init: Command 'restart audio_proxy_service' action=onrestart (<Service 'audioserver' onrestart>:5) took 0ms and failed: service audio_proxy_service not found
[ 1142.456300] [    T1] init: Service 'cameraserver' (pid 594) received signal 9
[ 1142.457186] [    T1] init: Service 'media' (pid 621) received signal 9
[ 1142.457239] [    T1] init: Sending signal 9 to service 'vendor.rockit-hal-1-0' (pid 515) process group...
[ 1142.462722] [    T1] libprocessgroup: Successfully killed process cgroup uid 1013 pid 515 in 5ms
[ 1142.464434] [    T1] init: Service 'vendor.rockit-hal-1-0' (pid 515) received signal 9
[ 1142.466848] [    T1] init: Service 'wificond' (pid 635) received signal 9
[ 1142.467500] [    T1] init: Untracked pid 1260 received signal 9
[ 1142.467540] [    T1] init: Untracked pid 1260 did not have an associated service entry and will not be reaped
[ 1142.467738] [    T1] init: Untracked pid 1290 received signal 9
[ 1142.467756] [    T1] init: Untracked pid 1290 did not have an associated service entry and will not be reaped
[ 1142.467909] [    T1] init: Untracked pid 1416 received signal 9
[ 1142.467927] [    T1] init: Untracked pid 1416 did not have an associated service entry and will not be reaped
[ 1142.468087] [    T1] init: Untracked pid 1462 received signal 9
[ 1142.468104] [    T1] init: Untracked pid 1462 did not have an associated service entry and will not be reaped
[ 1142.468246] [    T1] init: Untracked pid 1480 received signal 9
[ 1142.468263] [    T1] init: Untracked pid 1480 did not have an associated service entry and will not be reaped
[ 1142.468427] [    T1] init: Untracked pid 1485 received signal 9
[ 1142.468444] [    T1] init: Untracked pid 1485 did not have an associated service entry and will not be reaped
[ 1142.468584] [    T1] init: Untracked pid 1676 received signal 9
[ 1142.468601] [    T1] init: Untracked pid 1676 did not have an associated service entry and will not be reaped
[ 1142.468749] [    T1] init: Untracked pid 1745 received signal 9
[ 1142.468766] [    T1] init: Untracked pid 1745 did not have an associated service entry and will not be reaped
[ 1142.469083] [    T1] init: Untracked pid 1746 received signal 9
[ 1142.469102] [    T1] init: Untracked pid 1746 did not have an associated service entry and will not be reaped
[ 1142.469280] [    T1] init: Untracked pid 1747 received signal 9
[ 1142.469298] [    T1] init: Untracked pid 1747 did not have an associated service entry and will not be reaped
[ 1142.469440] [    T1] init: Untracked pid 1759 received signal 9
[ 1142.469457] [    T1] init: Untracked pid 1759 did not have an associated service entry and will not be reaped
[ 1142.469641] [    T1] init: Untracked pid 1765 received signal 9
[ 1142.469659] [    T1] init: Untracked pid 1765 did not have an associated service entry and will not be reaped
[ 1142.469834] [    T1] init: Untracked pid 1818 received signal 9
[ 1142.469852] [    T1] init: Untracked pid 1818 did not have an associated service entry and will not be reaped
[ 1142.470013] [    T1] init: Untracked pid 1840 received signal 9
[ 1142.470030] [    T1] init: Untracked pid 1840 did not have an associated service entry and will not be reaped
[ 1142.470193] [    T1] init: Untracked pid 1937 received signal 9
[ 1142.470211] [    T1] init: Untracked pid 1937 did not have an associated service entry and will not be reaped
[ 1142.470373] [    T1] init: Untracked pid 1980 received signal 9
[ 1142.470390] [    T1] init: Untracked pid 1980 did not have an associated service entry and will not be reaped
[ 1142.470578] [    T1] init: Untracked pid 1981 received signal 9
[ 1142.470596] [    T1] init: Untracked pid 1981 did not have an associated service entry and will not be reaped
[ 1142.470745] [    T1] init: Untracked pid 2058 received signal 9
[ 1142.470761] [    T1] init: Untracked pid 2058 did not have an associated service entry and will not be reaped
[ 1142.470914] [    T1] init: Untracked pid 2059 received signal 9
[ 1142.470931] [    T1] init: Untracked pid 2059 did not have an associated service entry and will not be reaped
[ 1142.471074] [    T1] init: Untracked pid 2166 received signal 9
[ 1142.471091] [    T1] init: Untracked pid 2166 did not have an associated service entry and will not be reaped
[ 1142.471237] [    T1] init: Untracked pid 2280 received signal 9
[ 1142.471254] [    T1] init: Untracked pid 2280 did not have an associated service entry and will not be reaped
[ 1142.471393] [    T1] init: Untracked pid 2346 received signal 9
[ 1142.471411] [    T1] init: Untracked pid 2346 did not have an associated service entry and will not be reaped
[ 1142.471582] [    T1] init: Untracked pid 2361 received signal 9
[ 1142.471600] [    T1] init: Untracked pid 2361 did not have an associated service entry and will not be reaped
[ 1142.471736] [    T1] init: Untracked pid 2393 received signal 9
[ 1142.471754] [    T1] init: Untracked pid 2393 did not have an associated service entry and will not be reaped
[ 1142.471901] [    T1] init: Untracked pid 2414 received signal 9
[ 1142.471919] [    T1] init: Untracked pid 2414 did not have an associated service entry and will not be reaped
[ 1142.472060] [    T1] init: Untracked pid 2431 received signal 9
[ 1142.472078] [    T1] init: Untracked pid 2431 did not have an associated service entry and will not be reaped
[ 1142.472214] [    T1] init: Untracked pid 2460 received signal 9
[ 1142.472232] [    T1] init: Untracked pid 2460 did not have an associated service entry and will not be reaped
[ 1142.472375] [    T1] init: Untracked pid 2490 received signal 9
[ 1142.472393] [    T1] init: Untracked pid 2490 did not have an associated service entry and will not be reaped
[ 1142.472545] [    T1] init: Untracked pid 2516 received signal 9
[ 1142.472562] [    T1] init: Untracked pid 2516 did not have an associated service entry and will not be reaped
[ 1142.473877] [    T1] init: Untracked pid 2534 received signal 9
[ 1142.473920] [    T1] init: Untracked pid 2534 did not have an associated service entry and will not be reaped
[ 1142.474178] [    T1] init: Untracked pid 2677 received signal 9
[ 1142.474197] [    T1] init: Untracked pid 2677 did not have an associated service entry and will not be reaped
[ 1142.474348] [    T1] init: Untracked pid 2713 received signal 9
[ 1142.474366] [    T1] init: Untracked pid 2713 did not have an associated service entry and will not be reaped
[ 1142.474569] [    T1] init: Untracked pid 2808 received signal 9
[ 1142.474587] [    T1] init: Untracked pid 2808 did not have an associated service entry and will not be reaped
[ 1142.474777] [    T1] init: Untracked pid 2851 received signal 9
[ 1142.474797] [    T1] init: Untracked pid 2851 did not have an associated service entry and will not be reaped
[ 1142.474976] [    T1] init: Untracked pid 2880 received signal 9
[ 1142.474995] [    T1] init: Untracked pid 2880 did not have an associated service entry and will not be reaped
[ 1142.475147] [    T1] init: Untracked pid 3206 received signal 9
[ 1142.475165] [    T1] init: Untracked pid 3206 did not have an associated service entry and will not be reaped
[ 1142.475331] [    T1] init: Untracked pid 3311 received signal 9
[ 1142.475349] [    T1] init: Untracked pid 3311 did not have an associated service entry and will not be reaped
[ 1142.475475] [    T1] init: Untracked pid 3332 received signal 9
[ 1142.475492] [    T1] init: Untracked pid 3332 did not have an associated service entry and will not be reaped
[ 1142.475618] [    T1] init: Untracked pid 3337 received signal 9
[ 1142.475636] [    T1] init: Untracked pid 3337 did not have an associated service entry and will not be reaped
[ 1142.475754] [    T1] init: Untracked pid 3359 received signal 9
[ 1142.475772] [    T1] init: Untracked pid 3359 did not have an associated service entry and will not be reaped
[ 1142.475904] [    T1] init: Untracked pid 3428 received signal 9
[ 1142.475923] [    T1] init: Untracked pid 3428 did not have an associated service entry and will not be reaped
[ 1142.476047] [    T1] init: Untracked pid 3496 received signal 9
[ 1142.476066] [    T1] init: Untracked pid 3496 did not have an associated service entry and will not be reaped
[ 1142.476189] [    T1] init: Untracked pid 3501 received signal 9
[ 1142.476207] [    T1] init: Untracked pid 3501 did not have an associated service entry and will not be reaped
[ 1142.476339] [    T1] init: Untracked pid 3528 received signal 9
[ 1142.476358] [    T1] init: Untracked pid 3528 did not have an associated service entry and will not be reaped
[ 1142.476544] [    T1] init: Untracked pid 3633 received signal 9
[ 1142.476564] [    T1] init: Untracked pid 3633 did not have an associated service entry and will not be reaped
[ 1142.476782] [    T1] init: Service 'exec 18 (/system/bin/vdc volume abort_fuse)' (pid 4176) exited with status 0 oneshot service took 0.415000 seconds in background
[ 1142.476811] [    T1] init: Sending signal 9 to service 'exec 18 (/system/bin/vdc volume abort_fuse)' (pid 4176) process group...
[ 1142.477045] [    T1] libprocessgroup: Successfully killed process cgroup uid 1000 pid 4176 in 0ms
[ 1142.477568] [    T1] init: Untracked pid 473 received signal 9
[ 1142.477612] [    T1] init: Untracked pid 473 did not have an associated service entry and will not be reaped
[ 1142.477828] [    T1] init: Untracked pid 474 received signal 9
[ 1142.477851] [    T1] init: Untracked pid 474 did not have an associated service entry and will not be reaped
[ 1142.478040] [    T1] init: Untracked pid 1654 received signal 9
[ 1142.478060] [    T1] init: Untracked pid 1654 did not have an associated service entry and will not be reaped
[ 1142.478258] [    T1] init: Untracked pid 3048 received signal 9
[ 1142.478280] [    T1] init: Untracked pid 3048 did not have an associated service entry and will not be reaped
[ 1142.478455] [    T1] init: Untracked pid 3149 received signal 9
[ 1142.478474] [    T1] init: Untracked pid 3149 did not have an associated service entry and will not be reaped
[ 1142.478648] [    T1] init: Untracked pid 3204 received signal 9
[ 1142.478670] [    T1] init: Untracked pid 3204 did not have an associated service entry and will not be reaped
[ 1142.479069] [    T1] init: processing action (init.svc.media=*) from (/system/etc/init/mediaserver.rc:1)
[ 1142.479779] [    T1] init: starting service 'zygote_secondary'...
[ 1142.480338] [    T1] init: Created socket '/dev/socket/zygote_secondary', mode 660, user 0, group 1000
[ 1142.480756] [    T1] init: Created socket '/dev/socket/usap_pool_secondary', mode 660, user 0, group 1000
[ 1142.485267] [ T4205] libprocessgroup: SetCgroup::ExecuteForProcess: failed to open /dev/stune/top-app/cgroup.procs: No such file or directory
[ 1142.485266] [    T1] init: ... started service 'zygote_secondary' has pid 4205
[ 1142.485297] [ T4205] libprocessgroup: Failed to apply MaxPerformance process profile
[ 1142.485308] [ T4205] init: failed to set task profiles
[ 1142.485580] [    T1] init: starting service 'zygote'...
[ 1142.486095] [    T1] init: Created socket '/dev/socket/zygote', mode 660, user 0, group 1000
[ 1142.486471] [    T1] init: Created socket '/dev/socket/usap_pool_primary', mode 660, user 0, group 1000
[ 1142.490954] [ T4206] libprocessgroup: SetCgroup::ExecuteForProcess: failed to open /dev/stune/top-app/cgroup.procs: No such file or directory
[ 1142.490988] [ T4206] libprocessgroup: Failed to apply MaxPerformance process profile
[ 1142.491002] [ T4206] init: failed to set task profiles
[ 1142.491074] [    T1] init: ... started service 'zygote' has pid 4206
[ 1142.497867] [    T1] init: starting service 'audioserver'...
[ 1142.502033] [ T4207] libprocessgroup: SetCgroup::ExecuteForProcess: failed to open /dev/stune/foreground/cgroup.procs: No such file or directory
[ 1142.502064] [ T4207] libprocessgroup: Failed to apply HighPerformance process profile
[ 1142.502079] [ T4207] init: failed to set task profiles
[ 1142.502205] [    T1] init: ... started service 'audioserver' has pid 4207
[ 1142.502516] [    T1] init: starting service 'cameraserver'...
[ 1142.507483] [    T1] init: ... started service 'cameraserver' has pid 4208
[ 1142.507766] [ T4208] libprocessgroup: SetCgroup::ExecuteForProcess: failed to open /dev/stune/top-app/cgroup.procs: No such file or directory
[ 1142.507801] [ T4208] libprocessgroup: Failed to apply MaxPerformance process profile
[ 1142.507817] [ T4208] init: failed to set task profiles
[ 1142.507940] [    T1] init: starting service 'media'...
[ 1142.513960] [    T1] init: ... started service 'media' has pid 4209
[ 1142.514381] [    T1] init: starting service 'netd'...
[ 1142.515107] [    T1] init: Created socket '/dev/socket/dnsproxyd', mode 660, user 0, group 3003
[ 1142.515568] [ T4209] libprocessgroup: SetCgroup::ExecuteForProcess: failed to open /dev/stune/foreground/cgroup.procs: No such file or directory
[ 1142.515611] [ T4209] libprocessgroup: Failed to apply HighPerformance process profile
[ 1142.515629] [ T4209] init: failed to set task profiles
[ 1142.515653] [    T1] init: Created socket '/dev/socket/mdns', mode 660, user 0, group 1000
[ 1142.516133] [    T1] init: Created socket '/dev/socket/fwmarkd', mode 660, user 0, group 3003
[ 1142.521261] [    T1] init: ... started service 'netd' has pid 4210
[ 1142.521667] [    T1] init: starting service 'wificond'...
[ 1142.530381] [    T1] init: ... started service 'wificond' has pid 4211
[ 1142.530981] [    T1] init: starting service 'vendor.rockit-hal-1-0'...
[ 1142.541958] [    T1] init: ... started service 'vendor.rockit-hal-1-0' has pid 4212
[ 1142.542879] [    T1] init: processing action (init.svc.media=*) from (/system/etc/init/mediaserver.rc:1)
[ 1142.545388] [    T1] init: processing action (init.svc.audioserver=running) from (/system/etc/init/audioserver.rc:35)
[ 1142.545514] [    T1] init: service 'vendor.audio-hal' requested start, but it is already running (flags: 4)
[ 1142.545657] [    T1] init: Command 'start vendor.audio-hal-aidl' action=init.svc.audioserver=running (/system/etc/init/audioserver.rc:37) took 0ms and failed: service vendor.audio-hal-aidl not found
[ 1142.545779] [    T1] init: Command 'start vendor.audio-effect-hal-aidl' action=init.svc.audioserver=running (/system/etc/init/audioserver.rc:38) took 0ms and failed: service vendor.audio-effect-hal-aidl not found
[ 1142.545842] [    T1] init: Command 'start vendor.audio-hal-4-0-msd' action=init.svc.audioserver=running (/system/etc/init/audioserver.rc:39) took 0ms and failed: service vendor.audio-hal-4-0-msd not found
[ 1142.545889] [    T1] init: Command 'start audio_proxy_service' action=init.svc.audioserver=running (/system/etc/init/audioserver.rc:40) took 0ms and failed: service audio_proxy_service not found
[ 1142.546183] [    T1] init: processing action (init.svc.media=*) from (/system/etc/init/mediaserver.rc:1)
[ 1142.605639] [  T276] servicemanager: Could not find android.hardware.net.nlinterceptor.IInterceptor/default in the VINTF manifest.
[ 1142.649386] [  T567] ad82129_shutdown(257), -----------
[ 1142.655651] [    T1] init: Service 'vendor.audio-hal' (pid 477) received signal 9
[ 1142.655712] [    T1] init: Sending signal 9 to service 'vendor.audio-hal' (pid 477) process group...
[ 1142.656018] [    T1] libprocessgroup: Successfully killed process cgroup uid 1041 pid 477 in 0ms
[ 1142.657920] [    T1] init: Sending signal 9 to service 'audioserver' (pid 4207) process group...
[ 1142.663549] [    T1] libprocessgroup: Successfully killed process cgroup uid 1041 pid 4207 in 5ms
[ 1142.670292] [    T1] init: Service 'audioserver' (pid 4207) received signal 9
[ 1142.670465] [    T1] init: Command 'restart vendor.audio-hal-aidl' action=onrestart (<Service 'audioserver' onrestart>:2) took 0ms and failed: service vendor.audio-hal-aidl not found
[ 1142.670513] [    T1] init: Command 'restart vendor.audio-effect-hal-aidl' action=onrestart (<Service 'audioserver' onrestart>:3) took 0ms and failed: service vendor.audio-effect-hal-aidl not found
[ 1142.670548] [    T1] init: Command 'restart vendor.audio-hal-4-0-msd' action=onrestart (<Service 'audioserver' onrestart>:4) took 0ms and failed: service vendor.audio-hal-4-0-msd not found
[ 1142.670580] [    T1] init: Command 'restart audio_proxy_service' action=onrestart (<Service 'audioserver' onrestart>:5) took 0ms and failed: service audio_proxy_service not found
[ 1142.672131] [    T1] init: Control message: Could not find 'android.hardware.audio@7.1::IDevicesFactory/default' for ctl.interface_start from pid: 277 (/system/bin/hwservicemanager)
[ 1142.672914] [    T1] init: starting service 'vendor.audio-hal'...
[ 1142.678771] [    T1] init: ... started service 'vendor.audio-hal' has pid 4227
[ 1142.679564] [  T284] logd: logdr: UID=0 GID=0 PID=4223 n tail=0 logMask=4 pid=0 start=0ns deadline=0ns
[ 1142.680375] [ T4227] libprocessgroup: SetCgroup::ExecuteForProcess: failed to open /dev/stune/foreground/cgroup.procs: No such file or directory
[ 1142.680442] [ T4227] libprocessgroup: Failed to apply HighPerformance process profile
[ 1142.680463] [ T4227] init: failed to set task profiles
[ 1142.696792] [  T205] selinux: SELinux: Could not get canonical path for /sys/devices/virtual/net/ipsec_test/queues/tx-0 restorecon: No such file or directory.
[ 1142.696850] [  T205] ueventd: selinux_android_restorecon(/sys/devices/virtual/net/ipsec_test/queues/tx-0) failed: No such file or directory
[ 1142.746153] [  T276] servicemanager: Found android.system.net.netd.INetd/default in framework VINTF manifest.
[ 1142.753349] [  T276] servicemanager: Found android.frameworks.cameraservice.service.ICameraService/default in framework VINTF manifest.
[ 1142.753993] [  T276] servicemanager: Found android.frameworks.cameraservice.service.ICameraService/default in framework VINTF manifest.
[ 1142.760059] [  T276] servicemanager: Found android.hardware.bluetooth.audio.IBluetoothAudioProviderFactory/default in device VINTF manifest.
[ 1142.877141] [ T4205] audit: audit_lost=320 audit_rate_limit=5 audit_backlog_limit=64
[ 1142.877163] [ T4205] audit: rate limit exceeded
[ 1142.877938] [  T287] type=1400 audit(**********.816:1452): avc:  denied  { read } for  comm="main" name="u:object_r:vendor_default_prop:s0" dev="tmpfs" ino=333 scontext=u:r:zygote:s0 tcontext=u:object_r:vendor_default_prop:s0 tclass=file permissive=1
[ 1144.124236] [  T287] type=1400 audit(**********.060:1460): avc:  denied  { dac_read_search } for  comm="system_server" capability=2  scontext=u:r:system_server:s0 tcontext=u:r:system_server:s0 tclass=capability permissive=1 bug=b/228030183
[ 1144.378372] [  T276] servicemanager: Since 'apexservice' could not be found, trying to start it as a lazy AIDL service. (if it's not configured to be a lazy service, it may be stuck starting or still starting).
[ 1144.379570] [    T1] init: starting service 'apexd'...
[ 1144.383040] [    T1] init: ... started service 'apexd' has pid 4301
[ 1144.383120] [    T1] init: Control message: Processed ctl.interface_start for 'aidl/apexservice' from pid: 276 (/system/bin/servicemanager)
[ 1144.404548] [ T4301] apexd: Scanning /system/apex for pre-installed ApexFiles
[ 1144.404819] [ T4301] apexd: Found pre-installed APEX /system/apex/com.android.adbd.capex
[ 1144.405216] [ T4301] apexd: Found pre-installed APEX /system/apex/com.android.adservices.capex
[ 1144.405512] [ T4301] apexd: Found pre-installed APEX /system/apex/com.android.sdkext.apex
[ 1144.405892] [ T4301] apexd: Found pre-installed APEX /system/apex/com.android.appsearch.capex
[ 1144.406150] [ T4301] apexd: Found pre-installed APEX /system/apex/com.android.art.capex
[ 1144.406410] [ T4301] apexd: Found pre-installed APEX /system/apex/com.android.resolv.capex
[ 1144.406677] [ T4301] apexd: Found pre-installed APEX /system/apex/com.android.apex.cts.shim.apex
[ 1144.406943] [ T4301] apexd: Found pre-installed APEX /system/apex/com.android.mediaprovider.capex
[ 1144.407183] [ T4301] apexd: Found pre-installed APEX /system/apex/com.android.btservices.apex
[ 1144.407480] [ T4301] apexd: Found pre-installed APEX /system/apex/com.android.ipsec.capex
[ 1144.407712] [ T4301] apexd: Found pre-installed APEX /system/apex/com.android.rkpd.apex
[ 1144.407977] [ T4301] apexd: Found pre-installed APEX /system/apex/com.android.configinfrastructure.capex
[ 1144.408273] [ T4301] apexd: Found pre-installed APEX /system/apex/com.android.virt.apex
[ 1144.408587] [ T4301] apexd: Found pre-installed APEX /system/apex/com.android.conscrypt.capex
[ 1144.408873] [ T4301] apexd: Found pre-installed APEX /system/apex/com.android.scheduling.capex
[ 1144.409340] [ T4301] apexd: Found pre-installed APEX /system/apex/com.android.healthfitness.apex
[ 1144.409647] [ T4301] apexd: Found pre-installed APEX /system/apex/com.android.tethering.capex
[ 1144.409950] [ T4301] apexd: Found pre-installed APEX /system/apex/com.android.extservices.capex
[ 1144.410234] [ T4301] apexd: Found pre-installed APEX /system/apex/com.android.tzdata.apex
[ 1144.410501] [ T4301] apexd: Found pre-installed APEX /system/apex/com.android.media.swcodec.capex
[ 1144.410758] [ T4301] apexd: Found pre-installed APEX /system/apex/com.android.media.capex
[ 1144.411005] [ T4301] apexd: Found pre-installed APEX /system/apex/com.android.i18n.apex
[ 1144.411319] [ T4301] apexd: Found pre-installed APEX /system/apex/com.android.runtime.apex
[ 1144.411586] [ T4301] apexd: Found pre-installed APEX /system/apex/com.android.ondevicepersonalization.capex
[ 1144.411826] [ T4301] apexd: Found pre-installed APEX /system/apex/com.android.uwb.capex
[ 1144.412103] [ T4301] apexd: Found pre-installed APEX /system/apex/com.android.devicelock.apex
[ 1144.412396] [ T4301] apexd: Found pre-installed APEX /system/apex/com.android.vndk.current.apex
[ 1144.412762] [ T4301] apexd: Found pre-installed APEX /system/apex/com.android.permission.capex
[ 1144.413537] [ T4301] apexd: Found pre-installed APEX /system/apex/com.android.os.statsd.apex
[ 1144.413903] [ T4301] apexd: Found pre-installed APEX /system/apex/com.android.neuralnetworks.capex
[ 1144.414257] [ T4301] apexd: Found pre-installed APEX /system/apex/com.android.wifi.capex
[ 1144.414538] [ T4301] apexd: Scanning /system_ext/apex for pre-installed ApexFiles
[ 1144.414568] [ T4301] apexd: /system_ext/apex does not exist. Skipping
[ 1144.414580] [ T4301] apexd: Scanning /product/apex for pre-installed ApexFiles
[ 1144.414597] [ T4301] apexd: /product/apex does not exist. Skipping
[ 1144.414610] [ T4301] apexd: Scanning /vendor/apex for pre-installed ApexFiles
[ 1144.414699] [ T4301] apexd: Found pre-installed APEX /vendor/apex/com.rockchip.hardware.sensors.apex
[ 1144.415071] [ T4301] apexd: No block apex metadata partition found, not adding block apexes
[ 1144.415094] [ T4301] apexd: Populating APEX database from mounts...
[ 1144.415766] [ T4301] apexd: Found "/apex/com.android.vndk.v34@1" backed by file /system/apex/com.android.vndk.current.apex
[ 1144.415909] [ T4301] apexd: Found "/apex/com.android.btservices@340090000" backed by file /system/apex/com.android.btservices.apex
[ 1144.416192] [ T4301] apexd: Found "/apex/com.android.devicelock@1" backed by file /system/apex/com.android.devicelock.apex
[ 1144.416566] [ T4301] apexd: Found "/apex/com.android.extservices@340090000" backed by file /data/apex/decompressed/<EMAIL>
[ 1144.416708] [ T4301] apexd: Found "/apex/com.android.tzdata@340090000" backed by file /system/apex/com.android.tzdata.apex
[ 1144.416836] [ T4301] apexd: Found "/apex/com.android.sdkext@340090000" backed by file /system/apex/com.android.sdkext.apex
[ 1144.416989] [ T4301] apexd: Found "/apex/com.android.rkpd@1" backed by file /system/apex/com.android.rkpd.apex
[ 1144.417135] [ T4301] apexd: Found "/apex/com.android.runtime@1" backed by file /system/apex/com.android.runtime.apex
[ 1144.417256] [ T4301] apexd: Found "/apex/com.android.apex.cts.shim@1" backed by file /system/apex/com.android.apex.cts.shim.apex
[ 1144.417354] [ T4301] apexd: Found "/apex/com.android.virt@2" backed by file /system/apex/com.android.virt.apex
[ 1144.417583] [ T4301] apexd: Found "/apex/com.android.i18n@1" backed by file /system/apex/com.android.i18n.apex
[ 1144.417750] [ T4301] apexd: Found "/apex/com.rockchip.hardware.sensors@1" backed by file /vendor/apex/com.rockchip.hardware.sensors.apex
[ 1144.417892] [ T4301] apexd: Found "/apex/com.android.healthfitness@340090000" backed by file /system/apex/com.android.healthfitness.apex
[ 1144.418249] [ T4301] apexd: Found "/apex/com.android.tethering@340090000" backed by file /data/apex/decompressed/<EMAIL>
[ 1144.418552] [ T4301] apexd: Found "/apex/com.android.wifi@340090000" backed by file /data/apex/decompressed/<EMAIL>
[ 1144.418805] [ T4301] apexd: Found "/apex/com.android.media.swcodec@340090000" backed by file /data/apex/decompressed/<EMAIL>
[ 1144.419067] [ T4301] apexd: Found "/apex/com.android.adbd@340090000" backed by file /data/apex/decompressed/<EMAIL>
[ 1144.419367] [ T4301] apexd: Found "/apex/com.android.ipsec@340090000" backed by file /data/apex/decompressed/<EMAIL>
[ 1144.419605] [ T4301] apexd: Found "/apex/com.android.appsearch@340090000" backed by file /data/apex/decompressed/<EMAIL>
[ 1144.419857] [ T4301] apexd: Found "/apex/com.android.adservices@340090000" backed by file /data/apex/decompressed/<EMAIL>
[ 1144.419978] [ T4301] apexd: Found "/apex/com.android.os.statsd@340090000" backed by file /system/apex/com.android.os.statsd.apex
[ 1144.420210] [ T4301] apexd: Found "/apex/com.android.resolv@340090000" backed by file /data/apex/decompressed/<EMAIL>
[ 1144.420439] [ T4301] apexd: Found "/apex/com.android.uwb@340090000" backed by file /data/apex/decompressed/<EMAIL>
[ 1144.420693] [ T4301] apexd: Found "/apex/com.android.mediaprovider@340090000" backed by file /data/apex/decompressed/<EMAIL>
[ 1144.421128] [ T4301] apexd: Found "/apex/com.android.media@340090000" backed by file /data/apex/decompressed/<EMAIL>
[ 1144.421380] [ T4301] apexd: Found "/apex/com.android.configinfrastructure@340090000" backed by file /data/apex/decompressed/<EMAIL>
[ 1144.421622] [ T4301] apexd: Found "/apex/com.android.scheduling@340090000" backed by file /data/apex/decompressed/<EMAIL>
[ 1144.421874] [ T4301] apexd: Found "/apex/com.android.conscrypt@340090000" backed by file /data/apex/decompressed/<EMAIL>
[ 1144.422148] [ T4301] apexd: Found "/apex/com.android.art@340090000" backed by file /data/apex/decompressed/<EMAIL>
[ 1144.422387] [ T4301] apexd: Found "/apex/com.android.permission@340090000" backed by file /data/apex/decompressed/<EMAIL>
[ 1144.422624] [ T4301] apexd: Found "/apex/com.android.ondevicepersonalization@340090000" backed by file /data/apex/decompressed/<EMAIL>
[ 1144.422879] [ T4301] apexd: Found "/apex/com.android.neuralnetworks@340090000" backed by file /data/apex/decompressed/<EMAIL>
[ 1144.422981] [ T4301] apexd: 32 packages restored.
[ 1144.423022] [ T4301] apexd: Scanning /data/apex/active for data ApexFiles
[ 1144.423142] [ T4301] AidlLazyServiceRegistrar: Registering service apexservice
[ 1144.423979] [  T276] BpBinder: onLastStrongRef automatically unlinking death recipients: 
[ 1144.424404] [ T4301] AidlLazyServiceRegistrar: Trying to shut down the service. No clients in use for any service in process.
[ 1144.424610] [  T276] servicemanager: Tried to unregister apexservice, but there is about to be a client.
[ 1144.424702] [ T4303] apexd: getActivePackages received by ApexService
[ 1144.424721] [ T4301] AidlLazyServiceRegistrar: Failed to unregister service apexservice (Status(-5, EX_ILLEGAL_STATE): 'Can't unregister, pending client.')
[ 1144.463767] [  T276] servicemanager: Could not find android.hardware.power.stats.IPowerStats/default in the VINTF manifest.
[ 1144.465351] [    T1] init: Service 'simple_bugreportd' (pid 4152) exited with status 0 oneshot service took 2.916000 seconds in background
[ 1144.465382] [    T1] init: Sending signal 9 to service 'simple_bugreportd' (pid 4152) process group...
[ 1144.465531] [    T1] libprocessgroup: Successfully killed process cgroup uid 0 pid 4152 in 0ms
[ 1144.467549] [  T276] servicemanager: Found android.frameworks.stats.IStats/default in framework VINTF manifest.
[ 1144.467960] [  T276] servicemanager: Could not find android.hardware.memtrack.IMemtrack/default in the VINTF manifest.
[ 1144.508404] [ T1634] audit: audit_lost=327 audit_rate_limit=5 audit_backlog_limit=64
[ 1144.508421] [ T1634] audit: rate limit exceeded
[ 1144.508539] [  T287] type=1400 audit(**********.444:1461): avc:  denied  { read } for  comm="binder:318_5" name="wakeup4" dev="sysfs" ino=39988 scontext=u:r:system_suspend:s0 tcontext=u:object_r:sysfs:s0 tclass=dir permissive=1
[ 1144.508651] [  T287] type=1400 audit(**********.444:1462): avc:  denied  { open } for  comm="binder:318_5" path="/sys/devices/virtual/power_supply/test_ac/wakeup4" dev="sysfs" ino=39988 scontext=u:r:system_suspend:s0 tcontext=u:object_r:sysfs:s0 tclass=dir permissive=1
[ 1144.508708] [  T287] type=1400 audit(**********.444:1463): avc:  denied  { read } for  comm="binder:318_5" name="event_count" dev="sysfs" ino=39995 scontext=u:r:system_suspend:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[ 1144.508765] [  T287] type=1400 audit(**********.444:1464): avc:  denied  { open } for  comm="binder:318_5" path="/sys/devices/virtual/power_supply/test_ac/wakeup4/event_count" dev="sysfs" ino=39995 scontext=u:r:system_suspend:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[ 1144.570600] [  T276] servicemanager: Found android.hardware.power.IPower/default in device VINTF manifest.
[ 1144.578145] [  T276] servicemanager: Found android.hardware.light.ILights/default in device VINTF manifest.
[ 1144.790010] [  T276] servicemanager: Notifying apexservice they do (previously: don't) have clients when service is guaranteed to be in use
[ 1144.790184] [ T4303] AidlLazyServiceRegistrar: Process has 1 (of 1 available) client(s) in use after notification apexservice has clients: 1
[ 1144.790350] [ T4303] apexd: getAllPackages received by ApexService
[ 1145.522932] [ T2666] logd: start watching /data/system/packages.list ...
[ 1145.531388] [ T2666] logd: ReadPackageList, total packages: 101
[ 1145.571576] [    T1] init: starting service 'idmap2d'...
[ 1145.578144] [    T1] init: ... started service 'idmap2d' has pid 4340
[ 1145.578283] [    T1] init: Control message: Processed ctl.start for 'idmap2d' from pid: 4276 (system_server)
[ 1145.649017] [  T276] servicemanager: Found android.hardware.sensors.ISensors/default in device VINTF manifest.
[ 1145.651292] [  T276] servicemanager: Found android.hardware.health.IHealth/default in device VINTF manifest.
[ 1145.654263] [  T484] healthd: battery l=50 v=3300 t=2.6 h=2 st=3 c=-1600 fc=100 chg=au
[ 1145.703212] [  T287] type=1400 audit(**********.640:1466): avc:  denied  { dac_read_search } for  comm="system_server" capability=2  scontext=u:r:system_server:s0 tcontext=u:r:system_server:s0 tclass=capability permissive=1 bug=b/228030183
[ 1145.751268] [  T276] servicemanager: Could not find android.hardware.vibrator.IVibratorManager/default in the VINTF manifest.
[ 1145.751580] [  T276] servicemanager: Could not find android.hardware.vibrator.IVibrator/default in the VINTF manifest.
[ 1146.928871] [  T287] type=1400 audit(**********.864:1467): avc:  denied  { read } for  comm="android.ui" name="name" dev="sysfs" ino=26811 scontext=u:r:system_server:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[ 1146.929061] [  T287] type=1400 audit(**********.864:1468): avc:  denied  { open } for  comm="android.ui" path="/sys/devices/platform/2602e000.syscon/2602e000.syscon:usb2-phy@2000/extcon/extcon0/cable.3/name" dev="sysfs" ino=26811 scontext=u:r:system_server:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[ 1146.929153] [  T287] type=1400 audit(**********.864:1469): avc:  denied  { getattr } for  comm="android.ui" path="/sys/devices/platform/2602e000.syscon/2602e000.syscon:usb2-phy@2000/extcon/extcon0/cable.3/name" dev="sysfs" ino=26811 scontext=u:r:system_server:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[ 1146.956113] [  T276] servicemanager: Found android.frameworks.sensorservice.ISensorManager/default in framework VINTF manifest.
[ 1147.501537] [    T1] init: starting service 'audioserver'...
[ 1147.505901] [ T4380] libprocessgroup: SetCgroup::ExecuteForProcess: failed to open /dev/stune/foreground/cgroup.procs: No such file or directory
[ 1147.505941] [ T4380] libprocessgroup: Failed to apply HighPerformance process profile
[ 1147.505954] [ T4380] init: failed to set task profiles
[ 1147.506231] [    T1] init: ... started service 'audioserver' has pid 4380
[ 1147.506660] [    T1] init: processing action (init.svc.audioserver=running) from (/system/etc/init/audioserver.rc:35)
[ 1147.506730] [    T1] init: service 'vendor.audio-hal' requested start, but it is already running (flags: 4)
[ 1147.506832] [    T1] init: Command 'start vendor.audio-hal-aidl' action=init.svc.audioserver=running (/system/etc/init/audioserver.rc:37) took 0ms and failed: service vendor.audio-hal-aidl not found
[ 1147.506894] [    T1] init: Command 'start vendor.audio-effect-hal-aidl' action=init.svc.audioserver=running (/system/etc/init/audioserver.rc:38) took 0ms and failed: service vendor.audio-effect-hal-aidl not found
[ 1147.507055] [    T1] init: Command 'start vendor.audio-hal-4-0-msd' action=init.svc.audioserver=running (/system/etc/init/audioserver.rc:39) took 0ms and failed: service vendor.audio-hal-4-0-msd not found
[ 1147.507103] [    T1] init: Command 'start audio_proxy_service' action=init.svc.audioserver=running (/system/etc/init/audioserver.rc:40) took 0ms and failed: service audio_proxy_service not found
[ 1148.036487] [    T1] init: processing action (sys.sysctl.extra_free_kbytes=*) from (/system/etc/init/hw/init.rc:1233)
[ 1148.037086] [    T1] init: starting service 'exec 19 (/system/bin/extra_free_kbytes.sh 24300)'...
[ 1148.041696] [    T1] init: ... started service 'exec 19 (/system/bin/extra_free_kbytes.sh 24300)' has pid 4401
[ 1148.041800] [    T1] init: SVC_EXEC service 'exec 19 (/system/bin/extra_free_kbytes.sh 24300)' pid 4401 (uid 0 gid 0+0 context default) started; waiting...
[ 1148.115496] [  T276] servicemanager: Could not find android.hardware.oemlock.IOemLock/default in the VINTF manifest.
[ 1148.246556] [    T1] init: Service 'exec 19 (/system/bin/extra_free_kbytes.sh 24300)' (pid 4401) exited with status 0 waiting took 0.207000 seconds
[ 1148.246598] [    T1] init: Sending signal 9 to service 'exec 19 (/system/bin/extra_free_kbytes.sh 24300)' (pid 4401) process group...
[ 1148.246744] [    T1] libprocessgroup: Successfully killed process cgroup uid 0 pid 4401 in 0ms
[ 1148.300247] [  T287] type=1400 audit(**********.236:1470): avc:  denied  { dac_read_search } for  comm="system_server" capability=2  scontext=u:r:system_server:s0 tcontext=u:r:system_server:s0 tclass=capability permissive=1 bug=b/228030183
[ 1148.337341] [  T276] servicemanager: Found android.hardware.wifi.IWifi/default in device VINTF manifest.
[ 1148.339869] [  T276] servicemanager: Found android.hardware.wifi.supplicant.ISupplicant/default in device VINTF manifest.
[ 1148.341743] [  T276] servicemanager: Found android.hardware.wifi.supplicant.ISupplicant/default in device VINTF manifest.
[ 1148.655760] [  T276] servicemanager: Found android.frameworks.location.altitude.IAltitudeService/default in framework VINTF manifest.
[ 1148.700499] [  T276] servicemanager: Could not find android.hardware.soundtrigger3.ISoundTriggerHw/default in the VINTF manifest.
[ 1148.717223] [  T276] servicemanager: Found android.hardware.thermal.IThermal/default in device VINTF manifest.
[ 1148.718284] [  T287] type=1400 audit(**********.656:1471): avc:  denied  { call } for  comm="system-server-i" scontext=u:r:system_server:s0 tcontext=u:r:hal_hdmi_default:s0 tclass=binder permissive=1
[ 1148.718335] [  T276] servicemanager: Found android.hardware.usb.gadget.IUsbGadget/default in device VINTF manifest.
[ 1148.719951] [  T287] type=1400 audit(**********.656:1472): avc:  denied  { transfer } for  comm="system-server-i" scontext=u:r:system_server:s0 tcontext=u:r:hal_hdmi_default:s0 tclass=binder permissive=1
[ 1148.723613] [ T4337] dwc3 23000000.usb: request 0000000000000000 was not queued to ep0out
[ 1148.723863] [ T4216] android_work: sent uevent USB_STATE=DISCONNECTED
[ 1148.730759] [  T276] servicemanager: Found android.hardware.usb.IUsb/default in device VINTF manifest.
[ 1148.841217] [  T276] SELinux: avc:  denied  { add } for pid=4276 uid=1000 name=uacgadget scontext=u:r:system_server:s0 tcontext=u:object_r:default_android_service:s0 tclass=service_manager permissive=1
[ 1148.859496] [ T4456] dwc3 23000000.usb: device reset
[ 1148.889139] [  T276] servicemanager: Could not find android.hardware.authsecret.IAuthSecret/default in the VINTF manifest.
[ 1148.905206] [ T3900] android_work: sent uevent USB_STATE=CONNECTED
[ 1148.905317] [ T3900] rk_send_wakeup_key: KEY_WAKEUP event sent
[ 1149.017567] [  T276] servicemanager: Found android.hardware.wifi.IWifi/default in device VINTF manifest.
[ 1149.020025] [  T543] [dhd-sdio-1] CFG80211-ERROR) wl_cfg80211_netdev_notifier_call : wrong cfg ptr (000000006385d3e5)
[ 1149.020168] [  T543] [dhd-pcie-0] [wlan0] dhd_pri_stop : tx queue stopped
[ 1149.020179] [  T543] [dhd-pcie-0] [wlan0] dhd_stop : Enter
[ 1149.020410] [  T287] type=1400 audit(**********.956:1473): avc:  denied  { read } for  comm="system_server" name="btb_state" dev="sysfs" ino=48300 scontext=u:r:system_server:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[ 1149.020545] [  T287] type=1400 audit(**********.956:1474): avc:  denied  { open } for  comm="system_server" path="/sys/devices/platform/bluetooch_det/btb_state" dev="sysfs" ino=48300 scontext=u:r:system_server:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[ 1149.020622] [  T287] type=1400 audit(**********.956:1475): avc:  denied  { getattr } for  comm="system_server" path="/sys/devices/platform/bluetooch_det/btb_state" dev="sysfs" ino=48300 scontext=u:r:system_server:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[ 1149.022083] [  T276] servicemanager: Found android.hardware.health.IHealth/default in device VINTF manifest.
[ 1149.024348] [  T543] [dhd-pcie-0] dhd_stop: ######### called for ifidx=0 #########
[ 1149.024792] [ T3900] android_work: sent uevent USB_STATE=CONFIGURED
[ 1149.029449] [  T543] [dhd-pcie-0] CFGP2P-ERROR) wl_cfgp2p_disable_discovery :  do nothing, not initialized
[ 1149.031351] [  T543] [dhd-pcie-0] dhd_stop: making dhdpub up FALSE
[ 1149.031389] [  T543] [dhd-pcie-0] dhd_tcpack_suppress_set: TCP ACK Suppress mode 3 -> mode 0
[ 1149.031400] [  T543] [dhd-pcie-0] dhd_tcpack_suppress_set: TCPACK_INFO_MAXNUM=40, TCPDATA_INFO_MAXNUM=40
[ 1149.031523] [  T543] [dhd-pcie-0] dhd_pktlog_ring_reinit: ENTER
[ 1149.031538] [  T543] [dhd-pcie-0] dhd_pktlog_ring_reinit: EXIT
[ 1149.031547] [  T543] [dhd-pcie-0] dhd_dev_apf_delete_filter: id 200
[ 1149.031559] [  T543] [dhd-pcie-0] [wlan0] wl_android_wifi_off : g_wifi_on=1 force_off=1
[ 1149.031584] [  T543] [dhd-pcie-0] dhd_bus_devreset: == Power OFF ==
[ 1149.031595] [  T543] [dhd-pcie-0] dhdpcie_advertise_bus_cleanup: DB7 Not sent!!!
[ 1149.031626] [  T543] [dhd-pcie-0] dhd_bus_stop: making DHD_BUS_DOWN
[ 1149.031659] [  T543] [dhd-pcie-0] dhd_dpc_kill: tasklet disabled
[ 1149.034417] [  T543] [dhd-pcie-0] dhd_bus_devreset: making DHD_BUS_DOWN
[ 1149.034457] [  T543] [dhd-pcie-0] dhd_bus_devreset:  WLAN OFF Done
[ 1149.034472] [  T543] [dhd-pcie-0] wifi_platform_set_power = 0, delay: 10 msec
[ 1149.034483] [  T543] [dhd-pcie-0] ======== PULL WL_REG_ON(-1) LOW! ========
[ 1149.034490] [  T543] [WLAN_RFKILL]: rockchip_wifi_power: 0
[ 1149.034499] [  T543] [WLAN_RFKILL]: rockchip_wifi_power: toggle = false
[ 1149.034506] [  T543] wifi power off
[ 1149.141130] [  T543] [WLAN_RFKILL]: rockchip_wifi_power: toggle = false
[ 1149.141175] [  T543] [WLAN_RFKILL]: wifi shut off power [GPIO54-0]
[ 1149.152261] [  T543] [dhd-pcie-0] wifi_platform_set_power = 0, sleep done: 10 msec
[ 1149.152304] [  T543] [dhd-pcie-0] [wlan0] wl_android_wifi_off : out
[ 1149.153838] [  T543] [dhd-pcie-0] [wlan0] dhd_stop : Exit
[ 1149.154007] [  T543] [dhd-sdio-1] CFG80211-ERROR) wl_cfg80211_netdev_notifier_call : wrong cfg ptr (000000006385d3e5)
[ 1149.159667] [  T276] servicemanager: Found android.hardware.wifi.IWifi/default in device VINTF manifest.
[ 1149.161244] [    T1] init: processing action (wlan.driver.status=ok) from (/vendor/etc/init/hw/init.connectivity.rc:60)
[ 1149.165501] [    T1] init: processing action (vendor.wifi.direct.interface=p2p-dev-wlan0) from (/vendor/etc/init/hw/init.connectivity.rc:67)
[ 1149.166276] [  T276] servicemanager: Found android.hardware.wifi.IWifi/default in device VINTF manifest.
[ 1149.168063] [    T1] init: processing action (vendor.wifi.direct.interface=p2p-dev-wlan0) from (/vendor/etc/init/hw/init.connectivity.rc:67)
[ 1149.169580] [  T543] [dhd-pcie-0] [wlan0] dhd_open : Enter
[ 1149.169605] [  T543] [dhd-pcie-0] Dongle Host Driver, version 101.10.591.91.39 (20241101-1)(fe3ee42)
[ 1149.169605] [  T543] drivers/net/wireless/rockchip_wlan/rkwifi/bcmdhd compiled on Jun 12 2025 at 20:40:19
[ 1149.169605] [  T543] 
[ 1149.169618] [  T543] [dhd-pcie-0] ANDROID_VERSION = 14
[ 1149.170413] [  T543] [dhd-pcie-0] dhd_open: ######### called for ifidx=0 #########
[ 1149.170435] [  T543] [dhd-pcie-0] [wlan0] wl_android_wifi_on : in g_wifi_on=0
[ 1149.170441] [  T543] [dhd-pcie-0] wifi_platform_set_power = 1, delay: 200 msec
[ 1149.170449] [  T543] [dhd-pcie-0] ======== PULL WL_REG_ON(-1) HIGH! ========
[ 1149.170455] [  T543] [WLAN_RFKILL]: rockchip_wifi_power: 1
[ 1149.170461] [  T543] [WLAN_RFKILL]: rockchip_wifi_power: toggle = false
[ 1149.214461] [  T276] servicemanager: Found android.hardware.thermal.IThermal/default in device VINTF manifest.
[ 1149.277017] [  T543] [WLAN_RFKILL]: wifi turn on power [GPIO54-1]
[ 1149.410379] [ T4491] audit: audit_lost=328 audit_rate_limit=5 audit_backlog_limit=64
[ 1149.410402] [ T4491] audit: rate limit exceeded
[ 1149.484999] [  T543] [dhd-pcie-0] wifi_platform_set_power = 1, sleep done: 200 msec
[ 1149.485029] [  T543] [dhd-pcie-0] dhd_bus_devreset: == Power ON ==
[ 1149.573922] [  T276] servicemanager: Could not find android.hardware.gnss.IGnss/default in the VINTF manifest.
[ 1149.702553] [  T276] servicemanager: Could not find android.hardware.input.processor.IInputProcessor/default in the VINTF manifest.
[ 1149.917500] [  T276] servicemanager: Notifying apexservice they don't (previously: do) have clients when we now have no record of a client
[ 1149.917815] [ T4303] AidlLazyServiceRegistrar: Process has 0 (of 1 available) client(s) in use after notification apexservice has clients: 0
[ 1149.917837] [ T4303] AidlLazyServiceRegistrar: Trying to shut down the service. No clients in use for any service in process.
[ 1149.918560] [  T276] servicemanager: Unregistering apexservice
[ 1149.918631] [  T276] BpBinder: onLastStrongRef automatically unlinking death recipients: 
[ 1149.918852] [ T4303] AidlLazyServiceRegistrar: Unregistered all clients and exiting
[ 1149.923762] [    T1] init: Service 'apexd' (pid 4301) exited with status 0 oneshot service took 5.542000 seconds in background
[ 1149.923806] [    T1] init: Sending signal 9 to service 'apexd' (pid 4301) process group...
[ 1149.924023] [    T1] libprocessgroup: Successfully killed process cgroup uid 0 pid 4301 in 0ms
[ 1149.935996] [  T287] type=1400 audit(1749778125.872:1481): avc:  denied  { open } for  comm="com.android.se" path="/data/user/0/com.android.se/cache/oat_primary/arm64/SecureElement.art" dev="mmcblk0p15" ino=8677 scontext=u:r:secure_element:s0:c44,c260,c512,c768 tcontext=u:object_r:system_data_file:s0:c44,c260,c512,c768 tclass=file permissive=1
[ 1149.986192] [  T276] servicemanager: Could not find android.hardware.secure_element.ISecureElement/eSE1 in the VINTF manifest.
[ 1149.993540] [  T276] servicemanager: Could not find android.hardware.secure_element.ISecureElement/SIM1 in the VINTF manifest.
[ 1150.025456] [  T287] type=1400 audit(1749778125.960:1482): avc:  denied  { dac_read_search } for  comm="system_server" capability=2  scontext=u:r:system_server:s0 tcontext=u:r:system_server:s0 tclass=capability permissive=1 bug=b/228030183
[ 1150.025690] [  T287] type=1400 audit(1749778125.960:1483): avc:  denied  { read } for  comm="system_server" name="state" dev="sysfs" ino=48102 scontext=u:r:system_server:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[ 1150.025814] [  T287] type=1400 audit(1749778125.960:1484): avc:  denied  { open } for  comm="system_server" path="/sys/devices/platform/ad82129-sound/extcon/extcon5/state" dev="sysfs" ino=48102 scontext=u:r:system_server:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[ 1150.025908] [  T287] type=1400 audit(1749778125.960:1485): avc:  denied  { getattr } for  comm="system_server" path="/sys/devices/platform/ad82129-sound/extcon/extcon5/state" dev="sysfs" ino=48102 scontext=u:r:system_server:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[ 1150.069651] [  T276] servicemanager: Could not find android.hardware.input.processor.IInputProcessor/default in the VINTF manifest.
[ 1150.073037] [  T543] rk-pcie 2a200000.pcie: PCIe Link up, LTSSM is 0x30011
[ 1150.073093] [  T543] rk-pcie 2a200000.pcie: rockchip_dw_pcie_pm_ctrl_for_user ltssm=30011
[ 1150.073556] [  T543] [dhd-pcie-0] ******** Perform FLR ********
[ 1150.073598] [  T543] [dhd-pcie-0] config space 0x88 is 0x8080
[ 1150.073608] [  T543] [dhd-pcie-0] ******** device not in FLR ********
[ 1150.073922] [  T543] [dhd-pcie-0] ******** device force FLR only not set ********
[ 1150.143891] [  T276] servicemanager: VINTF HALs require names in the format type/instance (e.g. some.package.foo.IFoo/default) but got: suspend_control
[ 1150.144615] [  T276] servicemanager: Could not find android.hardware.bluetooth.IBluetoothHci/default in the VINTF manifest.
[ 1150.150231] [  T478] [BT_RFKILL]: bt shut off power
[ 1150.150377] [  T478] [BT_RFKILL]: rfkill_rk_set_power: set bt wake_host high!
[ 1150.153025] [  T543] [dhd-pcie-0] read_config: reg=0x88 read val=0xb080
[ 1150.153116] [  T543] [dhd-pcie-0] read_config: reg=0x88 read val=0xb080
[ 1150.153172] [  T543] [dhd-pcie-0] read_config: reg=0x88 read val=0xb080
[ 1150.153227] [  T543] [dhd-pcie-0] read_config: reg=0x88 read val=0xb080
[ 1150.153281] [  T543] [dhd-pcie-0] read_config: reg=0x88 read val=0xb080
[ 1150.153336] [  T543] [dhd-pcie-0] read_config: reg=0x88 read val=0xb080
[ 1150.153391] [  T543] [dhd-pcie-0] read_config: reg=0x88 read val=0xb080
[ 1150.153445] [  T543] [dhd-pcie-0] read_config: reg=0x88 read val=0xb080
[ 1150.153499] [  T543] [dhd-pcie-0] read_config: reg=0x88 read val=0xb080
[ 1150.153553] [  T543] [dhd-pcie-0] read_config: reg=0x88 read val=0xb080
[ 1150.153607] [  T543] [dhd-pcie-0] read_config: reg=0x88 read val=0xb080
[ 1150.153661] [  T543] [dhd-pcie-0] read_config: reg=0x88 read val=0xb080
[ 1150.153716] [  T543] [dhd-pcie-0] read_config: reg=0x88 read val=0xb080
[ 1150.153771] [  T543] [dhd-pcie-0] read_config: reg=0x88 read val=0xb080
[ 1150.153824] [  T543] [dhd-pcie-0] read_config: reg=0x88 read val=0xb080
[ 1150.153879] [  T543] [dhd-pcie-0] read_config: reg=0x88 read val=0xb080
[ 1150.153933] [  T543] [dhd-pcie-0] read_config: reg=0x88 read val=0xb080
[ 1150.153987] [  T543] [dhd-pcie-0] read_config: reg=0x88 read val=0xb080
[ 1150.154041] [  T543] [dhd-pcie-0] read_config: reg=0x88 read val=0xb080
[ 1150.154095] [  T543] [dhd-pcie-0] read_config: reg=0x88 read val=0xb080
[ 1150.154149] [  T543] [dhd-pcie-0] read_config: reg=0x88 read val=0x8080
[ 1150.154556] [  T543] [dhd-pcie-0] ******** FLR Succedeed ********
[ 1150.154693] [  T543] [dhd-pcie-0] Disable CTO for chip 0xaae8
[ 1150.155759] [  T543] [dhd-pcie-0] dhd_dump_pcie_slave_wrapper_regs pcie slave wrapper base not populated
[ 1150.156028] [  T543] [dhd-pcie-0] DHD: dongle ram size is set to 1310720(orig 1310720) at 0x170000
[ 1150.156113] [  T543] [dhd-pcie-0] dhdpcie_request_irq: INTx enabled, irq=104
[ 1150.156159] [  T543] [dhd-pcie-0] dhd_bus_download_firmware: firmware path=fw_bcmdhd.bin, nvram path=nvram.txt
[ 1150.156204] [  T543] [dhd-pcie-0] dhd_conf_set_path_params : Final fw_path=/fw_bcm43752a2_pcie_ag.bin
[ 1150.156214] [  T543] [dhd-pcie-0] dhd_conf_set_path_params : Final nv_path=/nvram_ap6275p.txt
[ 1150.156222] [  T543] [dhd-pcie-0] dhd_conf_set_path_params : Final clm_path=/clm_bcm43752a2_pcie_ag.blob
[ 1150.156230] [  T543] [dhd-pcie-0] dhd_conf_set_path_params : Final conf_path=/config_bcm43752a2_pcie_ag.txt
[ 1150.156365] [  T543] pcieh-pcie-0 0000:01:00.0: Direct firmware load for /config_bcm43752a2_pcie_ag.txt failed with error -2
[ 1150.156389] [  T543] [dhd-pcie-0] dhd_os_get_img_fwreq: request_firmware /config_bcm43752a2_pcie_ag.txt err: -2
[ 1150.156403] [  T543] [dhd-pcie-0] dhd_os_get_img(Request Firmware API) error : -30
[ 1150.156412] [  T543] [dhd-pcie-0] dhd_conf_read_config : Ignore config file /config_bcm43752a2_pcie_ag.txt
[ 1150.156548] [  T543] [dhd-pcie-0] d2h_intr_method -> PCIE_INTX(0); d2h_intr_control -> D2H_INTMASK(0)
[ 1150.156656] [  T543] [dhd-pcie-0] dhdpcie_download_code_file: dhd_tcm_test_enable 0, dhd_tcm_test_status 0, dhd_tcm_test_mode 2
[ 1150.156670] [  T543] [dhd-pcie-0] dhdpcie_download_code_file: download firmware /fw_bcm43752a2_pcie_ag.bin
[ 1150.157638] [  T543] [dhd-pcie-0] dhd_os_get_img_fwreq: /fw_bcm43752a2_pcie_ag.bin (946249 bytes) open success
[ 1150.200637] [  T276] servicemanager: Could not find android.hardware.radio.data.IRadioData/slot1 in the VINTF manifest.
[ 1150.201269] [  T276] servicemanager: Could not find android.hardware.radio.messaging.IRadioMessaging/slot1 in the VINTF manifest.
[ 1150.201884] [  T276] servicemanager: Could not find android.hardware.radio.modem.IRadioModem/slot1 in the VINTF manifest.
[ 1150.202483] [  T276] servicemanager: Could not find android.hardware.radio.network.IRadioNetwork/slot1 in the VINTF manifest.
[ 1150.203024] [  T276] servicemanager: Could not find android.hardware.radio.sim.IRadioSim/slot1 in the VINTF manifest.
[ 1150.203606] [  T276] servicemanager: Could not find android.hardware.radio.voice.IRadioVoice/slot1 in the VINTF manifest.
[ 1150.204132] [  T276] servicemanager: Could not find android.hardware.radio.ims.IRadioIms/slot1 in the VINTF manifest.
[ 1150.205025] [  T478] [BT_RFKILL]: rfkill_rk_set_power: set bt wake_host input!
[ 1150.205080] [  T478] [BT_RFKILL]: ENABLE UART_RTS
[ 1150.209344] [  T276] servicemanager: Could not find android.hardware.radio.data.IRadioData/slot1 in the VINTF manifest.
[ 1150.209964] [  T276] servicemanager: Could not find android.hardware.radio.messaging.IRadioMessaging/slot1 in the VINTF manifest.
[ 1150.210383] [  T276] servicemanager: Could not find android.hardware.radio.modem.IRadioModem/slot1 in the VINTF manifest.
[ 1150.210663] [  T276] servicemanager: Could not find android.hardware.radio.network.IRadioNetwork/slot1 in the VINTF manifest.
[ 1150.210884] [  T276] servicemanager: Could not find android.hardware.radio.sim.IRadioSim/slot1 in the VINTF manifest.
[ 1150.211271] [  T276] servicemanager: Could not find android.hardware.radio.voice.IRadioVoice/slot1 in the VINTF manifest.
[ 1150.211725] [  T276] servicemanager: Could not find android.hardware.radio.ims.IRadioIms/slot1 in the VINTF manifest.
[ 1150.215884] [  T276] servicemanager: Could not find android.hardware.radio.ims.IRadioIms/slot1 in the VINTF manifest.
[ 1150.218580] [  T276] servicemanager: Found android.hardware.graphics.allocator.IAllocator/default in device VINTF manifest.
[ 1150.224158] [  T276] servicemanager: Found android.hardware.graphics.allocator.IAllocator/default in device VINTF manifest.
[ 1150.253095] [  T543] [dhd-pcie-0] dhd_os_get_img_fwreq: /nvram_ap6275p.txt (7458 bytes) open success
[ 1150.253160] [  T543] [dhd-pcie-0] # AP6275P_NVRAM_V1.3_20230109A
[ 1150.271748] [  T543] [dhd-pcie-0] dhdpcie_bus_write_vars: Download, Upload and compare of NVRAM succeeded.
[ 1150.271775] [  T543] [dhd-pcie-0] dhdpcie_bus_write_vars: New varsize is 6040, length token(nvram_csm)=0xfa1905e6
[ 1150.272194] [  T543] [dhd-pcie-0] Download and compare of TLV 0xfeedc0de succeeded (size 128, addr 2ae7dc).
[ 1150.272299] [  T543] [dhd-pcie-0] dhdpcie_bus_download_state: Took ARM out of Reset
[ 1150.313084] [  T478] [BT_RFKILL]: DISABLE UART_RTS
[ 1150.313126] [  T478] [BT_RFKILL]: bt turn on power
[ 1150.313225] [  T478] [BT_RFKILL]: Request irq for bt wakeup host
[ 1150.313612] [  T478] [BT_RFKILL]: ** disable irq
[ 1150.363124] [  T543] [dhd-pcie-0] dhdpcie_readshared: addr=0x20ecf4 nvram_csm=0xfa1905e6
[ 1150.363156] [  T543] [dhd-pcie-0] ### Total time ARM OOR to Readshared pass took 90893 usec ###
[ 1150.363164] [  T543] [dhd-pcie-0] dhdpcie_readshared: PCIe shared addr (0x0020ecf4) read took 90000 usec before dongle is ready
[ 1150.363435] [  T543] [dhd-pcie-0] FW supports DAR ? N
[ 1150.363584] [  T543] [dhd-pcie-0] dhdpcie_readshared: max H2D queues 64
[ 1150.363592] [  T543] [dhd-pcie-0] FW supports MD ring ? N
[ 1150.363624] [  T543] [dhd-pcie-0] dhd_bus_init: Enabling bus->intr_enabled
[ 1150.363633] [  T543] [dhd-pcie-0] dhdpcie_oob_intr_register OOB irq=128 flags=0x4
[ 1150.363666] [  T543] [dhd-pcie-0] dhdpcie_oob_intr_register: enable_irq_wake
[ 1150.363706] [  T543] [dhd] STATIC-MSG) dhd_wlan_mem_prealloc : bus_type 1, index 0, section 9, size 32896
[ 1150.363722] [  T543] [dhd-pcie-0] dhd_prot_init:4165: h2d_max_txpost = 512
[ 1150.363730] [  T543] [dhd-pcie-0] dhd_prot_init:4171: h2d_htput_max_txpost = 2048
[ 1150.363739] [  T543] [dhd-pcie-0] dhd_prot_init: max_rxbufpost:511 rx_buf_burst:64 rx_bufpost_threshold:64
[ 1150.363747] [  T543] [dhd-pcie-0] ENABLING DW:0
[ 1150.363777] [  T543] [dhd-pcie-0] dhd_prot_d2h_sync_init(): D2H sync mechanism is XORCSUM 
[ 1150.363795] [  T543] [dhd-pcie-0] dhd_bus_hostready : Read PCICMD Reg: 0x00100006
[ 1150.363825] [  T543] [dhd-pcie-0] dhd_bus_hostready: Ring Hostready:1
[ 1150.363968] [  T543] [dhd-pcie-0] trying to send create d2h info ring: id 70
[ 1150.363977] [  T543] [dhd-pcie-0] dhd_send_d2h_ringcreate ringid: 3 idx: 70 max_h2d: 67
[ 1150.364798] [    C0] [dhd-pcie-0] dhd_prot_process_d2h_ring_create_complete ring create Response status = 0 ring 3, id 0xfffc
[ 1150.364842] [    C0] [dhd-pcie-0] info buffer post after ring create
[ 1150.366601] [  T543] [dhd-pcie-0] wlc_ver_major 12, wlc_ver_minor 1
[ 1150.366618] [  T543] [dhd-pcie-0] dhd_get_memdump_info: MEMDUMP ENABLED = 3
[ 1150.367144] [  T543] [dhd-pcie-0] dhd_sync_with_dongle: GET_REVINFO device 0x449d, vendor 0x14e4, chipnum 0xaae8
[ 1150.367738] [  T543] [dhd-pcie-0] dhd_sync_with_dongle: RxBuf Post : 2048
[ 1150.367749] [  T543] [dhd-pcie-0] dhd_sync_with_dongle: RxBuf Post Alloc : 2048
[ 1150.370909] [  T543] [dhd-pcie-0] dhd_preinit_ioctls: preinit_status IOVAR not supported, use legacy preinit
[ 1150.370924] [  T543] [dhd-pcie-0] dhd_tcpack_suppress_set: TCP ACK Suppress mode 0 -> mode 3
[ 1150.370930] [  T543] [dhd-pcie-0] dhd_tcpack_suppress_set: TCPACK_INFO_MAXNUM=40, TCPDATA_INFO_MAXNUM=40
[ 1150.371707] [  T543] [dhd-pcie-0] dhd_legacy_preinit_ioctls: hostwake_oob enabled
[ 1150.373147] [  T543] [dhd-pcie-0] dhd_legacy_preinit_ioctls: use firmware generated mac_address 9c:b8:b4:86:48:2c
[ 1150.373522] [  T543] [dhd-pcie-0] dhd_os_get_img_fwreq: /clm_bcm43752a2_pcie_ag.blob (32297 bytes) open success
[ 1150.375810] [  T543] [dhd-pcie-0] dhd_check_current_clm_data: ----- This FW is not included CLM data -----
[ 1150.403717] [  T543] [dhd-pcie-0] dhd_apply_default_clm: CLM download succeeded 
[ 1150.404284] [  T543] [dhd-pcie-0] dhd_check_current_clm_data: ----- This FW is included CLM data -----
[ 1150.409590] [  T543] [dhd-pcie-0] Firmware up: op_mode=0x0005, MAC=9c:b8:b4:86:48:2c
[ 1150.418766] [  T543] [dhd-pcie-0] dhd_legacy_preinit_ioctls: event_log_max_sets: 26 ret: 0
[ 1150.427186] [  T543] [dhd-pcie-0] arp_enable:1 arp_ol:0
[ 1150.431517] [  T543] [dhd-pcie-0]   Driver: 101.10.591.91.39 (20241101-1)
[ 1150.431517] [  T543] [dhd-pcie-0]   Firmware: wl0: Nov 22 2024 02:13:44 version 18.35.387.23.266 (gb0383124) FWID 01-d8dffa05
[ 1150.431517] [  T543] [dhd-pcie-0]   CLM: 9.9.20_SS (2022-11-21 17:26:58) 
[ 1150.433119] [  T543] [dhd-pcie-0] dhd_pno_init: Support Android Location Service
[ 1150.434654] [  T543] [dhd-pcie-0] rtt_do_get_ioctl: failed to send getbuf proxd iovar (CMD ID : 1), status=-23
[ 1150.434692] [  T543] [dhd-pcie-0] dhd_rtt_init : FTM is not supported
[ 1150.434704] [  T543] [dhd-pcie-0] dhd_rtt_init EXIT, err = 0
[ 1150.455600] [  T543] [dhd-pcie-0] dhd_legacy_preinit_ioctls: d3_hostwake_delay IOVAR not present, proceed
[ 1150.487060] [    C0] [dhd-pcie-0] dhd_rx_frame: net device is NOT registered. drop event packet
[ 1150.487129] [    C0] [dhd-pcie-0] dhd_rx_frame: net device is NOT registered. drop event packet
[ 1150.487151] [    C0] [dhd-pcie-0] dhd_update_interface_flow_info: ifindex:0 previous role:0 new role:0
[ 1150.487169] [    C0] [dhd-pcie-0] dhd_rx_frame: net device is NOT registered. drop event packet
[ 1150.489475] [  T543] [dhd-pcie-0] dhd_conf_set_country : set country CN, revision 0
[ 1150.494807] [  T543] [dhd-pcie-0] dhd_conf_set_country : Country code: CN (CN/0)
[ 1150.503786] [  T543] [dhd-pcie-0] dhd_bus_devreset: WLAN Power On Done
[ 1150.503820] [  T543] [dhd-pcie-0] [wlan0] wl_android_wifi_on : Success
[ 1150.530234] [    C0] [dhd-pcie-0] dhd_update_interface_flow_info: ifindex:0 previous role:0 new role:0
[ 1150.530284] [    C0] [dhd-pcie-0] dhd_rx_frame: net device is NOT registered. drop event packet
[ 1150.544293] [  T543] [dhd-pcie-0] [wlan0] wl_cfg80211_up : Roam channel cache enabled
[ 1150.545880] [  T543] [dhd-pcie-0] [wlan0] dhd_open : Exit ret=0
[ 1150.546490] [  T543] [dhd-sdio-1] CFG80211-ERROR) wl_cfg80211_netdev_notifier_call : wrong cfg ptr (000000006385d3e5)
[ 1150.546518] [  T543] [dhd-pcie-0] dhd_pri_open : no mutex held
[ 1150.546523] [  T543] [dhd-pcie-0] dhd_pri_open : set mutex lock
[ 1150.546530] [  T543] [dhd-pcie-0] [wlan0] dhd_open : Primary net_device is already up
[ 1150.546542] [  T543] [dhd-pcie-0] [wlan0] dhd_pri_open : tx queue started
[ 1150.546557] [  T543] [dhd-pcie-0] [wlan0] custom_xps_map_set : Done. mapping cpu
[ 1150.546563] [  T543] [dhd-pcie-0] dhd_pri_open : mutex is released.
[ 1150.546646] [  T543] [dhd-sdio-1] CFG80211-ERROR) wl_cfg80211_netdev_notifier_call : wrong cfg ptr (000000006385d3e5)
[ 1150.546726] [    T1] init: processing action (wlan.driver.status=ok) from (/vendor/etc/init/hw/init.connectivity.rc:60)
[ 1150.554483] [    T1] init: processing action (vendor.wifi.direct.interface=p2p-dev-wlan0) from (/vendor/etc/init/hw/init.connectivity.rc:67)
[ 1150.575649] [  T276] servicemanager: Found android.hardware.wifi.IWifi/default in device VINTF manifest.
[ 1150.581937] [    T1] init: processing action (vendor.wifi.direct.interface=p2p-dev-wlan0) from (/vendor/etc/init/hw/init.connectivity.rc:67)
[ 1150.590223] [  T543] netlink: 'binder:506_3': attribute type 15 has an invalid length.
[ 1150.614220] [  T276] servicemanager: Found android.hardware.wifi.IWifi/default in device VINTF manifest.
[ 1150.614842] [  T276] servicemanager: Found android.hardware.wifi.supplicant.ISupplicant/default in device VINTF manifest.
[ 1150.615958] [  T276] servicemanager: Found android.hardware.wifi.supplicant.ISupplicant/default in device VINTF manifest.
[ 1150.616370] [  T276] servicemanager: Since 'android.hardware.wifi.supplicant.ISupplicant/default' could not be found, trying to start it as a lazy AIDL service. (if it's not configured to be a lazy service, it may be stuck starting or still starting).
[ 1150.618039] [    T1] init: starting service 'wpa_supplicant'...
[ 1150.619179] [    T1] init: Created socket '/dev/socket/wpa_wlan0', mode 660, user 1010, group 1010
[ 1150.625265] [    T1] init: ... started service 'wpa_supplicant' has pid 4823
[ 1150.625389] [    T1] init: Control message: Processed ctl.interface_start for 'aidl/android.hardware.wifi.supplicant.ISupplicant/default' from pid: 276 (/system/bin/servicemanager)
[ 1150.668765] [  T276] servicemanager: Found android.hardware.wifi.supplicant.ISupplicant/default in device VINTF manifest.
[ 1150.669629] [  T276] BpBinder: onLastStrongRef automatically unlinking death recipients: 
[ 1150.687364] [ T4823] [dhd-sdio-1] CFG80211-ERROR) wl_cfg80211_netdev_notifier_call : wrong cfg ptr (000000006385d3e5)
[ 1150.687441] [ T4823] [dhd-sdio-1] CFG80211-ERROR) wl_cfg80211_netdev_notifier_call : wrong cfg ptr (000000006385d3e5)
[ 1150.696754] [  T276] servicemanager: Found android.hardware.wifi.IWifi/default in device VINTF manifest.
[ 1150.717459] [ T4823] [dhd-pcie-0] wl_android_priv_cmd: Android private cmd "COUNTRY CN" on wlan0
[ 1150.718010] [ T4823] [dhd-pcie-0] dhd_conf_same_country : country code = CN/0 is already configured
[ 1150.722300] [ T4823] [dhd-pcie-0] wl_android_priv_cmd: Android private cmd "SETSUSPENDMODE 1" on wlan0
[ 1150.747417] [  T543] [dhd-pcie-0] dhd_os_start_logging , ring_id : 7 log_level : 0, time_intval : 0, threshod 0 Bytes
[ 1150.747861] [  T543] [dhd-pcie-0] dhd_os_start_logging , ring_id : 7 log_level : 1, time_intval : 3600, threshod 16384 Bytes
[ 1150.749037] [ T4823] [dhd-pcie-0] wl_android_priv_cmd: Android private cmd "BTCOEXSCAN-STOP" on wlan0
[ 1150.749533] [ T4823] [dhd-pcie-0] wl_android_priv_cmd: Android private cmd "RXFILTER-STOP" on wlan0
[ 1150.749993] [ T4823] [dhd-pcie-0] wl_android_priv_cmd: Android private cmd "RXFILTER-ADD 2" on wlan0
[ 1150.751651] [ T4823] [dhd-pcie-0] wl_android_priv_cmd: Android private cmd "RXFILTER-START" on wlan0
[ 1150.752017] [ T4823] [dhd-pcie-0] wl_android_priv_cmd: Android private cmd "RXFILTER-STOP" on wlan0
[ 1150.752272] [ T4823] [dhd-pcie-0] wl_android_priv_cmd: Android private cmd "RXFILTER-ADD 3" on wlan0
[ 1150.752417] [ T4823] [dhd-pcie-0] wl_android_priv_cmd: Android private cmd "RXFILTER-START" on wlan0
[ 1150.752577] [ T4823] [dhd-pcie-0] wl_android_priv_cmd: Android private cmd "SETSUSPENDMODE 1" on wlan0
[ 1150.759029] [    T1] init: processing action (vendor.wifi.direct.interface=p2p-dev-wlan0) from (/vendor/etc/init/hw/init.connectivity.rc:67)
[ 1150.766004] [ T4823] [dhd-pcie-0] wl_android_priv_cmd: Android private cmd "SETSUSPENDMODE 0" on wlan0
[ 1150.769904] [  T276] servicemanager: Found android.hardware.wifi.hostapd.IHostapd/default in device VINTF manifest.
[ 1150.770361] [  T276] servicemanager: Found android.hardware.wifi.hostapd.IHostapd/default in device VINTF manifest.
[ 1150.770524] [  T276] servicemanager: Found android.hardware.wifi.hostapd.IHostapd/default in device VINTF manifest.
[ 1150.770637] [  T276] servicemanager: Since 'android.hardware.wifi.hostapd.IHostapd/default' could not be found, trying to start it as a lazy AIDL service. (if it's not configured to be a lazy service, it may be stuck starting or still starting).
[ 1150.771654] [    T1] init: starting service 'hostapd'...
[ 1150.775516] [    T1] init: ... started service 'hostapd' has pid 4855
[ 1150.775625] [    T1] init: Control message: Processed ctl.interface_start for 'aidl/android.hardware.wifi.hostapd.IHostapd/default' from pid: 276 (/system/bin/servicemanager)
[ 1150.815608] [  T276] servicemanager: Found android.hardware.wifi.hostapd.IHostapd/default in device VINTF manifest.
[ 1150.817487] [  T276] BpBinder: onLastStrongRef automatically unlinking death recipients: 
[ 1150.821482] [  T276] servicemanager: Found android.hardware.wifi.IWifi/default in device VINTF manifest.
[ 1150.825661] [    T1] init: processing action (vendor.wifi.direct.interface=p2p-dev-wlan0) from (/vendor/etc/init/hw/init.connectivity.rc:67)
[ 1150.840346] [  T276] servicemanager: Found android.hardware.wifi.IWifi/default in device VINTF manifest.
[ 1150.849407] [  T542] [dhd-pcie-0] CFG80211-ERROR) wl_cfg80211_netdev_notifier_call : wrong cfg ptr (000000003686c2b9)
[ 1150.849534] [  T542] [dhd-sdio-1] [wlan1] dhd_pri_stop : tx queue stopped
[ 1150.849543] [  T542] [dhd-sdio-1] [wlan1] dhd_stop : Enter
[ 1150.850648] [  T542] [dhd-sdio-1] dhd_stop: ######### called for ifidx=0 #########
[ 1150.852390] [  T542] [dhd-sdio-1] CFGP2P-ERROR) wl_cfgp2p_disable_discovery :  do nothing, not initialized
[ 1150.852616] [  T542] [dhd-sdio-1] dhd_stop: making dhdpub up FALSE
[ 1150.852629] [  T542] [dhd-sdio-1] dhd_tcpack_suppress_set: TCP ACK Suppress mode 1 -> mode 0
[ 1150.852635] [  T542] [dhd-sdio-1] dhd_tcpack_suppress_set: TCPACK_INFO_MAXNUM=40, TCPDATA_INFO_MAXNUM=40
[ 1150.852729] [  T542] [dhd-sdio-1] dhd_pktlog_ring_reinit: ENTER
[ 1150.852744] [  T542] [dhd-sdio-1] dhd_pktlog_ring_reinit: EXIT
[ 1150.852750] [  T542] [dhd-sdio-1] dhd_dev_apf_delete_filter: id 200
[ 1150.852760] [  T542] [dhd-sdio-1] [wlan1] wl_android_wifi_off : g_wifi_on=1 force_off=1
[ 1150.852780] [  T542] [dhd-sdio-1] dhd_bus_devreset: == Power OFF ==
[ 1150.852988] [  T542] [dhd-sdio-1] dhd_bus_stop: making DHD_BUS_DOWN
[ 1150.853033] [  T542] [dhd-sdio-1] bcmsdh_oob_intr_unregister: Enter
[ 1150.853057] [  T542] [dhd-sdio-1] dhd_bus_devreset: making dhdpub up FALSE
[ 1150.853063] [  T542] [dhd-sdio-1] dhd_txglom_enable: enable 0
[ 1150.853070] [  T542] [dhd-sdio-1] dhd_bus_devreset: making DHD_BUS_DOWN
[ 1150.853113] [  T542] [dhd-sdio-1] wifi_platform_set_power = 0, delay: 10 msec
[ 1150.853124] [  T542] [dhd-sdio-1] ======== PULL WL_REG_ON(-1) LOW! ========
[ 1150.853129] [  T542] [WLAN_RFKILL-1]: rockchip_wifi_power1: 0
[ 1150.853136] [  T542] [WLAN_RFKILL-1]: rockchip_wifi_power1: toggle = false
[ 1150.853141] [  T542] [WLAN_RFKILL-1]: rockchip_wifi_power1: toggle = false
[ 1150.853146] [  T542] [WLAN_RFKILL-1]: wifi shut off power [GPIO-1-1]
[ 1150.864168] [  T542] [dhd-sdio-1] wifi_platform_set_power = 0, sleep done: 10 msec
[ 1150.864191] [  T542] [dhd-sdio-1] [wlan1] wl_android_wifi_off : out
[ 1150.864206] [  T542] [dhd-sdio-1] [wlan1] dhd_stop : Exit
[ 1150.867002] [ T4766] audit: audit_lost=335 audit_rate_limit=5 audit_backlog_limit=64
[ 1150.867018] [ T4766] audit: rate limit exceeded
[ 1150.867590] [  T542] [dhd-pcie-0] CFG80211-ERROR) wl_cfg80211_netdev_notifier_call : wrong cfg ptr (000000003686c2b9)
[ 1150.867764] [  T542] [dhd-pcie-0] CFG80211-ERROR) wl_cfg80211_netdev_notifier_call : wrong cfg ptr (000000003686c2b9)
[ 1150.867785] [  T542] [dhd-sdio-1] [wlan1] dhd_set_mac_address : macaddr = 9c:b8:b4:8e:35:e4
[ 1150.867794] [  T542] [dhd-sdio-1] CFG80211-ERROR) wl_cfg80211_macaddr_sync_reqd : no macthing if type
[ 1150.867862] [  T542] [dhd-pcie-0] CFG80211-ERROR) wl_cfg80211_netdev_notifier_call : wrong cfg ptr (000000003686c2b9)
[ 1150.867957] [  T542] [dhd-pcie-0] CFG80211-ERROR) wl_cfg80211_netdev_notifier_call : wrong cfg ptr (000000003686c2b9)
[ 1150.867973] [  T542] [dhd-sdio-1] dhd_pri_open : no mutex held
[ 1150.867979] [  T542] [dhd-sdio-1] dhd_pri_open : set mutex lock
[ 1150.867985] [  T542] [dhd-sdio-1] [wlan1] dhd_open : Enter
[ 1150.867991] [  T542] [dhd-sdio-1] Dongle Host Driver, version 101.10.591.91.39 (20241101-1)(fe3ee42)
[ 1150.867991] [  T542] /mnt/data1/android_workspace/rk3576_android14/external/wifi_driver/bcmdhd compiled on Jun 12 2025 at 20:41:00
[ 1150.867991] [  T542] 
[ 1150.868000] [  T542] [dhd-sdio-1] ANDROID_VERSION = 14
[ 1150.868009] [  T542] [dhd-sdio-1] dhd_open: ######### called for ifidx=0 #########
[ 1150.868020] [  T542] [dhd-sdio-1] [wlan1] wl_android_wifi_on : in g_wifi_on=0
[ 1150.868027] [  T542] [dhd-sdio-1] wifi_platform_set_power = 1, delay: 200 msec
[ 1150.868035] [  T542] [dhd-sdio-1] ======== PULL WL_REG_ON(-1) HIGH! ========
[ 1150.868040] [  T542] [WLAN_RFKILL-1]: rockchip_wifi_power1: 1
[ 1150.868047] [  T542] [WLAN_RFKILL-1]: rockchip_wifi_power1: toggle = false
[ 1150.868053] [  T542] [WLAN_RFKILL-1]: wifi turn on power [GPIO-1-0]
[ 1150.995145] [  T276] servicemanager: Found android.hardware.graphics.allocator.IAllocator/default in device VINTF manifest.
[ 1151.000787] [  T276] servicemanager: Found android.hardware.graphics.allocator.IAllocator/default in device VINTF manifest.
[ 1151.072984] [  T542] [dhd-sdio-1] wifi_platform_set_power = 1, sleep done: 200 msec
[ 1151.073015] [  T542] [dhd-sdio-1] sdio_sw_reset: call mmc_hw_reset
[ 1151.293081] [  T542] mmc_host mmc1: Bus speed (slot 0) = 400000Hz (slot req 400000Hz, actual 400000HZ div = 0)
[ 1151.400435] [    T1] init: Control message: Processed ctl.stop for 'adbd' from pid: 4276 (system_server)
[ 1151.400635] [    T1] init: processing action (init.svc.adbd=stopped) from (/vendor/etc/init/hw/init.rk30board.usb.rc:258)
[ 1151.401863] [    T1] init: processing action (init.svc.adbd=stopped) from (/system/etc/init/hw/init.usb.configfs.rc:14)
[ 1151.402021] [  T504] dwc3 23000000.usb: request 0000000000000000 was not queued to ep0out
[ 1151.402627] [ T4216] android_work: sent uevent USB_STATE=DISCONNECTED
[ 1151.421082] [  T542] mmc_host mmc1: Bus speed (slot 0) = 100000000Hz (slot req 100000000Hz, actual 100000000HZ div = 0)
[ 1151.421181] [  T542] dwmmc_rockchip 2a320000.mmc: Successfully tuned phase to 180
[ 1151.421237] [  T542] [dhd-sdio-1] sdioh_start: set sd_f2_blocksize 256
[ 1151.421382] [  T542] [dhd-sdio-1] dhd_bus_devreset: == Power ON ==
[ 1151.421394] [  T542] [dhd-sdio-1] dhd_bus_devreset: set si_wd FALSE
[ 1151.421462] [  T542] [dhd-sdio-1] F1 signature read @0x18000000=0x15294345
[ 1151.423592] [  T542] [dhd-sdio-1] F1 signature OK, socitype:0x1 chip:0x4345 rev:0x9 pkg:0x2
[ 1151.424169] [  T542] [dhd-sdio-1] DHD: dongle ram size is set to 819200(orig 819200) at 0x198000
[ 1151.424247] [  T542] [dhd-sdio-1] dhd_bus_devreset: making DHD_BUS_DOWN
[ 1151.424277] [  T542] [dhd-sdio-1] dhdsdio_probe_init: making DHD_BUS_DOWN
[ 1151.424367] [  T542] [dhd-sdio-1] dhd_conf_set_path_params : Final fw_path=/fw_bcm43456c5_ag.bin
[ 1151.424377] [  T542] [dhd-sdio-1] dhd_conf_set_path_params : Final nv_path=/nvram_ap6256.txt
[ 1151.424385] [  T542] [dhd-sdio-1] dhd_conf_set_path_params : Final clm_path=/clm_bcm43456c5_ag.blob
[ 1151.424392] [  T542] [dhd-sdio-1] dhd_conf_set_path_params : Final conf_path=/config_bcm43456c5_ag.txt
[ 1151.424504] [  T542] bcmsdh_sdmmc mmc1:0001:2: Direct firmware load for /config_bcm43456c5_ag.txt failed with error -2
[ 1151.424521] [  T542] [dhd-sdio-1] dhd_os_get_img_fwreq: request_firmware /config_bcm43456c5_ag.txt err: -2
[ 1151.424534] [  T542] [dhd-sdio-1] dhd_os_get_img(Request Firmware API) error : -30
[ 1151.424544] [  T542] [dhd-sdio-1] dhd_conf_read_config : Ignore config file /config_bcm43456c5_ag.txt
[ 1151.425484] [  T542] [dhd-sdio-1] dhd_os_get_img_fwreq: /fw_bcm43456c5_ag.bin (629343 bytes) open success
[ 1151.442782] [ T4881] es8156_set_switch_hp(507), es8156->muted = 1
[ 1151.445643] [ T4881] ad82129_startup(241), -----------
[ 1151.445761] [ T4881] enter into es8156_set_dai_sysclk, freq = 4096000
[ 1151.447430] [  T276] servicemanager: Since 'apexservice' could not be found, trying to start it as a lazy AIDL service. (if it's not configured to be a lazy service, it may be stuck starting or still starting).
[ 1151.449587] [    T1] init: starting service 'apexd'...
[ 1151.452091] [   T80] enter into es8156_set_bias_level, level = 1
[ 1151.454206] [    T1] init: ... started service 'apexd' has pid 4883
[ 1151.454387] [    T1] init: Control message: Processed ctl.interface_start for 'aidl/apexservice' from pid: 276 (/system/bin/servicemanager)
[ 1151.501026] [  T542] [dhd-sdio-1] dhd_os_get_img_fwreq: /nvram_ap6256.txt (2732 bytes) open success
[ 1151.501096] [  T542] [dhd-sdio-1] #AP6256_NVRAM_V1.4_06112021
[ 1151.501537] [  T542] [dhd-sdio-1] dhdsdio_write_vars: Download, Upload and compare of NVRAM succeeded.
[ 1151.591743] [  T542] [dhd-sdio-1] dhd_bus_init: enable 0x06, ready 0x06 (waited 0us)
[ 1151.591950] [  T542] [dhd-sdio-1] bcmsdh_oob_intr_register: HW_OOB irq=131 flags=0x4
[ 1151.592075] [  T542] [dhd-sdio-1] dhd_get_memdump_info: MEMDUMP ENABLED = 3
[ 1151.594347] [  T542] [dhd-sdio-1] wlc_ver_major 4, wlc_ver_minor 1
[ 1151.594381] [  T542] [dhd-sdio-1] dhd_tcpack_suppress_set: TCP ACK Suppress mode 0 -> mode 1
[ 1151.594389] [  T542] [dhd-sdio-1] dhd_tcpack_suppress_set: TCPACK_INFO_MAXNUM=40, TCPDATA_INFO_MAXNUM=40
[ 1151.594803] [  T542] [dhd-sdio-1] dhd_conf_custom_mac : set custom MAC address 9c:b8:b4:8e:35:e4 from SIOCSIFHWADDR
[ 1151.595354] [  T542] [dhd-sdio-1] dhd_legacy_preinit_ioctls: use firmware generated mac_address 9c:b8:b4:8e:35:e4
[ 1151.595463] [  T542] bcmsdh_sdmmc mmc1:0001:2: Direct firmware load for /clm_bcm43456c5_ag.blob failed with error -2
[ 1151.595482] [  T542] [dhd-sdio-1] dhd_os_get_img_fwreq: request_firmware /clm_bcm43456c5_ag.blob err: -2
[ 1151.595494] [  T542] [dhd-sdio-1] dhd_os_get_img(Request Firmware API) error : -30
[ 1151.595505] [  T542] [dhd-sdio-1] dhd_apply_default_clm: Ignore clm file /clm_bcm43456c5_ag.blob
[ 1151.597064] [  T542] [dhd-sdio-1] Firmware up: op_mode=0x0005, MAC=9c:b8:b4:8e:35:e4
[ 1151.601500] [  T542] [dhd-sdio-1] dhd_legacy_preinit_ioctls: event_log_max_sets: 40 ret: -23
[ 1151.603225] [  T542] [dhd-sdio-1] dhd_legacy_preinit_ioctls set event_log_tag_control fail -23
[ 1151.604298] [  T542] [dhd-sdio-1] arp_enable:1 arp_ol:0
[ 1151.605562] [  T542] [dhd-sdio-1]   Driver: 101.10.591.91.39 (20241101-1)
[ 1151.605562] [  T542] [dhd-sdio-1]   Firmware: wl0: Nov 20 2023 17:22:11 version *********** (g0fffcc23) FWID 01-fbd67456 es7.c5.n4.a3
[ 1151.605562] [  T542] [dhd-sdio-1]   CLM: 9.2.9 (2016-02-03 04:34:31) 
[ 1151.605783] [  T542] [dhd-sdio-1] dhd_txglom_enable: enable 1
[ 1151.605790] [  T542] [dhd-sdio-1] dhd_conf_set_txglom_params : txglom_mode=copy
[ 1151.605796] [  T542] [dhd-sdio-1] dhd_conf_set_txglom_params : txglomsize=36, deferred_tx_len=0
[ 1151.605801] [  T542] [dhd-sdio-1] dhd_conf_set_txglom_params : txinrx_thres=128, dhd_txminmax=-1
[ 1151.605807] [  T542] [dhd-sdio-1] dhd_conf_set_txglom_params : tx_max_offset=0, txctl_tmo_fix=300
[ 1151.605814] [  T542] [dhd-sdio-1] dhd_conf_get_disable_proptx : fw_proptx=1, disable_proptx=-1
[ 1151.607092] [  T542] [dhd-sdio-1] dhd_wlfc_hostreorder_init(): successful bdcv2 tlv signaling, 64
[ 1151.608588] [  T542] [dhd-sdio-1] dhd_pno_init: Support Android Location Service
[ 1151.608992] [  T542] [dhd-sdio-1] rtt_do_get_ioctl: failed to send getbuf proxd iovar (CMD ID : 1), status=-4
[ 1151.609002] [  T542] [dhd-sdio-1] dhd_rtt_init : FTM is not supported
[ 1151.609009] [  T542] [dhd-sdio-1] dhd_rtt_init EXIT, err = 0
[ 1151.638134] [  T542] [dhd-sdio-1] dhd_legacy_preinit_ioctls: Failed to get preserve log # !
[ 1151.638437] [  T542] [dhd-sdio-1] dhd_legacy_preinit_ioctls: d3_hostwake_delay IOVAR not present, proceed
[ 1151.638794] [  T542] [dhd-sdio-1] dhd_bus_check_srmemsize : srmem_size no need to change.
[ 1151.643662] [ T4883] apexd: Scanning /system/apex for pre-installed ApexFiles
[ 1151.644120] [ T4883] apexd: Found pre-installed APEX /system/apex/com.android.adbd.capex
[ 1151.644607] [ T4883] apexd: Found pre-installed APEX /system/apex/com.android.adservices.capex
[ 1151.644951] [ T4883] apexd: Found pre-installed APEX /system/apex/com.android.sdkext.apex
[ 1151.645367] [ T4883] apexd: Found pre-installed APEX /system/apex/com.android.appsearch.capex
[ 1151.645592] [ T4883] apexd: Found pre-installed APEX /system/apex/com.android.art.capex
[ 1151.645799] [ T4883] apexd: Found pre-installed APEX /system/apex/com.android.resolv.capex
[ 1151.645991] [ T4883] apexd: Found pre-installed APEX /system/apex/com.android.apex.cts.shim.apex
[ 1151.646375] [ T4883] apexd: Found pre-installed APEX /system/apex/com.android.mediaprovider.capex
[ 1151.646638] [ T4883] apexd: Found pre-installed APEX /system/apex/com.android.btservices.apex
[ 1151.646868] [ T4883] apexd: Found pre-installed APEX /system/apex/com.android.ipsec.capex
[ 1151.647054] [ T4883] apexd: Found pre-installed APEX /system/apex/com.android.rkpd.apex
[ 1151.647270] [ T4883] apexd: Found pre-installed APEX /system/apex/com.android.configinfrastructure.capex
[ 1151.647514] [ T4883] apexd: Found pre-installed APEX /system/apex/com.android.virt.apex
[ 1151.647768] [ T4883] apexd: Found pre-installed APEX /system/apex/com.android.conscrypt.capex
[ 1151.648022] [ T4883] apexd: Found pre-installed APEX /system/apex/com.android.scheduling.capex
[ 1151.648238] [ T4883] apexd: Found pre-installed APEX /system/apex/com.android.healthfitness.apex
[ 1151.648439] [ T4883] apexd: Found pre-installed APEX /system/apex/com.android.tethering.capex
[ 1151.648625] [ T4883] apexd: Found pre-installed APEX /system/apex/com.android.extservices.capex
[ 1151.648804] [ T4883] apexd: Found pre-installed APEX /system/apex/com.android.tzdata.apex
[ 1151.649073] [ T4883] apexd: Found pre-installed APEX /system/apex/com.android.media.swcodec.capex
[ 1151.649312] [ T4883] apexd: Found pre-installed APEX /system/apex/com.android.media.capex
[ 1151.649491] [ T4883] apexd: Found pre-installed APEX /system/apex/com.android.i18n.apex
[ 1151.649701] [ T4883] apexd: Found pre-installed APEX /system/apex/com.android.runtime.apex
[ 1151.649896] [ T4883] apexd: Found pre-installed APEX /system/apex/com.android.ondevicepersonalization.capex
[ 1151.650075] [ T4883] apexd: Found pre-installed APEX /system/apex/com.android.uwb.capex
[ 1151.650251] [ T4883] apexd: Found pre-installed APEX /system/apex/com.android.devicelock.apex
[ 1151.650445] [ T4883] apexd: Found pre-installed APEX /system/apex/com.android.vndk.current.apex
[ 1151.650635] [ T4883] apexd: Found pre-installed APEX /system/apex/com.android.permission.capex
[ 1151.650805] [ T4883] apexd: Found pre-installed APEX /system/apex/com.android.os.statsd.apex
[ 1151.651000] [ T4883] apexd: Found pre-installed APEX /system/apex/com.android.neuralnetworks.capex
[ 1151.651183] [ T4883] apexd: Found pre-installed APEX /system/apex/com.android.wifi.capex
[ 1151.651365] [ T4883] apexd: Scanning /system_ext/apex for pre-installed ApexFiles
[ 1151.651391] [ T4883] apexd: /system_ext/apex does not exist. Skipping
[ 1151.651403] [ T4883] apexd: Scanning /product/apex for pre-installed ApexFiles
[ 1151.651420] [ T4883] apexd: /product/apex does not exist. Skipping
[ 1151.651432] [ T4883] apexd: Scanning /vendor/apex for pre-installed ApexFiles
[ 1151.651506] [ T4883] apexd: Found pre-installed APEX /vendor/apex/com.rockchip.hardware.sensors.apex
[ 1151.651808] [ T4883] apexd: No block apex metadata partition found, not adding block apexes
[ 1151.651825] [ T4883] apexd: Populating APEX database from mounts...
[ 1151.652359] [ T4883] apexd: Found "/apex/com.android.vndk.v34@1" backed by file /system/apex/com.android.vndk.current.apex
[ 1151.652427] [ T4883] apexd: Found "/apex/com.android.btservices@340090000" backed by file /system/apex/com.android.btservices.apex
[ 1151.652600] [ T4883] apexd: Found "/apex/com.android.devicelock@1" backed by file /system/apex/com.android.devicelock.apex
[ 1151.652774] [ T4883] apexd: Found "/apex/com.android.extservices@340090000" backed by file /data/apex/decompressed/<EMAIL>
[ 1151.652836] [ T4883] apexd: Found "/apex/com.android.tzdata@340090000" backed by file /system/apex/com.android.tzdata.apex
[ 1151.652901] [ T4883] apexd: Found "/apex/com.android.sdkext@340090000" backed by file /system/apex/com.android.sdkext.apex
[ 1151.652957] [ T4883] apexd: Found "/apex/com.android.rkpd@1" backed by file /system/apex/com.android.rkpd.apex
[ 1151.653042] [ T4883] apexd: Found "/apex/com.android.runtime@1" backed by file /system/apex/com.android.runtime.apex
[ 1151.653049] [   T80] enter into es8156_set_bias_level, level = 2
[ 1151.653114] [ T4883] apexd: Found "/apex/com.android.apex.cts.shim@1" backed by file /system/apex/com.android.apex.cts.shim.apex
[ 1151.653172] [ T4883] apexd: Found "/apex/com.android.virt@2" backed by file /system/apex/com.android.virt.apex
[ 1151.653227] [ T4883] apexd: Found "/apex/com.android.i18n@1" backed by file /system/apex/com.android.i18n.apex
[ 1151.653295] [ T4883] apexd: Found "/apex/com.rockchip.hardware.sensors@1" backed by file /vendor/apex/com.rockchip.hardware.sensors.apex
[ 1151.653366] [ T4883] apexd: Found "/apex/com.android.healthfitness@340090000" backed by file /system/apex/com.android.healthfitness.apex
[ 1151.653520] [ T4883] apexd: Found "/apex/com.android.tethering@340090000" backed by file /data/apex/decompressed/<EMAIL>
[ 1151.653644] [ T4883] apexd: Found "/apex/com.android.wifi@340090000" backed by file /data/apex/decompressed/<EMAIL>
[ 1151.653768] [ T4883] apexd: Found "/apex/com.android.media.swcodec@340090000" backed by file /data/apex/decompressed/<EMAIL>
[ 1151.653897] [ T4883] apexd: Found "/apex/com.android.adbd@340090000" backed by file /data/apex/decompressed/<EMAIL>
[ 1151.654021] [ T4883] apexd: Found "/apex/com.android.ipsec@340090000" backed by file /data/apex/decompressed/<EMAIL>
[ 1151.654144] [ T4883] apexd: Found "/apex/com.android.appsearch@340090000" backed by file /data/apex/decompressed/<EMAIL>
[ 1151.654270] [ T4883] apexd: Found "/apex/com.android.adservices@340090000" backed by file /data/apex/decompressed/<EMAIL>
[ 1151.654340] [ T4883] apexd: Found "/apex/com.android.os.statsd@340090000" backed by file /system/apex/com.android.os.statsd.apex
[ 1151.654475] [ T4883] apexd: Found "/apex/com.android.resolv@340090000" backed by file /data/apex/decompressed/<EMAIL>
[ 1151.654602] [ T4883] apexd: Found "/apex/com.android.uwb@340090000" backed by file /data/apex/decompressed/<EMAIL>
[ 1151.654736] [   T80] enter into es8156_set_bias_level, level = 3
[ 1151.654740] [ T4883] apexd: Found "/apex/com.android.mediaprovider@340090000" backed by file /data/apex/decompressed/<EMAIL>
[ 1151.654966] [ T4883] apexd: Found "/apex/com.android.media@340090000" backed by file /data/apex/decompressed/<EMAIL>
[ 1151.655106] [ T4883] apexd: Found "/apex/com.android.configinfrastructure@340090000" backed by file /data/apex/decompressed/<EMAIL>
[ 1151.655264] [ T4883] apexd: Found "/apex/com.android.scheduling@340090000" backed by file /data/apex/decompressed/<EMAIL>
[ 1151.655408] [ T4883] apexd: Found "/apex/com.android.conscrypt@340090000" backed by file /data/apex/decompressed/<EMAIL>
[ 1151.655541] [ T4883] apexd: Found "/apex/com.android.art@340090000" backed by file /data/apex/decompressed/<EMAIL>
[ 1151.655687] [ T4883] apexd: Found "/apex/com.android.permission@340090000" backed by file /data/apex/decompressed/<EMAIL>
[ 1151.655844] [ T4883] apexd: Found "/apex/com.android.ondevicepersonalization@340090000" backed by file /data/apex/decompressed/<EMAIL>
[ 1151.656006] [ T4883] apexd: Found "/apex/com.android.neuralnetworks@340090000" backed by file /data/apex/decompressed/<EMAIL>
[ 1151.656071] [ T4883] apexd: 32 packages restored.
[ 1151.656107] [ T4883] apexd: Scanning /data/apex/active for data ApexFiles
[ 1151.656234] [ T4883] AidlLazyServiceRegistrar: Registering service apexservice
[ 1151.657266] [  T276] BpBinder: onLastStrongRef automatically unlinking death recipients: 
[ 1151.658464] [ T4883] AidlLazyServiceRegistrar: Trying to shut down the service. No clients in use for any service in process.
[ 1151.658818] [ T4885] apexd: markBootCompleted() received by ApexService
[ 1151.658821] [  T276] servicemanager: Tried to unregister apexservice, but there is about to be a client.
[ 1151.658979] [ T4883] AidlLazyServiceRegistrar: Failed to unregister service apexservice (Status(-5, EX_ILLEGAL_STATE): 'Can't unregister, pending client.')
[ 1151.660831] [  T542] [dhd-sdio-1] dhd_conf_set_country : set country CN, revision 38
[ 1151.663621] [  T542] [dhd-sdio-1] dhd_conf_set_country : Country code: CN (CN/38)
[ 1151.666434] [    T1] init: processing action (sys.boot_completed=1) from (/system/etc/init/hw/init.rc:1224)
[ 1151.666797] [    T1] init: starting service 'exec 20 (/bin/rm -rf /data/per_boot)'...
[ 1151.670053] [    T1] init: ... started service 'exec 20 (/bin/rm -rf /data/per_boot)' has pid 4888
[ 1151.670130] [    T1] init: SVC_EXEC service 'exec 20 (/bin/rm -rf /data/per_boot)' pid 4888 (uid 1000 gid 1000+0 context default) started; waiting...
[ 1151.670270] [  T542] [dhd-sdio-1] [wlan1] wl_android_wifi_on : Success
[ 1151.676007] [ T4890] selinux: SELinux: Skipping restorecon on directory(/data/system_ce/0)
[ 1151.678045] [ T4891] selinux: SELinux: Skipping restorecon on directory(/data/vendor_ce/0)
[ 1151.680668] [ T4892] selinux: SELinux: Skipping restorecon on directory(/data/misc_ce/0)
[ 1151.695176] [    T1] init: Service 'exec 20 (/bin/rm -rf /data/per_boot)' (pid 4888) exited with status 0 waiting took 0.026000 seconds
[ 1151.695241] [    T1] init: Sending signal 9 to service 'exec 20 (/bin/rm -rf /data/per_boot)' (pid 4888) process group...
[ 1151.695494] [    T1] libprocessgroup: Successfully killed process cgroup uid 1000 pid 4888 in 0ms
[ 1151.697717] [    T1] init: Encryption policy of /data/per_boot set to aeeddf890fe65226063f067741e1970d v2 modes 1/4 flags 0x2
[ 1151.697839] [    T1] init: processing action (sys.boot_completed=1) from (/vendor/etc/init/hw/init.rk30board.rc:240)
[ 1151.698903] [    T1] init: processing action (sys.boot_completed=1) from (/vendor/etc/init/hw/init.rk3576.rc:2)
[ 1151.701827] [  T204] init: Top-level directory needs encryption action, eg mkdir /data/tof <mode> <uid> <gid> encryption=Require
[ 1151.701827] [  T287] type=1400 audit(1749778127.640:1489): avc:  denied  { write } for  comm="init" name="vehicle" dev="tmpfs" ino=784 scontext=u:r:vendor_init:s0 tcontext=u:object_r:device:s0 tclass=file permissive=1
[ 1151.702028] [  T287] type=1400 audit(1749778127.640:1490): avc:  denied  { setattr } for  comm="init" name="tof" dev="mmcblk0p15" ino=973897 scontext=u:r:vendor_init:s0 tcontext=u:object_r:system_data_file:s0 tclass=dir permissive=1
[ 1151.702165] [  T287] type=1400 audit(1749778127.640:1491): avc:  denied  { read } for  comm="init" name="tof" dev="mmcblk0p15" ino=973897 scontext=u:r:vendor_init:s0 tcontext=u:object_r:system_data_file:s0 tclass=dir permissive=1
[ 1151.702177] [  T204] init: Verified that /data/tof has the encryption policy f108e729abe705dc9c1118a9cab78588 v2 modes 1/4 flags 0x2
[ 1151.702237] [  T287] type=1400 audit(1749778127.640:1492): avc:  denied  { open } for  comm="init" path="/data/tof" dev="mmcblk0p15" ino=973897 scontext=u:r:vendor_init:s0 tcontext=u:object_r:system_data_file:s0 tclass=dir permissive=1
[ 1151.702301] [  T287] type=1400 audit(1749778127.640:1493): avc:  denied  { ioctl } for  comm="init" path="/data/tof" dev="mmcblk0p15" ino=973897 ioctlcmd=0x6615 scontext=u:r:vendor_init:s0 tcontext=u:object_r:system_data_file:s0 tclass=dir permissive=1
[ 1151.702350] [  T204] init: Top-level directory needs encryption action, eg mkdir /data/hysd <mode> <uid> <gid> encryption=Require
[ 1151.702504] [  T204] init: Verified that /data/hysd has the encryption policy f108e729abe705dc9c1118a9cab78588 v2 modes 1/4 flags 0x2
[ 1151.703207] [    T1] init: processing action (sys.boot_completed=1) from (/system/etc/init/dmesgd.rc:4)
[ 1151.703707] [    T1] init: processing action (sys.boot_completed=1) from (/system/etc/init/flags_health_check.rc:7)
[ 1151.710504] [    T1] init: processing action (sys.boot_completed=1) from (/system/etc/init/logd.rc:34)
[ 1151.711083] [    T1] init: starting service 'logd-auditctl'...
[ 1151.717448] [  T542] [dhd-sdio-1] CFG80211-ERROR) init_roam_cache : roamscan_mode iovar failed. -23
[ 1151.717466] [  T542] [dhd-sdio-1] CFG80211-ERROR) wl_cfg80211_up : Failed to enable RCC.
[ 1151.719032] [    T1] init: ... started service 'logd-auditctl' has pid 4907
[ 1151.719114] [  T542] [dhd-sdio-1] [wlan1] dhd_open : Exit ret=0
[ 1151.719126] [  T542] [dhd-sdio-1] [wlan1] dhd_pri_open : tx queue started
[ 1151.719131] [  T542] [dhd-sdio-1] dhd_pri_open : mutex is released.
[ 1151.719364] [  T542] [dhd-pcie-0] CFG80211-ERROR) wl_cfg80211_netdev_notifier_call : wrong cfg ptr (000000003686c2b9)
[ 1151.719406] [  T108] [dhd-sdio-1] [wlan1] _dhd_set_mac_address : close dev for mac changing
[ 1151.719429] [  T108] [dhd-pcie-0] CFG80211-ERROR) wl_cfg80211_netdev_notifier_call : wrong cfg ptr (000000003686c2b9)
[ 1151.719432] [    T1] init: processing action (sys.boot_completed=1) from (/system/etc/init/perfetto.rc:130)
[ 1151.719547] [  T108] [dhd-sdio-1] [wlan1] dhd_pri_stop : tx queue stopped
[ 1151.719556] [  T108] [dhd-sdio-1] [wlan1] dhd_stop : Enter
[ 1151.719562] [  T108] [dhd-sdio-1] dhd_stop: ######### called for ifidx=0 #########
[ 1151.719571] [  T108] [dhd-sdio-1] [wlan1] dhd_stop : skip chip reset.
[ 1151.719679] [  T108] [dhd-sdio-1] [wlan1] dhd_stop : Exit
[ 1151.720026] [    T1] init: starting service 'exec 21 (/system/bin/perfetto -c /data/misc/perfetto-configs/stopboottracetrigger.pbtxt --txt)'...
[ 1151.720098] [  T108] [dhd-pcie-0] CFG80211-ERROR) wl_cfg80211_netdev_notifier_call : wrong cfg ptr (000000003686c2b9)
[ 1151.724636] [  T108] [dhd-sdio-1] [wlan1] _dhd_set_mac_address : MACID 9c:b8:b4:8e:35:e4 is overwritten
[ 1151.724670] [  T108] [dhd-pcie-0] CFG80211-ERROR) wl_cfg80211_netdev_notifier_call : wrong cfg ptr (000000003686c2b9)
[ 1151.724685] [  T108] [dhd-sdio-1] dhd_pri_open : no mutex held
[ 1151.724689] [  T108] [dhd-sdio-1] dhd_pri_open : set mutex lock
[ 1151.724695] [  T108] [dhd-sdio-1] [wlan1] dhd_open : Primary net_device is already up
[ 1151.724707] [  T108] [dhd-sdio-1] [wlan1] dhd_pri_open : tx queue started
[ 1151.724712] [  T108] [dhd-sdio-1] dhd_pri_open : mutex is released.
[ 1151.724935] [  T108] [dhd-pcie-0] CFG80211-ERROR) wl_cfg80211_netdev_notifier_call : wrong cfg ptr (000000003686c2b9)
[ 1151.724958] [  T108] [dhd-sdio-1] [wlan1] _dhd_set_mac_address : notify mac changed done
[ 1151.726040] [    T1] init: ... started service 'exec 21 (/system/bin/perfetto -c /data/misc/perfetto-configs/stopboottracetrigger.pbtxt --txt)' has pid 4911
[ 1151.726291] [    T1] init: SVC_EXEC service 'exec 21 (/system/bin/perfetto -c /data/misc/perfetto-configs/stopboottracetrigger.pbtxt --txt)' pid 4911 (uid 0 gid 0+0 context default) started; waiting...
[ 1151.733344] [    T1] init: Service 'logd-auditctl' (pid 4907) exited with status 0 oneshot service took 0.018000 seconds in background
[ 1151.733383] [    T1] init: Sending signal 9 to service 'logd-auditctl' (pid 4907) process group...
[ 1151.733534] [    T1] libprocessgroup: Successfully killed process cgroup uid 1036 pid 4907 in 0ms
[ 1151.742641] [    T1] init: Service 'exec 21 (/system/bin/perfetto -c /data/misc/perfetto-configs/stopboottracetrigger.pbtxt --txt)' (pid 4911) exited with status 1 waiting took 0.019000 seconds
[ 1151.742671] [    T1] init: Sending signal 9 to service 'exec 21 (/system/bin/perfetto -c /data/misc/perfetto-configs/stopboottracetrigger.pbtxt --txt)' (pid 4911) process group...
[ 1151.742774] [    T1] libprocessgroup: Successfully killed process cgroup uid 0 pid 4911 in 0ms
[ 1151.743036] [    T1] init: processing action (sys.boot_completed=1 && sys.wifitracing.started=1 && wifi.interface=*) from (/system/etc/init/wifi.rc:98)
[ 1151.744567] [    T1] init: processing action (sys.boot_completed=1) from (/vendor/etc/init/init.tune_io.rc:12)
[ 1151.749648] [    T1] init: processing action (persist.sys.zram_enabled=1 && sys-boot-completed-set) from (/vendor/etc/init/hw/init.rk30board.rc:253)
[ 1151.750108] [    T1] zram: Cannot change disksize for initialized device
[ 1151.771961] [ T4855] [dhd-sdio-1] wl_android_priv_cmd: Android private cmd "SET_AP_WPS_P2P_IE 1" on wlan1
[ 1151.772026] [ T4855] [dhd-sdio-1] wl_android_priv_cmd: Android private cmd "SET_AP_WPS_P2P_IE 2" on wlan1
[ 1151.772046] [ T4855] [dhd-sdio-1] wl_android_priv_cmd: Android private cmd "SET_AP_WPS_P2P_IE 4" on wlan1
[ 1151.772980] [    T1] mkswap: mkswap: xwrite: Text file busy
[ 1151.773877] [    T1] mkswap: mkswap terminated by exit(1)
[ 1151.773877] [    T1] 
[ 1151.774015] [    T1] init: [libfs_mgr] mkswap failed for /dev/block/zram0
[ 1151.774085] [    T1] init: Command 'swapon_all /vendor/etc/fstab_swap.ext${vendor.ext_ram}' action=persist.sys.zram_enabled=1 && sys-boot-completed-set (/vendor/etc/init/hw/init.rk30board.rc:254) took 24ms and failed: fs_mgr_swapon_all() failed
[ 1151.774300] [    T1] init: processing action (bootreceiver.enable=1 && dmesgd.start=1 && ro.product.cpu.abilist64=*) from (/system/etc/init/dmesgd.rc:12)
[ 1151.774521] [    T1] init: starting service 'dmesgd'...
[ 1151.776293] [  T347] [dhd] STATIC-MSG) dhd_wlan_mem_prealloc : bus_type 3, index 1, section 8, size 44584
[ 1151.776338] [  T347] [dhd-sdio-1] dhd_wlfc_enable: proptx=1, ptxmode=2, ret=0
[ 1151.776690] [ T4855] [dhd-sdio-1] [wlan1] wl_cfg80211_set_channel : netdev_ifidx(11) target channel(5g-36 20MHz)
[ 1151.777217] [ T4855] [dhd-sdio-1] [wlan1] wl_cfg80211_set_channel : hostapd bw(20MHz) => chip bw(80MHz)
[ 1151.780078] [    T1] init: ... started service 'dmesgd' has pid 4916
[ 1151.781351] [    T1] init: processing action (sys.user.0.ce_available=true) from (/system/etc/init/wifi.rc:21)
[ 1151.784027] [    T1] selinux: SELinux: Skipping restorecon on directory(/data/misc_ce/0/apexdata/com.android.wifi)
[ 1151.807744] [  T347] [dhd-sdio-1] _dhd_wlfc_mac_entry_update():2047, entry(32)
[ 1151.808250] [ T4855] [dhd-sdio-1] CFG80211-ERROR) wl_cfg80211_bcn_bringup_ap : failed to disable uapsd, error=-5
[ 1151.808547] [  T347] [dhd-sdio-1] _dhd_wlfc_mac_entry_update():2047, entry(32)
[ 1151.811265] [ T4855] [dhd-sdio-1] [wlan1] wl_cfg80211_bcn_bringup_ap : Creating AP with sec=wpa2/psk/mfpn/aes
[ 1151.834050] [  T276] servicemanager: Notifying apexservice they do (previously: don't) have clients when service is guaranteed to be in use
[ 1151.834277] [ T4887] AidlLazyServiceRegistrar: Process has 1 (of 1 available) client(s) in use after notification apexservice has clients: 1
[ 1151.834567] [ T4887] apexd: destroyCeSnapshotsNotSpecified() received by ApexService user_id : 0 retain_rollback_ids : []
[ 1151.881321] [  T347] [dhd-sdio-1] WLC_E_LINK: idx:0, action:UP, iftype:STA, [9c:b8:b4:8e:35:e4]
[ 1151.881459] [  T174] [dhd-sdio-1] [wlan1] wl_iw_event : Link UP with 9c:b8:b4:8e:35:e4
[ 1151.881486] [  T174] [dhd-sdio-1] [wlan1] wl_ext_iapsta_link : Link up w/o creating? (etype=16)
[ 1151.883827] [ T3892] [dhd-sdio-1] [wlan1] wl_notify_connect_status_ap : AP/GO Link up (5g-36 80MHz)
[ 1151.895360] [ T4855] [dhd-sdio-1] [wlan1] wl_cfg80211_del_station : Disconnect STA : ff:ff:ff:ff:ff:ff scb_val.val 3
[ 1151.903394] [  T484] healthd: battery l=50 v=3300 t=2.6 h=2 st=3 c=-1600 fc=100 chg=au
[ 1151.954830] [  T276] servicemanager: Found android.hardware.wifi.IWifi/default in device VINTF manifest.
[ 1151.976222] [  T276] servicemanager: Found android.hardware.wifi.IWifi/default in device VINTF manifest.
[ 1151.998239] [  T276] servicemanager: Found android.hardware.wifi.IWifi/default in device VINTF manifest.
[ 1152.003499] [  T276] servicemanager: Found android.hardware.wifi.IWifi/default in device VINTF manifest.
[ 1152.082558] [  T276] servicemanager: Could not find android.hardware.tetheroffload.IOffload/default in the VINTF manifest.
[ 1152.115023] [  T276] servicemanager: Found android.hardware.wifi.IWifi/default in device VINTF manifest.
[ 1152.125730] [  T276] servicemanager: Found android.hardware.wifi.IWifi/default in device VINTF manifest.
[ 1152.136220] [  T276] servicemanager: Found android.hardware.wifi.IWifi/default in device VINTF manifest.
[ 1152.141965] [  T276] servicemanager: Found android.hardware.wifi.IWifi/default in device VINTF manifest.
[ 1152.147406] [  T276] servicemanager: Found android.hardware.wifi.IWifi/default in device VINTF manifest.
[ 1152.152453] [  T276] servicemanager: Found android.hardware.wifi.IWifi/default in device VINTF manifest.
[ 1152.158490] [  T276] servicemanager: Found android.hardware.wifi.IWifi/default in device VINTF manifest.
[ 1152.164129] [  T276] servicemanager: Found android.hardware.wifi.IWifi/default in device VINTF manifest.
[ 1152.281410] [ T4211] [dhd-pcie-0] [wlan0] wl_run_escan : LEGACY_SCAN sync ID: 10, bssidx: 0
[ 1152.346703] [ T4823] [dhd-pcie-0] wl_android_priv_cmd: Android private cmd "BTCOEXSCAN-STOP" on wlan0
[ 1152.388256] [    T1] init: Service 'dmesgd' (pid 4916) exited with status 0 oneshot service took 0.611000 seconds in background
[ 1152.388344] [    T1] init: Sending signal 9 to service 'dmesgd' (pid 4916) process group...
[ 1152.389035] [    T1] libprocessgroup: Successfully killed process cgroup uid 1086 pid 4916 in 0ms
[ 1152.402479] [ T4306] audit: audit_lost=342 audit_rate_limit=5 audit_backlog_limit=64
[ 1152.402522] [ T4306] audit: rate limit exceeded
[ 1152.453606] [ T4949] dwc3 23000000.usb: device reset
[ 1152.498979] [  T276] servicemanager: Found android.hardware.bluetooth.audio.IBluetoothAudioProviderFactory/default in device VINTF manifest.
[ 1152.499455] [  T276] servicemanager: Found android.hardware.bluetooth.audio.IBluetoothAudioProviderFactory/default in device VINTF manifest.
[ 1152.501482] [ T3900] android_work: sent uevent USB_STATE=CONNECTED
[ 1152.501551] [ T3900] rk_send_wakeup_key: KEY_WAKEUP event sent
[ 1152.596839] [ T3900] android_work: sent uevent USB_STATE=CONFIGURED
[ 1152.680666] [ T4823] [dhd-pcie-0] wl_android_priv_cmd: Android private cmd "BTCOEXSCAN-STOP" on wlan0
[ 1152.825822] [  T276] servicemanager: Found android.hardware.graphics.allocator.IAllocator/default in device VINTF manifest.
[ 1152.836900] [  T276] servicemanager: Found android.hardware.graphics.allocator.IAllocator/default in device VINTF manifest.
[ 1152.936568] [  T276] servicemanager: Found android.hardware.graphics.allocator.IAllocator/default in device VINTF manifest.
[ 1152.952895] [    T1] init: starting service 'simple_bugreportd'...
[ 1152.953522] [    T1] init: Created socket '/dev/socket/dumpstate', mode 660, user 2000, group 1007
[ 1152.958300] [    T1] init: ... started service 'simple_bugreportd' has pid 5303
[ 1152.958477] [    T1] init: Control message: Processed ctl.start for 'simple_bugreportd' from pid: 4276 (system_server)
[ 1152.960913] [  T276] servicemanager: Found android.hardware.graphics.allocator.IAllocator/default in device VINTF manifest.
[ 1153.098592] [  T287] type=1400 audit(1749778129.036:1501): avc:  denied  { read } for  comm="binder:318_3" name="wakeup4" dev="sysfs" ino=39988 scontext=u:r:system_suspend:s0 tcontext=u:object_r:sysfs:s0 tclass=dir permissive=1
[ 1153.098784] [  T287] type=1400 audit(1749778129.036:1502): avc:  denied  { open } for  comm="binder:318_3" path="/sys/devices/virtual/power_supply/test_ac/wakeup4" dev="sysfs" ino=39988 scontext=u:r:system_suspend:s0 tcontext=u:object_r:sysfs:s0 tclass=dir permissive=1
[ 1153.098872] [  T287] type=1400 audit(1749778129.036:1503): avc:  denied  { read } for  comm="binder:318_3" name="event_count" dev="sysfs" ino=39995 scontext=u:r:system_suspend:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[ 1153.098937] [  T287] type=1400 audit(1749778129.036:1504): avc:  denied  { open } for  comm="binder:318_3" path="/sys/devices/virtual/power_supply/test_ac/wakeup4/event_count" dev="sysfs" ino=39995 scontext=u:r:system_suspend:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[ 1153.098999] [  T287] type=1400 audit(1749778129.036:1505): avc:  denied  { getattr } for  comm="binder:318_3" path="/sys/devices/virtual/power_supply/test_ac/wakeup4/event_count" dev="sysfs" ino=39995 scontext=u:r:system_suspend:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[ 1153.123757] [  T284] logd: logdr: UID=0 GID=0 PID=5348 n tail=0 logMask=99 pid=0 start=0ns deadline=0ns
[ 1153.136298] [  T285] logd: Skipping entries from slow reader, pid 5348, fd 12, from LogBuffer::Prune()
[ 1153.220781] [  T285] logd: Skipping entries from slow reader, pid 5348, fd 12, from LogBuffer::Prune()
[ 1153.711998] [  T284] logd: logdr: UID=0 GID=0 PID=5589 n tail=0 logMask=4 pid=0 start=0ns deadline=0ns
[ 1153.906058] [  T276] servicemanager: Found android.hardware.graphics.allocator.IAllocator/default in device VINTF manifest.
[ 1153.910548] [  T276] servicemanager: Found android.hardware.graphics.allocator.IAllocator/default in device VINTF manifest.
[ 1154.337706] [  T287] type=1400 audit(1749778130.272:1506): avc:  denied  { getattr } for  comm="mount" path="/data_mirror/cur_profiles" dev="mmcblk0p15" ino=1424061 scontext=u:r:dumpstate:s0 tcontext=u:object_r:user_profile_root_file:s0 tclass=dir permissive=1
[ 1154.337920] [  T287] type=1400 audit(1749778130.276:1507): avc:  denied  { getattr } for  comm="mount" path="/data_mirror/ref_profiles" dev="mmcblk0p15" ino=1424062 scontext=u:r:dumpstate:s0 tcontext=u:object_r:user_profile_data_file:s0 tclass=dir permissive=1
[ 1154.341651] [  T287] type=1400 audit(1749778130.276:1508): avc:  denied  { search } for  comm="mount" name="pass_through" dev="tmpfs" ino=16 scontext=u:r:dumpstate:s0 tcontext=u:object_r:mnt_pass_through_file:s0 tclass=dir permissive=1
[ 1154.341845] [  T287] type=1400 audit(1749778130.276:1509): avc:  denied  { getattr } for  comm="mount" path="/mnt/pass_through/0/emulated" dev="mmcblk0p15" ino=728377 scontext=u:r:dumpstate:s0 tcontext=u:object_r:media_userdir_file:s0 tclass=dir permissive=1
[ 1154.372076] [  T287] type=1400 audit(1749778130.308:1510): avc:  denied  { dac_read_search } for  comm="android.fg" capability=2  scontext=u:r:system_server:s0 tcontext=u:r:system_server:s0 tclass=capability permissive=1 bug=b/228030183
[ 1154.383034] [ T5770] audit: audit_lost=343 audit_rate_limit=5 audit_backlog_limit=64
[ 1154.383064] [ T5770] audit: rate limit exceeded
[ 1154.785585] [ T2666] logd: start watching /data/system/packages.list ...
[ 1154.786897] [ T2666] logd: ReadPackageList, total packages: 101
[ 1154.914034] [  T276] servicemanager: Notifying apexservice they don't (previously: do) have clients when we now have no record of a client
[ 1154.914570] [ T4887] AidlLazyServiceRegistrar: Process has 0 (of 1 available) client(s) in use after notification apexservice has clients: 0
[ 1154.914616] [ T4887] AidlLazyServiceRegistrar: Trying to shut down the service. No clients in use for any service in process.
[ 1154.915040] [  T276] servicemanager: Unregistering apexservice
[ 1154.915110] [  T276] BpBinder: onLastStrongRef automatically unlinking death recipients: 
[ 1154.915388] [ T4887] AidlLazyServiceRegistrar: Unregistered all clients and exiting
[ 1154.921168] [    T1] init: Service 'apexd' (pid 4883) exited with status 0 oneshot service took 3.469000 seconds in background
[ 1154.921212] [    T1] init: Sending signal 9 to service 'apexd' (pid 4883) process group...
[ 1154.921472] [    T1] libprocessgroup: Successfully killed process cgroup uid 0 pid 4883 in 0ms
[ 1155.448901] [  T287] type=1400 audit(1749778131.384:1517): avc:  denied  { ioctl } for  comm="d.eshare.server" path="socket:[158487]" dev="sockfs" ino=158487 ioctlcmd=0x8927 scontext=u:r:system_app:s0 tcontext=u:r:system_app:s0 tclass=udp_socket permissive=1
[ 1155.609167] [  T287] type=1400 audit(1749778131.544:1518): avc:  denied  { read } for  comm="hdmi@1.0-servic" name="audio_present" dev="sysfs" ino=44688 scontext=u:r:hal_hdmi_default:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[ 1155.609350] [  T287] type=1400 audit(1749778131.544:1519): avc:  denied  { open } for  comm="hdmi@1.0-servic" path="/sys/devices/platform/2ac80000.i2c/i2c-5/5-002b/audio_present" dev="sysfs" ino=44688 scontext=u:r:hal_hdmi_default:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[ 1155.875825] [  T287] type=1400 audit(1749778131.812:1520): avc:  denied  { ioctl } for  comm="m.ecloud.emedia" path="socket:[163593]" dev="sockfs" ino=163593 ioctlcmd=0x8927 scontext=u:r:platform_app:s0:c512,c768 tcontext=u:r:platform_app:s0:c512,c768 tclass=udp_socket permissive=1 app=com.ecloud.emedia
[ 1155.921006] [ T4177] audit: audit_lost=349 audit_rate_limit=5 audit_backlog_limit=64
[ 1155.921041] [ T4177] audit: rate limit exceeded
[ 1155.921848] [  T287] type=1400 audit(1749778131.856:1521): avc:  denied  { read } for  comm="binder:634_4" name="address" dev="sysfs" ino=34757 scontext=u:r:systemmanagerserver:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[ 1156.064095] [  T343] binder_debug: 59 callbacks suppressed
[ 1156.064103] [  T343] binder: undelivered transaction 1593002, process died.
[ 1156.065202] [    T1] init: Service 'bootanim' (pid 4161) exited with status 0 oneshot service took 14.227000 seconds in background
[ 1156.065246] [    T1] init: Sending signal 9 to service 'bootanim' (pid 4161) process group...
[ 1156.065504] [    T1] libprocessgroup: Successfully killed process cgroup uid 1003 pid 4161 in 0ms
[ 1156.525422] [  T287] type=1400 audit(1749778132.460:1526): avc:  denied  { open } for  comm="ecloud.eairplay" path="/dev/__properties__/u:object_r:serialno_prop:s0" dev="tmpfs" ino=279 scontext=u:r:system_app:s0 tcontext=u:object_r:serialno_prop:s0 tclass=file permissive=1
[ 1156.527864] [  T287] type=1400 audit(1749778132.460:1527): avc:  denied  { getattr } for  comm="ecloud.eairplay" path="/dev/__properties__/u:object_r:serialno_prop:s0" dev="tmpfs" ino=279 scontext=u:r:system_app:s0 tcontext=u:object_r:serialno_prop:s0 tclass=file permissive=1
[ 1156.528013] [  T287] type=1400 audit(1749778132.460:1528): avc:  denied  { map } for  comm="ecloud.eairplay" path="/dev/__properties__/u:object_r:serialno_prop:s0" dev="tmpfs" ino=279 scontext=u:r:system_app:s0 tcontext=u:object_r:serialno_prop:s0 tclass=file permissive=1
[ 1157.454721] [  T287] type=1400 audit(1749778133.392:1529): avc:  denied  { ioctl } for  comm="d.eshare.server" path="socket:[178249]" dev="sockfs" ino=178249 ioctlcmd=0x8927 scontext=u:r:system_app:s0 tcontext=u:r:system_app:s0 tclass=udp_socket permissive=1
[ 1158.465472] [  T287] type=1400 audit(1749778134.404:1530): avc:  denied  { read } for  comm="binder:634_4" name="address" dev="sysfs" ino=34757 scontext=u:r:systemmanagerserver:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[ 1158.465620] [  T287] type=1400 audit(1749778134.404:1531): avc:  denied  { open } for  comm="binder:634_4" path="/sys/devices/platform/2a220000.ethernet/net/eth0/address" dev="sysfs" ino=34757 scontext=u:r:systemmanagerserver:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[ 1158.465684] [  T287] type=1400 audit(1749778134.404:1532): avc:  denied  { getattr } for  comm="binder:634_4" path="/sys/devices/platform/2a220000.ethernet/net/eth0/address" dev="sysfs" ino=34757 scontext=u:r:systemmanagerserver:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[ 1158.658261] [  T287] type=1400 audit(1749778134.592:1533): avc:  denied  { ioctl } for  comm="ecloud.eairplay" path="socket:[158650]" dev="sockfs" ino=158650 ioctlcmd=0x8927 scontext=u:r:system_app:s0 tcontext=u:r:system_app:s0 tclass=udp_socket permissive=1
[ 1159.651593] [    T1] init: Service 'simple_bugreportd' (pid 5303) exited with status 0 oneshot service took 6.696000 seconds in background
[ 1159.651652] [    T1] init: Sending signal 9 to service 'simple_bugreportd' (pid 5303) process group...
[ 1159.651894] [    T1] libprocessgroup: Successfully killed process cgroup uid 0 pid 5303 in 0ms
[ 1159.765497] [    T1] init: Sending signal 9 to service 'idmap2d' (pid 4340) process group...
[ 1159.771006] [    T1] libprocessgroup: Successfully killed process cgroup uid 1000 pid 4340 in 5ms
[ 1159.771727] [    T1] init: Control message: Processed ctl.stop for 'idmap2d' from pid: 4276 (system_server)
[ 1159.772034] [    T1] init: Service 'idmap2d' (pid 4340) received signal 9
[ 1162.069383] [  T146] m02_b_LT6911UXE 5-002b: plugin_detect_irq_handler: present:1
[ 1162.128139] [ T3893] m02_b_LT6911UXE 5-002b: lt6911uxe_delayed_work_hotplug: first call, state=1
[ 1162.128908] [  T287] type=1400 audit(1749778138.064:1534): avc:  denied  { write } for  comm="hdmi@1.0_event" name="property_service" dev="tmpfs" ino=366 scontext=u:r:hal_hdmi_default:s0 tcontext=u:object_r:property_socket:s0 tclass=sock_file permissive=1
[ 1162.129093] [  T287] type=1400 audit(1749778138.064:1535): avc:  denied  { connectto } for  comm="hdmi@1.0_event" path="/dev/socket/property_service" scontext=u:r:hal_hdmi_default:s0 tcontext=u:r:init:s0 tclass=unix_stream_socket permissive=1
[ 1162.129407] [  T287] type=1107 audit(1749778138.068:1536): uid=0 auid=********** ses=********** subj=u:r:init:s0 msg='avc:  denied  { set } for property=vendor.czur.hdmiin pid=511 uid=1000 gid=1000 scontext=u:r:hal_hdmi_default:s0 tcontext=u:object_r:vendor_default_prop:s0 tclass=property_service permissive=1'
[ 1162.129648] [  T545] binder: 545:511 cannot find target node
[ 1162.129667] [  T545] binder: 511:545 transaction call to 0:0 failed 1641621/29189/-22, size 124-16 line 3265
[ 1162.185607] [    T1] init: starting service 'simple_bugreportd'...
[ 1162.186150] [    T1] init: Created socket '/dev/socket/dumpstate', mode 660, user 2000, group 1007
[ 1162.190409] [    T1] init: ... started service 'simple_bugreportd' has pid 6354
[ 1162.190485] [    T1] init: Control message: Processed ctl.start for 'simple_bugreportd' from pid: 4276 (system_server)
[ 1162.190682] [    T1] init: Service 'hdmi_server' (pid 511) received signal 6
[ 1162.190717] [    T1] init: Sending signal 9 to service 'hdmi_server' (pid 511) process group...
[ 1162.190924] [    T1] libprocessgroup: Successfully killed process cgroup uid 1000 pid 511 in 0ms
[ 1162.191677] [    T1] init: Untracked pid 6351 exited with status 0
[ 1162.191706] [    T1] init: Untracked pid 6351 did not have an associated service entry and will not be reaped
[ 1162.191750] [    T1] init: Untracked pid 6353 exited with status 0
[ 1162.191763] [    T1] init: Untracked pid 6353 did not have an associated service entry and will not be reaped
[ 1162.192208] [    T1] init: starting service 'hdmi_server'...
[ 1162.196263] [    T1] init: ... started service 'hdmi_server' has pid 6355
[ 1162.257095] [  T287] type=1400 audit(1749778138.192:1537): avc:  denied  { ioctl } for  comm="m.ecloud.emedia" path="socket:[170125]" dev="sockfs" ino=170125 ioctlcmd=0x8927 scontext=u:r:platform_app:s0:c512,c768 tcontext=u:r:platform_app:s0:c512,c768 tclass=udp_socket permissive=1 app=com.ecloud.emedia
[ 1162.305559] [  T284] logd: logdr: UID=0 GID=0 PID=6362 n tail=0 logMask=99 pid=0 start=0ns deadline=0ns
[ 1162.667436] [  T287] type=1400 audit(1749778138.604:1538): avc:  denied  { ioctl } for  comm="ecloud.eairplay" path="socket:[184124]" dev="sockfs" ino=184124 ioctlcmd=0x8927 scontext=u:r:system_app:s0 tcontext=u:r:system_app:s0 tclass=udp_socket permissive=1
[ 1162.689142] [  T284] logd: logdr: UID=0 GID=0 PID=6364 n tail=0 logMask=4 pid=0 start=0ns deadline=0ns
[ 1162.914058] [  T287] type=1400 audit(1749778138.852:1539): avc:  denied  { getattr } for  comm="mount" path="/data_mirror/cur_profiles" dev="mmcblk0p15" ino=1424061 scontext=u:r:dumpstate:s0 tcontext=u:object_r:user_profile_root_file:s0 tclass=dir permissive=1
[ 1162.914250] [  T287] type=1400 audit(1749778138.852:1540): avc:  denied  { getattr } for  comm="mount" path="/data_mirror/ref_profiles" dev="mmcblk0p15" ino=1424062 scontext=u:r:dumpstate:s0 tcontext=u:object_r:user_profile_data_file:s0 tclass=dir permissive=1
[ 1162.915878] [ T6371] audit: audit_lost=353 audit_rate_limit=5 audit_backlog_limit=64
[ 1162.915905] [ T6371] audit: rate limit exceeded
[ 1163.671382] [  T287] type=1400 audit(1749778139.608:1543): avc:  denied  { ioctl } for  comm="ecloud.eairplay" path="socket:[182616]" dev="sockfs" ino=182616 ioctlcmd=0x8927 scontext=u:r:system_app:s0 tcontext=u:r:system_app:s0 tclass=udp_socket permissive=1
[ 1165.559877] [  T287] type=1400 audit(1749778141.496:1544): avc:  denied  { ioctl } for  comm="m.ecloud.emedia" path="socket:[170135]" dev="sockfs" ino=170135 ioctlcmd=0x8927 scontext=u:r:platform_app:s0:c512,c768 tcontext=u:r:platform_app:s0:c512,c768 tclass=udp_socket permissive=1 app=com.ecloud.emedia
[ 1166.368728] [    T1] init: Service 'simple_bugreportd' (pid 6354) exited with status 0 oneshot service took 4.180000 seconds in background
[ 1166.368802] [    T1] init: Sending signal 9 to service 'simple_bugreportd' (pid 6354) process group...
[ 1166.369090] [    T1] libprocessgroup: Successfully killed process cgroup uid 0 pid 6354 in 0ms
[ 1168.444027] [  T287] type=1400 audit(1749778144.380:1545): avc:  denied  { read } for  comm="binder:634_4" name="address" dev="sysfs" ino=34757 scontext=u:r:systemmanagerserver:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[ 1168.444453] [  T287] type=1400 audit(1749778144.380:1546): avc:  denied  { open } for  comm="binder:634_4" path="/sys/devices/platform/2a220000.ethernet/net/eth0/address" dev="sysfs" ino=34757 scontext=u:r:systemmanagerserver:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[ 1168.445751] [  T287] type=1400 audit(1749778144.380:1547): avc:  denied  { getattr } for  comm="binder:634_4" path="/sys/devices/platform/2a220000.ethernet/net/eth0/address" dev="sysfs" ino=34757 scontext=u:r:systemmanagerserver:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[ 1168.814708] [  T146] m02_b_LT6911UXE 5-002b: plugin_detect_irq_handler: present:0
[ 1168.818066] [  T146] m02_b_LT6911UXE 5-002b: plugin_detect_irq_handler: present:0
[ 1168.878505] [ T3893] m02_b_LT6911UXE 5-002b: lt6911uxe_delayed_work_hotplug: state changed to 0, reset count
[ 1169.428286] [ T3893] m02_b_LT6911UXE 5-002b: lt6911uxe_delayed_work_hotplug: reporting new stable state: 0
[ 1169.428643] [ T3893] m02_b_LT6911UXE 5-002b: lt6911uxe_delayed_work_hotplug: HDMI disconnected, marking sound card as disconnected
[ 1169.428674] [ T3893] m02_b_LT6911UXE 5-002b: lt6911uxe_delayed_work_hotplug: current process: kworker/1:0 (3893)
[ 1169.428693] [ T3893] m02_b_LT6911UXE 5-002b: lt6911uxe_delayed_work_hotplug: g_pcm_active=0
[ 1169.428708] [ T3893] m02_b_LT6911UXE 5-002b: lt6911uxe_delayed_work_hotplug: PCM stream not active
[ 1169.430322] [  T287] type=1400 audit(1749778145.368:1548): avc:  denied  { write } for  comm="hdmi@1.0_event" name="property_service" dev="tmpfs" ino=366 scontext=u:r:hal_hdmi_default:s0 tcontext=u:object_r:property_socket:s0 tclass=sock_file permissive=1
[ 1169.430549] [  T287] type=1400 audit(1749778145.368:1549): avc:  denied  { connectto } for  comm="hdmi@1.0_event" path="/dev/socket/property_service" scontext=u:r:hal_hdmi_default:s0 tcontext=u:r:init:s0 tclass=unix_stream_socket permissive=1
[ 1169.430688] [  T287] type=1400 audit(1749778145.368:1550): avc:  denied  { read } for  comm="UEventObserver" name="uevent" dev="sysfs" ino=37896 scontext=u:r:system_server:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[ 1169.430816] [  T287] type=1400 audit(1749778145.368:1551): avc:  denied  { open } for  comm="UEventObserver" path="/sys/devices/platform/2ac80000.i2c/i2c-5/5-002b/uevent" dev="sysfs" ino=37896 scontext=u:r:system_server:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[ 1169.430949] [  T287] type=1400 audit(1749778145.368:1552): avc:  denied  { getattr } for  comm="UEventObserver" path="/sys/devices/platform/2ac80000.i2c/i2c-5/5-002b/uevent" dev="sysfs" ino=37896 scontext=u:r:system_server:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[ 1172.339312] [ T4211] [dhd-pcie-0] [wlan0] wl_run_escan : LEGACY_SCAN sync ID: 11, bssidx: 0
[ 1172.669053] [ T3900] usb 1-1.3: new low-speed USB device number 5 using xhci-hcd
[ 1172.826479] [ T3900] rk_send_wakeup_key: KEY_WAKEUP event sent
[ 1172.826532] [ T3900] usb 1-1.3: New USB device found, idVendor=18f8, idProduct=0f97, bcdDevice= 1.00
[ 1172.826548] [ T3900] usb 1-1.3: New USB device strings: Mfr=0, Product=1, SerialNumber=0
[ 1172.826561] [ T3900] usb 1-1.3: Product: USB OPTICAL MOUSE 
[ 1172.909426] [ T3900] input: USB OPTICAL MOUSE  as /devices/platform/23400000.usb/xhci-hcd.0.auto/usb1/1-1/1-1.3/1-1.3:1.0/0003:18F8:0F97.0003/input/input12
[ 1172.971040] [  T287] type=1400 audit(1749778148.908:1553): avc:  denied  { dac_read_search } for  comm="InputReader" capability=2  scontext=u:r:system_server:s0 tcontext=u:r:system_server:s0 tclass=capability permissive=1 bug=b/228030183
[ 1172.972391] [ T3900] hid-generic 0003:18F8:0F97.0003: input,hidraw0: USB HID v1.10 Mouse [USB OPTICAL MOUSE ] on usb-xhci-hcd.0.auto-1.3/input0
[ 1172.986893] [ T3900] input: USB OPTICAL MOUSE  Keyboard as /devices/platform/23400000.usb/xhci-hcd.0.auto/usb1/1-1/1-1.3/1-1.3:1.1/0003:18F8:0F97.0004/input/input13
[ 1173.058260] [ T3900] hid-generic 0003:18F8:0F97.0004: input,hiddev96,hidraw1: USB HID v1.10 Keyboard [USB OPTICAL MOUSE ] on usb-xhci-hcd.0.auto-1.3/input1
[ 1173.996028] [  T276] servicemanager: Found android.hardware.graphics.allocator.IAllocator/default in device VINTF manifest.
[ 1174.001213] [  T276] servicemanager: Found android.hardware.graphics.allocator.IAllocator/default in device VINTF manifest.
[ 1174.707318] [  T287] type=1400 audit(1749778150.644:1554): avc:  denied  { ioctl } for  comm="ecloud.eairplay" path="socket:[182753]" dev="sockfs" ino=182753 ioctlcmd=0x8927 scontext=u:r:system_app:s0 tcontext=u:r:system_app:s0 tclass=udp_socket permissive=1
[ 1175.485072] [  T287] type=1400 audit(1749778151.420:1555): avc:  denied  { ioctl } for  comm="m.ecloud.emedia" path="socket:[182759]" dev="sockfs" ino=182759 ioctlcmd=0x8927 scontext=u:r:platform_app:s0:c512,c768 tcontext=u:r:platform_app:s0:c512,c768 tclass=udp_socket permissive=1 app=com.ecloud.emedia
[ 1175.547980] [  T276] servicemanager: Found android.hardware.graphics.allocator.IAllocator/default in device VINTF manifest.
[ 1175.554276] [  T276] servicemanager: Found android.hardware.graphics.allocator.IAllocator/default in device VINTF manifest.
[ 1175.850756] [  T287] type=1400 audit(1749778151.788:1556): avc:  denied  { dac_read_search } for  comm="android.fg" capability=2  scontext=u:r:system_server:s0 tcontext=u:r:system_server:s0 tclass=capability permissive=1 bug=b/228030183
[ 1178.127796] [  T287] type=1400 audit(1749778154.060:1557): avc:  denied  { search } for  comm="DefaultDispatch" name="net" dev="sysfs" ino=22546 scontext=u:r:system_app:s0 tcontext=u:object_r:sysfs_net:s0 tclass=dir permissive=1
[ 1178.131615] [  T287] type=1400 audit(1749778154.060:1558): avc:  denied  { read } for  comm="DefaultDispatch" name="address" dev="sysfs" ino=48004 scontext=u:r:system_app:s0 tcontext=u:object_r:sysfs_net:s0 tclass=file permissive=1
[ 1178.132893] [  T287] type=1400 audit(1749778154.060:1559): avc:  denied  { open } for  comm="DefaultDispatch" path="/sys/devices/virtual/net/wlan0/address" dev="sysfs" ino=48004 scontext=u:r:system_app:s0 tcontext=u:object_r:sysfs_net:s0 tclass=file permissive=1
[ 1178.133102] [  T287] type=1400 audit(1749778154.060:1560): avc:  denied  { getattr } for  comm="DefaultDispatch" path="/sys/devices/virtual/net/wlan0/address" dev="sysfs" ino=48004 scontext=u:r:system_app:s0 tcontext=u:object_r:sysfs_net:s0 tclass=file permissive=1
[ 1178.166267] [ T4177] audit: audit_lost=355 audit_rate_limit=5 audit_backlog_limit=64
[ 1178.166313] [ T4177] audit: rate limit exceeded
[ 1178.220628] [  T287] type=1400 audit(1749778154.104:1561): avc:  denied  { read } for  comm="binder:634_4" name="address" dev="sysfs" ino=34757 scontext=u:r:systemmanagerserver:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[ 1178.220810] [  T287] type=1400 audit(1749778154.104:1562): avc:  denied  { open } for  comm="binder:634_4" path="/sys/devices/platform/2a220000.ethernet/net/eth0/address" dev="sysfs" ino=34757 scontext=u:r:systemmanagerserver:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=1
[ 1181.867702] [  T276] servicemanager: Found android.hardware.graphics.allocator.IAllocator/default in device VINTF manifest.
[ 1181.871120] [  T276] servicemanager: Found android.hardware.graphics.allocator.IAllocator/default in device VINTF manifest.
[ 1182.715865] [  T284] logd: logdr: UID=1000 GID=1000 PID=6504 n tail=0 logMask=99 pid=0 start=0ns deadline=0ns
[ 1182.987172] [  T287] type=1400 audit(1749778158.924:1564): avc:  denied  { syslog_read } for  comm="dmesg" scontext=u:r:system_app:s0 tcontext=u:r:kernel:s0 tclass=system permissive=1

=========COMMAND END (dmesg)=========
