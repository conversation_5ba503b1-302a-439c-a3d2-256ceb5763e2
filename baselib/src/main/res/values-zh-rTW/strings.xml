<resources xmlns:tools="http://schemas.android.com/tools">
    <string name="baselib_app_name">baselib</string>
    <string name="baselib_wifi_saved">已保存</string>
    <string name="baselib_wifi_connected">已連接</string>
    <string name="baselib_wifi_connecting">連接中...</string>
    <string name="baselib_title_bar_back">返回</string>
    <string name="baselib_alert_dialog_title">提示</string>
    <string name="baselib_network_error">網路請求失敗</string>
    <string name="dialog_normal_cancel">取消</string>
    <string name="dialog_normal_confirm">確定</string>
    <string name="dialog_normal_know_it">我知道了</string>
    <string name="dialog_normal_title_tips">提示</string>
    <string name="str_notify_bar_not_login">未登錄</string>
    <string name="toast_operation_failure">操作失敗</string>
    <string name="toast_operation_failure_by_net">操作失敗，請檢查網路</string>
    <string name="toast_operation_success">操作成功</string>
    <string name="float_tip_notice_msg">通知資訊</string>
    <string name="float_tip_network_info">網路資訊</string>
    <string name="float_tip_personal_center">個人中心</string>
    <string name="float_tip_empty">空</string>
    <string name="symbol_ellipsis">…</string>
    <string name="str_sunday">星期日</string>
    <string name="str_monday">星期一</string>
    <string name="str_tuesday">星期二</string>
    <string name="str_wednesday">星期三</string>
    <string name="str_thursday">星期四</string>
    <string name="str_friday">星期五</string>
    <string name="str_saturday">星期六</string>
    <string name="baselib_wifi_conneted_failed">連接失敗</string>
    <string name="wireless_screen">外設模式</string>
    <string name="baselib_wifi_set_portal">【需認證】</string>
    <string name="dialog_title_newByom_request">檢測到新設備調用會議星鏡頭或麥克風，是否切換？</string>
    <string name="baselib_alert_dialog_no_prompt">不再提示</string>
    <string name="screen_short_toast">"截圖可在【文檔】》【拍照 / 截圖】中查看"</string>
    <string name="func_coming_soon">敬請期待</string>
    <string name="comma">，</string>
    <string name="dialog_byom_camera_mic_occupy_hint">外設模式正在佔用麥克風或監視器，是否退出？</string>
    <string name="app_name_army_share_screen">投屏</string>
    <string name="tv_empty_file">無文檔</string>
    <string name="str_create_temp_success1">已上傳！</string>
</resources>
