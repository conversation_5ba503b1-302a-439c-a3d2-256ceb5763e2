<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="500px"
        android:layout_height="360px"
        android:layout_gravity="center"
        android:paddingLeft="40px"
        android:paddingRight="40px"
        app:bl_corners_radius="10px"
        app:bl_solid_color="@color/dialog_bg_color">

        <TextView
            android:id="@+id/normalDialogTitleTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="27px"
            android:textColor="@color/white"
            android:textSize="36px"
            android:textStyle="bold"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="提示" />

        <TextView
            android:id="@+id/normalDialogContentTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="150px"
            android:textColor="@color/white"
            android:textSize="24px"
            android:textStyle="bold"
            android:gravity="center"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="密码错误!" />

        <com.czur.starry.device.baselib.widget.CommonButton
            android:id="@+id/normalDialogCancelBtn"
            android:layout_width="180px"
            android:layout_height="50px"
            android:layout_marginBottom="30px"
            android:text="@string/dialog_normal_cancel"
            android:textSize="20px"
            app:baselib_theme="dark"
            android:layout_marginRight="30px"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@id/normalDialogConfirmBtn" />

        <com.czur.starry.device.baselib.widget.CommonButton
            android:id="@+id/normalDialogConfirmBtn"
            android:layout_width="180px"
            android:layout_height="50px"
            android:text="@string/dialog_normal_confirm"
            android:textSize="20px"
            app:baselib_theme="white2"
            android:layout_marginBottom="30px"
            app:layout_constraintLeft_toRightOf="@id/normalDialogCancelBtn"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>
    </androidx.constraintlayout.widget.ConstraintLayout>


</FrameLayout>