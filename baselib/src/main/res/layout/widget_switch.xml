<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:padding="6px"
    tools:viewBindingIgnore="true">

    <LinearLayout
        android:id="@+id/switchOFF"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal">

        <com.czur.starry.device.baselib.widget.CircleView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="invisible" />

        <TextView
            android:layout_width="0px"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_weight="1"
            android:gravity="center_horizontal"
            android:text="OFF"
            android:textStyle="bold"
            android:textColor="@color/white"
            android:textSize="16px" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/switchON"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal">

        <TextView
            android:layout_width="0px"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_weight="1"
            android:gravity="center_horizontal"
            android:text="ON"
            android:textStyle="bold"
            android:textColor="@color/white"
            android:textSize="16px" />

        <com.czur.starry.device.baselib.widget.CircleView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="invisible" />
    </LinearLayout>

    <com.czur.starry.device.baselib.widget.CircleView
        android:id="@+id/switchCircleView"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

</FrameLayout>