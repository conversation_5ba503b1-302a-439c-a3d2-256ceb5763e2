<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    tools:background="#5879FC"
    tools:viewBindingIgnore="true">

    <EditText
        android:id="@+id/editText"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center_vertical"
        android:singleLine="true"
        android:includeFontPadding="false"
        android:paddingHorizontal="10px"
        android:lines="1"
        android:textColor="@color/white"
        android:background="@null"
        tools:background="#33000000"
        android:textCursorDrawable="@drawable/drawable_base_cursor"
        android:textColorHint="#80FFFFFF"
        android:textSize="48px"
        android:inputType="textNoSuggestions"
        android:imeOptions="actionDone"
        android:nextFocusDown="@id/editText"
        android:textStyle="bold"/>

    <TextView
        android:id="@+id/textView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center_vertical"
        android:textColor="@color/white"
        android:textSize="48px"
        android:textStyle="bold"
        android:lines="1"
        android:ellipsize="end"
        android:includeFontPadding="false"
        android:paddingHorizontal="10px"
        />
</merge>