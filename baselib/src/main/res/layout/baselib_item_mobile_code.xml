<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="70px"
    xmlns:tools="http://schemas.android.com/tools"
    android:paddingLeft="28px"
    android:paddingRight="28px"
    tools:viewBindingIgnore="true"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <TextView
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:textColor="#ffffff"
        android:textSize="24px"
        android:id="@+id/tv_code"
        android:gravity="center"
        android:text="Adadadadada"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>
    <ImageView
        android:id="@+id/iv_selected"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:src="@drawable/ic_hook"
        android:layout_width="21px"
        android:layout_height="22px"/>
</androidx.constraintlayout.widget.ConstraintLayout>