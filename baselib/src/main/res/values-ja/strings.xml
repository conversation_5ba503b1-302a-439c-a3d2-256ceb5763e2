<resources xmlns:tools="http://schemas.android.com/tools">
    <string name="baselib_app_name">baselib</string>
    <string name="baselib_wifi_saved">保存済</string>
    <string name="baselib_wifi_connected">接続済</string>
    <string name="baselib_wifi_connecting">接続中…</string>
    <string name="baselib_wifi_conneted_failed">接続できません。</string>
    <string name="baselib_wifi_set_portal">認証が必要です</string>
    <string name="baselib_title_bar_back">戻る</string>
    <string name="baselib_alert_dialog_title">通知</string>
    <string name="baselib_network_error">ネットワーク障害</string>
    <string name="dialog_normal_cancel">いいえ</string>
    <string name="dialog_normal_confirm">はい</string>
    <string name="dialog_normal_know_it">メモしました</string>
    <string name="dialog_normal_title_tips">通知</string>
    <string name="str_notify_bar_not_login">ログインしていません</string>
    <string name="toast_operation_failure">操作に失敗</string>
    <string name="toast_operation_failure_by_net">操作できません。インターネット接続を確認してください。</string>
    <string name="toast_operation_success">完了</string>
    <string name="float_tip_notice_msg">メッセージ</string>
    <string name="float_tip_network_info">ネットワーク情報</string>
    <string name="float_tip_personal_center">パーソナルセンター</string>
    <string name="float_tip_empty">空</string>
    <string name="symbol_ellipsis">…</string>
    <string name="str_sunday">日曜日</string>
    <string name="str_monday">月曜日</string>
    <string name="str_tuesday">火曜日</string>
    <string name="str_wednesday">水曜日</string>
    <string name="str_thursday">木曜日</string>
    <string name="str_friday">金曜日</string>
    <string name="str_saturday">土曜日</string>
    <string name="wireless_screen">周辺機器モード</string>
    <string name="dialog_title_newByom_request">新しいデバイスが、StarryHubをカメラまたはマイクとして適用しようと試みています。StarryHubをこの新しいデバイスへ切り替えますか？</string>
    <string name="baselib_alert_dialog_no_prompt">これ以降通知しない。</string>
    <string name="screen_short_toast">"スクリーンショットは【ファイル】》【写真を撮る / スクリーンショット】で見る"</string>
    <string name="func_coming_soon">ご期待ください</string>
    <string name="comma">,</string>
    <string name="dialog_byom_camera_mic_occupy_hint">マイク。またはカメラが周辺モードで占有されている。いずれにしても脱退？</string>
    <string name="app_name_army_share_screen">画面の共有</string>
    <string name="tv_empty_file">空</string>
    <string name="str_create_temp_success1">アップロード完了！</string>
</resources>
