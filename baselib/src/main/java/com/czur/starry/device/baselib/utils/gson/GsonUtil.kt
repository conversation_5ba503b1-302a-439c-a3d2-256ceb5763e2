package com.czur.starry.device.baselib.utils.gson

import com.czur.starry.device.baselib.network.core.parse.GMT8DateJsonDeserializer
import com.google.gson.JsonArray
import com.google.gson.JsonElement

/**
 * Created by 陈丰尧 on 2022/1/12
 */


fun <T> JsonArray.mapNotNull(transform: (JsonElement) -> T?): List<T> {
    val list = mutableListOf<T>()
    forEach { jsonElement ->
        val t = transform(jsonElement)
        if (t != null) {
            list.add(t)
        }
    }
    return list
}

class CallRecordGMT8DateDeserializer : GMT8DateJsonDeserializer() {
    override val datePattern: String
        get() = "yyyy-MM-dd HH:mm:ss"

}