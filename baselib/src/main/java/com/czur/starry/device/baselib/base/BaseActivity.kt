package com.czur.starry.device.baselib.base

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.view.KeyEvent
import android.view.MotionEvent
import android.view.View
import android.view.Window
import android.view.WindowManager
import android.widget.EditText
import androidx.fragment.app.Fragment
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.R
import com.czur.starry.device.baselib.base.listener.KeyDownListener
import com.czur.starry.device.baselib.base.listener.KeyUpListener
import com.czur.starry.device.baselib.base.v2.aty.CZBaseAty
import com.czur.starry.device.baselib.base.v2.aty.IAtyTag
import com.czur.starry.device.baselib.base.v2.fragment.CZBusinessFragment
import com.czur.starry.device.baselib.tips.TipsPool
import com.czur.starry.device.baselib.utils.getLocationOnScreenRect
import com.czur.starry.device.baselib.utils.keyboard.hideKeyboard
import com.czur.starry.device.baselib.view.BaseFragment

/**
 * Created by 陈丰尧 on 12/29/20
 */
private const val TAG = "BaseActivity"

abstract class BaseActivity : CZBaseAty(), IAtyTag {
    companion object {
        const val RESULT_PRE = 1
        const val RESULT_FLOW = "resultFlow"

        const val LAYOUT_NONE = 0
        const val LAYOUT_CUSTOM = -2    // 自定义setContentView的方式

        //信号源切换退出应用
        const val ESHARE_EXIT_ACTION = "com.czur.sharescreen.stopbackground.action"
        const val HDMI_EXIT_ACTION = "com.czur.hdmi.stopbackground.action"
        //语音助手设置eshare
        const val ESHARE_SETTING_ACTION = "com.czur.eshare.setting.action"
    }

    protected enum class DispatcherStyle {
        FOLD,   // 分发给全部Fragment
        QUEUE   // 按顺序分发
    }

    override val atyTag = this::class.java.simpleName

    /**
     * down事件的分发规则
     */
    protected open val keyDownStyle = DispatcherStyle.FOLD

    /**
     * Activity真实的类名
     */
    private val realAtyName: String by lazy {
        this::class.java.simpleName
    }

    // 是否禁止后台
    open val banBackGround: Boolean = false
    open val keepScreenOn: Boolean = false

    @Deprecated("不要需要再使用该属性, 所以页面都可以在会议中显示")
    // 可以在会议中展示
    open val showWhenMeeting: Boolean = true

    /**
     * 是否关心锁屏放啊
     */
    open val careScreenLock: Boolean = false

    /**
     * 是否关闭启动动画
     */
    open val closeBootAnim: Boolean = false

    private var executedFinishMethod = false

    private val fragments: List<Fragment>
        get() {
            val fragments = supportFragmentManager.fragments
            val navHostFragment = fragments.find { it::class.java.simpleName == "NavHostFragment" }
            val childFragment = navHostFragment?.childFragmentManager?.fragments ?: emptyList()
            fragments.addAll(childFragment)
            return fragments
        }

    private val lockReceiver by lazy {
        ScreenLockReceiver()
    }

    private var hasSkip = false

    override fun onCreate(savedInstanceState: Bundle?) {
        if (skipAndFinish()) {
            logTagI(TAG, "${realAtyName}skipAndFinish!!!")
            hasSkip = true
            super.onCreate(savedInstanceState)
            finish()
            return
        }

        if (closeBootAnim) {
            overridePendingTransition(0, R.anim.anim_aty_none)
        }

        super.onCreate(savedInstanceState)
        intent?.let {
            handlePreIntent(it)
        }
        changeKeepScreenOn(keepScreenOn)
        initWindow(window)
        val resId = getLayout()
        if (resId > 0) {
            setContentView(resId)
        } else if (resId == LAYOUT_CUSTOM) {
            customSetContentView()
        }
        initViews()
        initListener()
        initData(savedInstanceState)

        if (careScreenLock) {
            logTagD(realAtyName, "注册锁屏广播")
            registerReceiver(lockReceiver, IntentFilter(Intent.ACTION_SCREEN_OFF))
        }

        // 注册的越靠后, 回调越靠前
        lifecycle.addObserver(object : DefaultLifecycleObserver {
            override fun onDestroy(owner: LifecycleOwner) {
                super.onDestroy(owner)
                owner.lifecycle.removeObserver(this)
                if (!executedFinishMethod) {
                    onTaskRemoved()
                }

                TipsPool.clearPop()
            }
        })
    }

    /**
     * 子类自定义setContentView的方式
     * 需要 让 getLayout方法 返回 LAYOUT_CUSTOM
     */
    protected open fun customSetContentView() {}

    open fun skipAndFinish(): Boolean {
        return false
    }

    open fun handlePreIntent(preIntent: Intent) {}
    open fun initWindow(window: Window) {}
    abstract fun getLayout(): Int
    open fun initViews() {}
    open fun initListener() {}
    open fun initData(savedInstanceState: Bundle?) {}

    override fun finish() {
        super.finish()
        executedFinishMethod = true
    }

    /**
     * 被从后台划掉的回调
     * 原理是没执行finish 直接走了onDestroy
     */
    open fun onTaskRemoved() {}

    override fun onDestroy() {
        if (hasSkip) {
            super.onDestroy()
            return
        }
        if (careScreenLock) {
            unregisterReceiver(lockReceiver)
        }

        super.onDestroy()
    }


    /**
     * 拦截onKeyDown事件
     */
    open fun onInterceptKeyDown(keyCode: Int, event: KeyEvent?): Boolean = false

    /**
     * 如果子类复写了这个方法
     * 按照需求手动调用 [dispatchKeyDown2Fragment]方法
     * 来将事件分发到Fragment
     */
    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        if (onInterceptKeyDown(keyCode, event)) {
            // Activity将keyDown事件拦截了
            return true
        }
        // 将事件分发给Fragment
        val fragmentKeyDown = dispatchKeyDown2Fragment(keyCode, event)
        return if (fragmentKeyDown) true else super.onKeyDown(keyCode, event)
    }

    /**
     * 将KeyDown事件分发给Fragment
     */
    protected fun dispatchKeyDown2Fragment(keyCode: Int, event: KeyEvent?): Boolean {
        // 将事件分发给最上面的Fragment
        return when (keyDownStyle) {
            DispatcherStyle.FOLD -> {
                fragments
                    .filter { it is KeyDownListener && it.isVisible }
                    .fold(false) { res, it ->
                        (it as KeyDownListener).onKeyDown(keyCode, event) || res
                    }
            }

            DispatcherStyle.QUEUE -> {
                fragments
                    .filter { it is KeyDownListener && it.isVisible }
                    .sortedByDescending {
                        when (it) {
                            is BaseFragment -> it.showTime
                            is CZBusinessFragment -> it.createTime
                            else -> -1L
                        }
                    }
                    .forEach {
                        if ((it as KeyDownListener).onKeyDown(keyCode, event)) {
                            return true
                        }
                    }
                false
            }
        }

    }

    override fun onKeyUp(keyCode: Int, event: KeyEvent?): Boolean {
        val fragmentKeyUp = dispatchKeyUp2Fragment(keyCode, event)
        return if (fragmentKeyUp) fragmentKeyUp else super.onKeyUp(keyCode, event)
    }

    protected fun dispatchKeyUp2Fragment(keyCode: Int, event: KeyEvent?): Boolean {
        return fragments
            .filter { it is KeyUpListener && it.isVisible }
            .fold(false) { res, it ->
                (it as KeyUpListener).onKeyUp(keyCode, event) || res
            }
    }

    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        try {
            if (ev?.action == MotionEvent.ACTION_DOWN) {
                val v = currentFocus ?: return super.dispatchTouchEvent(ev)
                if (isShouldHideInput(v, ev)) {
                    // 点击任意位置 收起软键盘
                    hideKeyboard(v.windowToken)
                }
            }
        } catch (e:Exception) {
            logTagW(TAG, "BaseActivity dispatchTouchEvent,", tr = e)
        }

        return super.dispatchTouchEvent(ev)
    }

    private fun isShouldHideInput(v: View, ev: MotionEvent): Boolean {
        if (v !is EditText) {
            // 焦点View 不是EditText 证明输入法不可能弹出来
            return false
        }

        val viewRect = v.getLocationOnScreenRect()
        // 点击范围是输入法内部, 则不做任何操作
        return !viewRect.contains(ev.rawX.toInt(), ev.rawY.toInt())
    }

    open fun onScreenOFF() {}

    inner class ScreenLockReceiver : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            when (intent?.action) {
                Intent.ACTION_SCREEN_OFF -> onScreenOFF()
            }
        }

    }


    /**
     * 修改保持屏幕常量的方法
     */
    protected fun changeKeepScreenOn(keepScreenOn: Boolean) {
        if (keepScreenOn) {
            window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        } else {
            window.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        }
    }
}