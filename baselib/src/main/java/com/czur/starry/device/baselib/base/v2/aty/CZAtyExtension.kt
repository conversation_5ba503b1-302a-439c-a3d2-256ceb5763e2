package com.czur.starry.device.baselib.base.v2.aty

import androidx.fragment.app.Fragment
import com.czur.starry.device.baselib.base.v2.fragment.CZBaseFragment

/**
 * Created by 陈丰尧 on 2023/6/8
 */

val CZBaseAty.fragments:List<Fragment>
    get() {
        val fragments = supportFragmentManager.fragments
        val navHostFragment = fragments.find { it::class.java.simpleName == "NavHostFragment" }
        val childFragment = navHostFragment?.childFragmentManager?.fragments ?: emptyList()
        fragments.addAll(childFragment)
        return fragments
    }

val CZBaseAty.czFragments:List<CZBaseFragment>
    get() {
        return fragments.filterIsInstance<CZBaseFragment>()
    }