package com.czur.starry.device.baselib.widget

import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.AdapterView.OnItemClickListener
import android.widget.BaseAdapter
import android.widget.LinearLayout
import android.widget.ListView
import android.widget.PopupWindow
import android.widget.TextView
import androidx.appcompat.content.res.AppCompatResources
import com.czur.starry.device.baselib.R

private var currentSelected = 0
private const val MARGIN_BOTTOM = 10
private const val MAX_DISPLAY_COUNT = 18      //列表最多现实18个item

/**
 * 构建一个下拉列表
 */
fun showPopupWindow(
    context: Context,
    showView: View,
    current: Int = 0,
    totalCount: Int = 1,
    onPopupItemClick: (position: Int) -> Unit,
) {
    currentSelected = current
    val view = View.inflate(context, R.layout.popup_pdf_list, null)
    val listItem = arrayListOf<Int>()
    for (i in 1..totalCount) {
        listItem.add(i)
    }

    val mAdapter = MyAdapter(context, listItem)
    val mListView: ListView = view.findViewById(R.id.popup_listview)
    mListView.adapter = mAdapter


    val item = mAdapter.getView(0, null, mListView)
    item.measure(0, 0)
    var totalHeight = item.measuredHeight * listItem.size + MARGIN_BOTTOM
    //列表最多现实18个item
    if (listItem.size > MAX_DISPLAY_COUNT) {
        totalHeight = item.measuredHeight * MAX_DISPLAY_COUNT + MARGIN_BOTTOM
    }
    mListView.layoutParams.height = totalHeight

    val mPopup =
        PopupWindow(view, ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT)
    mPopup.isFocusable = true
    mPopup.isOutsideTouchable = true

    mPopup.update()
    val xOffset = (showView.width - 80) / 2 // 80是popup的宽度
    mPopup.showAsDropDown(showView, xOffset, 0)

    mListView.onItemClickListener =
        OnItemClickListener { _, _, position, _ ->
            onPopupItemClick(position)
            currentSelected = position
            mAdapter.notifyDataSetChanged()
        }


}

class MyAdapter(private val context: Context, private val list: ArrayList<Int>) : BaseAdapter() {

    override fun getCount(): Int {
        return list.size
    }

    override fun getItem(position: Int): Any {
        return position
    }

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    override fun getView(position: Int, convertView: View?, parent: ViewGroup?): View {
        val vh = (convertView?.tag as? ViewHolder) ?: run {
            val view = View.inflate(context, R.layout.popup_pdf_item, null)
            val vh = ViewHolder(view)
            view.tag = vh
            vh
        }
        vh.tv.text = list[position].toString()

        if (position == currentSelected) {
            AppCompatResources.getDrawable(context, R.drawable.bg_pdf_pop_list)?.let {
                vh.tv.background = it
            }
            vh.tv.setTextColor(context.getColor(R.color.white))
        } else {
            vh.tv.setBackgroundColor(context.getColor(R.color.white))
            vh.tv.setTextColor(context.getColor(R.color.pop_color))
        }
        return convertView ?: vh.itemView
    }

    class ViewHolder(val itemView: View) {
        val tv: TextView = itemView.findViewById(R.id.tv_text)
        val layout: LinearLayout = itemView.findViewById(R.id.item_view)
    }
}