package com.czur.starry.device.baselib.utils.keyboard

import android.content.Context
import android.os.IBinder
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import androidx.fragment.app.Fragment
import com.czur.starry.device.baselib.base.CZURAtyManager

/**
 * Created by 陈丰尧 on 2021/9/30
 */
private val inputManager by lazy {
    CZURAtyManager.appContext.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
}

fun hideKeyboard(windowToken: IBinder?) {
    if (windowToken == null) {
        return
    }
    inputManager.hideSoftInputFromWindow(windowToken, 0)
}

fun showKeyboard(et: EditText, delay: Long = 100L) {
    et.postDelayed({
        inputManager.showSoftInput(et, 0)
    }, delay)
}

fun Fragment.keyboardHide(windowToken: IBinder? = view?.windowToken) {
    hideKeyboard(windowToken)
}

/**
 * editText 隐藏输入法
 */
fun EditText.keyboardHide() {
    hideKeyboard(windowToken)
}

/**
 * EditText 显示输入法
 */
fun EditText.keyboardShow(delay: Long = 100L) {
    showKeyboard(this, delay)
}

fun EditText.focusAndShowKeyboard(delay: Long = 100L)  {
    requestFocus()
    postDelayed({
        inputManager.showSoftInput(this, 0)
    }, delay)
}