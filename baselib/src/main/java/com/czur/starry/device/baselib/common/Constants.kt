package com.czur.starry.device.baselib.common

import android.os.Build
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.BuildConfig
import com.czur.starry.device.baselib.common.hw.StarryHWInfo
import com.czur.starry.device.baselib.utils.prop.getIntSystemProp
import com.czur.starry.device.baselib.utils.prop.getStringSystemProp
import com.czur.starry.device.baselib.utils.prop.setStringSystemProp

/**
 * 通过修改代理类, 来修改 生产环境/开发环境的常量
 */
object Constants : EnvConstants {
    private const val TAG = "Constants"

    private const val DEVICE_INDUSTRY_PARTY_BUILD = "A"         // 党建
    private const val DEVICE_INDUSTRY_ARMY_BUILD = "J"          // 军队


    const val DEF_SECRET = "1qaz2wsx3edc4rfv"

    const val PATH_SDCARD = "/mnt/sdcard/"
    const val PATH_SDCARD_OTA = "/mnt/sdcard/update.zip"
    const val PATH_SDCARD_OTA_CAMERA = "/mnt/sdcard/update.bin"
    const val PATH_SDCARD_OTA_CLICK_DROP = "/mnt/sdcard/clickdrop/"

    // 动画默认持续时间(短)
    const val ANIM_DURATION_SHORT = 250L

    // 双击毫秒数
    const val CLICK_DEBOUNCE = 350L

    // 默认音量
    const val VOLUME_CALL_DEF = 12
    const val VOLUME_MEDIA_DEF = 7

    // 设备的APPID
    const val DEVICE_APP_ID = "com.czur.starry"

    /**
     * 设备流水号 例如: CPH22A2109000001
     * C: 表示 CZUR
     * P: 表示Starry系列
     * H: 国内线上; P: 国内线下; A海外
     * 22: 光机亮度2200ANSI
     * A: 机型代码
     * 2109: 21年9月生产
     * 000001: 流水号
     */
    val SERIAL: String by lazy(LazyThreadSafetyMode.NONE) {
        val debugSN = getStringSystemProp(KEY_DEVICE_DEBUG_SN, "")
        if (debugSN.isEmpty()) return@lazy Build.getSerial()
        if (debugSN.length < 16) {
            logTagW(TAG, "流水号长度不足16位(${debugSN}), 请检查!")
            Build.getSerial()
        } else {
            debugSN
        }
    }

    /**
     * 固件名称
     */
    val FIRMWARE_NAME: String by lazy(LazyThreadSafetyMode.NONE) {
        Build.DISPLAY
    }

    /**
     * 设备的硬件信息
     */
    val starryHWInfo: StarryHWInfo by lazy(LazyThreadSafetyMode.NONE) {
        StarryHWInfo(SERIAL)
    }

    /**
     * 触控板名字
     */
    val TOUCH_PAD_NAMES = listOf(
        "Starry Touchpad",  // 旧版固件名字
        "StarryHub Touch"   // 修改后名字
    )

    /**
     * 行业设备标识符
     * 如果不是行业版本, 则为空
     * A: 党建版本
     */
    private val industryFlg: String by lazy {
        val versionNameInfo = FIRMWARE_NAME.split("-")[1]
        val flag = if (!versionNameInfo.startsWith("CPP")) {
            // 不是行业版本, 则不去解析
            if (versionNameInfo.startsWith("CPH24J")) {
                // 军队版本 版本名为: Starry-CPH24J
                DEVICE_INDUSTRY_ARMY_BUILD
            } else {
                ""
            }
        } else {
            versionNameInfo.getOrNull(3)?.toString() ?: ""
        }
        getStringSystemProp(KEY_DEVICE_INDUSTRY, flag)
    }

    /**
     * 服务器环境
     */
    val serverEnv: ServerEnv by lazy(LazyThreadSafetyMode.NONE) {
        when (adbServerEnv) {
            ServerEnv.PRODUCT.serverEnvCode -> ServerEnv.PRODUCT
            ServerEnv.DEVELOP.serverEnvCode -> ServerEnv.DEVELOP
            else -> {
                // 通过ADB指定了, 则使用ADB指定的
                // 没有通过ADB指定, 则使用编译时指定的
                val serverEnvCode = BuildConfig.CONSTANT_ENV
                ServerEnv.createByServerEnvCode(serverEnvCode)
            }
        }
    }

    /**
     * 通过ADB命令指定的服务器环境
     */
    private val adbServerEnv: Int by lazy {
        getIntSystemProp(KEY_DEVICE_SERVER_ENV, -1)
    }

    /**
     * 是否是生产环境
     */
    val isProductEnv: Boolean by lazy(LazyThreadSafetyMode.NONE) {
        serverEnv == ServerEnv.PRODUCT
    }

    /**
     * 通过ADB命令指定的BASE HOST
     */
    private val adbBaseHOST: String by lazy {
        getStringSystemProp(KEY_DEVICE_BASE_HOST, "")
    }

    /**
     * 版本使用的行业
     */
    val versionIndustry: VersionIndustry by lazy(LazyThreadSafetyMode.NONE) {
        when (industryFlg) {
            DEVICE_INDUSTRY_PARTY_BUILD -> VersionIndustry.PARTY_BUILDING
            DEVICE_INDUSTRY_ARMY_BUILD -> VersionIndustry.DEVICE_INDUSTRY_ARMY_BUILD
            else -> VersionIndustry.UNIVERSAL
        }
    }

    /**
     * 服务器地址(国内/海外)
     */
    var serverLocation: ServerLocation
        set(value) {
            setStringSystemProp(KEY_DEVICE_SERVER_LOCATE, value.loCode)
        }
        get() {
            val code = getStringSystemProp(KEY_DEVICE_SERVER_LOCATE, "")
            return ServerLocation.create(code)
        }

    private val envContacts: EnvConstants by lazy {
        when (serverEnv to starryHWInfo.salesLocale) {
            ServerEnv.PRODUCT to StarryDevLocale.Mainland -> ProductMainlandConstants
            ServerEnv.DEVELOP to StarryDevLocale.Mainland -> DEVMainlandConstants
            ServerEnv.PRODUCT to StarryDevLocale.Overseas -> ProductOverseaConstants
            ServerEnv.DEVELOP to StarryDevLocale.Overseas -> DEVOverseaConstants
            else -> {
                logTagE("StarryContacts", "还没有实装的环境!!!")
                DEVMainlandConstants
            }
        }
    }

    // 可以通过adb 手动指定对应的
    override val BASE_HOST: String
        get() {
            if (adbBaseHOST.isBlank()) {
                return envContacts.BASE_HOST
            }
            if (adbBaseHOST.startsWith("http", true)) {
                logTagW(TAG, "指定的BaseHost:${adbBaseHOST} 不能以http开头!")
                return envContacts.BASE_HOST
            }
            if (adbBaseHOST.endsWith("/", true)) {
                logTagW(TAG, "指定的BaseHost:${adbBaseHOST} 不能以/结尾!")
                return envContacts.BASE_HOST
            }
            return adbBaseHOST
        }
    override val PASSPORT_COUNTRY_URL: String
        get() = envContacts.PASSPORT_COUNTRY_URL

    override val PERSONAL_WEB_URL: String
        get() = envContacts.PERSONAL_WEB_URL

    override val BASE_URL_SHARE_V2: String
        get() = envContacts.BASE_URL_SHARE_V2

    override val OTA_BASE_URL: String
        get() = envContacts.OTA_BASE_URL

    override val APP_STORE_BASE_URL: String
        get() = envContacts.APP_STORE_BASE_URL

    override val TMP_BUCKET_NAME: String
        get() = envContacts.TMP_BUCKET_NAME

    override val DOWNLOAD_HOST: String
        get() = envContacts.DOWNLOAD_HOST

    override val TMP_END_POINT: String
        get() = envContacts.TMP_END_POINT

    override val WELCOME_BASE_URL: String
        get() = envContacts.WELCOME_BASE_URL
    override val CHECK_OUT_HOST_URL: String
        get() = envContacts.CHECK_OUT_HOST_URL

    override val MEETING_APP_ID: String
        get() = envContacts.MEETING_APP_ID

    override val OTA_APP_ID: String
        get() = envContacts.OTA_APP_ID

    override val OTA_CLIENT_ID: String
        get() = envContacts.OTA_CLIENT_ID

    override val OTA_WEB_CLIENT_ID: String
        get() = envContacts.OTA_WEB_CLIENT_ID

    override val OTA_ONLINE_CHANNEL: String
        get() = envContacts.OTA_ONLINE_CHANNEL

    override val OTA_UPDATE_INFO: Int
        get() = envContacts.OTA_UPDATE_INFO

    override val BASE_NOTES_HOST: String
        get() = envContacts.BASE_NOTES_HOST

    override val NETTY_SERVER_IP: String
        get() = envContacts.NETTY_SERVER_IP
    override val MEETING_SHARE_HOST: String
        get() = envContacts.MEETING_SHARE_HOST
    override val OFFIC_PREVIEW_URL: String
        get() = envContacts.OFFIC_PREVIEW_URL
    override val LEAGLE_WEB_URL: String
        get() = envContacts.LEAGLE_WEB_URL
    override val FEED_BACK_EMAIL: String
        get() = envContacts.FEED_BACK_EMAIL

    override val VIDEO_ID_WINDOWS: Int
        get() = envContacts.VIDEO_ID_WINDOWS
    override val VIDEO_ID_ANDROID: Int
        get() = envContacts.VIDEO_ID_ANDROID
    override val VIDEO_ID_APPLE: Int
        get() = envContacts.VIDEO_ID_APPLE
    override val VIDEO_ID_LINUX: Int
        get() = envContacts.VIDEO_ID_LINUX

    override val VIDEO_ID_DLNA: Int
        get() = envContacts.VIDEO_ID_DLNA
    override val VIDEO_ID_SHARE: Int
        get() = envContacts.VIDEO_ID_SHARE
    override val VIDEO_ID_CLICK_DROP: Int
        get() = envContacts.VIDEO_ID_CLICK_DROP
    override val VIDEO_ID_P2P: Int
        get() = envContacts.VIDEO_ID_P2P
}