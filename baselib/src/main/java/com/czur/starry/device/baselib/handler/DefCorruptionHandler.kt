package com.czur.starry.device.baselib.handler

import android.content.Context
import androidx.datastore.core.handlers.ReplaceFileCorruptionHandler
import androidx.datastore.preferences.core.emptyPreferences
import com.czur.czurutils.global.globalAppCtx
import com.czur.czurutils.log.logTagE
import java.io.File

/**
 * Created by 陈丰尧 on 2024/5/29
 */
private const val TAG = "DefCorruptionHandler"
fun createDefCorruptionHandler(dsName: String) =
    ReplaceFileCorruptionHandler {
        val dir = File(globalAppCtx.applicationContext.filesDir, "datastore")
        val file = File(dir, "${dsName}.preferences_pb")
        file.delete()
        logTagE(TAG, "dataStore Error, delete file $file", tr = it)
        emptyPreferences()
    }
