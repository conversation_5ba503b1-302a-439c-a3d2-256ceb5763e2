package com.czur.starry.device.baselib.utils.keyboard

import android.app.Instrumentation
import android.view.KeyEvent
import com.czur.czurutils.log.logTagV
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Created by 陈丰尧 on 2023/6/6
 */
private const val TAG = "KeyInjectionUtil"

private val inst by lazy { Instrumentation() }

/**
 * 模拟按键
 * @param keyCode 按键码
 */
suspend fun injectKey(keyCode: Int) = withContext(Dispatchers.Default) {
    logTagV(TAG, "注入按键:${keyCode}")
    inst.sendKeyDownUpSync(keyCode)
}

/**
 * 注入返回键
 */
suspend fun injectKeyBack() = injectKey(KeyEvent.KEYCODE_BACK)

/**
 * 注入Home键
 */
suspend fun injectKeyHome() = injectKey(KeyEvent.KEYCODE_HOME)

/**
 * 注入多任务键
 */
suspend fun injectKeyAPPSwitch() = injectKey(KeyEvent.KEYCODE_APP_SWITCH)

