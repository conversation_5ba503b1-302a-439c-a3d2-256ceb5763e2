package com.czur.starry.device.baselib.base.v2.fragment.floating

import android.graphics.Bitmap
import android.os.Bundle
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import androidx.fragment.app.commit
import androidx.viewbinding.ViewBinding
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.base.CZURAtyManager
import com.czur.starry.device.baselib.base.listener.KeyDownListener
import com.czur.starry.device.baselib.base.v2.aty.CZBaseAty
import com.czur.starry.device.baselib.base.v2.fragment.CZBusinessFragment
import com.czur.starry.device.baselib.databinding.FragmentCzvbFloatingBinding
import com.czur.starry.device.baselib.tips.TipsPool
import com.czur.starry.device.baselib.utils.doWithoutCatch
import com.czur.starry.device.baselib.utils.invisible
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import java.lang.reflect.ParameterizedType
import kotlin.properties.Delegates

/**
 * Created by 陈丰尧 on 2023/6/12
 * 使用ViewBinding的悬浮窗
 */
private val showingFloatTags = mutableListOf<String>()
private const val TAG = "CZVBFloatingFragment"

fun clearShowingFloatTags() {
    logTagD(TAG, "clearShowingFloatTags: 清除所有悬浮窗标签")
    showingFloatTags.clear()
}

abstract class CZVBFloatingFragment<VB : ViewBinding> : CZBusinessFragment(),
    ViewTreeObserver.OnGlobalLayoutListener, KeyDownListener, IDismissible {

    enum class FloatingBtnType {
        NEGATIVE,// 消极按钮
        POSITIVE,// 积极按钮
    }

    final override val fragmentParams: FloatingFragmentParams = FloatingFragmentParams()
    internal var outBinding: FragmentCzvbFloatingBinding by Delegates.notNull()
    protected var binding: VB by Delegates.notNull()
    private val animHelper: CZFloatingFragmentAnimHelper by lazy { CZFloatingFragmentAnimHelper(this) }

    private var enterAnimFinish = false // 进入动画是否完成
    var isDismissing = false // 是否正在执行dismiss
        private set
    private var lastInputMode: Int? = null

    private val showTag: String by lazy(LazyThreadSafetyMode.NONE) {
        when (fragmentParams.floatingRepeatMode) {
            is FloatingRepeatMode.Normal -> fragmentParams.floatingTag
            is FloatingRepeatMode.Single -> (fragmentParams.floatingRepeatMode as FloatingRepeatMode.Single).repeatTag
            is FloatingRepeatMode.Repeat -> (fragmentParams.floatingRepeatMode as FloatingRepeatMode.Repeat).repeatTag
        }
    }

    private var onDismissListener: (() -> Unit)? = null

    fun setOnDismissListener(listener: (() -> Unit)?) {
        onDismissListener = listener
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }

    final override fun initParams() {
        fragmentParams.initFloatingParams()
    }

    override fun initByFragmentParams() {
        super.initByFragmentParams()
        fragmentParams.overrideInputMode?.let {
            logTagV(TAG, "overrideInputMode: $it")
            lastInputMode = requireActivity().window.attributes.softInputMode
            requireActivity().window.setSoftInputMode(it)
        }
    }

    final override fun FragmentParams.initFragmentParams() {}
    protected open fun FloatingFragmentParams.initFloatingParams() {}

    final override fun createCusContentView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        outBinding = FragmentCzvbFloatingBinding.inflate(inflater, container, false)
        binding = generateViewBinding(outBinding.root).also {
            outBinding.root.addView(it.root)    // 将ViewBinding的根布局添加到外部布局中
            it.root.invisible() // 首先让View不可见, 等待动画结束后再显示
            it.root.viewTreeObserver.addOnGlobalLayoutListener(this@CZVBFloatingFragment)
        }
        return outBinding.root
    }

    final override fun initViews() {
        super.initViews()

        // 设置外部点击事件
        if (fragmentParams.clickThrough) {
            // 点击穿透
            outBinding.root.isClickable = false
            outBinding.root.isFocusable = false
        } else {
            // 点击不穿透
            outBinding.root.isClickable = true
            outBinding.root.isFocusable = true
            outBinding.root.setOnDebounceClickListener {
                // 如果点击了外部, 并且动画还没结束, 则不响应
                if (!fragmentParams.outSideClickBeforeAnim && !enterAnimFinish) return@setOnDebounceClickListener
                if (isDismissing) return@setOnDebounceClickListener // 正在销毁, 也不响应点击事件
                if (!fragmentParams.outSideDismiss) return@setOnDebounceClickListener
                logTagI(TAG, "${fragmentParams.floatingTag} 点击外部, dismiss")
                dismiss()
            }
        }

        // 设置内部View的点击事件, 内部View的范围不响应外部点击事件
        binding.root.isClickable = true

        binding.initBindingViews()
    }

    protected open fun VB.initBindingViews() {}

    /**
     * view已经加载(宽高数据已经获取)完成
     */
    override fun onGlobalLayout() {
        binding.root.viewTreeObserver.removeOnGlobalLayoutListener(this)
        animHelper.showFloatingBgEnterAnim(fragmentParams.floatingBgMode)    // 动画显示背景

        // 动画显示悬浮窗
        animHelper.playEnterAnim(binding.root) {
            enterAnimFinish = true
            onShow()
        }
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        return if (fragmentParams.keyBackDismiss && enterAnimFinish && keyCode == KeyEvent.KEYCODE_BACK) {
            dismiss()
            true
        } else keyCode == KeyEvent.KEYCODE_SOFT_LEFT || keyCode == KeyEvent.KEYCODE_SOFT_RIGHT || keyCode == KeyEvent.KEYCODE_DPAD_LEFT || keyCode == KeyEvent.KEYCODE_DPAD_RIGHT
    }

    /**
     * 当Float显示完成后回调
     */
    protected open fun onShow() {}

    /**
     * 当消失动画结束后回调
     */
    protected open fun onDismiss() {}


    /**
     * 显示
     */
    fun show(
        animParam: CZFloatingAnimParam = CZFloatingAnimParam.DEFAULT,
        hostAty: CZBaseAty? = CZURAtyManager.currentActivityOrNull(),
        args: Bundle? = null
    ) {
        val aty = hostAty ?: return

        initParams()

        animHelper.enterAnimParam = animParam   // 记录动画参数

        // 1. 检查是否可以显示
        if (fragmentParams.floatingRepeatMode is FloatingRepeatMode.Single) {
            if (showingFloatTags.contains(showTag)) {
                logTagD(
                    TAG,
                    "${fragmentParams.floatingTag} show: 重复的悬浮窗, tag = $showTag 不再显示"
                )
                return
            }
        }

        // 2. 显示前准备
        TipsPool.clearPop() // 清除所有的FloatTips

        // 3. 添加Fragment
        aty.supportFragmentManager.commit(true) {
            if (fragmentParams.floatingRepeatMode is FloatingRepeatMode.Repeat) {
                val oldFragment = aty.supportFragmentManager.findFragmentByTag(showTag)
                if (oldFragment != null) {
                    logTagI(
                        TAG,
                        "${fragmentParams.floatingTag} show: 移除旧的Fragment, tag = $showTag"
                    )
                    remove(oldFragment)
                }
            }
            args?.let {
                arguments = it
            }

            add(
                android.R.id.content,
                this@CZVBFloatingFragment,
                showTag
            )
            logTagD(TAG, "showingFloatTags.add(${showTag})")
            showingFloatTags.add(showTag)
        }

    }

    fun dismissWithoutAnim() {
        if (isDismissing) {
            logTagD(TAG, "${fragmentParams.floatingTag} dismiss: 正在执行dismiss, 不再执行")
            return
        }
        isDismissing = true
        TipsPool.clearPop() // 清除所有的FloatTips
        logTagI(TAG, "${fragmentParams.floatingTag} dismiss: 动画结束, 移除Fragment")
        lastInputMode?.let {
            // 还原InputMode
            logTagE(TAG, "还原InputMode")
            requireActivity().window.setSoftInputMode(it)
        }
        onDismiss()
        onDismissListener?.invoke() // 调用消失回调
        doWithoutCatch(TAG, "移除浮窗失败") {
            removeSelf()
        }
    }

    /**
     * 消失
     */
    override fun dismiss() {
        if (isDismissing) {
            logTagD(TAG, "${fragmentParams.floatingTag} dismiss: 正在执行dismiss, 不再执行")
            return
        }
        isDismissing = true
        TipsPool.clearPop() // 清除所有的FloatTips
        animHelper.showFloatingBgExitAnim(fragmentParams.floatingBgMode)    // 动画隐藏背景
        animHelper.playExitAnim(binding.root) {
            logTagI(TAG, "${fragmentParams.floatingTag} dismiss: 动画结束, 移除Fragment")
            lastInputMode?.let {
                // 还原InputMode
                logTagE(TAG, "还原InputMode")
                requireActivity().window.setSoftInputMode(it)
            }
            onDismiss()
            onDismissListener?.invoke() // 调用消失回调
            doWithoutCatch(TAG, "移除浮窗失败") {
                removeSelf()
            }
        }
    }

    /**
     * 从Activity中移除自己
     */
    private fun removeSelf() {
        logTagD(TAG, "${fragmentParams.floatingTag} removeSelf: 移除Fragment")
        activity?.supportFragmentManager?.commit(true) {
            showingFloatTags.remove(showTag)
            remove(this@CZVBFloatingFragment)
        } ?: kotlin.run {
            showingFloatTags.remove(showTag)
            logTagE(TAG, "${fragmentParams.floatingTag} removeSelf: activity = null")
            val manager = CZURAtyManager.currentActivityOrNull()?.supportFragmentManager
            if (manager != null) {
                logTagD(TAG, "尝试通过当前Activity来remove")
                val currentFragments = manager.fragments
                if (currentFragments.contains(this@CZVBFloatingFragment)) {
                    logTagD(TAG, "当前Activity包含该Fragment, 移除")
                    manager.commit(true) {
                        remove(this@CZVBFloatingFragment)
                    }
                } else {
                    logTagE(TAG, "当前Activity不包含该Fragment, 无法移除")
                }
            } else {
                logTagE(TAG, "当前Activity为null, 无法移除")
            }
        }
        isDismissing = false
    }

    /**
     * 悬浮窗的参数
     */
    protected inner class FloatingFragmentParams : FragmentParams() {
        var floatingTag: String = CZVBFloatingFragment::class.java.simpleName   // 悬浮窗的标签
        var floatingBgMode: FloatingBgMode = FloatingBgMode.Dark    // 悬浮窗背景模式

        var outSideDismiss: Boolean = true   // 点击外部是否消失
        var clickThrough: Boolean = false     // 点击事件是否可以穿透到浮窗外部
        var outSideClickBeforeAnim = false    // 点击外部是否在动画前就消失

        var floatingRepeatMode: FloatingRepeatMode = FloatingRepeatMode.Normal   // 悬浮窗的重复模式

        var keyBackDismiss: Boolean = true   // 点击返回键是否消失
        var overrideInputMode: Int? = null    // 覆盖inputMode
    }

    /**
     * 悬浮窗的重复模式
     */
    sealed class FloatingRepeatMode {
        object Normal : FloatingRepeatMode()  // 普通模式
        class Single(val repeatTag: String) :
            FloatingRepeatMode()  // 单一模式, 只显示一个, 如果再次显示, 新实例会被抛弃

        class Repeat(val repeatTag: String) :
            FloatingRepeatMode()  // 替换模式, 只显示一个, 如果再次显示, 将会用新实力替换旧实例
    }

    sealed class FloatingBgMode {
        data object Dark : FloatingBgMode()  // 黑色背景
        data object NONE : FloatingBgMode()  // 无背景
        class Image(val bitmap: Bitmap, val darkImg: Boolean = false) : FloatingBgMode()  // 图片背景
    }

    private fun generateViewBinding(rootView: ViewGroup?): VB {
        val type = this::class.java.genericSuperclass as? ParameterizedType
            ?: throw IllegalArgumentException("没有指定ViewBinding的泛型")
        val vbClazz = type.actualTypeArguments.find {
            // 找到泛型声明为 实现了 ViewBinding接口的类型
            (it as Class<*>).genericInterfaces[0] == ViewBinding::class.java
        } as? Class<*> ?: throw IllegalArgumentException("ViewBinding class not found")

        val method = vbClazz.getMethod(
            "inflate",
            LayoutInflater::class.java,
            ViewGroup::class.java,
            Boolean::class.java
        )
        return method.invoke(null, layoutInflater, rootView, false) as VB
    }
}