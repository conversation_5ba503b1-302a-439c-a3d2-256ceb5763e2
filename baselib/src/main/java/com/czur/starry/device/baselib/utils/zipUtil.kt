package com.czur.starry.device.baselib.utils

import java.io.ByteArrayOutputStream
import java.util.zip.Deflater
import java.util.zip.Inflater

/**
 * Created by 陈丰尧 on 2021/8/10
 */
private const val ZIP_BYTE_BUFF = 256

/**
 * 压缩字节数组
 */
fun zip(srcBytes: ByteArray): ByteArray {
    val compresser = Deflater(Deflater.BEST_COMPRESSION)
    compresser.setInput(srcBytes)
    compresser.finish()

    val bytes = ByteArray(ZIP_BYTE_BUFF)
    val outputStream = ByteArrayOutputStream(ZIP_BYTE_BUFF)

    val result = outputStream.use {
        while (!compresser.finished()) {
            //压缩输入数据并用压缩数据填充指定的缓冲区
            val length = compresser.deflate(bytes)
            it.write(bytes, 0, length)
        }
        it.toByteArray()
    }


    //关闭压缩器并丢弃任何未处理的输入
    compresser.end()

    return result
}

/**
 * 解压字节数组
 */
fun unzip(zipBytes: ByteArray): ByteArray {
    val inflater = Inflater()
    inflater.setInput(zipBytes)

    val bytes = ByteArray(ZIP_BYTE_BUFF)
    val outputStream = ByteArrayOutputStream(ZIP_BYTE_BUFF)

    val unzipArr = outputStream.use {
        while (!inflater.finished()) {
            val length = inflater.inflate(bytes)
            it.write(bytes, 0, length)
        }
        it.toByteArray()
    }
    inflater.end()
    return unzipArr
}