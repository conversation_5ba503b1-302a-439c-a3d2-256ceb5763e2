package com.czur.starry.device.baselib.common

/**
 * Created by 陈丰尧 on 2021/7/6
 */
object BootParam {
    const val ACTION_BOOT_TRANSCRIPTION = "com.czur.starry.device.transcription.BOOT_APP"
    const val ACTION_BOOT_PERSONAL = "com.czur.starry.device.personalcenter.BOOT_APP"
    const val ACTION_BOOT_PERSONAL_RECHARGE = "com.czur.starry.device.personalcenter.BOOT_RECHARGE"
    const val ACTION_BOOT_SCREEN_SHARE = "com.czur.starry.device.sharescreen.BOOT_APP"
    const val ACTION_BOOT_NOTICE_CENTER = "com.czur.starry.device.noticecenter.BOOT_APP"
    const val ACTION_BOOT_FILE = "com.czur.starry.device.file.BOOT_APP"
    const val ACTION_BOOT_WRITE_PAD = "com.czur.starry.device.starrypad.BOOT_APP"
    const val ACTION_BOOT_NOTICE_SETTING = "com.czur.starry.device.settings.BOOT_APP"
    const val ACTION_BOOT_BYOM_GUIDE = "com.czur.starry.device.sharescreen.PERIPHERAL_MODE_GUIDE"
    const val ACTION_BOOT_HDMI = "com.czur.starry.device.hdmiin.MAIN"
    const val ACTION_BOOT_VOICE_ASSISTANT = "com.czur.starry.device.voiceassistant.BOOT_VOICE"
    const val BOOT_KEY_SETTING_PAGE_MENU_KEY = "pageSubMenuKey"

    const val BOOT_KEY_PAGE_MENU_NAME = "pageSubMenuName" //语音打开页面名称
    const val BOOT_KEY_PAGE_MENU_NAVIGATE = "pageSubMenuNavigate" //语音打开页面并执行

    object CZService {
        const val ACTION_SERVICE_FILE_WATCH_UPDATE = "com.czur.starry.device.file.FILE_WATCH_UPDATE"

    }
}