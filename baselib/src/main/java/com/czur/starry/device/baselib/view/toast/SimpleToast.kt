package com.czur.starry.device.baselib.view.toast

import android.content.Context
import android.view.Gravity
import android.view.LayoutInflater
import android.widget.Toast
import androidx.annotation.StringRes
import com.czur.starry.device.baselib.R
import com.czur.starry.device.baselib.databinding.ToastSimpleBinding
import com.czur.starry.device.baselib.utils.gone

/**
 * Created by 陈丰尧 on 2021/9/3
 * 自定义Toast,有3种模式
 * 1. 成功
 * 2. 失败
 * 3. 纯文字
 * 成功和失败是带图片的, 纯文字不带图片
 * 4.isWarning 白底红字
 */
class SimpleToast(
    context: Context,
    msg: String,
    imgRes: Int? = null,
    isWarning: Boolean = false
) {
    private var toast: Toast = Toast(context)
    private lateinit var binding: ToastSimpleBinding

    init {
        if (imgRes == null && !isWarning) {
            // 纯文字, 并且不是警告
            toast = Toast.makeText(context, msg, Toast.LENGTH_LONG)
            toast.setGravity(Gravity.BOTTOM or Gravity.CENTER, 0, 10)
        } else {
            binding = ToastSimpleBinding.inflate(LayoutInflater.from(context))
            binding.toastMsgTv.text = msg
            if (imgRes != null) {
                binding.toastImgIv.setImageResource(imgRes)
            } else {
                binding.toastImgIv.gone()
            }
            toast.view = binding.root
            toast.setGravity(Gravity.BOTTOM or Gravity.CENTER, 0, 10)
            toast.duration = Toast.LENGTH_LONG
            /*    if (isWarning) {
                    view.background = DrawableCreator.Builder()
                        .setCornersRadius(10f)
                        .setSolidColor(Color.parseColor("#FFFFFF")).build()
                    view.toastMsgTv.setTextColor(Color.RED)
                    toast.duration = Toast.LENGTH_LONG
                }*/
        }
    }

    fun show() {
        toast.show()
    }

    companion object {
        /**
         * 构建成功/失败的Toast
         * @param success: 是否显示成功的Toast
         */
        fun makeToast(context: Context, success: Boolean = true): SimpleToast {
            return if (success) {
                SimpleToast(
                    context,
                    context.getString(R.string.toast_operation_success),
                    R.drawable.ic_toast_success
                )
            } else {
                SimpleToast(
                    context,
                    context.getString(R.string.toast_operation_failure),
                    R.drawable.ic_toast_fail
                )
            }
        }

        /**
         * 构建文字Toast
         * @param resId: 文字ID
         */
        fun makeText(context: Context, @StringRes resId: Int): SimpleToast =
            SimpleToast(context, context.getString(resId))


        /**
         * 构建文字Toast
         * @param toastStr: Toast要显示的文字内容
         */
        fun makeText(context: Context, toastStr: String, isWarning: Boolean = false): SimpleToast =
            SimpleToast(context, toastStr, isWarning = isWarning)

    }
}