package com.czur.starry.device.baselib.utils

import android.graphics.Bitmap
import android.graphics.Canvas
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagW
import com.google.android.renderscript.Toolkit
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import androidx.core.graphics.scale

/**
 * Created by 陈丰尧 on 2024/5/13
 * 对Bitmap的扩展工具
 */
private const val TAG = "BitmapExt"
private const val DEF_BLUR_RADIUS = 15

/**
 * 保存Bitmap到文件
 * @param file 目标文件
 * @param quality 图片质量, 0-100
 * @param format 图片格式, 如果为null, 则根据文件后缀自动判断
 * @exception IllegalArgumentException 不支持的图片格式:当没有指定format时, 且文件后缀不是jpg, jpeg, png, webp时抛出
 */
suspend fun Bitmap.saveToFile(
    file: File,
    quality: Int = 100,
    format: Bitmap.CompressFormat? = null
) =
    withContext(Dispatchers.IO) {
        val dir = file.parentFile
        if (dir == null) {
            logTagW(TAG, "文件路径不合法: ${file.absolutePath}")
            return@withContext
        } else if (!dir.exists() && !dir.mkdirs()) {
            logTagE(TAG, "创建文件夹失败: ${dir.absolutePath}")
            return@withContext
        }
        if (file.exists()) file.delete()
        val compressFormat = format ?: when (file.extension.lowercase()) {
            "jpg", "jpeg" -> Bitmap.CompressFormat.JPEG
            "png" -> Bitmap.CompressFormat.PNG
            "webp" -> Bitmap.CompressFormat.WEBP_LOSSLESS
            else -> throw IllegalArgumentException("不支持的图片格式")
        }
        FileOutputStream(file).use {
            compress(compressFormat, quality, it)
            it.flush()
        }
    }

/**
 * 对Bitmap进行转换, 转换后的Bitmap的Config为targetConfig
 * 转换之后,会生成新的Bitmap,原Bitmap会被回收
 * @param targetConfig 目标Config
 * @return 转换后的Bitmap
 */
suspend fun Bitmap.transformationConfig(
    targetConfig: Bitmap.Config
): Bitmap = withContext(Dispatchers.Default) {
    if (config == targetConfig) return@withContext this@transformationConfig
    val newBmp = copy(targetConfig, true)
    recycle()
    newBmp
}

/**
 * 生成模糊图片
 * @param radius 模糊半径
 * @param samplingRate 采样率, 默认为1, 1表示不缩放, 2表示缩放为原来的1/2, 以此类推
 * @return 模糊后的图片
 */
suspend fun Bitmap.blur(radius: Int = DEF_BLUR_RADIUS, samplingRate: Int = 1): Bitmap {
    return withContext(Dispatchers.Default) {
        // 确保位图格式是支持的格式
        val validBitmap =
            if (config != Bitmap.Config.ARGB_8888 && config != Bitmap.Config.ALPHA_8) {
                // 如果格式不支持，则转换为 ARGB_8888
                copy(Bitmap.Config.ARGB_8888, true)
            } else {
                // 如果格式已经支持，则直接使用原始位图
                this@blur
            }

        val blurBmp = if (samplingRate != 1) {
            val scaledBitmap = validBitmap.scale(width / samplingRate, height / samplingRate, false)
            val blurImg = Toolkit.blur(scaledBitmap, radius).also {
                scaledBitmap.recycle()
            }
            blurImg.scale(width, height, false).also {
                blurImg.recycle()
            }
        } else {
            Toolkit.blur(this@blur, radius)
        }
        // 如果原始位图不是支持的格式，回收原始位图
        if (this@blur !== validBitmap) {
            validBitmap.recycle()
        }
        blurBmp
    }
}