package com.czur.starry.device.baselib.base.v2.fragment.floating

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.app.Activity
import android.graphics.PointF
import android.view.View
import android.view.animation.AccelerateInterpolator
import android.view.animation.DecelerateInterpolator
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.Constants.ANIM_DURATION_SHORT
import com.czur.starry.device.baselib.common.hw.StarryModel
import com.czur.starry.device.baselib.utils.getTopControlBarHeight
import com.czur.starry.device.baselib.utils.getScreenHeight
import com.czur.starry.device.baselib.utils.getScreenWidth
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.invisible
import com.czur.starry.device.baselib.utils.show

/**
 * Created by 陈丰尧 on 2023/6/12
 * 用来处理悬浮窗的动画部分
 */
private const val TAG = "CZFloatingFragmentAnimHelper"

private const val ANIM_DURATION_BG = ANIM_DURATION_SHORT
private const val ANIM_DURATION_ENTER = ANIM_DURATION_SHORT

private const val ANIM_PARAMS_SCALE_FROM = 0.4F
private const val ANIM_PARAMS_SCALE_ALPHA_FROM = 0.5F

internal class CZFloatingFragmentAnimHelper(private val floatingFragment: CZVBFloatingFragment<*>) {
    // 进入动画的差值器
    private val enterInterpolator = DecelerateInterpolator()

    // 退出动画的差值器
    private val outInterpolator = AccelerateInterpolator()

    var enterAnimParam: CZFloatingAnimParam? = null

    fun showFloatingBgEnterAnim(bgMode: CZVBFloatingFragment.FloatingBgMode) {
        when (bgMode) {
            CZVBFloatingFragment.FloatingBgMode.Dark -> {
                floatingFragment.outBinding.floatingDarkBgView.show()
                floatingFragment.outBinding.floatImgBgView.gone()
                floatingFragment.outBinding.floatingDarkFrontView.gone()
            }

            is CZVBFloatingFragment.FloatingBgMode.Image -> {
                floatingFragment.outBinding.floatingDarkBgView.gone()
                floatingFragment.outBinding.floatImgBgView.apply {
                    show()
                    setImageBitmap(bgMode.bitmap)
                }
                floatingFragment.outBinding.floatingDarkFrontView.gone(!bgMode.darkImg)
            }

            CZVBFloatingFragment.FloatingBgMode.NONE -> floatingFragment.outBinding.floatingBgFrameLayout.gone()
        }

        if (bgMode != CZVBFloatingFragment.FloatingBgMode.NONE) {
            floatingFragment.outBinding.floatingBgFrameLayout.apply {
                invisible()
                alpha = 0f
                show()
                animate().alpha(1f)
                    .setDuration(ANIM_DURATION_BG)
                    .setInterpolator(enterInterpolator).start()
            }
        }
    }

    /**
     * 背景的消失动画
     */
    fun showFloatingBgExitAnim(bgMode: CZVBFloatingFragment.FloatingBgMode) {
        if (bgMode != CZVBFloatingFragment.FloatingBgMode.NONE) {
            floatingFragment.outBinding
                .floatingBgFrameLayout
                .animate().alpha(0f)
                .setDuration(ANIM_DURATION_BG)
                .setInterpolator(outInterpolator)
                .start()
        }
    }


    /**
     * 展示进入动画
     */
    fun playEnterAnim(floatView: View, onAnimAnd: () -> Unit) {
        val animParams = enterAnimParam ?: CZFloatingAnimParam.DEFAULT.also {
            logTagD(
                TAG,
                "没有指定动画参数, 使用默认参数"
            )
        }
        val targetPointF = calculateLocation(floatView, animParams) // 目标位置
        // 播放动画
        when (animParams.animType) {
            is CZFloatingAnimParam.AnimType.SCALE -> playScaleAnim(
                floatView,
                targetPointF,
                onAnimAnd
            )

            is CZFloatingAnimParam.AnimType.TRANSLATE -> {
                val startPointF =
                    calculateStartLocation(animParams.animType, targetPointF, floatView) // 起始位置
                playTransAnim(floatView, startPointF, targetPointF, onAnimAnd)
            }
        }
    }

    /**
     * 播放退出动画
     */
    fun playExitAnim(floatView: View, onAnimAnd: () -> Unit) {
        val animParams = enterAnimParam ?: CZFloatingAnimParam.DEFAULT
        // 播放动画
        when (animParams.animType) {
            is CZFloatingAnimParam.AnimType.SCALE -> {
                val anim = floatView.animate()
                anim.scaleX(ANIM_PARAMS_SCALE_FROM)
                    .scaleY(ANIM_PARAMS_SCALE_FROM)
                    .alpha(ANIM_PARAMS_SCALE_ALPHA_FROM)
                    .setDuration(ANIM_DURATION_ENTER)
                    .setInterpolator(outInterpolator)
                    .setListener(object : AnimatorListenerAdapter() {
                        override fun onAnimationEnd(animation: Animator) {
                            super.onAnimationEnd(animation)
                            anim.setListener(null)
                            onAnimAnd()
                        }
                    }).start()
            }

            is CZFloatingAnimParam.AnimType.TRANSLATE -> {
                val targetPointF = PointF(floatView.x, floatView.y)
                val endPointF =
                    calculateStartLocation(animParams.animType, targetPointF, floatView) // 起始位置
                val anim = floatView.animate()
                anim.x(endPointF.x)
                    .y(endPointF.y)
                    .translationZ(-floatView.elevation)
                    .setDuration(ANIM_DURATION_ENTER)
                    .setInterpolator(outInterpolator)
                    .setListener(object : AnimatorListenerAdapter() {
                        override fun onAnimationEnd(animation: Animator) {
                            super.onAnimationEnd(animation)
                            anim.setListener(null)
                            onAnimAnd()
                        }
                    }).start()
            }
        }
    }

    private fun playScaleAnim(floatView: View, targetPointF: PointF, onAnimAnd: () -> Unit) {
        val fromZ = -floatView.elevation
        floatView.apply {
            invisible()
            x = targetPointF.x
            y = targetPointF.y
            translationZ = fromZ
            alpha = ANIM_PARAMS_SCALE_ALPHA_FROM
            scaleX = ANIM_PARAMS_SCALE_FROM
            scaleY = ANIM_PARAMS_SCALE_FROM


            show()
            val anim = animate()
            anim.scaleX(1F)
                .scaleY(1F)
                .alpha(1F)
                .translationZ(0F)
                .setDuration(ANIM_DURATION_ENTER)
                .setInterpolator(enterInterpolator)
                .setListener(object : AnimatorListenerAdapter() {
                    override fun onAnimationEnd(animation: Animator) {
                        super.onAnimationEnd(animation)
                        anim.setListener(null)
                        onAnimAnd()
                    }
                }).start()
        }
    }

    private fun playTransAnim(
        floatView: View,
        startPointF: PointF,
        targetPointF: PointF,
        onAnimAnd: () -> Unit
    ) {
        val fromZ = -floatView.elevation

        floatView.apply {
            invisible()
            x = startPointF.x
            y = startPointF.y
            translationZ = fromZ
            show()
            val anim = animate()
            anim.x(targetPointF.x)
                .y(targetPointF.y)
                .translationZ(0F)
                .setDuration(ANIM_DURATION_ENTER)
                .setInterpolator(enterInterpolator)
                .setListener(object : AnimatorListenerAdapter() {
                    override fun onAnimationEnd(animation: Animator) {
                        super.onAnimationEnd(animation)
                        anim.setListener(null)
                        onAnimAnd()
                    }
                }).start()
        }
    }

    private fun calculateStartLocation(
        animType: CZFloatingAnimParam.AnimType,
        targetPointF: PointF,
        floatView: View
    ): PointF {
        return when (animType) {
            // 缩放动画的起始位置就是目标位置
            CZFloatingAnimParam.AnimType.SCALE -> PointF(targetPointF.x, targetPointF.y)
            // 平移动画的起始位置, 根据方向不同, 在屏幕外
            is CZFloatingAnimParam.AnimType.TRANSLATE -> when (animType.direction) {
                CZFloatingAnimParam.AnimType.TRANSLATE.AnimDirection.LEFT -> PointF(
                    -floatView.width.toFloat(),
                    targetPointF.y
                )

                CZFloatingAnimParam.AnimType.TRANSLATE.AnimDirection.TOP -> PointF(
                    targetPointF.x,
                    -floatView.height.toFloat()
                )

                CZFloatingAnimParam.AnimType.TRANSLATE.AnimDirection.RIGHT -> PointF(
                    getScreenWidth().toFloat(),
                    targetPointF.y
                )

                CZFloatingAnimParam.AnimType.TRANSLATE.AnimDirection.BOTTOM -> PointF(
                    targetPointF.x,
                    getScreenHeight().toFloat()
                )
            }
        }
    }

    /**
     * 计算动画要移动到的位置
     */
    private fun calculateLocation(floatView: View, animParams: CZFloatingAnimParam): PointF {
        return when (animParams.animLocation) {
            // 通过屏幕位置来计算
            is CZFloatingAnimParam.AnimLocation.AnimLocationByScreen ->
                calculateLocationByScreen(floatView, animParams.animLocation)
            // 通过View来计算
            is CZFloatingAnimParam.AnimLocation.AnimLocationByView ->
                calculateLocationByView(floatView, animParams.animLocation)
        }
    }


    /**
     * 通过屏幕位置来计算
     */
    private fun calculateLocationByScreen(
        floatView: View,
        animLocation: CZFloatingAnimParam.AnimLocation.AnimLocationByScreen
    ): PointF {
        var dx = when (animLocation.alignment.horizontal) {
            CZFloatingAnimParam.AnimLocation.AlignmentHorizontal.LEFT -> 0F
            CZFloatingAnimParam.AnimLocation.AlignmentHorizontal.CENTER -> (getScreenWidth() - floatView.width) / 2F
            CZFloatingAnimParam.AnimLocation.AlignmentHorizontal.RIGHT -> (getScreenWidth() - floatView.width).toFloat()
        }

        var dy = when (animLocation.alignment.vertical) {
            CZFloatingAnimParam.AnimLocation.AlignmentVertical.TOP -> 0F
            CZFloatingAnimParam.AnimLocation.AlignmentVertical.CENTER -> (getScreenHeight() - floatView.height) / 2F
            CZFloatingAnimParam.AnimLocation.AlignmentVertical.BOTTOM -> (getScreenHeight() - floatView.height).toFloat()
        }

        dx += animLocation.showOffset.offsetX
        dy += animLocation.showOffset.offsetY

        return PointF(dx, dy)
    }

    private fun calculateLocationByView(
        floatView: View,
        animLocation: CZFloatingAnimParam.AnimLocation.AnimLocationByView
    ): PointF {
        // 获取View的坐标
        val intArr = IntArray(2)
        animLocation.view.getLocationOnScreen(intArr)
        val viewX = intArr[0]
        var viewY = intArr[1]

        if (Constants.starryHWInfo.model == StarryModel.StudioModel.StudioSPlus){
            if (animLocation.view.context is Activity){
                viewY -= getTopControlBarHeight()
            }
        }
        var dx = when (animLocation.alignment.horizontal) {
            CZFloatingAnimParam.AnimLocation.AlignmentHorizontal.LEFT -> viewX.toFloat()
            CZFloatingAnimParam.AnimLocation.AlignmentHorizontal.CENTER -> viewX + animLocation.view.width / 2F - floatView.width / 2F
            CZFloatingAnimParam.AnimLocation.AlignmentHorizontal.RIGHT -> viewX + animLocation.view.width - floatView.width.toFloat()
        }

        var dy = when (animLocation.alignment.vertical) {
            CZFloatingAnimParam.AnimLocation.AlignmentVertical.TOP -> viewY.toFloat()
            CZFloatingAnimParam.AnimLocation.AlignmentVertical.CENTER -> viewY + animLocation.view.height / 2F - floatView.height / 2F
            CZFloatingAnimParam.AnimLocation.AlignmentVertical.BOTTOM -> {
                viewY + animLocation.view.height.toFloat()
            }
        }

        dx += animLocation.showOffset.offsetX
        dy += animLocation.showOffset.offsetY
        return PointF(dx, dy)
    }
}