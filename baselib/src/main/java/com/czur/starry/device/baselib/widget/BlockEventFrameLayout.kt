package com.czur.starry.device.baselib.widget

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import android.widget.FrameLayout

/**
 * Created by 陈丰尧 on 2022/4/11
 */
class BlockEventFrameLayout @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {
    override fun dispatchTouchEvent(ev: MotionEvent): Bo<PERSON>an {
        //解决recyclerView和viewPager的滑动影响
        //当滑动recyclerView时，告知父控件不要拦截事件，交给子view处理
        parent.requestDisallowInterceptTouchEvent(true)
        return super.dispatchTouchEvent(ev)
    }
    override fun onGenericMotionEvent(event: MotionEvent?): Bo<PERSON>an {
        return true
    }
}