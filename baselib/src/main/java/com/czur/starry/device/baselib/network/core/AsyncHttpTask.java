package com.czur.starry.device.baselib.network.core;

import android.os.Handler;
import android.os.Looper;

import com.czur.czurutils.log.CZURLogUtilsKt;

import java.io.IOException;
import java.lang.reflect.Type;
import java.util.HashMap;

class AsyncHttpTask<T> implements Runnable {

    private String url;
    private boolean isMethodGet;
    private Type type;
    private HashMap<String, String> postParam;
    private MiaoHttpManager.Callback<T> callback;
    private HashMap<String, String> headers;
    private String body;
    private Handler handler = new Handler(Looper.getMainLooper());

    AsyncHttpTask(String url, Type type, MiaoHttpManager.Callback<T> callback, HashMap<String, String> headers, HashMap<String, String> postParam) {
        isMethodGet = true;
        this.url = url;
        this.type = type;
        this.callback = callback;
        this.headers = headers;
        this.postParam = postParam;
    }

    AsyncHttpTask(String url, Type type, HashMap<String, String> postParam, MiaoHttpManager.Callback<T> callback, HashMap<String, String> headers, String body) {
        isMethodGet = false;
        this.url = url;
        this.type = type;
        this.postParam = postParam;
        this.callback = callback;
        this.headers = headers;
        this.body = body;
    }

    @Override
    public void run() {
        try {
            final MiaoHttpEntity<T> entity;
            if (isMethodGet) {
                entity = SyncHttpTask.getInstance().syncGet(url, type, headers, postParam);
            } else {
                entity = SyncHttpTask.getInstance().syncPost(url, type, postParam, headers, body);
            }
            if (entity != null) {
                switch (entity.getCode()) {
                    case MiaoHttpManager.STATUS_SUCCESS:
                        handler.post(() -> callback.onResponse(entity));
                        break;
                    case MiaoHttpManager.STATUS_FAIL:
                        handler.post(new ErrorRunnable(new Exception("服务器异常")));
                        break;
                    case MiaoHttpManager.STATUS_TIMEOUT:
                        break;
                    default:
                        handler.post(() -> callback.onFailure(entity));
                        break;
                }
            } else {
                handler.post(new ErrorRunnable(new IOException("请求失败,还没封装异常处理")));
            }
        } catch (Exception e) {
            handler.post(new ErrorRunnable(e));
        }
    }

    private class ErrorRunnable implements Runnable {
        private Exception exception;

        ErrorRunnable(Exception exception) {
            this.exception = exception;
        }

        @Override
        public void run() {
            CZURLogUtilsKt.logTagE("AsyncHttpTask", new String[]{"exception:"}, exception);
            callback.onError(exception);
        }
    }
}
