package com.czur.starry.device.baselib.base

import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView

/**
 * Created by 陈丰尧 on 2021/10/22
 * 使用DiffUtil来处理的Adapter
 */
abstract class BaseDifferSyncAdapter<T> : RecyclerView.Adapter<BaseVH>() {
    protected val itemCallback = SyncDiffCallBack()
    protected var currentList = listOf<T>()

    open fun setData(newData: List<T>) {
        itemCallback.newList = newData
        val diffResult = DiffUtil.calculateDiff(itemCallback, true)
        currentList = newData
        diffResult.dispatchUpdatesTo(this)
    }

    fun getDataList(): List<T> = currentList
    fun getData(pos: Int): T = currentList[pos]

    override fun onBindViewHolder(holder: BaseVH, position: Int) {
        val itemData = getData(position)
        bindViewHolder(holder, position, itemData)
    }

    override fun getItemCount(): Int = currentList.size
    abstract fun bindViewHolder(holder: BaseVH, position: Int, itemData: T)
    abstract fun areItemsTheSame(oldItem: T, newItem: T): Boolean
    abstract fun areContentsTheSame(oldItem: T, newItem: T): Boolean

    protected inner class SyncDiffCallBack : DiffUtil.Callback() {
        var newList: List<T> = currentList

        override fun getOldListSize(): Int {
            return currentList.size
        }

        override fun getNewListSize(): Int {
            return newList.size
        }

        override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            val oldItem = currentList[oldItemPosition]
            val newItem = newList[newItemPosition]
            return areItemsTheSame(oldItem, newItem)
        }

        override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            val oldItem = currentList[oldItemPosition]
            val newItem = newList[newItemPosition]
            return areContentsTheSame(oldItem, newItem)
        }

    }
}