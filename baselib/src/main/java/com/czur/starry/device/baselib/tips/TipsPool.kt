package com.czur.starry.device.baselib.tips

import android.annotation.SuppressLint
import android.view.View

/**
 * Created by 陈丰尧 on 2021/11/9
 */
object TipsPool {
    @SuppressLint("StaticFieldLeak")
    private var currentListener: FloatTipHoverListener? = null

    fun updatePopup(l: FloatTipHoverListener) {
        currentListener?.tipsPopup?.dismiss()
        currentListener = l
    }

    fun removePop(l: FloatTipHoverListener) {
        l.tipsPopup.dismiss()
        if (currentListener === l) {
            currentListener = null
        }
    }

    fun clearPop() {
        currentListener?.tipsPopup?.dismiss()
        currentListener?.removeAllHandlerMsg()
        currentListener = null
    }

    fun clearPopByIds(vararg ids: Int) {
        currentListener?.let {
            if (it.viewId != View.NO_ID && it.viewId in ids) {
                clearPop()
            }
        }
    }
}