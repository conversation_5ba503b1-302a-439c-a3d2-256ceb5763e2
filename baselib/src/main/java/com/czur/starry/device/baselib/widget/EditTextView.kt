package com.czur.starry.device.baselib.widget

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.Drawable
import android.text.InputFilter
import android.util.AttributeSet
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.FrameLayout
import android.widget.TextView
import androidx.core.widget.doAfterTextChanged
import com.czur.starry.device.baselib.R
import com.czur.starry.device.baselib.utils.doOnLoseFocus
import com.czur.starry.device.baselib.utils.keyboard.hideKeyboard
import com.czur.starry.device.baselib.utils.keyboard.keyboardHide
import com.noober.background.drawable.DrawableCreator


class EditTextView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0,
) : FrameLayout(context, attrs, defStyleAttr) {
    companion object {
        private const val DEF_SOLID_COLOR: Int = 0x0D000000 // 20%
        private const val DEF_EDIT_COLOR: Int = 0x33000000  // 5%
    }

    // 是否可以进入编辑模式
    var editable = true
        set(value) {
            if (field != value) {
                field = value
                updateUI()
            }
        }
    var editMode: Boolean = false
        set(value) {
            if (field != value) {
                field = value
                updateUI()
                editModeChangeListener?.invoke(value)
            }
        }

    var text: String
        set(value) {
            editText.setText(value)
            editText.setSelection(editText.text.length)
        }
        get() = editText.text?.toString() ?: ""

    var filters: Array<InputFilter>
        get() = editText.filters
        set(value) {
            editText.filters = value
        }

    private val hint: String

    var editModeChangeListener: ((editMode: Boolean) -> Unit)? = null
    var onTextChangeListener: ((text: String?) -> Unit)? = null
    var onEnterClick: (() -> Boolean)? = null

    private val showBg: Drawable by lazy {
        DrawableCreator.Builder()
            .setCornersRadius(10F)
            .setSolidColor(DEF_SOLID_COLOR)
            .build()
    }
    private val editBg: Drawable by lazy {
        DrawableCreator.Builder()
            .setCornersRadius(10F)
            .setSolidColor(DEF_EDIT_COLOR)
            .build()
    }

    private val editText: EditText by lazy {
        findViewById(R.id.editText)
    }
    private val textView: TextView by lazy {
        findViewById(R.id.textView)
    }

    // 显示时的文字颜色
    private var showTextColor: Int

    init {
        val ta = context.obtainStyledAttributes(attrs, R.styleable.EditTextView)
        hint = ta.getString(R.styleable.EditTextView_hint) ?: ""
        showTextColor = ta.getColor(R.styleable.EditTextView_showTextColor, Color.WHITE)
        ta.recycle()

        LayoutInflater.from(context).inflate(R.layout.baselib_widget_edittextview, this, true)
        initView()
    }

    private fun updateUI() {
        // 是否可以编辑, 切换背景
        background = if (!editable) {
            null
        } else if (editMode) {
            editBg
        } else {
            showBg
        }

        if (editMode && editable) {
            textView.visibility = View.GONE
            editText.visibility = View.VISIBLE
            onTextChangeListener?.invoke(editText.text?.toString())
            showSoftInput(editText)
            editText.post {
                editText.setSelection(editText.text.toString().length)
            }
        } else {
            editText.visibility = View.GONE
            textView.visibility = View.VISIBLE
        }

    }

    private fun initView() {
        updateUI()

        textView.setTextColor(showTextColor)
        textView.setOnClickListener {
            if (editable) {
                editMode = true
            }
        }

        editText.doOnLoseFocus {
            hideKeyboard(editText.windowToken)
        }
        editText.doAfterTextChanged {
            textView.text = it.toString()
            onTextChangeListener?.invoke(it?.toString())
        }

        editText.setOnEditorActionListener { v, actionId, event ->
            if (actionId == EditorInfo.IME_ACTION_DONE  // 输入法软键盘的发送按钮
                || (event?.keyCode == KeyEvent.KEYCODE_ENTER && event.action == KeyEvent.ACTION_DOWN) // 键盘回车键
            ) {
                onEnterClick?.invoke() ?: false
            } else {
                false
            }
        }
    }

    /**
     * 设置InputType
     */
    fun setInputType(type: Int) {
        editText.inputType = type
    }

    fun setHint(hint: String) {
        editText.hint = hint
    }

    fun keyboardHide() {
        editText.keyboardHide()
    }


    private fun showSoftInput(editText: EditText) {
        editText.isFocusable = true
        editText.isFocusableInTouchMode = true
        editText.requestFocus()
        val imm: InputMethodManager =
            context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        imm.showSoftInput(editText, 0)
    }
}