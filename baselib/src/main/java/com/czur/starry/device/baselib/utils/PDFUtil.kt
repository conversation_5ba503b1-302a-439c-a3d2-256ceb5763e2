package com.czur.starry.device.baselib.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.RectF
import android.os.Parcel
import android.os.Parcelable
import android.view.Gravity
import android.view.LayoutInflater
import android.widget.TextView
import android.widget.Toast
import com.czur.czurutils.encryption.md5
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.R
import kotlinx.coroutines.runBlocking
import java.math.BigInteger
import java.security.MessageDigest
import java.security.NoSuchAlgorithmException

/**
 * created by wangh 22.0715
 */


object PDFUtil {
    private val TAG = "PDFUtil"


    // 超过200ms的滑动事件, 就认为是2次事件
    val SCROLL_THRESHOLD = 200

    /**
     * For debug overlays. Scale pixel value according to screen density.
     */
    fun dp2px(context: Context, dp: Int): Int {
        return (context.resources.displayMetrics.density * dp + 0.5).toInt()
    }


    /**
     * 将要绘制的pdf页数据
     * 位置信息、bitmap
     */
    data class DrawingPage(
        val pageRect: PageRect?,
        val bitmap: Bitmap?,
        val pageIndex: Int
    ) : Parcelable {
        constructor(parcel: Parcel) : this(
            parcel.readParcelable(PageRect::class.java.classLoader),
            parcel.readParcelable(Bitmap::class.java.classLoader),
            parcel.readInt()
        )

        override fun writeToParcel(parcel: Parcel, flags: Int) {
            parcel.writeParcelable(pageRect, flags)
            parcel.writeParcelable(bitmap, flags)
            parcel.writeInt(pageIndex)
        }

        override fun describeContents(): Int {
            return 0
        }

        companion object CREATOR : Parcelable.Creator<DrawingPage> {
            override fun createFromParcel(parcel: Parcel): DrawingPage {
                return DrawingPage(parcel)
            }

            override fun newArray(size: Int): Array<DrawingPage?> {
                return arrayOfNulls(size)
            }
        }
    }

    /**
     * page的尺寸信息
     */
    class PageRect(
        val fillWidthScale: Float = 1f, //缩放到屏幕宽度的缩放倍数
        val fillWidthRect: RectF //缩放到屏幕宽度的page的尺寸数据
    ) : Parcelable {
        constructor(parcel: Parcel) : this(
            parcel.readFloat(),
            parcel.readParcelable(RectF::class.java.classLoader) ?: RectF()
        )

        override fun writeToParcel(parcel: Parcel, flags: Int) {
            parcel.writeFloat(fillWidthScale)
            parcel.writeParcelable(fillWidthRect, flags)
        }

        override fun describeContents(): Int {
            return 0
        }

        companion object CREATOR : Parcelable.Creator<PageRect> {
            override fun createFromParcel(parcel: Parcel): PageRect {
                return PageRect(parcel)
            }

            override fun newArray(size: Int): Array<PageRect?> {
                return arrayOfNulls(size)
            }
        }

    }


    /**
     * 获取缓存key
     * md5(文件名_页面索引)
     */
    fun getCachedKey(pageIndex: Int, mPdfName: String, pdfSize: Long): String {
        val cacheKey = try {
            runBlocking { "${mPdfName}_$pageIndex${pdfSize}".md5() }
        } catch (e: NoSuchAlgorithmException) {
            e.printStackTrace()
            "${mPdfName.hashCode()}_$pageIndex"
        }
        logTagD(TAG, "getCachedKey--pageIndex:$pageIndex--cacheKey:$cacheKey")
        return cacheKey
    }

    class PDFToast(
        context: Context,
        msg: String
    ) {
        private val toast: Toast = Toast(context)

        init {
            val view = LayoutInflater.from(context)
                .inflate(R.layout.toast_pdf, null)
            val toastMsgTv = view.findViewById<TextView>(R.id.toastMsgTv)
            toastMsgTv.text = msg
            toast.view = view
            toast.setGravity(Gravity.BOTTOM or Gravity.CENTER, 0, 10)
            toast.duration = Toast.LENGTH_LONG
        }

        fun show() {
            toast.show()
        }

        fun cancel() {
            toast.cancel()
        }
    }

    fun calculateInSampleSize(
        options: BitmapFactory.Options,
        mWidth: Int,
        mHeight: Int
    ): Int {
        val width = options.outWidth
        val height = options.outHeight

        var scale = 1
        if (height > mHeight) {
            scale = (height / mHeight)
        } else if (width > mWidth) {
            scale = (width / mWidth)
        }
        if (scale <= 0) {
            scale = 1
        }
        return scale
    }


}