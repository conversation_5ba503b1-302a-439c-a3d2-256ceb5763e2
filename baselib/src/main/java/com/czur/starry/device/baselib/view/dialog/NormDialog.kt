package com.czur.starry.device.baselib.view.dialog

import android.app.Dialog
import android.os.Bundle
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.czur.starry.device.baselib.R
import com.czur.starry.device.baselib.base.BaseDialog
import com.czur.starry.device.baselib.databinding.DialogNormalBinding
import com.czur.starry.device.baselib.utils.getString

/**
 * Created by 陈丰尧 on 3/2/21
 * 普通Dialog, 标题-提示信息-按钮
 */
open class NormDialog : BaseDialog() {
    lateinit var title: String
    lateinit var info: String
    lateinit var confirmBtnText: String
    lateinit var cancelBtnText: String
    private var buttonStyle = ButtonStyle.DOUBLE_BUTTONS
    private var cancelable = true

    var confirmClickListener: (() -> Unit)? = null
    var cancelClickListener: (() -> Unit)? = null

    private lateinit var binding: DialogNormalBinding

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)
        if (!cancelable) {
            dialog.setCanceledOnTouchOutside(false)
            dialog.setCancelable(false)
        }
        dialog.setOnKeyListener { dialog, keyCode, event ->
            if (!cancelable) {
                if (keyCode == KeyEvent.KEYCODE_BACK && event.action == KeyEvent.ACTION_UP) {
                    true
                } // 屏蔽back键
                else {
                    false
                }
            } else {
                false
            }

        }
        return dialog;
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogNormalBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.normalDialogTitleTv.text = title
        binding.normalDialogContentTv.text = info
        binding.normalDialogConfirmBtn.text = confirmBtnText
        binding.normalDialogCancelBtn.text = cancelBtnText

        if (buttonStyle == ButtonStyle.SINGLE_BUTTON) {
            binding.normalDialogCancelBtn.visibility = View.GONE
        }
        binding.normalDialogConfirmBtn.setOnClickListener {
            confirmClickListener?.invoke()
            dismiss()
        }
        binding.normalDialogCancelBtn.setOnClickListener {
            cancelClickListener?.invoke()
            dismiss()
        }
    }

    class Builder {
        private var title = ""
        private var info = ""
        private var confirmText = getString(R.string.dialog_normal_confirm)
        private var cancelText = getString(R.string.dialog_normal_cancel)
        private var confirmClickListener: (() -> Unit)? = null
        private var cancelClickListener: (() -> Unit)? = null
        private var cancelable = false
        private var canceledOnTouchOutside = false
        private var buttonStyle = ButtonStyle.DOUBLE_BUTTONS

        fun setTitle(title: String): Builder {
            this.title = title
            return this
        }

        fun setInfo(info: String): Builder {
            this.info = info
            return this
        }

        fun setConfirmText(text: String): Builder {
            this.confirmText = text
            return this
        }

        fun setCancelText(text: String): Builder {
            this.cancelText = text
            return this
        }

        fun setConfirmClickListener(listener: () -> Unit): Builder {
            confirmClickListener = listener
            return this
        }

        fun setCancelClickListener(listener: () -> Unit): Builder {
            cancelClickListener = listener
            return this
        }

        fun setCancelable(cancelable: Boolean): Builder {
            this.cancelable = cancelable
            return this
        }

        fun setCanceledOnTouchOutside(canceledOnTouchOutside: Boolean): Builder {
            this.canceledOnTouchOutside = canceledOnTouchOutside
            return this
        }

        fun setButtonStyle(buttonStyle: ButtonStyle): Builder {
            this.buttonStyle = buttonStyle
            return this
        }

        fun build(): NormDialog {
            val dialog = NormDialog()
            dialog.title = title
            dialog.info = info
            dialog.confirmBtnText = confirmText
            dialog.cancelBtnText = cancelText
            dialog.confirmClickListener = confirmClickListener
            dialog.cancelClickListener = cancelClickListener
            dialog.buttonStyle = buttonStyle
            dialog.cancelable = cancelable
            return dialog
        }
    }

    enum class ButtonStyle {
        SINGLE_BUTTON, DOUBLE_BUTTONS
    }
}