// Top-level build file where you can add configuration options common to all sub-projects/modules.
plugins {
    alias(libs.plugins.application).apply(false)
    alias(libs.plugins.library).apply(false)
    alias(libs.plugins.kotlinAndroid).apply(false)
    alias(libs.plugins.kotlinNoarg).apply(false)
    alias(libs.plugins.navigationSafeargs).apply(false)
    alias(libs.plugins.kotlin.serialization) apply false
}

ext {
//    签名信息
    set("storePassword", "czur9007")
    set("keyAlias", "starry")
    set("keyPassword", "czur9007")

    // 通用配置
    set("atyConfigChange","fontScale|keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|navigation|layoutDirection|touchscreen")
    set("startUpatyConfigChange","fontScale|keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|navigation|layoutDirection|touchscreen|locale|layoutDirection")
}

subprojects {
    getTasksByName("assembleUnsigned", false).forEach {
        copyApkFile(it, project)
    }

    getTasksByName("assembleDebug", false).forEach {
        copyApkFile(it, project)
    }
}

/**
 * 复制APK文件 到apks路径下
 */
private fun copyApkFile(tasks: Task, project: Project) {
    if (project.plugins.hasPlugin("com.android.application")) {
        println("===> Handle Project Config by [com.android.application] Logic")
    } else {
        return
    }

    val appModuleDir = project.projectDir.absolutePath // app module dir
    val buildDir = File("$appModuleDir/build/outputs/apk")
    val destDir = File("$rootDir/apks")
    if (buildDir.exists()) {
        buildDir.deleteRecursively()    // 删除旧的apk文件
    }
    tasks.doLast {
        println("doLast: ${tasks.name}")
        buildDir.walkTopDown().filter { it.name.endsWith(".apk") }.forEach {
            val destFileAbs = it.absolutePath.replace(buildDir.absolutePath, destDir.absolutePath)
            val destFile = File(destFileAbs)
            println("copy APK: ${it.name} -> $destFileAbs")
            it.copyTo(destFile, true)
        }
    }
}
