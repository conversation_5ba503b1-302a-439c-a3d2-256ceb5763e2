package com.czur.starry.device.diagnosis.command

/**
 * Created by 陈丰尧 on 2021/9/14
 */
data class Command(val name: String, val command: Array<String>) {
    val hasNoName: Boolean
        get() = name.isEmpty()

    override fun equals(other: Any?): <PERSON><PERSON>an {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as Command

        if (name != other.name) return false
        if (!command.contentEquals(other.command)) return false

        return true
    }

    override fun hashCode(): Int {
        var result = name.hashCode()
        result = 31 * result + command.contentHashCode()
        return result
    }
}

fun command(name: String, commandBuilder: CmdBuilder.() -> List<String>): Command {
    return Command(name, commandBuilder(CmdBuilder()).toTypedArray())
}

class CmdBuilder {
    operator fun String.unaryPlus(): List<String> = listOf(this)
}



