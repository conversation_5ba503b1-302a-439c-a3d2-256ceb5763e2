package com.czur.starry.device.diagnosis.algo

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import androidx.lifecycle.LifecycleService
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.utils.AudioUtil
import com.czur.starry.device.baselib.utils.ONE_MIN
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.SettingUtil
import com.czur.starry.device.baselib.utils.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.withContext
import java.lang.Exception
import java.util.concurrent.atomic.AtomicBoolean

/**
 * Created by 陈丰尧 on 2023/5/17
 * 算法收集的服务
 * 收集条件:
 * 1. 灭屏
 * 2. voip 的prop 为空(没有应用在使用Mic)
 */
private const val TAG = "AlgoCollectService"
private const val WATCH_VOIP_INTERVAL = ONE_MIN
private const val VOIP_IDLE_TIMES = 10

class AlgoCollectService : LifecycleService() {
    private var screenReceiver: ScreenReceiver? = null
    private var watchVoipJob: Job? = null
    private val audioUtil = AudioUtil()
    private var isUploading = AtomicBoolean(false)
    private val uploadHelper by lazy { UploadHelper() }

    override fun onCreate() {
        super.onCreate()
        logTagD(TAG, "算法信息采集服务启动")
        startWatchSetting()
        registerScreenReceiver()
    }

    private fun registerScreenReceiver() {
        screenReceiver = ScreenReceiver()
        val filter = IntentFilter().apply {
            addAction(Intent.ACTION_SCREEN_ON)
            addAction(Intent.ACTION_SCREEN_OFF)
        }
        registerReceiver(screenReceiver, filter)
    }

    private fun startWatchVoip() {
        stopWatchVoip()
        watchVoipJob = launch {
            logTagD(TAG, "开始监听Voip")
            var idleTimes = 0
            while (isActive) {
                delay(WATCH_VOIP_INTERVAL)
                if (isUploading.get() || audioUtil.getUseMicPid().isEmpty()) {
                    idleTimes++
                    logTagV(TAG, "设备空闲检查:空闲${idleTimes}次")
                } else {
                    idleTimes = 0
                }

                if (idleTimes >= VOIP_IDLE_TIMES) {
                    logTagD(TAG, "设备空闲, 开始上传算法调试数据")
                    startUploadAlgoDebugData()
                    uploadHelper.clearEmptyFolder() // 上传完成后清空空文件夹
                    idleTimes = 0
                }
            }
        }
    }

    private suspend fun startUploadAlgoDebugData() {
        if (isUploading.get()) {
            logTagW(TAG, "正在上传算法调试数据, 不重复上传")
            return
        }
        isUploading.set(true)
        logTagV(TAG, "检查是否有算法调试数据")
        if (!uploadHelper.hasLogFile()) {
            logTagI(TAG, "没有算法调试数据")
            isUploading.set(false)
            stopWatchVoip()
            return
        }

        // 有算法调试数据, 开始上传
        try {
            logTagD(TAG, "开始上传算法调试数据")
            uploadHelper.startUpload()
        } catch (e: Throwable) {
            logTagW(TAG, "上传算法调试数据失败: ${e.message}")
        } finally {
            isUploading.set(false)
        }
    }

    private fun stopWatchVoip() {
        watchVoipJob?.let {
            logTagD(TAG, "停止监听Voip")
            it.cancel()
            watchVoipJob = null
        }
    }

    private fun startWatchSetting() {
        // 监听设置变化
        logTagV(TAG, "监听设置变化")
        SettingUtil.CloudConfigSetting.registerObserver(this) {
            logTagD(TAG, "CloudConfigSetting changed: $it")
            if (!SettingUtil.CloudConfigSetting.isEnableAlgoCollection()) {
                logTagD(TAG, "不开启算法数据采集服务, 停止Service")
                stopSelf()
            }
        }
    }

    override fun onDestroy() {
        logTagD(TAG, "算法信息采集服务停止")
        unregisterReceiver(screenReceiver)
        super.onDestroy()
    }

    private inner class ScreenReceiver : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent) {
            if (intent.action == Intent.ACTION_SCREEN_ON) {
                logTagD(TAG, "屏幕亮起")
                stopWatchVoip()
            } else if (intent.action == Intent.ACTION_SCREEN_OFF) {
                logTagD(TAG, "屏幕关闭")
                startWatchVoip()
            }
        }
    }
}