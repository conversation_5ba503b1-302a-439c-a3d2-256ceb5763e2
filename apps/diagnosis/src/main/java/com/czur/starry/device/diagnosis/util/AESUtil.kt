package com.czur.starry.device.diagnosis.util

import java.io.InputStream
import java.nio.charset.StandardCharsets
import javax.crypto.Cipher
import javax.crypto.CipherInputStream
import javax.crypto.spec.IvParameterSpec
import javax.crypto.spec.SecretKeySpec

/**
 * Created by 陈丰尧 on 2021/9/15
 */

/**
 * 秘钥长度需要是16,24,32字节, 这里使用16字节
 */
private const val KEY: String = "czur9007czur9007"

/**
 * 加密算法
 */
private const val KEY_ALGORITHM = "AES"


/**
 * 字符编码
 */
private val CHARSET_UTF8 = StandardCharsets.UTF_8

/**
 * 加解密算法/工作模式/填充方式
 */
private const val CIPHER_ALGORITHM = "AES/CBC/PKCS5Padding"


/**
 * 初始化 AES Cipher
 *
 * @return 密钥
 */
private fun createEncodeCipher(): Cipher {
    // 创建密钥
    val secretKeySpec = SecretKeySpec(KEY.toByteArray(CHARSET_UTF8), KEY_ALGORITHM)
    // 获取密钥
    val cipher = Cipher.getInstance(CIPHER_ALGORITHM)
    // 偏移量
    val iv = IvParameterSpec(KEY.toByteArray(CHARSET_UTF8))
    // 初始化
    cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec, iv)
    return cipher
}


/********** public API **********/


val encodeCipher by lazy {
    createEncodeCipher()
}

/**
 * 将输入流转换成加密输入流
 */
fun InputStream.encodeCipherStream(): CipherInputStream = CipherInputStream(this, encodeCipher)