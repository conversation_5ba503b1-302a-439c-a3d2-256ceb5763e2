package com.czur.starry.device.diagnosis.android

import android.os.Build
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.utils.doWithoutCatch
import com.czur.starry.device.baselib.utils.fw.proxy.SystemManagerProxy
import com.czur.starry.device.diagnosis.LogCollectExecutor
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File

/**
 * Created by 陈丰尧 on 2023/5/5
 * 收集设备的硬件信息, 包括以下内容
 * 1. SN号
 * 2. MAC地址
 * 3. 固件版本
 */
private const val EMPTY_MAC = "02:00:00:00:00:00"

private const val WIFI_MAC_FILE_PATH = "/sys/class/net/wlan0/address"
private const val HOTSPOT_MAC_FILE_PATH = "/sys/class/net/wlan1/address"

class HardwareInfoExecutor : LogCollectExecutor() {
    private val systemManager: SystemManagerProxy by lazy {
        SystemManagerProxy()
    }

    override val outputFileName: String
        get() = "HardwareInfo.txt"

    override suspend fun doExec(file: File) {
        val pw = file.printWriter()
        // 1. SN号
        val sn = Constants.SERIAL
        pw.println("SN: $sn")

        // 2. MAC地址
        val wifiMac = getMacAddress(WIFI_MAC_FILE_PATH)
        val hotspotMac = getMacAddress(HOTSPOT_MAC_FILE_PATH)
        val ethMac = systemManager.getEthernetMac()
        pw.println()
        pw.println("==== MAC ====")
        pw.print("WIFI MAC: $wifiMac")
        pw.print("ETH MAC: $ethMac")
        pw.print("HOTSPOT MAC: $hotspotMac")
        pw.println()
        // 3. 固件版本
        val firmwareVersion = Constants.FIRMWARE_NAME
        pw.println("Firmware Version: $firmwareVersion")
        pw.close()
    }


    private suspend fun getMacAddress(filePath: String): String = withContext(Dispatchers.IO) {
        var wifiAddress = EMPTY_MAC
        doWithoutCatch {
            wifiAddress = File(filePath).readText()
        }
        wifiAddress
    }
}