package com.czur.starry.device.diagnosis.command

import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.doWithoutCatch
import com.czur.starry.device.diagnosis.LogCollectExecutor
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import java.io.File
import java.io.InputStreamReader
import java.io.PrintWriter

/**
 * Created by 陈丰尧 on 2021/9/10
 */


/**
 * 命令执行器
 */
abstract class CommandExecutor : LogCollectExecutor() {

    /**
     * 命令列表
     */
    abstract val commandList: List<Command>

    /**
     * 每一条命令的超时时间
     */
    open val timeOut: Long = 0

    override suspend fun doExec(file: File) {
        try {
            val pw = file.printWriter()
            pw.use { writer ->
                commandList.forEach { command ->
                    val commandName = if (command.hasNoName) "" else " (${command.name})"
                    writer.append("\n=========COMMAND START${commandName}=========\n")
                    doWithoutCatch(ignoreError = true) {
                        // 本身就是收集Log, 就不输出日志了
                        runBlocking {
                            execCmd(command.command, writer)
                        }
                    }
                    writer.append("\n=========COMMAND END${commandName}=========\n")
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 执行Command
     */
    private fun CoroutineScope.execCmd(
        command: Array<String>,
        writer: PrintWriter,
    ) {
        val process = Runtime.getRuntime().exec(command)
        // 超时协程
        val timeOutJob = if (timeOut > 0) {
            launch {
                delay(timeOut)
                process.destroy()
            }
        } else null

        process.inputStream.use { inputStream ->
            val reader = InputStreamReader(inputStream)
            reader.forEachLine { line ->
                writer.println(line)
            }
        }

        timeOutJob?.cancel()
    }
}

class LogcatCommandExecutor : CommandExecutor() {
    override val timeOut: Long
        get() = 60 * ONE_SECOND
    override val outputFileName: String
        get() = "LogCommand.txt"
    override val commandList: List<Command>
        get() = listOf(
            command("LogCat") {
                +"logcat" + "-d" + "-v" + "time"
            }
        )
}

class DropBoxCommandExecutor : CommandExecutor() {
    override val outputFileName: String
        get() = "DropBox.txt"
    override val commandList: List<Command>
        get() = listOf(
            command("DropBox") {
                +"dumpsys" + "dropbox" + "--print" + "--file"
            }
        )
}

class BugreportCommandExecutor : CommandExecutor() {
    override val outputFileName: String
        get() = "Bugreport.txt"
    override val commandList: List<Command>
        get() = listOf(
            command("Bugreport") {
                +"/system/bin/bugreport"
            }
        )
}

class DumpCommandExecutor : CommandExecutor() {
    override val outputFileName: String
        get() = "DumpCommand.txt"
    override val commandList: List<Command>
        get() = listOf(
            command("cpuInfo") {
                +"/system/bin/dumpsys" + "cpuinfo"
            }
        )

}

class DMESGCommandExecutor : CommandExecutor() {
    override val outputFileName: String
        get() = "dmesg.txt"
    override val commandList: List<Command>
        get() = listOf(
            command("dmesg") {
                +"dmesg"
            }
        )
}
