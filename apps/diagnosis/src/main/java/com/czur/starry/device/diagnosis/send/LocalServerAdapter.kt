package com.czur.starry.device.diagnosis.send

import android.view.ViewGroup
import com.czur.starry.device.baselib.base.BaseDifferAdapter
import com.czur.starry.device.baselib.base.BaseVH
import com.czur.starry.device.diagnosis.R

/**
 * Created by 陈丰尧 on 2022/8/2
 */
class LocalServerAdapter : BaseDifferAdapter<LocalServerInfoEntity>() {
    override fun bindViewHolder(holder: BaseVH, position: Int, itemData: LocalServerInfoEntity) {
        holder.setText(itemData.name, R.id.serverNameTv)
    }

    override fun areItemsTheSame(
        oldItem: LocalServerInfoEntity,
        newItem: LocalServerInfoEntity
    ): Boolean = oldItem.id == newItem.id

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseVH {
        return BaseVH(R.layout.item_local_server, parent)
    }
}