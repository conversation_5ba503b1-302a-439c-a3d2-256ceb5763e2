package com.czur.starry.device.diagnosis.db.entity

import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 *  author : <PERSON><PERSON><PERSON>
 *  time   :2024/09/18
 */

@Entity(tableName = "tab_crash_info")
data class CrashInfoEntity (
    val processName: String,
    val packageName: String,
    val appName: String,
    val currentDate: String,
    val currentTime: Long,
    val sn: String,
    val version: String,
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0L
)