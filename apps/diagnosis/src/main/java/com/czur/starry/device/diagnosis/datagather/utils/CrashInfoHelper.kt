package com.czur.starry.device.diagnosis.datagather.utils

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.os.Build
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import com.czur.czurutils.global.globalAppCtx
import com.czur.czurutils.log.logTagE
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.network.HttpManager
import com.czur.starry.device.baselib.utils.doWithoutCatch
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.diagnosis.datagather.net.IUploadService
import com.czur.starry.device.diagnosis.db.DiagnosisDatabase
import com.czur.starry.device.diagnosis.db.entity.CrashInfoEntity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 *  author : WangHao
 *  time   :2024/09/19
 */

private const val TAG = "CrashInfoHelper"

class CrashInfoHelper(
    private val context: Context,
    private val lifecycleOwner: LifecycleOwner,
    private val mPackageManager: PackageManager
) : DefaultLifecycleObserver {
    companion object {
        const val ACTION_CRASH_INFO = "com.czur.starry.device.datagatherservice.crash"
        private const val EXTRA_PROCESS = "processName"
        private const val EXTRA_PACKAGE = "packageName"
    }

    private val crashInfoDao by lazy {
        DiagnosisDatabase.instance.CrashInfoDao()
    }

    fun start() {
        //注册崩溃监听广播
        registerCrashInfoReceiver()
    }

    suspend fun upload() = withContext(Dispatchers.IO) {
        val list = crashInfoDao.getAllCrashInfoList(getCurrentDate()) as List<CrashInfoEntity>
        doWithoutCatch {
            if (list.isNullOrEmpty()) {
                return@doWithoutCatch
            }
            val result = HttpManager.getService<IUploadService>(Constants.OTA_BASE_URL).uploadCrashInfo(list)
            if (result.isSuccess) {
                crashInfoDao.clearAll(getCurrentDate())
            }
        }
    }

    private fun registerCrashInfoReceiver() {
        val crashInfoReceiverFilter = IntentFilter().apply {
            addAction(ACTION_CRASH_INFO)
        }
        context.registerReceiver(crashInfoReceiver, crashInfoReceiverFilter)
    }

    override fun onDestroy(owner: LifecycleOwner) {
        super.onDestroy(owner)
        globalAppCtx.unregisterReceiver(crashInfoReceiver)
    }


    suspend fun savaCrashInfoToDb(processName: String, packageName: String, appName: String) =
        withContext(
            Dispatchers.IO
        ) {
            crashInfoDao.insert(
                CrashInfoEntity(
                    processName,
                    packageName,
                    appName,
                    getCurrentDate(),
                    System.currentTimeMillis(),
                    Constants.SERIAL,
                    Constants.FIRMWARE_NAME
                )
            )
        }

    private var crashInfoReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            logTagE(TAG, "收到崩溃信息")
            val processName = intent?.getStringExtra(EXTRA_PROCESS) ?: ""
            val packageName = intent?.getStringExtra(EXTRA_PACKAGE) ?: ""
            val applicationInfo = mPackageManager.getApplicationInfo(packageName, 0)
            val appName: String =
                applicationInfo?.let { mPackageManager.getApplicationLabel(it) } as String
            logTagE(TAG, "====appName:$appName====packageName:$packageName")
            lifecycleOwner.launch {
                savaCrashInfoToDb(processName, packageName, appName)
            }
        }
    }
}