package com.czur.starry.device.diagnosis

import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.network.core.MiaoHttpEntity
import com.czur.starry.device.baselib.network.core.MiaoHttpGet
import com.czur.starry.device.baselib.network.core.MiaoHttpParam
import com.czur.starry.device.baselib.network.core.MiaoHttpPost
import com.czur.starry.device.temposs.OSSTokenEntity

/**
 * Created by 陈丰尧 on 2021/9/16
 * 网络接口
 */
interface NetService {
    /**
     * 提交日志记录
     * @param sn        设备SN号, 会自动获取
     * @param deviceFeedbackType    反馈类型 用逗号隔开, 如果为空,则默认为others
     * @param contactWay 用户输入的联系方式(电话或邮箱), 可能为空
     * @param ossKey    log文件OSS路径, log路径, 可能为空
     * @param feedback  用户输入的反馈内容, 可能为空
     */
    @MiaoHttpPost("/api/starry/logs/record")
    fun logRecord(
        @MiaoHttpParam("sn")
        sn: String = Constants.SERIAL,
        @MiaoHttpParam("deviceFeedbackType")
        deviceFeedbackType: String = "others",
        @MiaoHttpParam("contactWay")
        contactWay: String? = null,
        @MiaoHttpParam("ossKey")
        ossKey: String? = null,
        @MiaoHttpParam("feedback")
        feedback: String? = null,
        clazz: Class<Int> = Int::class.java,
    ): MiaoHttpEntity<Int>

    @MiaoHttpGet("/api/starry/improvement/oss/token")
    fun getToken(
        clazz: Class<OSSTokenEntity> = OSSTokenEntity::class.java
    ): MiaoHttpEntity<OSSTokenEntity>

    /**
     * 上传记录
     * @param createTime    文件生成时间,格式yyyy-MM-dd HH:mm:ss
     * @param fileName      文件名
     * @param sn            设备SN号, 会自动获取
     */
    @MiaoHttpPost("/api/starry/improvement/updateRecord")
    fun updateRecord(
        @MiaoHttpParam("createTime")
        createTime:String,
        @MiaoHttpParam("fileName")
        fileName:String,
        @MiaoHttpParam("sn")
        sn:String = Constants.SERIAL,
        clazz: Class<String> = String::class.java
    ): MiaoHttpEntity<String>
}