<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="600px"
    android:layout_height="800px"
    android:background="@color/white">

    <TextView
        android:id="@+id/pcListTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="接收端列表"
        android:textColor="@color/black"
        android:textStyle="bold"
        android:textSize="50px"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/pcList"
        app:layout_constraintTop_toBottomOf="@id/pcListTitle"
        android:layout_width="match_parent"
        android:layout_height="0px"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#4D000000"
        android:padding="50px"
        android:visibility="gone" />

</androidx.constraintlayout.widget.ConstraintLayout>