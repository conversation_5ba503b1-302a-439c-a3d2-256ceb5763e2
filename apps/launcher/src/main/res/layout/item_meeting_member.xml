<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="212px"
    android:layout_height="60px"
    app:bl_corners_radius="10px"
    app:bl_solid_color="#5879FC"
    android:id="@+id/itemMeetingMemberBg">

    <TextView
        android:id="@+id/tv_name"
        android:layout_width="130px"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="center"
        android:singleLine="true"
        android:textColor="#ffffff"
        android:textSize="24px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <ImageView
        android:id="@+id/iv_cancel"
        android:layout_width="16px"
        android:layout_height="16px"
        android:layout_marginEnd="20px"
        android:src="@drawable/ic_del_meeting_item"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:float_tips="@string/float_tip_remove"
        />
</androidx.constraintlayout.widget.ConstraintLayout>