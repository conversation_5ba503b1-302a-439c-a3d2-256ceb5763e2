package com.czur.starry.device.launcher.widget

import android.content.Context
import android.graphics.drawable.BitmapDrawable
import android.util.AttributeSet
import android.widget.FrameLayout
import android.widget.ImageView
import com.bumptech.glide.Glide
import com.czur.starry.device.baselib.tips.setCustomFloatTip
import com.czur.starry.device.baselib.utils.*
import com.czur.starry.device.baselib.utils.view.findView
import com.czur.starry.device.launcher.R
import com.czur.starry.device.launcher.data.bean.NetMeetingApp
import com.czur.starry.device.launcher.meeting.NetMeetingAppStatus

/**
 * Created by 陈丰尧 on 2022/8/20
 * 其他视频会议的图标控件
 * 图标显示:
 * 1. 能从本地提取, 显示本地提取的图标
 * 2. 不能从本地提取, 使用网络图标
 * 3. 网络图标加载失败使用默认图标
 * 状态:
 * 1. 已经的单纯显示图标
 * 2. 未安装的显示未安装标记
 * 3. 下载中的显示进度条
 */
class OtherQuickBootAppIconIv @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {
    private val iconIv: ImageView by findView(R.id.iconIv)
    private val downloadFlagIv: ImageView by findView(R.id.downloadFlagIv)
    private val progressView: OtherMeetingProcess by findView(R.id.otherDownloadProgressView)

    init {
        inflate(context, R.layout.widget_other_meeting_icon_iv, this)
    }

    // 下载进度
    private var process = -1    // 小于0的时候, 不显示进度条
        set(value) {
            if (field == value) return
            field = value
            if (value < 0) {
                progressView.gone()
            } else {
                progressView.show()
                progressView.progress = field
            }
        }

    /**
     * BYOM ICON使用
     */
    fun updateByomIcon() {
        downloadFlagIv.gone()
        iconIv.setImageResource(R.drawable.icon_byom)
    }

    fun updateIcon(netMeetingApp: NetMeetingApp) {
        // 加载图标
        if (netMeetingApp.appIcon != null) {
            iconIv.scaleType = ImageView.ScaleType.FIT_XY
            val bitmap = (netMeetingApp.appIcon as? BitmapDrawable)?.bitmap
            if (bitmap != null) {
                // 和LauncherPad共用一个Drawable, 所以用里面的Bitmap, 而不用Drawable, 否则有可能显示的缩放比例不对
                iconIv.setImageBitmap(bitmap)
            } else {
                iconIv.setImageDrawable(netMeetingApp.appIcon)
            }
            process = -1
        } else {
            // 加载网络Icon
            iconIv.setImageResource(R.drawable.ic_def_icon)
            Glide.with(iconIv)
                .load(netMeetingApp.iconUrl)
                .error(R.drawable.ic_def_icon)
                .into(iconIv)
        }

        // 未安装的标记
        when (netMeetingApp.status) {
            NetMeetingAppStatus.INSTALL -> {
                iconIv.alpha = 1F
                downloadFlagIv.gone()
            }
            NetMeetingAppStatus.UNINSTALL -> {
                iconIv.alpha = 0.3F
                if (netMeetingApp.downloadProcess < 0) {
                    // 没有在下载
                    downloadFlagIv.show()
                } else {
                    downloadFlagIv.gone()
                }
            }
        }
        process = netMeetingApp.downloadProcess

        // 设置Tips
        if (netMeetingApp.status == NetMeetingAppStatus.UNINSTALL && netMeetingApp.downloadProcess < 0) {
            val tipsStr = getString(R.string.tips_click_download)
            setCustomFloatTip(tipsStr)
        } else {
            setCustomFloatTip("")
        }
    }
}