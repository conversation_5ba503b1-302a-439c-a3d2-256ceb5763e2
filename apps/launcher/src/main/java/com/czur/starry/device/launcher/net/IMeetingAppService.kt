package com.czur.starry.device.launcher.net

import android.os.Build
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.network.core.MiaoHttpEntity
import com.czur.starry.device.baselib.network.core.MiaoHttpParam
import com.czur.starry.device.baselib.network.core.MiaoHttpPost
import com.czur.starry.device.baselib.utils.SettingHandler
import com.czur.starry.device.launcher.data.bean.OtherQuickBootAppItem
import com.google.gson.reflect.TypeToken
import java.lang.reflect.Type

/**
 * Created by 陈丰尧 on 2022/8/20
 */
interface IMeetingAppService {
    /**
     * 对应应用商店中视频会议App
     */
    fun getOtherNetMeetingApps(
        sdkVersion: String = Build.VERSION.SDK_INT.toString(),
        page: Int = 0,
        size: Int = Int.MAX_VALUE,
        tagCode: String,
        language: String = SettingHandler.czurLang.serverCode,
        frameworkVersion: String = Constants.FIRMWARE_NAME, // 固件名称
        type: Type = object :
            TypeToken<List<OtherQuickBootAppItem>>() {}.type
    ): MiaoHttpEntity<OtherQuickBootAppItem>
}

interface IMeetingAppMainlandService : IMeetingAppService {
    /**
     * 对应应用商店中视频会议App
     */
    @MiaoHttpPost("/api/app/store/app/list")
    override fun getOtherNetMeetingApps(
        @MiaoHttpParam("version")
        sdkVersion: String,
        @MiaoHttpParam("page")
        page: Int,
        @MiaoHttpParam("size")
        size: Int,
        @MiaoHttpParam("tagCode")
        tagCode: String,
        @MiaoHttpParam("language")
        language: String,
        @MiaoHttpParam("frameworkVersion")
        frameworkVersion: String,
        type: Type
    ): MiaoHttpEntity<OtherQuickBootAppItem>
}

interface IMeetingAppOverseasService : IMeetingAppService {
    /**
     * 对应应用商店中视频会议App
     */
    @MiaoHttpPost("/api/app/store/app/na/list")
    override fun getOtherNetMeetingApps(
        @MiaoHttpParam("version")
        sdkVersion: String,
        @MiaoHttpParam("page")
        page: Int,
        @MiaoHttpParam("size")
        size: Int,
        @MiaoHttpParam("tagCode")
        tagCode: String,
        @MiaoHttpParam("language")
        language: String,
        @MiaoHttpParam("frameworkVersion")
        frameworkVersion: String,
        type: Type
    ): MiaoHttpEntity<OtherQuickBootAppItem>
}