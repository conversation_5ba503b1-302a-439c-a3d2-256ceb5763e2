package com.czur.starry.device.launcher.pages.view.launcher.quickboot.v2

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.content.Intent
import android.graphics.Bitmap
import android.os.Bundle
import android.view.MotionEvent
import android.view.View
import android.view.ViewOutlineProvider
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.Group
import androidx.core.view.marginTop
import androidx.core.view.updateLayoutParams
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.base.v2.fragment.CZViewBindingFragment
import com.czur.starry.device.baselib.common.BootParam
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.StarryDevLocale
import com.czur.starry.device.baselib.utils.InternetStatus
import com.czur.starry.device.baselib.utils.blur
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.invisible
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.repeatCollectOnCreate
import com.czur.starry.device.baselib.utils.setDebounceTouchClickListener
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.baselib.utils.setOnSuspendClickListener
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.baselib.utils.takeScreenShot
import com.czur.starry.device.file.filelib.FileHandlerLive.fileShareCodeEnable
import com.czur.starry.device.file.filelib.FileHandlerLive.fileShareEnable
import com.czur.starry.device.launcher.R
import com.czur.starry.device.launcher.backdrop.BackdropManager
import com.czur.starry.device.launcher.backdrop.BackdropManager.BackdropColor.*
import com.czur.starry.device.launcher.backdrop.BackdropManager.BackdropEvent
import com.czur.starry.device.launcher.databinding.FragmentQuickBootComplicatedBinding
import com.czur.starry.device.launcher.meeting.NetMeetingAppStatus
import com.czur.starry.device.launcher.meeting.OtherMeetingFloat
import com.czur.starry.device.launcher.pages.view.launcher.AppInfoViewModel
import com.czur.starry.device.launcher.pages.view.launcher.KeyCodeVM
import com.czur.starry.device.launcher.pages.view.launcher.LauncherMainViewModel
import com.czur.starry.device.launcher.pages.view.launcher.quickboot.QuickBootApp
import com.czur.starry.device.launcher.pages.view.launcher.quickboot.ShareViewModel
import com.czur.starry.device.launcher.pages.view.launcher.quickboot.fileCodeLiveData
import com.czur.starry.device.launcher.pages.view.launcher.quickboot.quickBootApps
import com.czur.starry.device.launcher.utils.bootApp
import com.czur.starry.device.launcher.utils.bootAppByAction
import com.czur.starry.device.launcher.widget.BlurImageView
import com.czur.starry.device.launcher.widget.LauncherMainMarkView
import com.czur.starry.device.launcher.widget.OtherQuickBootAppIconIv
import com.czur.starry.device.launcher.widget.ShadowRedPoint


/**
 * Created by 陈丰尧 on 2022/8/19
 * 复杂的快速启动项
 */
class ComplicatedQuickBootFragment : CZViewBindingFragment<FragmentQuickBootComplicatedBinding>() {
    companion object {
        private const val TAG = "ComplicatedQuickBootFragment"
        private const val ANIM_DURATION = 100L
        private const val ANIM_SCALE_RATIO = 1.07f
    }

    private val shareViewModel: ShareViewModel by viewModels({ requireActivity() })
    private val keyCodeVM: KeyCodeVM by viewModels({ requireActivity() })
    private val mainViewModel: LauncherMainViewModel by activityViewModels()


    // 应用信息的ViewModel
    private val appsVM: AppInfoViewModel by viewModels({ requireActivity() })


    private val shareNameTv: TextView by lazy {
        binding.quickBoot2.findViewById<TextView>(R.id.shareNameTv)
    }
    private val wifiNameLl: LinearLayout by lazy {
        binding.quickBoot2.findViewById<LinearLayout>(R.id.wifiNameLl)
    }
    private val wifiNameTv: TextView by lazy {
        binding.quickBoot2.findViewById<TextView>(R.id.wifiNameTv)
    }
    private val wifiNameIv: ImageView by lazy {
        binding.quickBoot2.findViewById<ImageView>(R.id.wifiNameIv)
    }
    private val ethernetTv: TextView by lazy {
        binding.quickBoot2.findViewById<TextView>(R.id.ethernetTv)
    }

    private val fileCodeTitleTv by lazy {
        binding.quickBoot5.findViewById<TextView>(R.id.fileCodeTitleTv)
    }
    private val fileCodeContentTv by lazy {
        binding.quickBoot5.findViewById<TextView>(R.id.fileCodeContentTv)
    }

    // 视频会议弹窗是否显示
    private var isNetMeetingShowing = false

    private val quickBootLayouts by lazy {
        listOf(
            binding.quickBoot1, // 视频会议
            binding.quickBoot2,
            binding.quickBoot4,
            binding.quickBoot5,
            binding.quickBoot6,
        )
    }

    private val iconViews by lazy {
        listOf(
            NetAppIconView(
                binding.netMeetingAppIcon1, binding.netMeetingAppName1, binding.netMeetingApp1
            ),
            NetAppIconView(
                binding.netMeetingAppIcon2, binding.netMeetingAppName2, binding.netMeetingApp2
            ),
        )
    }

    private val blurImageViewList = mutableListOf<BlurImageView>()

    override fun FragmentQuickBootComplicatedBinding.initBindingViews() {

        if (Constants.starryHWInfo.salesLocale == StarryDevLocale.Overseas) {
            netMeetingAppTitle2Tv.gone()
        } else {
            netMeetingAppTitle2Tv.show()
        }

        quickBootLayouts.forEachIndexed { index, quickBootLayout ->

            val quickBootApp = quickBootApps[index]
            // 阴影
            quickBootLayout.outlineProvider = object : ViewOutlineProvider() {
                override fun getOutline(view: View, outline: android.graphics.Outline) {
                    // 设置圆角矩形的outline
                    outline.setRoundRect(
                        0, 0, view.width, view.height, 10F
                    )
                }
            }
            quickBootLayout.elevation = 20F // 设置elevation以产生阴影

            // 设置动画效果
            quickBootLayout.setOnHoverListener { view, event ->
                when (event.action) {
                    MotionEvent.ACTION_HOVER_EXIT -> scaleView(view, false, index)
                    MotionEvent.ACTION_HOVER_ENTER -> scaleView(view, true, index)
                }
                false
            }

            if (index == 0) {
                // 视频会议模块
                initOtherMeetingUI()
            } else {
                setQuickBootUI(quickBootLayout, quickBootApp)
            }

        }
    }

    /**
     * 初始化第三方网络会议UI
     */
    private fun initOtherMeetingUI() {
        fun scareIcon(view: View, scaleUp: Boolean) {
            view.pivotX = view.width.toFloat() / 2F
            view.pivotY = view.height.toFloat() / 2F
            if (scaleUp) {
                scaleUp(view)
            } else {
                scaleDown(view)
            }
        }

        binding.quickBoot1BlurIv.post {
            launch {
                BackdropManager.setBlurBitmapToBlurIv(binding.quickBoot1BlurIv)
            }
        }

        binding.quickBoot1.setOnDebounceClickListener {
            if (isNetMeetingShowing) {
                logTagV(TAG, "已经显示弹窗了, 忽略")
                return@setOnDebounceClickListener
            }
            isNetMeetingShowing = true
            // 弹出视频会议弹窗
            launch {
                try {
                    val bgImg = takeScreenShot(Bitmap.Config.ARGB_8888)?.blur(samplingRate = 4)
                    OtherMeetingFloat(
                        bgImg,
                        getString(R.string.float_title_other_net_meeting),
                        appsVM.otherNetMeetingAppsLive
                    ).apply {
                        setOnDismissListener {
                            isNetMeetingShowing = false
                        }
                    }.show()
                } catch (e: Exception) {
                    e.printStackTrace()
                    logTagE(TAG, "====${e}")
                }
            }
        }


        // 点击小项 click事件会影响Hover效果
        val netMeetingAppList =
            listOf(binding.byomAppIcon, binding.netMeetingAppIcon1, binding.netMeetingAppIcon2)
        netMeetingAppList.forEachIndexed { index, otherMeetingIconIv ->

            otherMeetingIconIv.setDebounceTouchClickListener {
                if (index == 0) {
                    bootAppByAction(BootParam.ACTION_BOOT_BYOM_GUIDE)
                } else {
                    bootOrInstallApp(index - 1)
                }
            }
            otherMeetingIconIv.setOnHoverListener { view, event ->
                when (event.action) {
                    MotionEvent.ACTION_HOVER_EXIT -> scareIcon(view, false)
                    MotionEvent.ACTION_HOVER_ENTER -> scareIcon(view, true)
                }
                false
            }
        }
    }

    /**
     * 启动对应的会议App
     */
    private fun bootOrInstallApp(index: Int) {
        val clickApp =
            appsVM.otherNetMeetingApps.getOrNull(index) ?: return   // 没有对应的App信息就直接Return
        when (clickApp.status) {
            NetMeetingAppStatus.INSTALL -> {
                logTagV(TAG, "启动App:${clickApp.appName}")
                bootApp(clickApp.pkgName)
            }

            NetMeetingAppStatus.UNINSTALL -> {
                if (clickApp.downloadProcess >= 0) {
                    logTagD(TAG, "正在下载, 不做任何操作")
                } else {
                    logTagD(TAG, "安装App:${clickApp.appName}")
                    appsVM.downloadNetMeetingApp(clickApp)
                }
            }
        }
    }


    /**
     * 设置QuickBootApp的行为
     */
    private fun setQuickBootUI(quickBootLayout: View, quickBootApp: QuickBootApp) {
        val quickBootLogoIv = quickBootLayout.findViewById<ImageView>(R.id.quickBootLogoIv)
        val quickBootTitleTv = quickBootLayout.findViewById<TextView>(R.id.quickBootTitleTv)
        // 模糊图片
        val quickBootBiv = quickBootLayout.findViewById<BlurImageView>(R.id.quickBootBiv)
        blurImageViewList.add(quickBootBiv)

        val quickBootMarkView =
            quickBootLayout.findViewById<LauncherMainMarkView>(R.id.quickBootMarkView)
        val quickBootBadgePoint = quickBootLayout.findViewById<ShadowRedPoint>(R.id.quickBootBadgePoint)

        if (quickBootApp.id == "StarryPad") {
            quickBootLogoIv.translationY = 6F   // StarryPad的logo需要下移一点
        }
        when (mainViewModel.backdropColor) {
            Dark -> {
                quickBootLogoIv.setImageResource(quickBootApp.iconResDark)
            }

            Light -> {
                quickBootLogoIv.setImageResource(quickBootApp.iconResLight)
            }
        }
        quickBootMarkView.updateDrawable(quickBootApp.bgDrawableDark, quickBootApp.bgDrawableLight)
        quickBootBiv.post {
            launch {
                BackdropManager.setBlurBitmapToBlurIv(quickBootBiv)
            }
        }

        quickBootTitleTv.setText(quickBootApp.nameRes)

        // 小红点的阴影颜色
        quickBootBadgePoint.shadowColor = quickBootApp.pointShadowColor
        quickBootApp.badgeLive?.observe(this) { new ->
//            // 显示角标的部分
            if (new) {
                quickBootBadgePoint.show()
            } else {
                quickBootBadgePoint.gone()
            }
        }


        quickBootLayout.setOnSuspendClickListener(lifecycleScope) {

            val bootIntent = quickBootApp.bootIntent
            if (bootIntent != null) {
                bootIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                try {
                    logTagD(TAG, "启动QuickBoot模块:${getString(quickBootApp.nameRes)}")
                    startActivity(bootIntent)
                } catch (e: Exception) {
                    logTagE(TAG, "quickBoot 启动失败:", tr = e)
                }
            }
        }
    }

    override fun onResume() {
        super.onResume()
        launch {
            shareViewModel.loadShareInfo()
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)

        fileCodeLiveData.observe(this) { fileCode ->
            // 显示传输文件校验码
            if (fileShareEnable && fileShareCodeEnable) {
                fileCodeTitleTv.show()
                fileCodeContentTv.text = fileCode
                fileCodeContentTv.show()
            } else {
                fileCodeTitleTv.gone()
                fileCodeContentTv.gone()
            }
        }

        keyCodeVM.scrollTime.observe(viewLifecycleOwner) {
            // 更新模糊图片
            binding.quickBoot1BlurIv.invalidate()
            blurImageViewList.forEach { blurImageView ->
                blurImageView.invalidate()
            }
        }

        shareViewModel.wifiSSIDNameLive.observe(this) {
            wifiNameIv.gone(it.isEmpty())
            wifiNameTv.gone(it.isEmpty())
            wifiNameTv.text = it
            val connect = shareViewModel.ethernetWorkLive.value == InternetStatus.CONNECT
            ethernetTv?.gone(it.isNotEmpty() || !connect)

        }

        shareViewModel.ethernetWorkLive.observe(this) {
            val wifiNet = shareViewModel.wifiSSIDNameLive.value ?: ""
            wifiNameLl.gone(it == InternetStatus.DISCONNECT)
            if (it == InternetStatus.CONNECT) {
                ethernetTv.gone(wifiNet.isNotEmpty())
            }
        }


        shareViewModel.showShareNameLive.observe(this) {
            shareNameTv.let { tv ->
                tv.show()
                tv.text = it
            }
        }

        // 第三方网络会议应用
        appsVM.otherNetMeetingAppsLive.observe(this) { otherMeetingApps ->
            /*
            Launcher上的显示逻辑:
            1. 如果有已安装的应用, 则只显示已安装的应用
            2. 一个已安装应用都没有, 显示后台配置列表中的前三个
             */

            val showApps = otherMeetingApps.filter {
                it.status == NetMeetingAppStatus.INSTALL
            }.ifEmpty {
                // 没有安装的应用
                otherMeetingApps
            }.take(iconViews.size)        // 最多2个
            repeat(iconViews.size) { index ->
                val appView = iconViews[index]
                showApps.getOrNull(index)?.let {
                    appView.groupView.show()
                    appView.iconView.updateIcon(it)
                    appView.nameView.text = it.appName
                } ?: kotlin.run {
                    appView.groupView.invisible()
                }
            }

        }

        repeatCollectOnCreate(BackdropManager.eventFlow) {
            if (it == BackdropEvent.CHANGE) {
                binding.quickBoot1BlurIv.post {
                    launch {
                        BackdropManager.setBlurBitmapToBlurIv(binding.quickBoot1BlurIv)
                    }
                }
                blurImageViewList.forEach { blurImageView ->
                    blurImageView.post {
                        launch {
                            BackdropManager.setBlurBitmapToBlurIv(blurImageView)
                        }
                    }
                }
            }
        }

        repeatCollectOnCreate(mainViewModel.backdropColorFlow) {
            when (it) {
                Dark -> quickBootLayouts.forEach {
                    it.outlineSpotShadowColor = 0xFF6186FF.toInt()
                    it.outlineAmbientShadowColor = 0xFF6186FF.toInt()
                }

                Light -> quickBootLayouts.forEach {
                    it.outlineSpotShadowColor = 0xFFA6C1FF.toInt()
                    it.outlineAmbientShadowColor = 0xFFA6C1FF.toInt()
                }
            }
        }

        binding.byomAppIcon.updateByomIcon()
    }

    private fun scaleView(view: View, scaleUp: Boolean, index: Int) {
        /**
         * 设置中心点位置
         */
        when (index) {
            0 -> {
                // 右下角
                view.pivotX = view.width.toFloat() / 2F
                view.pivotY = view.height.toFloat()
            }

            1 -> {
                // 左下角
                view.pivotX = 0F
                view.pivotY = view.height.toFloat()
            }

            2 -> {
                // 右上角
                view.pivotX = view.width.toFloat()
                view.pivotY = 0F
            }

            3 -> {
                // 顶边中间
                view.pivotX = view.width.toFloat() / 2F
                view.pivotY = 0F
            }

            4 -> {
                // 左上角
                view.pivotX = 0F
                view.pivotY = 0F
            }

        }
        if (scaleUp) {
            scaleUp(view)
            if (index == 0) {
                val x = view.width * (ANIM_SCALE_RATIO - 1) / 2F // 控件移动的距离
                quickBootLayouts[index + 1].tranX(x) // 右侧控件
            }
            if (index == 3) {
                val x = view.width * (ANIM_SCALE_RATIO - 1) / 2F // 控件移动的距离
                quickBootLayouts[index - 1].tranX(-x)  // 左侧控件
                quickBootLayouts[index + 1].tranX(x) // 右侧控件
            }
        } else {
            scaleDown(view)
            if (index == 0) {
                val x = view.width * (ANIM_SCALE_RATIO - 1) / 2F // 控件移动的距离
                quickBootLayouts[1].tranX(0F) // 右侧控件
            }
            if (index == 3) {
                quickBootLayouts[index - 1].tranX(0F)  // 左侧控件
                quickBootLayouts[index + 1].tranX(0F) // 右侧控件
            }
        }
    }

    private fun View.tranX(tranX: Float) {
        val anim = ObjectAnimator.ofFloat(this, "translationX", tranX)
        anim.duration = ANIM_DURATION
        anim.start()
    }

    /**
     * 放大
     */
    private fun scaleUp(view: View) {
        val animSet = AnimatorSet()
        val objectAnimatorX = ObjectAnimator.ofFloat(view, "scaleX", ANIM_SCALE_RATIO)
        val objectAnimatorY = ObjectAnimator.ofFloat(view, "scaleY", ANIM_SCALE_RATIO)
        objectAnimatorX.duration = ANIM_DURATION
        objectAnimatorY.duration = ANIM_DURATION
        animSet.play(objectAnimatorX).with(objectAnimatorY)
        animSet.start()
    }

    /**
     * 缩小
     */
    private fun scaleDown(view: View) {
        val animSet = AnimatorSet()
        val objectAnimatorX = ObjectAnimator.ofFloat(view, "scaleX", 1.0f)
        val objectAnimatorY = ObjectAnimator.ofFloat(view, "scaleY", 1.0f)
        objectAnimatorX.duration = ANIM_DURATION
        objectAnimatorY.duration = ANIM_DURATION
        animSet.play(objectAnimatorX).with(objectAnimatorY)
        animSet.start()
    }

    /**
     * IconView
     */
    private data class NetAppIconView(
        val iconView: OtherQuickBootAppIconIv, val nameView: TextView, val groupView: Group
    )
}