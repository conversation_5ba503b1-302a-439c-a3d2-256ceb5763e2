package com.czur.starry.device.launcher.pages.view.launcher

import android.os.Bundle
import androidx.fragment.app.viewModels
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.base.v2.fragment.CZViewBindingFragment
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.hw.StarryModel
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.launcher.R
import com.czur.starry.device.launcher.databinding.FragmentLauncherMainBinding
import com.czur.starry.device.sharescreen.esharelib.util.checkAndActiveEShare

/**
 * Created by 陈丰尧 on 2/19/21(改)
 */

class LauncherMainFragment : CZViewBindingFragment<FragmentLauncherMainBinding>() {
    companion object {
        private const val TAG = "LauncherMainFragment"
    }

    private val keyCodeVM: KeyCodeVM by viewModels({ requireActivity() })


    override fun FragmentLauncherMainBinding.initBindingViews() {
        launch {
            checkAndActiveEShare(requireContext())
        }

        fun moveToAppPad() {
            (activity as? LauncherMainActivity)?.moveToAppPadPage()
        }

        slideUpIv.setOnDebounceClickListener {
            // 点击时上划
            moveToAppPad()
        }

        if (Constants.starryHWInfo.model == StarryModel.StudioModel.StudioSPlus) {
            slideUpTv.setText(R.string.str_slide_up_hint_simple)
        }
        slideUpTv.setOnDebounceClickListener {
            moveToAppPad()
        }

    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        repeatCollectOnResume(keyCodeVM.showSlideUpGuideFlow) { showGuide ->
            if (showGuide) {
                logTagV(TAG, "显示引导UI")
                binding.slideUpGuideGroup.show()
            } else {
                // 隐藏引导UI
                logTagV(TAG, "隐藏引导UI")
                binding.slideUpGuideGroup.gone()
            }
        }
    }


}