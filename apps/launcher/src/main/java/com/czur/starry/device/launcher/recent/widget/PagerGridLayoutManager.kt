package com.czur.starry.device.launcher.recent.widget

import android.content.Context
import android.graphics.PointF
import android.graphics.Rect
import android.os.Parcelable
import android.util.AttributeSet
import android.view.View
import android.view.ViewGroup
import androidx.annotation.CallSuper
import androidx.core.view.ViewCompat
import androidx.recyclerview.widget.RecyclerView
import com.czur.czurutils.log.logTagW
import kotlinx.parcelize.Parcelize
import kotlin.math.abs
import kotlin.math.max
import kotlin.math.min

/**
 * 分页滑动网格布局LayoutManager
 */
class PagerGridLayoutManager @JvmOverloads constructor(
    rows: Int,
    columns: Int,
    orientation: Int = HORIZONTAL,
    reverseLayout: Boolean = false
) : RecyclerView.LayoutManager(), RecyclerView.SmoothScroller.ScrollVectorProvider {

    companion object {
        private const val TAG = "PagerGridLayoutManager"
        const val UN_SET: Int = 0

        /**
         * 水平滑动
         */
        const val HORIZONTAL: Int = RecyclerView.HORIZONTAL

        /**
         * 垂直滑动
         */
        const val VERTICAL: Int = RecyclerView.VERTICAL

        /**
         * @see .mCurrentPagerIndex
         */
        const val NO_ITEM: Int = -1
        const val NO_PAGER_COUNT: Int = 0
    }

    private var pagerGridSnapHelper: PagerGridSnapHelper? = null

    /**
     * 当前滑动方向
     */
    private var mOrientation = HORIZONTAL

    /**
     * 行数
     */
    private var mRows = 0

    /**
     * 列数
     */
    private var mColumns = 0

    /**
     * 一页的数量 [mRows] * [mColumns]
     */
    var onePageSize: Int = 0
        private set

    /**
     * 总页数
     */
    private var mPagerCount = NO_PAGER_COUNT

    /**
     * 当前页码下标
     * 从0开始
     */
    private var mCurrentPagerIndex = NO_ITEM

    /**
     * item的宽度
     */
    private var itemWidth: Int = UN_SET

    /**
     * item的高度
     */
    private var itemHeight: Int = UN_SET

    /**
     * 一个ItemView的所有ItemDecoration占用的宽度(px)
     */
    private var mItemWidthUsed = 0

    /**
     * 一个ItemView的所有ItemDecoration占用的高度(px)
     */
    private var mItemHeightUsed = 0

    /**
     * 用于保存一些状态
     */
    val layoutState: LayoutState

    private val mLayoutChunkResult: LayoutChunkResult
    /**
     * @return 左上角第一个view的位置
     */
    /**
     * 用于计算锚点坐标
     * [shouldReverseLayout] 为false：左上角第一个view的位置
     * [shouldReverseLayout] 为true：右上角第一个view的位置
     */
    val startSnapRect: Rect = Rect()
    /**
     * @return 右下角最后一个view的位置
     */
    /**
     * 用于计算锚点坐标
     * [shouldReverseLayout] 为false：右下角最后一个view的位置
     * [shouldReverseLayout] 为true：左上角最后一个view的位置
     */
    val endSnapRect: Rect = Rect()

    private var mRecyclerView: RecyclerView? = null

    /**
     * 定义是否应从头到尾计算布局
     *
     * @see .shouldReverseLayout
     */
    private var mReverseLayout = false

    /**
     * 这保留了 PagerGridLayoutManager 应该如何开始布局视图的最终值。
     * 它是通过检查 [reverseLayout] 和 View 的布局方向来计算的。
     */
    var shouldReverseLayout: Boolean = false

    private var mPagerChangedListener: PagerChangedListener? = null

    /**
     * 计算多出来的宽度，因为在均分的时候，存在除不尽的情况，要减去多出来的这部分大小，一般也就为几px
     * 不减去的话，会导致翻页计算不触发
     *
     * @see .onMeasure
     */
    private var diffWidth = 0

    /**
     * 计算多出来的高度，因为在均分的时候，存在除不尽的情况，要减去多出来的这部分大小，一般也就为几px
     * 不减去的话，会导致翻页计算不触发
     *
     * @see .onMeasure
     */
    private var diffHeight = 0
    /**
     * 是否启用处理滑动冲突滑动冲突，默认true
     */
    private var isHandlingSlidingConflictsEnabled: Boolean = true
    private var mMillisecondPreInch = PagerGridSmoothScroller.MILLISECONDS_PER_INCH
    private var mMaxScrollOnFlingDuration = PagerGridSmoothScroller.MAX_SCROLL_ON_FLING_DURATION

    private val onChildAttachStateChangeListener: RecyclerView.OnChildAttachStateChangeListener =
        object : RecyclerView.OnChildAttachStateChangeListener {
            override fun onChildViewAttachedToWindow(view: View) {
                val layoutParams = view.layoutParams as LayoutParams
                //判断ItemLayout的宽高是否是match_parent
                check(
                    !(layoutParams.width != ViewGroup.LayoutParams.MATCH_PARENT
                            || layoutParams.height != ViewGroup.LayoutParams.MATCH_PARENT)
                ) { "Item layout  must fill the whole PagerGridLayoutManager (use match_parent)" }
            }

            override fun onChildViewDetachedFromWindow(view: View) {
                // nothing
            }
        }

    private var onItemTouchListener: RecyclerView.OnItemTouchListener? = null

    private var columns: Int
        /**
         * @return 列数
         */
        get() = mColumns
        set(columns) {
            assertNotInLayoutOrScroll(null)

            if (mColumns == columns) {
                return
            }
            mColumns = max(columns.toDouble(), 1.0).toInt()
            mPagerCount = NO_PAGER_COUNT
            mCurrentPagerIndex = NO_ITEM
            calculateOnePageSize()
            requestLayout()
        }

    private var rows: Int
        /**
         * @return 行数
         */
        get() = mRows
        set(rows) {
            assertNotInLayoutOrScroll(null)

            if (mRows == rows) {
                return
            }
            mRows = max(rows.toDouble(), 1.0).toInt()
            mPagerCount = NO_PAGER_COUNT
            mCurrentPagerIndex = NO_ITEM
            calculateOnePageSize()
            requestLayout()
        }

    var orientation: Int
        get() = mOrientation
        /**
         * 设置滑动方向
         *
         * @param orientation [HORIZONTAL] or [VERTICAL]
         */
        set(orientation) {
            assertNotInLayoutOrScroll(null)

            require(!(orientation != HORIZONTAL && orientation != VERTICAL)) { "invalid orientation:$orientation" }
            if (orientation != mOrientation) {
                mOrientation = orientation

                requestLayout()
            }
        }

    private var reverseLayout: Boolean
        get() = mReverseLayout
        set(reverseLayout) {
            assertNotInLayoutOrScroll(null)

            if (reverseLayout == mReverseLayout) {
                return
            }
            mReverseLayout = reverseLayout
            requestLayout()
        }

    var pagerCount: Int
        /**
         * 返回总页数
         *
         * @return 0：[getItemCount] is 0
         */
        get() = max(mPagerCount.toDouble(), 0.0).toInt()
        /**
         * 设置总页数
         *
         * @param pagerCount
         */
        private set(pagerCount) {
            if (mPagerCount == pagerCount) {
                return
            }
            mPagerCount = pagerCount
            if (mPagerChangedListener != null) {
                mPagerChangedListener!!.onPagerCountChanged(pagerCount)
            }
        }

    var currentPagerIndex: Int
        /**
         * 获取当前的页码
         *
         * @return -1：[getItemCount] is 0,[NO_ITEM] . else [mCurrentPagerIndex]
         */
        get() = mCurrentPagerIndex
        /**
         * 设置当前页码
         *
         * @param pagerIndex 页码
         */
        private set(pagerIndex) {
            if (mCurrentPagerIndex == pagerIndex) {
                return
            }
            val prePagerIndex = mCurrentPagerIndex
            mCurrentPagerIndex = pagerIndex
            if (mPagerChangedListener != null) {
                mPagerChangedListener!!.onPagerIndexSelected(prePagerIndex, pagerIndex)
            }
        }

    private val realWidth: Int
        /**
         * 获取真实宽度
         *
         * @return
         */
        get() = width - paddingStart - paddingEnd

    private val realHeight: Int
        /**
         * 获取真实高度
         *
         * @return
         */
        get() = height - paddingTop - paddingBottom


    init {
        layoutState = createLayoutState()
        mLayoutChunkResult = createLayoutChunkResult()
        rows.also { this.rows = it }
        columns.also { this.columns = it }
        orientation.also { this.orientation = it }
        reverseLayout.also { this.reverseLayout = it }
    }

    /**
     * @return 子布局LayoutParams，默认全部填充，子布局会根据[mRows]和[mColumns] 均分RecyclerView
     */
    override fun generateDefaultLayoutParams(): RecyclerView.LayoutParams {
        return LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        )
    }

    override fun generateLayoutParams(c: Context, attrs: AttributeSet): RecyclerView.LayoutParams {
        return LayoutParams(c, attrs)
    }

    override fun generateLayoutParams(lp: ViewGroup.LayoutParams): RecyclerView.LayoutParams {
        return when (lp) {
            is RecyclerView.LayoutParams -> {
                LayoutParams(lp)
            }

            is ViewGroup.MarginLayoutParams -> {
                LayoutParams(lp)
            }

            else -> {
                LayoutParams(lp)
            }
        }
    }

    override fun checkLayoutParams(lp: RecyclerView.LayoutParams): Boolean {
        return lp is LayoutParams
    }

    override fun onAttachedToWindow(view: RecyclerView) {
        super.onAttachedToWindow(view)
        //默认先这么设置
        view.setHasFixedSize(true)
        if (isInScrollingContainer(view)) {
            //在一个可滑动的布局中
            if (isHandlingSlidingConflictsEnabled) {
                onItemTouchListener = PagerGridItemTouchListener(this, view)
                onItemTouchListener?.let {
                    view.addOnItemTouchListener(it)
                }
            } else {
                //不启用的话可以自行解决
                logTagW(TAG, "isHandlingSlidingConflictsEnabled: false.")
            }
        }
        view.addOnChildAttachStateChangeListener(onChildAttachStateChangeListener)
        pagerGridSnapHelper = PagerGridSnapHelper()
        pagerGridSnapHelper!!.attachToRecyclerView(view)
        mRecyclerView = view
    }


    override fun onMeasure(
        recycler: RecyclerView.Recycler,
        state: RecyclerView.State,
        widthSpec: Int,
        heightSpec: Int
    ) {
        val widthMode = View.MeasureSpec.getMode(widthSpec)
        val heightMode = View.MeasureSpec.getMode(heightSpec)
        val widthSize = View.MeasureSpec.getSize(widthSpec)
        val heightSize = View.MeasureSpec.getSize(heightSpec)
        //判断RecyclerView的宽度和高度是不是精确值
        if (widthMode == View.MeasureSpec.EXACTLY && heightMode == View.MeasureSpec.EXACTLY) {
            val realWidth = widthSize - paddingStart - paddingEnd
            val realHeight = heightSize - paddingTop - paddingBottom
            //均分宽
            itemWidth = if (mColumns > 0) realWidth / mColumns else 0
            //均分高
            itemHeight = if (mRows > 0) realHeight / mRows else 0

            //重置下宽高，因为在均分的时候，存在除不尽的情况，要减去多出来的这部分大小，一般也就为几px
            //不减去的话，会导致翻页计算不触发
            diffWidth = realWidth - itemWidth * mColumns
            diffHeight = realHeight - itemHeight * mRows

            mItemWidthUsed = realWidth - diffWidth - itemWidth
            mItemHeightUsed = realHeight - diffHeight - itemHeight
        } else {
            itemWidth = UN_SET
            itemHeight = UN_SET
            diffWidth = 0
            diffHeight = 0
            mItemWidthUsed = 0
            mItemHeightUsed = 0
            logTagW(
                TAG,
                "onMeasure-width or height is not exactly, widthMode: $widthMode, heightMode: $heightMode"
            )
        }
        super.onMeasure(recycler, state, widthSpec, heightSpec)
    }

    override fun onLayoutChildren(recycler: RecyclerView.Recycler, state: RecyclerView.State) {
        check(!(itemWidth == UN_SET || itemHeight == UN_SET)) { "RecyclerView's width and height must be exactly." }

        val itemCount = itemCount
        if (itemCount == 0) {
            removeAndRecycleAllViews(recycler)
            pagerCount = NO_PAGER_COUNT
            currentPagerIndex = NO_ITEM
            return
        }
        if (state.isPreLayout) {
            return
        }

        // resolve layout direction
        resolveShouldLayoutReverse()

        //计算锚点的坐标
        if (shouldReverseLayout) {
            //右上角第一个view的位置
            startSnapRect[width - paddingEnd - itemWidth, paddingTop, width - paddingEnd] =
                paddingTop + itemHeight
            //左下角最后一个view的位置
            endSnapRect[paddingStart, height - paddingBottom - itemHeight, paddingStart + itemWidth] =
                height - paddingBottom
        } else {
            //左上角第一个view的位置
            startSnapRect[paddingStart, paddingTop, paddingStart + itemWidth] =
                paddingTop + itemHeight
            //右下角最后一个view的位置
            endSnapRect[width - paddingEnd - itemWidth, height - paddingBottom - itemHeight, width - paddingEnd] =
                height - paddingBottom
        }

        //计算总页数
        var pagerCount = itemCount / onePageSize
        if (itemCount % onePageSize != 0) {
            ++pagerCount
        }

        //计算需要补充空间
        layoutState.replenishDelta = 0
        // 超过一页，计算补充空间距离
        if (pagerCount > 1) {
            val remain = itemCount % onePageSize
            var replenish = 0
            if (remain != 0) {
                var i = remain / mColumns
                val k = remain % mColumns
                if (mOrientation == HORIZONTAL) {
                    replenish = if ((i == 0)) (mColumns - k) * this.itemWidth else 0
                } else {
                    if (k > 0) {
                        ++i
                    }
                    replenish = (mRows - i) * this.itemHeight
                }
            }
            layoutState.replenishDelta = replenish
        }

        layoutState.mRecycle = false
        layoutState.mLayoutDirection = LayoutState.LAYOUT_END
        layoutState.mAvailable = end
        layoutState.mScrollingOffset = LayoutState.SCROLLING_OFFSET_NaN

        var pagerIndex = mCurrentPagerIndex
        pagerIndex = if (pagerIndex == NO_ITEM) {
            0
        } else {
            //取上次PagerIndex和最大MaxPagerIndex中最小值。
            min(pagerIndex.toDouble(), maxPagerIndex.toDouble()).toInt()
        }
        val firstView = if (!isIdle && childCount != 0) {
            //滑动中的更新状态
            childClosestToStart
        } else {
            //没有子view或者不在滑动状态
            null
        }

        //计算首个位置的偏移量，主要是为了方便child layout，计算出目标位置的上一个位置的坐标
        val left: Int
        val top: Int
        val right: Int
        val bottom: Int
        if (shouldReverseLayout) {
            if (firstView == null) {
                //按页且从右上角开始布局
                layoutState.mCurrentPosition = pagerIndex * onePageSize

                val calculateClipOffset = calculateClipOffset(true, layoutState.mCurrentPosition)

                if (mOrientation == RecyclerView.HORIZONTAL) {
                    bottom = height - paddingBottom
                    left = width - paddingEnd + calculateClipOffset
                } else {
                    bottom = paddingTop - calculateClipOffset
                    left = paddingStart
                }
            } else {
                //计算布局偏移量
                val position = getPosition(firstView)
                layoutState.mCurrentPosition = position
                val rect = layoutState.mOffsetRect

                val calculateClipOffset = calculateClipOffset(true, layoutState.mCurrentPosition)

                getDecoratedBoundsWithMargins(firstView, rect)
                if (mOrientation == RecyclerView.HORIZONTAL) {
                    if (isNeedMoveToNextSpan(position)) {
                        //为了方便计算
                        bottom = height - paddingBottom
                        left = rect.right + calculateClipOffset
                    } else {
                        bottom = rect.top
                        left = rect.left
                    }
                } else {
                    if (isNeedMoveToNextSpan(position)) {
                        //为了方便计算
                        bottom = rect.top - calculateClipOffset
                        left = paddingStart
                    } else {
                        bottom = rect.bottom
                        left = rect.right
                    }
                }
                //追加额外的滑动空间
                val scrollingOffset = if (mOrientation == HORIZONTAL) {
                    getDecoratedStart(firstView) - endAfterPadding
                } else {
                    getDecoratedStart(firstView)
                }
                layoutState.mAvailable -= scrollingOffset
            }

            top = bottom - itemHeight
            right = left + itemWidth
        } else {
            if (firstView == null) {
                //按页且从左上角开始布局
                layoutState.mCurrentPosition = pagerIndex * onePageSize

                val calculateClipOffset = calculateClipOffset(true, layoutState.mCurrentPosition)

                if (mOrientation == RecyclerView.HORIZONTAL) {
                    bottom = height - paddingBottom
                    right = paddingStart - calculateClipOffset
                } else {
                    bottom = paddingTop - calculateClipOffset
                    right = width - paddingEnd
                }
            } else {
                //计算布局偏移量
                val position = getPosition(firstView)
                layoutState.mCurrentPosition = position
                val rect = layoutState.mOffsetRect

                val calculateClipOffset = calculateClipOffset(true, layoutState.mCurrentPosition)

                getDecoratedBoundsWithMargins(firstView, rect)
                if (mOrientation == RecyclerView.HORIZONTAL) {
                    if (isNeedMoveToNextSpan(position)) {
                        //为了方便计算
                        bottom = height - paddingBottom
                        right = rect.left - calculateClipOffset
                    } else {
                        bottom = rect.top
                        right = rect.right
                    }
                } else {
                    if (isNeedMoveToNextSpan(position)) {
                        //为了方便计算
                        bottom = rect.top - calculateClipOffset
                        right = width - paddingEnd
                    } else {
                        bottom = rect.bottom
                        right = rect.left
                    }
                }
                //追加额外的滑动空间
                val scrollingOffset = getDecoratedStart(firstView)
                layoutState.mAvailable -= scrollingOffset
            }
            top = bottom - itemHeight
            left = right - itemWidth
        }
        layoutState.setOffsetRect(left, top, right, bottom)

        //回收views
        detachAndScrapAttachedViews(recycler)
        //填充views
        fill(recycler, state)

        if (firstView == null) {
            //移动状态不更新页数和页码
            this.pagerCount = pagerCount
            currentPagerIndex = pagerIndex
        }
    }


    override fun findViewByPosition(position: Int): View? {
        val childCount = childCount
        if (childCount == 0) {
            return null
        }
        val firstChild = getPosition(getChildAt(0)!!)
        val viewPosition = position - firstChild
        if (viewPosition in 0..<childCount) {
            val child = getChildAt(viewPosition)
            if (getPosition(child!!) == position) {
                return child
            }
        }
        return super.findViewByPosition(position)
    }

    override fun computeHorizontalScrollOffset(state: RecyclerView.State): Int {
        return computeScrollOffset(state)
    }

    override fun computeVerticalScrollOffset(state: RecyclerView.State): Int {
        return computeScrollOffset(state)
    }

    override fun computeHorizontalScrollExtent(state: RecyclerView.State): Int {
        return computeScrollExtent(state)
    }

    override fun computeVerticalScrollExtent(state: RecyclerView.State): Int {
        return computeScrollExtent(state)
    }

    override fun computeVerticalScrollRange(state: RecyclerView.State): Int {
        return computeScrollRange(state)
    }

    override fun computeHorizontalScrollRange(state: RecyclerView.State): Int {
        return computeScrollRange(state)
    }

    override fun onSaveInstanceState(): Parcelable {
        return SavedState(
            mOrientation,
            mRows,
            mColumns,
            mCurrentPagerIndex,
            mReverseLayout,
        )
    }

    override fun onRestoreInstanceState(state: Parcelable) {
        if (state is SavedState) {
            mOrientation = state.orientation
            mRows = state.rows
            mColumns = state.columns
            calculateOnePageSize()
            currentPagerIndex = state.currentPagerIndex
            mReverseLayout = state.reverseLayout
            requestLayout()
        }
    }

    override fun scrollToPosition(position: Int) {
        assertNotInLayoutOrScroll(null)

        //先找到目标position所在第几页
        val pagerIndex = getPagerIndexByPosition(position)
        scrollToPagerIndex(pagerIndex)
    }

    override fun smoothScrollToPosition(
        recyclerView: RecyclerView,
        state: RecyclerView.State,
        position: Int
    ) {
        assertNotInLayoutOrScroll(null)

        //先找到目标position所在第几页
        val pagerIndex = getPagerIndexByPosition(position)
        smoothScrollToPagerIndex(pagerIndex)
    }

    override fun scrollHorizontallyBy(
        dx: Int,
        recycler: RecyclerView.Recycler,
        state: RecyclerView.State
    ): Int {
        if (mOrientation == VERTICAL) {
            //垂直滑动不处理
            return 0
        }
        return scrollBy(dx, recycler, state)
    }

    override fun scrollVerticallyBy(
        dy: Int,
        recycler: RecyclerView.Recycler,
        state: RecyclerView.State
    ): Int {
        if (mOrientation == HORIZONTAL) {
            //水平滑动不处理
            return 0
        }
        return scrollBy(dy, recycler, state)
    }

    override fun onScrollStateChanged(state: Int) {
        when (state) {
            RecyclerView.SCROLL_STATE_IDLE -> {}
            RecyclerView.SCROLL_STATE_DRAGGING -> {}
            RecyclerView.SCROLL_STATE_SETTLING -> {}
        }
    }

    override fun canScrollHorizontally(): Boolean {
        return mOrientation == RecyclerView.HORIZONTAL
    }

    override fun canScrollVertically(): Boolean {
        return mOrientation == RecyclerView.VERTICAL
    }

    override fun getWidth(): Int {
        return super.getWidth() - getDiffWidth()
    }

    override fun getHeight(): Int {
        return super.getHeight() - getDiffHeight()
    }

    @CallSuper
    override fun onDetachedFromWindow(view: RecyclerView, recycler: RecyclerView.Recycler) {
        super.onDetachedFromWindow(view, recycler)
        if (mRecyclerView != null) {
            if (onItemTouchListener != null) {
                mRecyclerView!!.removeOnItemTouchListener(onItemTouchListener!!)
            }
            mRecyclerView!!.removeOnChildAttachStateChangeListener(onChildAttachStateChangeListener)
            mRecyclerView = null
        }
        pagerGridSnapHelper!!.attachToRecyclerView(null)
        pagerGridSnapHelper = null
        //这里不能置为null，因为在ViewPager2嵌套Fragment使用，
        //部分情况下Fragment不回调onDestroyView，但会导致onDetachedFromWindow触发。
        //所以如果想置null，请调用{@link #setPagerChangedListener(null)}
//        mPagerChangedListener = null;
    }

    /**
     * 设置监听回调
     *
     * @param listener
     */
    fun setPagerChangedListener(listener: PagerChangedListener?) {
        mPagerChangedListener = listener
    }

    var millisecondPreInch: Float
        /**
         * @return 滑动每像素需要花费的时间
         * @see PagerGridSmoothScroller.calculateSpeedPerPixel
         */
        get() = mMillisecondPreInch
        /**
         * 设置滑动每像素需要花费的时间，不可过小，不然可能会出现划过再回退的情况
         * 默认值：[PagerGridSmoothScroller.MILLISECONDS_PER_INCH]
         *
         *
         * set millisecond pre inch. not too small.
         * default value: [PagerGridSmoothScroller.MILLISECONDS_PER_INCH]
         *
         * @param millisecondPreInch 值越大，滚动速率越慢，反之
         * @see PagerGridSmoothScroller.calculateSpeedPerPixel
         */
        set(millisecondPreInch) {
            mMillisecondPreInch = max(1.0, millisecondPreInch.toDouble()).toFloat()
        }

    var maxScrollOnFlingDuration: Int
        /**
         * @return 最大滚动时间
         * @see PagerGridSmoothScroller.calculateTimeForScrolling
         */
        get() = mMaxScrollOnFlingDuration
        /**
         * 设置最大滚动时间，如果您想此值无效，请使用[Integer.MAX_VALUE]
         * 默认值：[PagerGridSmoothScroller.MAX_SCROLL_ON_FLING_DURATION]，单位：毫秒
         *
         *
         * set max scroll on fling duration.If you want this value to expire, use [Integer.MAX_VALUE]
         * default value: [PagerGridSmoothScroller.MAX_SCROLL_ON_FLING_DURATION],Unit: ms
         *
         * @param maxScrollOnFlingDuration 值越大，滑动时间越长，滚动速率越慢，反之
         * @see PagerGridSmoothScroller.calculateTimeForScrolling
         */
        set(maxScrollOnFlingDuration) {
            mMaxScrollOnFlingDuration = max(1.0, maxScrollOnFlingDuration.toDouble()).toInt()
        }

    /**
     * 计算一页的数量
     */
    private fun calculateOnePageSize() {
        onePageSize = mRows * mColumns
    }



    /**
     * @param position position
     * @return 获取当前position所在页下标
     */
    fun getPagerIndexByPosition(position: Int): Int {
        return position / onePageSize
    }

    private val maxPagerIndex: Int
        /**
         * @return 获取最大页数
         */
        get() = getPagerIndexByPosition(itemCount - 1)

    /**
     * 直接滚到第几页
     *
     * @param pagerIndex 第几页
     */
    fun scrollToPagerIndex(pagerIndex: Int) {
        var targetPagerIndex = pagerIndex
        assertNotInLayoutOrScroll(null)

        //先找到目标position所在第几页
        targetPagerIndex = min(max(targetPagerIndex.toDouble(), 0.0), maxPagerIndex.toDouble())
            .toInt()
        if (targetPagerIndex == mCurrentPagerIndex) {
            //同一页直接return
            return
        }
        currentPagerIndex = targetPagerIndex
        requestLayout()
    }

    /**
     * 直接滚动到上一页
     */
    fun scrollToPrePager() {
        assertNotInLayoutOrScroll(null)

        scrollToPagerIndex(mCurrentPagerIndex - 1)
    }

    /**
     * 直接滚动到下一页
     */
    fun scrollToNextPager() {
        assertNotInLayoutOrScroll(null)

        scrollToPagerIndex(mCurrentPagerIndex + 1)
    }

    /**
     * 平滑滚到第几页，为避免长时间滚动，会预先跳转到就近位置，默认3页
     *
     * @param pagerIndex 第几页，下标从0开始
     */
    fun smoothScrollToPagerIndex(pagerIndex: Int) {
        var targetIndex = pagerIndex
        assertNotInLayoutOrScroll(null)

        targetIndex = min(max(targetIndex, 0).toDouble(), maxPagerIndex.toDouble())
            .toInt()
        val previousIndex = mCurrentPagerIndex
        if (targetIndex == previousIndex) {
            //同一页直接return
            return
        }
        val isLayoutToEnd = targetIndex > previousIndex

        if (abs((targetIndex - previousIndex).toDouble()) > 3) {
            //先就近直接跳转
            val transitionIndex = if (targetIndex > previousIndex) targetIndex - 3 else targetIndex + 3
            scrollToPagerIndex(transitionIndex)

            if (mRecyclerView != null) {
                mRecyclerView!!.post(
                    SmoothScrollToPosition(
                        getPositionByPagerIndex(
                            targetIndex,
                            isLayoutToEnd
                        ), this, mRecyclerView!!
                    )
                )
            }
        } else {
            val smoothScroller = PagerGridSmoothScroller(mRecyclerView!!.context, this)
            smoothScroller.targetPosition = getPositionByPagerIndex(targetIndex, isLayoutToEnd)
            startSmoothScroll(smoothScroller)
        }
    }

    /**
     * 平滑到上一页
     */
    fun smoothScrollToPrePager() {
        assertNotInLayoutOrScroll(null)

        smoothScrollToPagerIndex(mCurrentPagerIndex - 1)
    }

    /**
     * 平滑到下一页
     */
    fun smoothScrollToNextPager() {
        assertNotInLayoutOrScroll(null)

        smoothScrollToPagerIndex(mCurrentPagerIndex + 1)
    }

    private fun createLayoutState(): LayoutState {
        return LayoutState()
    }

    private fun createLayoutChunkResult(): LayoutChunkResult {
        return LayoutChunkResult()
    }

    private val isLayoutRTL: Boolean
        get() = layoutDirection == ViewCompat.LAYOUT_DIRECTION_RTL



    /**
     * 由于View类中这个方法无法使用，直接copy处理
     *
     * @param view
     * @return 判断view是不是处在一个可滑动的布局中
     * @see ViewGroup.shouldDelayChildPressedState
     */
    private fun isInScrollingContainer(view: View): Boolean {
        var p = view.parent
        while (p is ViewGroup) {
            if (p.shouldDelayChildPressedState()) {
                return true
            }
            p = p.getParent()
        }
        return false
    }

    /**
     * 根据页码下标获取position
     *
     * @param pagerIndex    页码
     * @param isLayoutToEnd true:页的第一个位置，false:页的最后一个位置
     * @return
     */
    private fun getPositionByPagerIndex(pagerIndex: Int, isLayoutToEnd: Boolean): Int {
        return if (isLayoutToEnd) pagerIndex * onePageSize else pagerIndex * onePageSize + onePageSize - 1
    }

    private fun getDiffWidth(): Int {
        return max(diffWidth.toDouble(), 0.0).toInt()
    }

    private fun getDiffHeight(): Int {
        return max(diffHeight.toDouble(), 0.0).toInt()
    }


    /**
     * 填充布局
     *
     * @param recycler
     * @param state
     * @return 添加的像素数，用于滚动
     */
    private fun fill(recycler: RecyclerView.Recycler, state: RecyclerView.State): Int {
        val layoutState = layoutState
        val start = layoutState.mAvailable
        var remainingSpace = layoutState.mAvailable
        val layoutChunkResult = mLayoutChunkResult
        while (remainingSpace > 0 && layoutState.hasMore(state)) {
            if (shouldReverseLayout) {
                reverseLayoutChunk(recycler, state, layoutState, layoutChunkResult)
            } else {
                layoutChunk(recycler, state, layoutState, layoutChunkResult)
            }
            layoutState.mAvailable -= layoutChunkResult.mConsumed
            remainingSpace -= layoutChunkResult.mConsumed
        }
        val layoutToEnd = layoutState.mLayoutDirection == LayoutState.LAYOUT_END
        //因为最后一列或者一行可能只绘制了收尾的一个，补满
        while (layoutState.hasMore(state)) {
            val isNeedMoveSpan =
                if (layoutToEnd) isNeedMoveToNextSpan(layoutState.mCurrentPosition) else isNeedMoveToPreSpan(
                    layoutState.mCurrentPosition
                )
            if (isNeedMoveSpan) {
                //如果需要切换行或列，直接退出
                break
            }
            if (shouldReverseLayout) {
                reverseLayoutChunk(recycler, state, layoutState, layoutChunkResult)
            } else {
                layoutChunk(recycler, state, layoutState, layoutChunkResult)
            }
        }
        //回收View
        recycleViews(recycler)
        return start - layoutState.mAvailable
    }

    /**
     * 正项布局
     *
     * @param recycler
     * @param state
     * @param layoutState
     * @param layoutChunkResult
     * @see .layoutChunk
     * @see .shouldReverseLayout
     */
    private fun layoutChunk(
        recycler: RecyclerView.Recycler,
        state: RecyclerView.State,
        layoutState: LayoutState,
        layoutChunkResult: LayoutChunkResult
    ) {
        val layoutToEnd = layoutState.mLayoutDirection == LayoutState.LAYOUT_END
        val position = layoutState.mCurrentPosition
        val view = layoutState.next(recycler)
        if (layoutToEnd) {
            addView(view)
        } else {
            addView(view, 0)
        }
        layoutState.mCurrentPosition = if (layoutToEnd) layoutState.getNextPosition(
            position,
            mOrientation,
            mRows,
            mColumns,
            state
        ) else layoutState.getPrePosition(position, mOrientation, mRows, mColumns)
        measureChildWithMargins(view, mItemWidthUsed, mItemHeightUsed)
        //是否需要换行或者换列
        val isNeedMoveSpan =
            if (layoutToEnd) isNeedMoveToNextSpan(position) else isNeedMoveToPreSpan(position)
        layoutChunkResult.mConsumed =
            if (isNeedMoveSpan) if (mOrientation == HORIZONTAL) itemWidth else itemHeight else 0

        //记录的上一个View的位置
        val rect = layoutState.mOffsetRect
        val left: Int
        val top: Int
        val right: Int
        val bottom: Int
        if (mOrientation == HORIZONTAL) {
            //水平滑动
            if (layoutToEnd) {
                //向后填充，绘制方向：从上到下
                if (isNeedMoveSpan) {
                    //下一列绘制，从头部开始
                    left = rect.left + itemWidth + calculateClipOffset(true, position)
                    top = paddingTop
                } else {
                    //当前列绘制
                    left = rect.left
                    top = rect.bottom
                }
                right = left + itemWidth
                bottom = top + itemHeight
            } else {
                //向前填充，绘制方向：从下到上
                if (isNeedMoveSpan) {
                    //上一列绘制，从底部开启
                    left = rect.left - itemWidth - calculateClipOffset(false, position)
                    bottom = height - paddingBottom
                } else {
                    //当前列绘制
                    left = rect.left
                    bottom = rect.top
                }
                top = bottom - itemHeight
                right = left + itemWidth
            }
        } else {
            if (layoutToEnd) {
                //向下填充，绘制方向：从左到右
                if (isNeedMoveSpan) {
                    //下一行绘制，从头部开始
                    left = paddingStart
                    top = rect.bottom + calculateClipOffset(true, position)
                } else {
                    //当前行绘制
                    left = rect.left + itemWidth
                    top = rect.top
                }
                right = left + itemWidth
                bottom = top + itemHeight
            } else {
                //向上填充，绘制方向：从右到左
                if (isNeedMoveSpan) {
                    //上一行绘制，从尾部开始
                    right = width - paddingEnd
                    left = right - itemWidth
                    bottom = rect.top - calculateClipOffset(false, position)
                    top = bottom - itemHeight
                } else {
                    //当前行绘制
                    left = rect.left - itemWidth
                    top = rect.top
                    right = left + itemWidth
                    bottom = top + itemHeight
                }
            }
        }
        layoutState.setOffsetRect(left, top, right, bottom)
        layoutDecoratedWithMargins(view, left, top, right, bottom)
    }

    /**
     * 反向布局
     *
     * @param recycler
     * @param state
     * @param layoutState
     * @param layoutChunkResult
     * @see [layoutChunk]
     * @see [shouldReverseLayout]
     */
    private fun reverseLayoutChunk(
        recycler: RecyclerView.Recycler,
        state: RecyclerView.State,
        layoutState: LayoutState,
        layoutChunkResult: LayoutChunkResult
    ) {
        //仅处理水平反向滑动，垂直仅改变排列顺序
        val layoutToEnd = layoutState.mLayoutDirection == LayoutState.LAYOUT_END

        val position = layoutState.mCurrentPosition
        val view = layoutState.next(recycler)
        if (layoutToEnd) {
            addView(view)
        } else {
            addView(view, 0)
        }
        layoutState.mCurrentPosition = if (layoutToEnd) layoutState.getNextPosition(
            position,
            mOrientation,
            mRows,
            mColumns,
            state
        ) else layoutState.getPrePosition(position, mOrientation, mRows, mColumns)
        measureChildWithMargins(view, mItemWidthUsed, mItemHeightUsed)
        //是否需要换行或者换列
        val isNeedMoveSpan =
            if (layoutToEnd) isNeedMoveToNextSpan(position) else isNeedMoveToPreSpan(position)
        layoutChunkResult.mConsumed =
            if (isNeedMoveSpan) if (mOrientation == HORIZONTAL) itemWidth else itemHeight else 0

        //记录的上一个View的位置
        val rect = layoutState.mOffsetRect
        val left: Int
        val top: Int
        val right: Int
        val bottom: Int
        if (mOrientation == HORIZONTAL) {
            //水平滑动
            if (layoutToEnd) {
                //向前填充，绘制方向：从上到下
                if (isNeedMoveSpan) {
                    //上一列绘制，从头部开始
                    left = rect.left - itemWidth - calculateClipOffset(true, position)
                    top = paddingTop
                } else {
                    //当前列绘制
                    left = rect.left
                    top = rect.bottom
                }
                right = left + itemWidth
                bottom = top + itemHeight
            } else {
                //向后填充，绘制方向：从下到上
                if (isNeedMoveSpan) {
                    //下一列绘制，从底部开启
                    left = rect.left + itemWidth + calculateClipOffset(false, position)
                    bottom = height - paddingBottom
                } else {
                    //当前列绘制
                    left = rect.left
                    bottom = rect.top
                }
                top = bottom - itemHeight
                right = left + itemWidth
            }
        } else {
            if (layoutToEnd) {
                //向下填充，绘制方向：从右到左
                if (isNeedMoveSpan) {
                    //下一行绘制，从尾部开始
                    right = width - paddingEnd
                    top = rect.bottom + calculateClipOffset(true, position)
                } else {
                    //当前行绘制，向前布局
                    right = rect.left
                    top = rect.top
                }
                left = right - itemWidth
                bottom = top + itemHeight
            } else {
                //向上填充，绘制方向：从左到右
                if (isNeedMoveSpan) {
                    //上一行绘制，从头部开始
                    left = paddingStart
                    right = left + itemWidth
                    bottom = rect.top - calculateClipOffset(false, position)
                    top = bottom - itemHeight
                } else {
                    //当前行绘制，向后布局
                    left = rect.right
                    right = left + itemWidth
                    top = rect.top
                    bottom = top + itemHeight
                }
            }
        }
        layoutState.setOffsetRect(left, top, right, bottom)
        layoutDecoratedWithMargins(view, left, top, right, bottom)
    }

    /**
     * @param delta    手指滑动的距离
     * @param recycler
     * @param state
     * @return
     */
    private fun scrollBy(
        delta: Int,
        recycler: RecyclerView.Recycler,
        state: RecyclerView.State
    ): Int {
        if (childCount == 0 || delta == 0 || mPagerCount == 1) {
            return 0
        }
        layoutState.mRecycle = true
        val layoutDirection = if (shouldHorizontallyReverseLayout()) {
            if (delta > 0) LayoutState.LAYOUT_START else LayoutState.LAYOUT_END
        } else {
            if (delta > 0) LayoutState.LAYOUT_END else LayoutState.LAYOUT_START
        }
        layoutState.mLayoutDirection = layoutDirection
        val layoutToEnd = layoutDirection == LayoutState.LAYOUT_END
        val absDelta = abs(delta.toDouble()).toInt()

        updateLayoutState(layoutToEnd, absDelta, true, state)
        var consumed = layoutState.mScrollingOffset + fill(recycler, state)
        if (layoutToEnd) {
            //向后滑动，添加补充距离
            consumed += layoutState.replenishDelta
        }
        if (consumed < 0) {
            return 0
        }
        //是否已经完全填充到头部或者尾部，滑动的像素>消费的像素
        val isOver = absDelta > consumed
        //计算实际可移动值
        val scrolled = if (isOver) layoutDirection * consumed else delta
        //移动
        offsetChildren(-scrolled)
        layoutState.mLastScrollDelta = scrolled

        //回收view，此步骤在移动之后
        recycleViews(recycler)

        return scrolled
    }

    private fun updateLayoutState(
        layoutToEnd: Boolean, requiredSpace: Int,
        canUseExistingSpace: Boolean, state: RecyclerView.State
    ) {
        val child: View?
        //计算在不添加新view的情况下可以滚动多少（与布局无关）
        val scrollingOffset: Int
        if (layoutToEnd) {
            child = childClosestToEnd
            scrollingOffset = if (shouldHorizontallyReverseLayout()) {
                -getDecoratedStart(child) + startAfterPadding
            } else {
                getDecoratedEnd(child) - endAfterPadding
            }
        } else {
            child = childClosestToStart
            scrollingOffset = if (shouldHorizontallyReverseLayout()) {
                getDecoratedEnd(child) - endAfterPadding
            } else {
                -getDecoratedStart(child) + startAfterPadding
            }
        }
        getDecoratedBoundsWithMargins(child!!, layoutState.mOffsetRect)

        layoutState.mCurrentPosition = if (layoutToEnd) layoutState.getNextPosition(
            getPosition(
                child
            ), mOrientation, mRows, mColumns, state
        ) else layoutState.getPrePosition(
            getPosition(
                child
            ), mOrientation, mRows, mColumns
        )

        layoutState.mAvailable = requiredSpace
        if (canUseExistingSpace) {
            layoutState.mAvailable -= scrollingOffset
        }
        layoutState.mScrollingOffset = scrollingOffset
    }

    private val childClosestToEnd: View?
        get() = getChildAt(childCount - 1)

    private val childClosestToStart: View?
        get() = getChildAt(0)

    /**
     * 回收View
     *
     * @param recycler
     */
    private fun recycleViews(recycler: RecyclerView.Recycler) {
        //是否回收view
        if (!layoutState.mRecycle) {
            return
        }
        if (shouldHorizontallyReverseLayout()) {
            if (layoutState.mLayoutDirection == LayoutState.LAYOUT_START) {
                //水平向右或者垂直向下滑动
                recycleViewsFromStart(recycler)
            } else {
                //水平向左或者垂直向上滑动
                recycleViewsFromEnd(recycler)
            }
        } else {
            if (layoutState.mLayoutDirection == LayoutState.LAYOUT_START) {
                //水平向左或者垂直向上滑动
                recycleViewsFromEnd(recycler)
            } else {
                //水平向右或者垂直向下滑动
                recycleViewsFromStart(recycler)
            }
        }
    }

    private fun recycleViewsFromStart(recycler: RecyclerView.Recycler) {
        //如果clipToPadding==false，则不计算padding
        val clipToPadding = clipToPadding
        val start = if (clipToPadding) startAfterPadding else 0
        val childCount = childCount
        for (i in childCount - 1 downTo 0) {
            val childAt = getChildAt(i)
            if (childAt != null) {
                val decorated = getDecoratedEnd(childAt)
                if (decorated >= start) {
                    continue
                }
                logTagW(
                    TAG,
                    "recycleViewsFromStart-removeAndRecycleViewAt: $i, position: " + getPosition(
                        childAt
                    )
                )

                removeAndRecycleViewAt(i, recycler)
            }
        }
    }

    private fun recycleViewsFromEnd(recycler: RecyclerView.Recycler) {
        //如果clipToPadding==false，则不计算padding
        val clipToPadding = clipToPadding
        val end =
            if (clipToPadding) endAfterPadding else (if (mOrientation == HORIZONTAL) width else height)
        val childCount = childCount
        for (i in childCount - 1 downTo 0) {
            val childAt = getChildAt(i)
            if (childAt != null) {
                val decorated = getDecoratedStart(childAt)
                if (decorated <= end) {
                    continue
                }
                logTagW(
                    TAG,
                    "recycleViewsFromEnd-removeAndRecycleViewAt: $i, position: " + getPosition(
                        childAt
                    )
                )

                removeAndRecycleViewAt(i, recycler)
            }
        }
    }

    private fun getDecoratedEnd(child: View?): Int {
        val params = child!!.layoutParams as LayoutParams
        return if (mOrientation == HORIZONTAL) getDecoratedRight(
            child
        ) + params.rightMargin else getDecoratedBottom(
            child
        ) + params.bottomMargin
    }

    private fun getDecoratedStart(child: View?): Int {
        val params = child!!.layoutParams as LayoutParams
        return if (mOrientation == HORIZONTAL) getDecoratedLeft(
            child
        ) - params.leftMargin else getDecoratedTop(
            child
        ) - params.topMargin
    }

    private val endAfterPadding: Int
        get() = if (mOrientation == HORIZONTAL) width - paddingEnd else height - paddingBottom

    private val startAfterPadding: Int
        get() = if (mOrientation == HORIZONTAL) paddingStart else paddingTop

    private val clipToPaddingSize: Int
        get() = if (mOrientation == HORIZONTAL) paddingStart + paddingEnd else paddingTop + paddingBottom

    /**
     * 计算[getClipToPadding]==false时偏移量
     *
     * @param layoutToEnd 是否是向后布局
     * @param position    position
     * @return offset
     */
    private fun calculateClipOffset(layoutToEnd: Boolean, position: Int): Int {
        val clipToPadding = clipToPadding
        return if (!clipToPadding && (position % onePageSize == (if (layoutToEnd) 0 else onePageSize - 1))) clipToPaddingSize else 0
    }

    private val end: Int
        get() = if (mOrientation == HORIZONTAL) realWidth else realHeight

    /**
     * 移动Children
     *
     * @param delta 移动偏移量
     */
    private fun offsetChildren(delta: Int) {
        if (mOrientation == HORIZONTAL) {
            offsetChildrenHorizontal(delta)
        } else {
            offsetChildrenVertical(delta)
        }
    }

    private val isIdle: Boolean
        /**
         * @return 当前Recycler是否是静止状态
         */
        get() = mRecyclerView == null || mRecyclerView!!.scrollState == RecyclerView.SCROLL_STATE_IDLE

    /**
     * @param position
     * @return 是否需要换到下一行或列
     */
    private fun isNeedMoveToNextSpan(position: Int): Boolean {
        if (mOrientation == HORIZONTAL) {
            val surplus = position % onePageSize
            val rowIndex = surplus / mColumns
            //是否在最后一行
            return rowIndex == 0
        } else {
            return position % mColumns == 0
        }
    }

    /**
     * @param position
     * @return 是否需要换到上一行或列
     */
    private fun isNeedMoveToPreSpan(position: Int): Boolean {
        if (mOrientation == HORIZONTAL) {
            val surplus = position % onePageSize
            //在第几行
            val rowIndex = surplus / mColumns
            //是否在第一行
            return rowIndex == mRows - 1
        } else {
            return position % mColumns == mColumns - 1
        }
    }

    private fun computeScrollOffset(state: RecyclerView.State): Int {
        if (childCount == 0 || state.itemCount == 0) {
            return 0
        }
        val firstView = getChildAt(0) ?: return 0
        val position = getPosition(firstView)
        val avgSize = end.toFloat() / (if (mOrientation == HORIZONTAL) mColumns else mRows)
        val index: Int
        if (mOrientation == HORIZONTAL) {
            //所在第几列
            val pagerIndex = getPagerIndexByPosition(position)
            index = pagerIndex * mColumns + position % mColumns
        } else {
            //所在第几行
            index = position / mColumns
        }
        val scrollOffset: Int
        if (shouldHorizontallyReverseLayout()) {
            val scrollRange = computeScrollRange(state) - computeScrollExtent(state)
            scrollOffset =
                scrollRange - Math.round(index * avgSize + (getDecoratedEnd(firstView) - endAfterPadding))
        } else {
            scrollOffset =
                Math.round(index * avgSize + (startAfterPadding - getDecoratedStart(firstView)))
        }

        return scrollOffset
    }

    private fun computeScrollExtent(state: RecyclerView.State): Int {
        if (childCount == 0 || state.itemCount == 0) {
            return 0
        }
        val scrollExtent = end

        return scrollExtent
    }

    private fun computeScrollRange(state: RecyclerView.State): Int {
        if (childCount == 0 || state.itemCount == 0) {
            return 0
        }
        val scrollRange = (max(mPagerCount.toDouble(), 0.0) * end).toInt()

        return scrollRange
    }

    private fun resolveShouldLayoutReverse() {
        shouldReverseLayout = if (mOrientation == VERTICAL || !isLayoutRTL) {
            mReverseLayout
        } else {
            //水平滑动且是RTL
            !mReverseLayout
        }
    }

    /**
     * 根据下标计算页码
     *
     * @param position
     */
    fun calculateCurrentPagerIndexByPosition(position: Int) {
        currentPagerIndex = getPagerIndexByPosition(position)
    }

    /**
     * @return 是否水平方向反转布局
     */
    fun shouldHorizontallyReverseLayout(): Boolean {
        return shouldReverseLayout && mOrientation == HORIZONTAL
    }

    override fun computeScrollVectorForPosition(targetPosition: Int): PointF? {
        val childCount = childCount
        if (childCount == 0) {
            return null
        }
        var firstSnapPosition = RecyclerView.NO_POSITION
        for (i in childCount - 1 downTo 0) {
            val childAt = getChildAt(i)
            if (childAt != null) {
                val position = getPosition(childAt)
                if (position % onePageSize == 0) {
                    firstSnapPosition = position
                    break
                }
            }
        }
        if (firstSnapPosition == RecyclerView.NO_POSITION) {
            return null
        }
        var direction = if (targetPosition < firstSnapPosition) -1f else 1f
        if (shouldHorizontallyReverseLayout()) {
            direction = -direction
        }
        logTagW(
            TAG,
            "computeScrollVectorForPosition-firstSnapPosition: $firstSnapPosition, targetPosition:$targetPosition,mOrientation :$mOrientation, direction:$direction"
        )

        return if (mOrientation == HORIZONTAL) {
            PointF(direction, 0f)
        } else {
            PointF(0f, direction)
        }
    }

    /**
     * 自定义LayoutParams
     */
    class LayoutParams : RecyclerView.LayoutParams {
        constructor(c: Context?, attrs: AttributeSet?) : super(c, attrs)

        constructor(width: Int, height: Int) : super(width, height)

        constructor(source: ViewGroup.MarginLayoutParams?) : super(source)

        constructor(source: ViewGroup.LayoutParams?) : super(source)

        constructor(source: RecyclerView.LayoutParams?) : super(source)
    }

    private class SmoothScrollToPosition(
        private val mPosition: Int,
        private val mLayoutManager: PagerGridLayoutManager,
        private val mRecyclerView: RecyclerView
    ) : Runnable {
        override fun run() {
            val smoothScroller = PagerGridSmoothScroller(mRecyclerView.context, mLayoutManager)
            smoothScroller.targetPosition = mPosition
            mLayoutManager.startSmoothScroll(smoothScroller)
        }
    }

    class LayoutState {
        /**
         * 可填充的View空间大小
         */
        var mAvailable: Int = 0

        /**
         * 是否需要回收View
         */
        var mRecycle: Boolean = false

        var mCurrentPosition: Int = 0

        /**
         * 布局的填充方向
         * 值为 [LAYOUT_START] or [LAYOUT_END]
         */
        var mLayoutDirection: Int = 0

        /**
         * 在滚动状态下构造布局状态时使用。
         * 它应该设置我们可以在不创建新视图的情况下进行滚动量。
         * 有效的视图回收需要设置
         */
        var mScrollingOffset: Int = 0

        /**
         * 开始绘制的坐标位置
         */
        val mOffsetRect: Rect = Rect()

        /**
         * 最近一次的滑动数量
         */
        var mLastScrollDelta: Int = 0

        /**
         * 需要补充滑动的距离
         */
        var replenishDelta: Int = 0

        fun setOffsetRect(left: Int, top: Int, right: Int, bottom: Int) {
            mOffsetRect[left, top, right] = bottom
        }

        fun next(recycler: RecyclerView.Recycler): View {
            return recycler.getViewForPosition(mCurrentPosition)
        }

        fun hasMore(state: RecyclerView.State): Boolean {
            return mCurrentPosition >= 0 && mCurrentPosition < state.itemCount
        }

        /**
         * @param currentPosition 当前的位置
         * @param orientation     方向
         * @param rows            行数
         * @param columns         列数
         * @param state           状态
         * @return 下一个位置
         */
        fun getNextPosition(
            currentPosition: Int,
            orientation: Int,
            rows: Int,
            columns: Int,
            state: RecyclerView.State
        ): Int {
            var position: Int
            val onePageSize = rows * columns
            if (orientation == HORIZONTAL) {
                val surplus = currentPosition % onePageSize
                //水平滑动
                //向后追加item
                if (surplus == onePageSize - 1) {
                    //一页的最后一个位置
                    position = currentPosition + 1
                } else {
                    //在第几列
                    val columnsIndex = currentPosition % columns
                    //在第几行
                    val rowIndex = surplus / columns
                    //是否在最后一行
                    val isLastRow = rowIndex == rows - 1
                    if (isLastRow) {
                        position = currentPosition - rowIndex * columns + 1
                    } else {
                        position = currentPosition + columns
                        if (position >= state.itemCount) {
                            //越界了
                            if (columnsIndex != columns - 1) {
                                //如果不是最后一列，计算换行位置
                                position = currentPosition - rowIndex * columns + 1
                            }
                        }
                    }
                }
            } else {
                //垂直滑动
                position = currentPosition + 1
            }
            return position
        }

        /**
         * @param currentPosition 当前的位置
         * @param orientation     方向
         * @param rows            行数
         * @param columns         列数
         * @param state           状态
         * @return 上一个位置
         */
        fun getPrePosition(
            currentPosition: Int,
            orientation: Int,
            rows: Int,
            columns: Int,
        ): Int {
            val position: Int
            val onePageSize = rows * columns
            if (orientation == HORIZONTAL) {
                val surplus = currentPosition % onePageSize
                //水平滑动
                //向前追加item
                if (surplus == 0) {
                    //一页的第一个位置
                    position = currentPosition - 1
                } else {
                    //在第几行
                    val rowIndex = surplus / columns
                    //是否在第一行
                    val isFirstRow = rowIndex == 0
                    position = if (isFirstRow) {
                        currentPosition - 1 + (rows - 1) * columns
                    } else {
                        currentPosition - columns
                    }
                }
            } else {
                //垂直滑动
                position = currentPosition - 1
            }
            return position
        }

        companion object {
            const val LAYOUT_START: Int = -1

            const val LAYOUT_END: Int = 1


            const val SCROLLING_OFFSET_NaN: Int = Int.MIN_VALUE
        }
    }

    private class LayoutChunkResult {
        var mConsumed: Int = 0
        private var mFinished: Boolean = false
        private var mIgnoreConsumed: Boolean = false
        private var mFocusable: Boolean = false

        private fun resetInternal() {
            mConsumed = 0
            mFinished = false
            mIgnoreConsumed = false
            mFocusable = false
        }
    }

    /**
     * @see RecyclerView.LayoutManager.onSaveInstanceState
     * @see RecyclerView.LayoutManager.onRestoreInstanceState
     */
    @Parcelize
    private data class SavedState(
        val orientation: Int = 0,       // 滑动方向
        val rows: Int = 0,              // 行数
        val columns: Int = 0,           // 列数
        val currentPagerIndex: Int = NO_ITEM,   // 当前页码下标
        val reverseLayout: Boolean = false      // 是否反向布局
    ) : Parcelable

    interface PagerChangedListener {
        /**
         * 页面总数量变化
         *
         * @param pagerCount 页面总数，从1开始，为0时说明无数据，{[NO_PAGER_COUNT]}
         */
        fun onPagerCountChanged(pagerCount: Int)

        /**
         * 选中的页面下标
         *
         * @param prePagerIndex     上次的页码，当{[getItemCount]}为0时，为-1，{[NO_ITEM]}
         * @param currentPagerIndex 当前的页码，当{[getItemCount]}为0时，为-1，{[NO_ITEM]}
         */
        fun onPagerIndexSelected(
            prePagerIndex: Int,
            currentPagerIndex: Int
        )
    }
}
