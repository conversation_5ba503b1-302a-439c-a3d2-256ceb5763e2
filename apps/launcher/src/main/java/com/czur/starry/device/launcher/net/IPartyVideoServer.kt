package com.czur.starry.device.launcher.net

import com.czur.starry.device.baselib.network.core.MiaoHttpEntity
import com.czur.starry.device.baselib.network.core.MiaoHttpGet
import com.czur.starry.device.baselib.network.core.MiaoHttpParam
import com.czur.starry.device.launcher.data.bean.PartyVideoEntity
import com.google.gson.reflect.TypeToken
import java.lang.reflect.Type

/**
 * Created by 陈丰尧 on 2023/2/9
 */
interface IPartyVideoServer {
    /**
     * 获取党建文件列表接口
     * @param page:     第几页数据, 从1开始
     * @param pageSize: 每页多少条数据
     */
    @MiaoHttpGet("/api/starry/party/getFiles")
    fun getPartyFiles(
        @MiaoHttpParam("page")
        page: Int,
        @MiaoHttpParam("size")
        pageSize: Int,
        type: Type = object : TypeToken<PartyVideoEntity>() {}.type,
    ): MiaoHttpEntity<PartyVideoEntity>
}