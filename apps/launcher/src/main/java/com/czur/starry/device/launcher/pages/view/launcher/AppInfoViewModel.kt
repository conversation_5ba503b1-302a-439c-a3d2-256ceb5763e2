package com.czur.starry.device.launcher.pages.view.launcher

import android.app.Application
import android.content.BroadcastReceiver
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.ServiceConnection
import android.os.IBinder
import android.os.SystemClock
import androidx.appcompat.content.res.AppCompatResources
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asFlow
import androidx.lifecycle.map
import androidx.lifecycle.viewModelScope
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.appstore.IAppDownload
import com.czur.starry.device.appstore.IDownloadProcessCallback
import com.czur.starry.device.appstore.download.DownloadPublishInfo
import com.czur.starry.device.appstore.download.DownloadRequest
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.VersionIndustry
import com.czur.starry.device.baselib.utils.DifferentLiveData
import com.czur.starry.device.baselib.utils.ONE_MIN
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.SettingUtil
import com.czur.starry.device.baselib.utils.appContext
import com.czur.starry.device.baselib.utils.data.LiveDataDelegate
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.launcher.R
import com.czur.starry.device.launcher.data.bean.AppInfo
import com.czur.starry.device.launcher.data.bean.LaunchPadApp
import com.czur.starry.device.launcher.data.bean.NetMeetingApp
import com.czur.starry.device.launcher.guide.GuideUtil
import com.czur.starry.device.launcher.other.OtherMeetingQuickBootAppManager
import com.czur.starry.device.launcher.other.OtherStudyToolQuickBootAppManager
import com.czur.starry.device.launcher.utils.loadAppsInfo
import com.czur.starry.device.launcher.utils.uninstallApp
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.flowOn
import kotlin.math.ceil
import kotlin.math.max
import kotlin.math.min

/**
 * Created by 陈丰尧 on 2/20/21
 */


class AppInfoViewModel(application: Application) : AndroidViewModel(application) {
    companion object {
        private const val TAG = "AppInfoViewModel"
        private const val MIN_PAGE_COUNT = 2
        private const val APP_NUMBER_EACH_PAGE = 28
        private const val UNINSTALL_MODE_TIME = ONE_MIN

        // 下载服务的Service
        private const val ACTION_DOWNLOAD_SERVICE =
            "com.czur.starry.device.appstore.DownloadService"
    }

    /**
     * 正在卸载的应用
     */
    private val uninstallingPkg = mutableSetOf<String>()

    /**
     * 扫描系统获得的app信息
     */
    private val appsInfo: MutableLiveData<List<AppInfo>> = MutableLiveData(emptyList())

    /**
     * 要显示的app
     */
    val launchPadAppLive = MediatorLiveData<List<LaunchPadApp>>()


    // 至少显示2页
    val pageSize = appsInfo.map {
        // 计算PageSize
        val page = ceil(it.size.toDouble() / APP_NUMBER_EACH_PAGE).toInt() + 1
        max(page, MIN_PAGE_COUNT)
    }

    private val appChangeReceiver = AppInfoChangeReceiver()

    // 卸载模式
    val uninstallModeLive: LiveData<Boolean> = DifferentLiveData(false)
    var uninstallMode: Boolean by LiveDataDelegate(uninstallModeLive)
        private set


    /**
     * 其他视频会议
     */
    private val otherMeetingQuickBootAppManager by lazy {
        OtherMeetingQuickBootAppManager(appContext, viewModelScope, launchPadAppLive)
    }

    // 第三方视频会议列表
    val otherNetMeetingAppsLive: LiveData<List<NetMeetingApp>> by lazy {
        otherMeetingQuickBootAppManager.otherQuickBootAppsLive
    }
    val otherNetMeetingApps: List<NetMeetingApp>
        get() = otherMeetingQuickBootAppManager.otherQuickBootApps

    // 党建版本的
    private val otherStudyToolQuickBootAppManager by lazy {
        OtherStudyToolQuickBootAppManager(appContext, viewModelScope, launchPadAppLive)
    }

    val studyToolAppsLive: LiveData<List<NetMeetingApp>> by lazy {
        otherStudyToolQuickBootAppManager.otherQuickBootAppsLive
    }
    val studyToolApps: List<NetMeetingApp>
        get() = otherStudyToolQuickBootAppManager.otherQuickBootApps

    private var changeToNormalJob: Job? = null


    private var iAppDownload: IAppDownload? = null

    private val favAppPkgListFlow = MutableStateFlow(emptyList<String>())

    private val favAddInfo by lazy {
        AppInfo(
            "",
            "",
            AppCompatResources.getDrawable(appContext, R.drawable.ic_fav_app_add)!!,
            0L,
            false
        )
    }

    // 要显示的常用应用
    val favAppInfoListFlow =
        favAppPkgListFlow.combine(launchPadAppLive.asFlow()) { favPkgList, appList ->
            favPkgList.mapNotNull { pkgName ->
                appList.firstOrNull { app ->
                    app.appInfo.pkgName == pkgName
                }?.appInfo
            } + favAddInfo
        }.debounce(50).flowOn(Dispatchers.Default)

    private val cb = object : IDownloadProcessCallback.Stub() {
        override fun onInfoUpdate(publishInfos: MutableList<DownloadPublishInfo>?) {
            if (publishInfos.isNullOrEmpty()) {
                return
            }
            otherMeetingQuickBootAppManager.upgradeDownloadProcess(publishInfos)
            if (Constants.versionIndustry == VersionIndustry.PARTY_BUILDING) {
                otherStudyToolQuickBootAppManager.upgradeDownloadProcess(publishInfos)
            }
        }

        override fun onDownloadTerminate(pkgName: String?, reason: Int) {
            pkgName?.let {
                otherMeetingQuickBootAppManager.removeDownloadTermination(it)
                if (Constants.versionIndustry == VersionIndustry.PARTY_BUILDING) {
                    otherStudyToolQuickBootAppManager.removeDownloadTermination(it)
                }
            }
        }

    }

    private val conn = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            iAppDownload = IAppDownload.Stub.asInterface(service).apply {
                registerStatusCallback(cb)
            }
        }

        override fun onServiceDisconnected(name: ComponentName?) {
            iAppDownload = null
        }
    }

    init {
        // 注册广播
        registerAppReceiver()

        launchPadAppLive.addSource(uninstallModeLive) { mkShowApps() }
        launchPadAppLive.addSource(appsInfo) { mkShowApps() }

        launchPadAppLive.observeForever {
            // 本地应用改变了, 需要刷新一次数据
            otherMeetingQuickBootAppManager.update()
            if (Constants.versionIndustry == VersionIndustry.PARTY_BUILDING) {
                otherStudyToolQuickBootAppManager.update()
            }
        }

        otherMeetingQuickBootAppManager.initManager()
        if (Constants.versionIndustry == VersionIndustry.PARTY_BUILDING) {
            otherStudyToolQuickBootAppManager.initManager()
        }
        val needDelayTime = 15 * ONE_SECOND - SystemClock.elapsedRealtime()
        if (needDelayTime > 0) {
            launch {
                logTagI(TAG, "等待${needDelayTime}ms 后绑定AppStore Service")
                delay(needDelayTime)
                bindService()
            }
        } else {
            logTagI(TAG, "直接绑定AppStore Service")
            bindService()
        }
    }

    suspend fun loadFavAppPkgList() {
        logTagD(TAG, "加载收藏应用列表")
        favAppPkgListFlow.value = SettingUtil.PersonalizationSetting.getLauncherFavApps()
    }

    private fun bindService() {
        logTagD(TAG, "绑定应用市场Service")
        val intent = Intent(ACTION_DOWNLOAD_SERVICE).apply {
            setPackage("com.czur.starry.device.appstore")
        }
        appContext.bindService(intent, conn, Context.BIND_AUTO_CREATE)
    }

    private fun unBindService() {
        logTagD(TAG, "解绑应用市场Service")
        iAppDownload?.unregisterStatusCallback(cb)
        appContext.unbindService(conn)
    }

    private fun mkShowApps() {
        val apps = appsInfo.value ?: emptyList()
        val showApps = apps
            .filter {
                it.pkgName !in uninstallingPkg
            }
            .map {
                LaunchPadApp(it, uninstallMode)
            }
        launchPadAppLive.value = showApps
    }

    /**
     * 下载网络会议App
     */
    fun downloadNetMeetingApp(netApp: NetMeetingApp) {
        logTagV(TAG, "准备下载App:${netApp.appName}")
        otherMeetingQuickBootAppManager.saveRequestDownloadTime(netApp.pkgName)
        if (Constants.versionIndustry == VersionIndustry.PARTY_BUILDING) {
            otherStudyToolQuickBootAppManager.saveRequestDownloadTime(netApp.pkgName)
        }
        iAppDownload?.addDownloadReq(
            DownloadRequest(
                downloadUrl = netApp.downloadUrl,
                pkgName = netApp.pkgName,
                saveFileName = "",
                versionCode = netApp.netVersion,
                estimatedSize = netApp.downloadEstimatedSize
            )
        ) ?: kotlin.run {
            logTagW(TAG, "iAppDownload 为空!!")
        }
    }

    /**
     * 加载AppInfo
     */
    suspend fun loadApps() {
        val apps = loadAppsInfo()
        appsInfo.postValue(apps)
    }

    /**
     * 卸载指定应用
     */
    fun unInstallApp(appInfo: AppInfo) {
        refreshResetTime()  // 刷新无操作时间
        val unInstallPkg = appInfo.pkgName
        if (uninstallingPkg.contains(unInstallPkg)) {
            logTagD(TAG, "正在卸载中, 不重复卸载")
            return
        }
        uninstallingPkg.add(unInstallPkg)
        launch {
            mkShowApps() // 刷新
            uninstallApp(unInstallPkg)
        }
    }

    /**
     * 获取指定页面的AppInfo
     */
    fun getApps(page: Int): List<LaunchPadApp> {
        val allApps = launchPadAppLive.value ?: emptyList()
        val fromIndex = page * APP_NUMBER_EACH_PAGE
        val endIndex = min((page + 1) * APP_NUMBER_EACH_PAGE, allApps.size)
        return if (endIndex > fromIndex) {
            allApps.subList(page * APP_NUMBER_EACH_PAGE, endIndex)
        } else {
            emptyList()
        }
    }

    /**
     * 注册App安装/卸载/升级的广播
     */
    private fun registerAppReceiver() {
        logTagD(TAG, "注册App安装广播")
        val intentFilter = IntentFilter().apply {
            addAction(Intent.ACTION_PACKAGE_ADDED)      // 安装
            addAction(Intent.ACTION_PACKAGE_CHANGED)    // 升级
            addAction(Intent.ACTION_PACKAGE_REMOVED)    // 卸载
            addAction(Intent.ACTION_PACKAGE_REPLACED)   // 升级

            addDataScheme("package")
        }
        appContext.registerReceiver(appChangeReceiver, intentFilter)
    }

    /**
     * 切换到卸载模式
     */
    fun changeToUninstallMode() {
        uninstallMode = true
        refreshResetTime()

    }

    fun changeToNormalMode() {
        uninstallMode = false
        changeToNormalJob?.cancel()
        changeToNormalJob = null
    }

    /**
     * 刷新从卸载模式自动切换回普通模式的时间
     */
    fun refreshResetTime() {
        changeToNormalJob?.cancel()
        if (!uninstallMode) {
            logTagV(TAG, "当前已不是卸载模式, 不需要定时")
            return
        }
        changeToNormalJob = launch {
            delay(UNINSTALL_MODE_TIME)
            logTagI(TAG, "自动切换回正常模式")
            changeToNormalMode()
        }
    }

    override fun onCleared() {
        unBindService()
        appContext.unregisterReceiver(appChangeReceiver)
        super.onCleared()
    }

    inner class AppInfoChangeReceiver : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            if (intent == null) return
            when (intent.action) {
                Intent.ACTION_PACKAGE_REMOVED -> {
                    val pkgName = intent.data?.schemeSpecificPart ?: ""
                    logTagV(TAG, "卸载完成:${pkgName}")
                    uninstallingPkg.remove(pkgName)
                    if (pkgName in favAppPkgListFlow.value) {
                        logTagD(TAG, "卸载的是收藏应用, 刷新收藏应用列表")
                        favAppPkgListFlow.value = favAppPkgListFlow.value - pkgName
                        launch {
                            logTagD(TAG, "保存收藏应用列表到本地")
                            SettingUtil.PersonalizationSetting.setLauncherFavApps(favAppPkgListFlow.value)
                        }
                    }
                    launch {
                        GuideUtil.resetExtendTipsCount(pkgName)
                    }
                }
            }
            launch {
                loadApps()
            }
        }
    }

    private fun MutableStateFlow<Long>.update() {
        value = System.currentTimeMillis()
    }

}