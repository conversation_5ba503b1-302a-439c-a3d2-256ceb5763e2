package com.czur.starry.device.launcher.other

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.appstore.download.DownloadPublishInfo
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.StarryDevLocale
import com.czur.starry.device.baselib.utils.ONE_HOUR
import com.czur.starry.device.baselib.utils.ONE_MIN
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.data.LiveDataDelegate
import com.czur.starry.device.baselib.utils.has
import com.czur.starry.device.launcher.data.bean.LaunchPadApp
import com.czur.starry.device.launcher.data.bean.NetMeetingApp
import com.czur.starry.device.launcher.data.bean.OtherQuickBootAppItem
import com.czur.starry.device.launcher.meeting.NetMeetingAppStatus
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow

/**
 * Created by 陈丰尧 on 2023/2/6
 */

private const val NET_MEETING_APP_TAG = "VideoMeeting"
private const val NET_MEETING_APP_TAG_NA = "VideoMeetingNa"
private const val NET_STUDY_TOOL_APP_TAG = "StudyTool"

sealed class OtherQuickBootAppManager(
    private val appContext: Context,
    private val scope: CoroutineScope,
    private val launchPadAppLive: LiveData<List<LaunchPadApp>>,
    private val appStoreTag: String,
    private val defAppList: List<OtherQuickBootAppItem>
) {

    abstract val TAG: String

    /**
     * 其他应用管理
     */
    private val otherQuickBootAppRepository by lazy {
        OtherQuickBootAppRepository(context = appContext, appStoreTag, defAppList)
    }

    private val refreshNetAppFlow = MutableStateFlow(-1L)
    private var refreshNetAppJob: Job? = null

    // 第三方视频会议列表
    val otherQuickBootAppsLive: LiveData<List<NetMeetingApp>> = MutableLiveData(emptyList())
    var otherQuickBootApps by LiveDataDelegate(otherQuickBootAppsLive)
        private set

    fun initManager() {
        scope.launch {
            refreshNetAppFlow.collect {
                makeOtherMeetingApps()
                startRefreshNetApp()    // 每次更新都会添加下次更新任务
            }
        }
        startRefreshNetApp()
    }

    fun upgradeDownloadProcess(publishInfo: MutableList<DownloadPublishInfo>) {
        otherQuickBootAppRepository.upgradeDownloadProcess(publishInfo)
        update()
    }

    fun removeDownloadTermination(pkgName: String) {
        otherQuickBootAppRepository.removeDownloadTermination(pkgName)
        update()
    }

    fun saveRequestDownloadTime(pkgName: String) {
        otherQuickBootAppRepository.saveRequestDownloadTime(pkgName)
    }

    fun update() {
        refreshNetAppFlow.value = System.currentTimeMillis()
    }

    /**
     * 定时刷新网络应用
     */
    private fun startRefreshNetApp() {
        refreshNetAppJob?.cancel()
        refreshNetAppJob = scope.launch {
            val delayTime = getRefreshNetAppTime()
            logTagV(TAG, "下一次自动刷新时间:${delayTime / ONE_SECOND}s 后")
            delay(delayTime) // 每小时刷新一次
            logTagV(TAG, "刷新一次快速启动App")
            update()
        }
    }

    private suspend fun makeOtherMeetingApps() {
        withContext(Dispatchers.IO) {
            val installApps = launchPadAppLive.value
            // 不可能没有安装的app,排除Launcher刚启动, 还没准备好的情况
            if (installApps.isNullOrEmpty()) return@withContext

            val netApps = otherQuickBootAppRepository.getNetAppList()
            // 处理
            otherQuickBootApps = netApps.map {
                val installApp = installApps.find { launchPadApp ->
                    launchPadApp.appInfo.pkgName == it.packageName  // 包名相同
                }
                val status =
                    if (installApp == null) NetMeetingAppStatus.UNINSTALL else NetMeetingAppStatus.INSTALL
                val firstInstallTime = installApp?.appInfo?.installTime ?: Long.MAX_VALUE
                if (status == NetMeetingAppStatus.INSTALL) {
                    otherQuickBootAppRepository.removeDownloadTermination(it.packageName)
                }
                val downloadProcess = when (status) {
                    NetMeetingAppStatus.INSTALL -> -1
                    NetMeetingAppStatus.UNINSTALL -> otherQuickBootAppRepository.getDownloadProgress(
                        it.packageName
                    )
                }
                NetMeetingApp(
                    appName = installApp?.appInfo?.appName ?: it.name,
                    pkgName = it.packageName,
                    appIcon = installApp?.appInfo?.appIcon,
                    it.iconUrl,
                    status = status,
                    appID = it.id,
                    downloadUrl = it.downloadUrl,
                    downloadProcess = downloadProcess,
                    netVersion = it.versionCode,
                    downloadEstimatedSize = it.size,
                    firstInstallTime = firstInstallTime
                )
            }
                .sortedBy { it.firstInstallTime }   // sortedBy 是文档排序算法, 先排序副属性,再排序主属性
                .sortedBy {
                    it.status
                }
        }
    }

    /**
     * 根据不同条件更新网络的刷新时间
     */
    private fun getRefreshNetAppTime(): Long {
        return when {
            otherQuickBootApps.has { it.downloadProcess > 0 && it.status == NetMeetingAppStatus.UNINSTALL } -> ONE_SECOND   // 有正在下载的应用, 就1s更新一次
            !otherQuickBootAppRepository.hasCache() -> 2 * ONE_MIN    // 没有缓存, 2min更新一次
            else -> ONE_HOUR                                    // 正常情况下, 1小时更新一次
        }
    }

}

private val defAppItem = OtherQuickBootAppItem(
    id = 27L,
    name = "腾讯会议Rooms",
    iconUrl = "https://osscdn.czur.com/starry/app/com.tencent.wemeet.rooms/3.28.290.587/AndroidRooms_1410001533_3.28.290.587_czur_g.publish.apk",
    packageName = "com.tencent.wemeet.rooms",
    downloadUrl = "https://osscdn.czur.com/starry/app/com.tencent.wemeet.app/3.1.2.422/腾讯会议V3.1.2.422.apk",
    2023700907,
    227498733
)

// 默认网络App列表
private val defNetAppList = when (Constants.starryHWInfo.salesLocale) {
    StarryDevLocale.Mainland -> listOf(defAppItem)
    StarryDevLocale.Overseas -> emptyList()     // TODO 海外版还不知道默认有什么
}

private val netMeetingAppTag = when (Constants.starryHWInfo.salesLocale) {
    StarryDevLocale.Mainland -> NET_MEETING_APP_TAG
    StarryDevLocale.Overseas -> NET_MEETING_APP_TAG_NA
}

class OtherMeetingQuickBootAppManager(
    appContext: Context,
    scope: CoroutineScope,
    launchPadAppLive: LiveData<List<LaunchPadApp>>
) :
    OtherQuickBootAppManager(
        appContext,
        scope,
        launchPadAppLive,
        netMeetingAppTag, defNetAppList
    ) {
    override val TAG: String = "OtherMeetingQuickBootAppManager"
}

class OtherStudyToolQuickBootAppManager(
    appContext: Context,
    scope: CoroutineScope,
    launchPadAppLive: LiveData<List<LaunchPadApp>>
) :
    OtherQuickBootAppManager(
        appContext,
        scope,
        launchPadAppLive,
        NET_STUDY_TOOL_APP_TAG, emptyList()
    ) {
    override val TAG: String = "OtherStudyToolQuickBootAppManager"
}