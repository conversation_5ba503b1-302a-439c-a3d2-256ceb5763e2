package com.czur.starry.device.launcher.utils

import android.content.Context
import android.content.Intent
import androidx.fragment.app.Fragment
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.utils.ProcessName
import com.czur.starry.device.baselib.utils.doWithoutCatch
import com.czur.starry.device.baselib.utils.has
import com.czur.starry.device.baselib.utils.onSuspendMainScope
import com.czur.starry.device.launcher.app.App
import com.czur.starry.device.launcher.data.bean.AppInfo
import com.czur.starry.device.launcher.guide.GuideUtil
import kotlinx.coroutines.runBlocking

/**
 * Created by 陈丰尧 on 2021/9/24
 */
private const val TAG = "BootUtil"

object BootUtilValues {
    var peripheralUSBRunning = false    // 方便判断的
}

sealed class BootCheckResult {
    data object Success : BootCheckResult()
    data object ConflictWithMeeting : BootCheckResult()
    class ConflictWithMic(val useMicProcessList: List<ProcessName>) : BootCheckResult()
    data object HintGooglePlay: BootCheckResult()

}

suspend fun saveNeverRemindGooglePlayHint(){
    setDSValue("neverRemindGooglePlayHint", true)
}

private suspend fun getNeverRemindGooglePlayHint(): Boolean {
    return getDSValue("neverRemindGooglePlayHint", false)
}

fun checkBeforeBootApp(appInfo: AppInfo): BootCheckResult {
    val pkgName = appInfo.pkgName
    logTagD(TAG, "CheckBefore 启动app:$pkgName")
    if (needCheckMic(pkgName)) {
        logTagV(TAG, "开始检查mic占用情况")
        val processNames = getUseMicProcessName().filterNot { it.processName == pkgName }
        if (processNames.has { pkgName == it.processName }) {
            logTagI(TAG, "麦克风被占用，不允许启动${pkgName}")
            return BootCheckResult.ConflictWithMic(processNames)
        }
    }

    if (appInfo.pkgName == "com.android.vending") {
        val neverRemind = runBlocking {
            getNeverRemindGooglePlayHint()
        }
        // Google Play
        if (!neverRemind) {
            return BootCheckResult.HintGooglePlay
        }
    }

    return BootCheckResult.Success
}

fun bootApp(pkgName: String, className: String? = null, context: Context = App.context) {
    val intent = className?.let {
        mkClassIntent(pkgName, it)
    } ?: run {
        mkPkgIntent(pkgName)
    }
    intent?.let {
        it.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        doWithoutCatch {
            context.startActivity(it)
            onSuspendMainScope {
                if (!pkgName.contains("czur")) {
                    // 不是成者自己的应用
                    GuideUtil.startGuideWindow(
                        context,
                        pkgName
                    )
                    // 展示三方应用的Toast提示
                    GuideUtil.showExtendTips(context, pkgName)
                }
            }
        }
    }
}

/**
 * 通过Action启动Activity
 * Fragment扩展方法, context使用Fragment所依附的Activity
 */
fun Fragment.bootAppByAction(action: String, builder: Intent.() -> Unit = {}) =
    bootAppByAction(action, requireContext(), builder)

fun bootAppByAction(
    action: String,
    context: Context = App.context,
    builder: Intent.() -> Unit = {}
) {
    val intent = mkActionIntent(action, builder).apply {
        addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
    }
    doWithoutCatch {
        context.startActivity(intent)
    }
}

private fun mkClassIntent(pkgName: String, className: String) =
    Intent().apply {
        setClassName(pkgName, className)
    }

private fun mkPkgIntent(pkgName: String): Intent? =
    makeIntentByPkgManager(pkgName)

/**
 * 通过Action构建Intent
 * @param action:   Intent对应的Action
 * @param builder:  提供Intent的作用域来添加额外的参数
 */
private fun mkActionIntent(action: String, builder: Intent.() -> Unit = {}): Intent =
    Intent(action).apply(builder)
