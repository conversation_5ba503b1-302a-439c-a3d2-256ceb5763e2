<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:sharedUserId="android.uid.system"
    tools:ignore="ProtectedPermissions,ScopedStorage,ExportedService">

    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_UPDATES" />
    <uses-permission android:name="android.permission.DELETE_PACKAGES" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.SET_WALLPAPER" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <uses-permission android:name="android.permission.CONNECTIVITY_CHANGE" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
    <uses-permission android:name="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.REBOOT" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.ACCESS_CACHE_FILESYSTEM" />
    <uses-permission android:name="android.permission.WRITE_MEDIA_STORAGE" />
    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />
    <uses-permission android:name="android.permission.MANAGE_ACTIVITY_TASKS" />
    <uses-permission android:name="android.permission.REMOVE_TASKS" />
    <uses-permission android:name="android.permission.MANAGE_USB" />
    <permission android:name="com.czur.starry.contentProvider.rw.sp" />

    <application
        android:name=".app.App"
        android:label="@string/app_name"
        android:persistent="true"
        android:supportsRtl="true"
        android:theme="@style/AppTheme">
        <meta-data
            android:name="design_width_in_dp"
            android:value="1920" />
        <meta-data
            android:name="design_height_in_dp"
            android:value="1080" />

        <activity
            android:name=".pages.view.launcher.LauncherActivity"
            android:configChanges="fontScale|keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|navigation|layoutDirection|touchscreen"
            android:enabled="${LAUNCHER_ENABLE}"
            android:excludeFromRecents="true"
            android:exported="true"
            android:theme="@style/ActivityThemeBoot">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.HOME" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>


        <activity
            android:name=".pages.view.launcher.LauncherMainActivity"
            android:clearTaskOnLaunch="true"
            android:configChanges="fontScale|keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|navigation|layoutDirection|touchscreen|colorMode|density|fontScale"
            android:excludeFromRecents="true"
            android:launchMode="singleTask"
            android:theme="@style/ActivityThemeMain" />

        <activity
            android:name=".recent.view.RecentActivity"
            android:clearTaskOnLaunch="true"
            android:configChanges="fontScale|keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|navigation|layoutDirection|touchscreen"
            android:excludeFromRecents="true"
            android:exported="true"
            android:launchMode="singleInstance"
            android:theme="@style/ActivityRecentTheme">
            <intent-filter>
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <receiver
            android:name=".recent.view.RecentReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="com.czur.starry.device.launcher.BOOT_RECENT" />
            </intent-filter>
        </receiver>

        <provider
            android:name=".provider.SPProvider"
            android:authorities="com.czur.starry.launcher.spProvider"
            android:exported="true"
            android:permission="com.czur.starry.contentProvider.rw.sp" />

        <service
            android:name=".guide.ThirdPartGuideService"
            android:exported="true" />

        <service
            android:name=".pages.view.launcher.CheckProviderService"
            android:exported="true"
            android:process=":checkProvider" />

        <provider
            android:name=".notice.CZNotificationProvider"
            android:authorities="com.czur.starry.device.launcher.notice.CZNotificationProvider"
            android:exported="true" />
    </application>
</manifest>
