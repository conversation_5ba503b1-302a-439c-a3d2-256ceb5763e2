<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#FFF"
    android:clickable="true"
    android:focusable="true"
    tools:ignore="PxUsage">

    <com.czur.uilib.CZTitleBar
        android:id="@+id/titleBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:baselib_titlebar_color="#000"
        app:baselib_titlebar_title="@string/baselib_title_bar_back"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/logoImg"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="339px"
        android:src="@drawable/img_data_recovery_logo"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/recoveryTitleTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="40px"
        android:text="@string/str_data_recovery"
        android:textColor="#4B56A4"
        android:textSize="40px"
        android:textStyle="bold"
        android:visibility="invisible"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/logoImg" />

    <TextView
        android:id="@+id/hintTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="15px"
        android:text="@string/str_data_recovery_prepare_hint"
        android:textColor="#4B5875"
        android:textSize="36px"
        android:textStyle="normal"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/recoveryTitleTv" />


</androidx.constraintlayout.widget.ConstraintLayout>