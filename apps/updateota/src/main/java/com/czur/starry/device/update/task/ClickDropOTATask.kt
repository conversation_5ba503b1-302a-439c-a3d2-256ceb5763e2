package com.czur.starry.device.update.task

import com.czur.czurutils.encryption.md5
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.common.Constants.PATH_SDCARD_OTA_CLICK_DROP
import com.czur.starry.device.otalib.OTAHandler
import com.czur.starry.device.otalib.OTAHandler.allMeetingStatus
import com.czur.starry.device.settings.model.FWVersionModel
import com.czur.starry.device.update.utils.ClickDropOTAUtil
import kotlinx.coroutines.runBlocking
import java.io.File
import java.util.TimerTask
import java.util.concurrent.atomic.AtomicBoolean


class ClickDropOTATask : TimerTask() {
    companion object {
        const val TAG = "ClickDropOTATask"
        var isClickDropOTARunning = AtomicBoolean(false)
        var fwVersion: FWVersionModel? = null
        var downloadResult = false
    }

    override fun run() {
        //正在运行，视频会议
        if (isClickDropOTARunning.get() || allMeetingStatus) {
            return
        }

        runBlocking {
            fwVersion = ClickDropOTAUtil.checkVersionRequest()
        }

        if (fwVersion == null || fwVersion!!.update == 0) {
            return
        }
        logTagD(TAG, "=======fwVersion===" + fwVersion.toString())

        if (OTAHandler.currentClickDropVersion == fwVersion!!.version) {
            return
        }

        isClickDropOTARunning.set(true)

        val packageUrl = fwVersion!!.packageUrl
        val md5 = fwVersion!!.md5
        if (fwVersion!!.version != null && packageUrl != null && md5 != null) {
            val dir = File(PATH_SDCARD_OTA_CLICK_DROP)
            if (!dir.exists()) {
                dir.mkdirs()
            } else if (dir.isDirectory) {
                val files = dir.listFiles()
                if (files != null) {
                    for (file in files) {
                        val result = file.delete()
                        logTagD(TAG, "===旧升级包删除")
                    }
                }
            }

            logTagD(TAG, fwVersion.toString())
            val downloadTempPath = "$PATH_SDCARD_OTA_CLICK_DROP${fwVersion!!.version}.temp"
            val downloadFilePath = "$PATH_SDCARD_OTA_CLICK_DROP${fwVersion!!.version}.7z"

            logTagD(TAG, downloadFilePath)
            logTagD(TAG, packageUrl)
            runBlocking {
                downloadResult = ClickDropOTAUtil.downloadOTAFile(
                    downloadTempPath,
                    downloadFilePath,
                    fwVersion!!.packageUrl,
                    fwVersion!!.fileSize
                )
            }
            if (downloadResult) {
                logTagD(TAG, "OTA文件下载成功")
                val checkMD5Result = checkMD5(downloadFilePath, md5)
                if (checkMD5Result) {
                    logTagD(TAG, "OTA文件MD5相同")
                    OTAHandler.currentClickDropVersion = fwVersion!!.version
                } else {
                    logTagD(TAG, "OTA文件MD5不相同，删除文件")
                    File(downloadFilePath).delete()
                }
            } else {
                isClickDropOTARunning.set(false)
                logTagD(TAG, "OTA文件下载失败")
            }
        } else {
            logTagD(TAG, "不需要升级")
        }
        logTagD(TAG, "下载流程结束")
        isClickDropOTARunning.set(false)
    }

    private fun checkMD5(filePath: String, checkMD5: String): Boolean {
        val checkFile = File(filePath)
        if (!checkFile.exists()) {
            return false
        }
        val md5 = runBlocking { checkFile.md5() }
        return md5 == checkMD5
    }

}