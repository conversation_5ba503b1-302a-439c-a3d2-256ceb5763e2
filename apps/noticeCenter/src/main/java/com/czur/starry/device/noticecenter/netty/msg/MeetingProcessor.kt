package com.czur.starry.device.noticecenter.netty.msg

import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czer.starry.device.meetlib.*
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.notice.MsgType
import com.czur.starry.device.baselib.notice.MsgType.Companion.MEET
import com.czur.starry.device.baselib.notice.MsgType.Companion.MEETING_USER_UPDATE
import com.czur.starry.device.baselib.notice.MsgType.Companion.MEET_STOP
import com.czur.starry.device.baselib.notice.MsgType.Companion.MEET_USER_LIST
import com.czur.starry.device.baselib.notice.NoticeHandler
import com.czur.starry.device.baselib.notice.NoticeMsg
import com.czur.starry.device.baselib.utils.collection.LoopQueue
import com.czur.starry.device.noticecenter.netty.NettyClient
import com.czur.starry.device.noticecenter.netty.msg.BizBody.Companion.METHOD_CMD
import com.czur.starry.device.noticecenter.netty.msg.BizBody.Companion.METHOD_USER_LIST
import com.czur.starry.device.noticecenter.netty.msg.BizBody.Companion.METHOD_USER_UPDATE
import com.google.gson.Gson
import com.google.gson.JsonArray
import com.google.gson.JsonObject

/**
 * Created by 陈丰尧 on 4/21/21
 * 会议消息的处理器
 */
private val gson by lazy { Gson() }

/**
 * 已关闭的会议
 */
private val stopRoomNos = LoopQueue<String>(5)

/**
 * 处理 会议消息
 */
private const val TAG = "processMeetingMsg"
fun processMeetingMsg(msg: CZBizMsg, nettyClient: NettyClient) {
    when (msg.body.method) {
        METHOD_CMD -> processCMDNotice(msg, nettyClient)
        METHOD_USER_LIST -> processUserList(msg)
        METHOD_USER_UPDATE -> processUserUpdate(msg)
    }
}

/**
 * 处理单个用户更新事件
 */
private fun processUserUpdate(msg: CZBizMsg) {
    logTagD(TAG, "userUpdate Message")
    // 创建msgType
    val msgType = MsgType(MEET, MEETING_USER_UPDATE)
    val noticeMsg = NoticeMsg()

    val user = msg.body.reply!![MEETING_KEY_USER]

    if (user is JsonObject) {
        val roomNo = getRooNoFromUser(user)
        if (roomNo.isNotEmpty() && roomNo in stopRoomNos) {
            logTagI(TAG, "${roomNo}已经结束, 不再转发UserUpdate")
            return
        }
    }

    // 更新用户列表的消息是不需要同步会议状态的
    val jsonStr = gson.toJson(user)
    noticeMsg.put(MEETING_KEY_USER, jsonStr)
    NoticeHandler.sendMessage(msgType, noticeMsg)
}

/**
 * 处理用户列表更新事件
 */
private fun processUserList(msg: CZBizMsg) {
    logTagD(TAG, "userList Message")


    // 创建msgType
    val msgType = MsgType(MEET, MEET_USER_LIST)
    val noticeMsg = NoticeMsg()

    val users = msg.body.reply!![MEETING_KEY_USERS]
    if (users is JsonArray) {
        val roomNo = getRoomNoFromUsers(users)
        if (roomNo.isNotEmpty() && roomNo in stopRoomNos) {
            logTagI(TAG, "${roomNo}已经结束, 不再转发UserList")
            return
        }
    }
    val jsonStr = gson.toJson(users)

    val metadata = msg.body.reply?.get(MEETING_KEY_META_DATA)?.asJsonObject
    val locked = metadata?.get(MEETING_KEY_LOCKED)?.asBoolean ?: false
    val record = metadata?.get(MEETING_KEY_IS_RECORD)?.asBoolean ?: false
    val sbRecording = metadata?.get(MEETING_KEY_SB_RECORDING)?.asBoolean ?: false
    val recordingJsonArray = metadata?.get(MEETING_RECORDING_LIST)?.asJsonArray ?: JsonArray()
    // 转换成字符串
    val recordingListStr =
        recordingJsonArray.toString().removeSurrounding("[", "]").replace("\"", "")
    val meetingCode = metadata?.get(MEETING_KEY_MEETING_CODE)?.asString ?: ""
    val meetingPwd = metadata?.get(MEETING_KEY_MEETING_PWD)?.asString ?: ""
    val meetingName = metadata?.get(MEETING_KEY_MEETING_NAME)?.asString ?: " "// 会议主题不能是空字符串


    if (meetingCode.isEmpty()) {
        logTagW(TAG, "服务器数据异常!!, 忽略该消息", jsonStr)
        return
    }

    noticeMsg.put(MEETING_KEY_LOCKED, locked)           // 向Msg中添加Lock信息
    noticeMsg.put(MEETING_KEY_IS_RECORD, record)        // 向msg中添加是否录音信息
    noticeMsg.put(MEETING_KEY_SB_RECORDING, sbRecording) // 向msg中添加会议中是否有人在录音
    noticeMsg.put(MEETING_RECORDING_LIST, recordingListStr) // 向msg中添加录音人的列表
    noticeMsg.put(MEETING_KEY_MEETING_CODE, meetingCode)// 向msg中添加会议号信息
    noticeMsg.put(MEETING_KEY_MEETING_PWD, meetingPwd)  // 向msg中添加密码信息
    noticeMsg.put(MEETING_KEY_MEETING_NAME, meetingName)// 向msg中添加会议主题

    noticeMsg.put(MEETING_KEY_USERS, jsonStr)

    NoticeHandler.sendMessage(msgType, noticeMsg)
}

private fun getRoomNoFromUsers(users: JsonArray): String {
    if (users.size() > 0) {
        return getRooNoFromUser(users[0].asJsonObject)
    }
    return ""
}

private fun getRooNoFromUser(user: JsonObject): String {
    return user.get("meetingId").asString
}

/**
 * 处理服务器来的CMD消息
 */
private fun processCMDNotice(msg: CZBizMsg, nettyClient: NettyClient) {
    logTagD(TAG, "CMD Message")
    val cmd = msg.body.reply?.get(METHOD_CMD)?.asString
    logTagD(TAG, "CMD: $cmd")
    val msgType = when (cmd) {
        // 无论是被移除会议,还是会议结束, 实际上都是关闭会议模块
        // 所以整合成一个消息
        MeetingCMD.REMOVE.cmd -> {
            MsgType(MEET, MEET_STOP)
        }
        MeetingCMD.STOP.cmd, MeetingCMD.STOP_FORCE.cmd -> {
            msg.body.get<String>("room")?.let {
                stopRoomNos.add(it)
            }
            MsgType(MEET, MEET_STOP)
        }
        else -> MsgType(MEET, MsgType.MEET_CMD_FROM_REMOTE)
    }
    logTagD(TAG, "msgType:${msgType}")
    if (msgType.match(MEET, MEET_STOP)) {
        logTagD(TAG, "发送会议停止消息")
        NoticeHandler.sendMessage(msgType) {
            // 添加会议室号, 防止关闭错误的会议
            put(MEETING_KEY_ROOM, msg.body.get<String>(MEETING_KEY_ROOM) ?: "")
            cmd?.let {
                // 添加会议停止原因
                put(MEETING_STOP_REASON, cmd)
            }
        }
    } else {
        cmd?.let {
            logTagD(TAG, "发送CMD消息:${cmd}")
            NoticeHandler.sendMessage(msgType) {
                put(MEETING_KEY_CMD, cmd)// 发送指令

                if (cmd == MeetingCMD.OTHER_MEETING_JOIN.cmd) {
                    // 添加会议室号, 防止关闭错误的会议
                    put(MEETING_KEY_ROOM, msg.body.get<String>(MEETING_KEY_ROOM) ?: "")
                }
            }
        }
    }
}

fun sendMeetingClose(room: String, reason: String? = null) {
    NoticeHandler.sendMessage(MsgType(MEET, MEET_STOP)) {
        // 添加会议室号, 防止关闭错误的会议
        put(MEETING_KEY_ROOM, room)
        reason?.let {
            put(MEETING_STOP_REASON, it)
        }
    }
}