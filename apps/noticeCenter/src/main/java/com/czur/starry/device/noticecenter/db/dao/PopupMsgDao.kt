package com.czur.starry.device.noticecenter.db.dao

import android.adservices.adid.AdId
import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.czur.starry.device.noticecenter.db.entity.PopupMsgEntity

/**
 * Created by 陈丰尧 on 2024/8/12
 */
@Dao
abstract class PopupMsgDao {

    @Insert(onConflict = OnConflictStrategy.IGNORE)
    abstract suspend fun insertPopupMsg(msgList: List<PopupMsgEntity>)

    @Query("select * from tab_popup_msg")
    abstract suspend fun getAllPopupMsg(): List<PopupMsgEntity>

    @Delete
    abstract suspend fun delPopupMsgList(msgList: List<PopupMsgEntity>)

    @Query("select * from tab_popup_msg where disable = 0 and startTime <= :time and endTime >= :time order by startTime desc limit 1")
    abstract suspend fun getPopupMsgByTime(time: Long): PopupMsgEntity?

    @Query("update tab_popup_msg set disable = 1 where serverId = :serverId")
    abstract suspend fun disablePopupServerId(serverId: Long)

}