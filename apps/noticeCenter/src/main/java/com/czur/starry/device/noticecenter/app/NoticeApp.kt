package com.czur.starry.device.noticecenter.app

import android.content.Context
import com.czur.starry.device.baselib.base.listener.StarryApp
import kotlin.properties.Delegates

/**
 * Created by 陈丰尧 on 4/7/21
 */
private const val TAG = "NoticeApp"

class NoticeApp : StarryApp() {
    companion object {
        var context: Context by Delegates.notNull()
    }

    override fun onCreate() {
        super.onCreate()

        context = this
    }
}