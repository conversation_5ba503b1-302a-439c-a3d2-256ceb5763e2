package com.czur.starry.device.noticecenter.notice.cloudconfig

import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.utils.SettingUtil
import com.czur.starry.device.noticecenter.entity.AppCloudConfigEntity

/**
 * Created by 陈丰尧 on 2023/11/28
 */
interface AppConfigHandler {
    suspend fun onAppConfigChanged(appConfig: AppCloudConfigEntity)
}

class WritePadAppConfigHandler : AppConfigHandler {
    private companion object {
        private const val KEY_ENABLE = "enable"
    }

    override suspend fun onAppConfigChanged(appConfig: AppCloudConfigEntity) {
        appConfig.onEachConfig { key, value ->
            when (key) {
                KEY_ENABLE -> {
                    val enable = value as Boolean
                    logTagV("WritePadAppConfigHandler", "手写板功能是否启用: $enable")
                    SettingUtil.WritePadSetting.setEnable(enable)
                }
            }

        }
    }
}