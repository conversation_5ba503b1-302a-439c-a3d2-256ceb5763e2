package com.czur.starry.device.noticecenter.db

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import com.czur.czurutils.global.globalAppCtx
import com.czur.starry.device.noticecenter.db.dao.PopupMsgDao
import com.czur.starry.device.noticecenter.db.entity.PopupMsgEntity

/**
 * Created by 陈丰尧 on 2024/8/12
 */
@Database(
    entities = [PopupMsgEntity::class],
    version = 2
)
abstract class NoticeCenterDataBase : RoomDatabase() {
    companion object {
        val instance = Single.sin
    }

    abstract fun popupMsgDao(): PopupMsgDao

    private object Single {
        val sin: NoticeCenterDataBase = Room.databaseBuilder(
            globalAppCtx,
            NoticeCenterDataBase::class.java,
            "NoticeCenter.db"
        )
            .fallbackToDestructiveMigration()   // 之前应用没有启用, 所以直接删除历史数据
            .build()
    }
}