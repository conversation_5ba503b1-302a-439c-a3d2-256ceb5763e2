package com.czur.starry.device.noticecenter.notice.cloudconfig

import android.os.SystemClock
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.network.HttpManager
import com.czur.starry.device.baselib.utils.ONE_HOUR
import com.czur.starry.device.noticecenter.entity.CloudConfigEntity
import com.czur.starry.device.noticecenter.net.ICloudConfigureService
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Created by 陈丰尧 on 2023/11/27
 */
const val REFRESH_CONFIG_INTERVAL = ONE_HOUR

private const val TAG = "CloudConfigManager"

class CloudConfigManager {
    private val cloudConfigureService by lazy { HttpManager.getService(ICloudConfigureService::class.java) }

    private var lastRefreshSuccessTime = -REFRESH_CONFIG_INTERVAL    // 上次刷新成功的时间

    private val configHandlers = mapOf(
        "com.czur.starry.device.starrypad" to WritePadAppConfigHandler()
    )

    suspend fun refreshCloudConfig() = withContext(Dispatchers.IO) {
        if (SystemClock.elapsedRealtime() - lastRefreshSuccessTime < REFRESH_CONFIG_INTERVAL) {
            logTagV(
                TAG,
                "刷新云端配置文件, 但是上次刷新成功的时间还没有超过${REFRESH_CONFIG_INTERVAL}ms, 不刷新"
            )
            return@withContext
        }

        logTagV(TAG, "刷新云端配置文件")

        val config = cloudConfigureService.getCloudConfig()
        if (config.isSuccess) {
            lastRefreshSuccessTime = SystemClock.elapsedRealtime()
            logTagV(TAG, "刷新云端配置文件成功")
            saveCloudConfig(config.body)
        } else {
            logTagV(TAG, "刷新云端配置文件失败")
        }
    }

    private suspend fun saveCloudConfig(config: CloudConfigEntity) = withContext(Dispatchers.IO) {
        config.appEntities.forEach {
            configHandlers[it.pkgName]?.onAppConfigChanged(it)
        }
    }
}