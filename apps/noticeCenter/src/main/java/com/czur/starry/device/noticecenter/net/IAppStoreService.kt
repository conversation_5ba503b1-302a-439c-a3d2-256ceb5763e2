package com.czur.starry.device.noticecenter.net

import com.czur.starry.device.baselib.network.core.MiaoHttpEntity
import com.czur.starry.device.baselib.network.core.MiaoHttpGet
import com.czur.starry.device.baselib.network.core.MiaoHttpParam
import com.google.gson.reflect.TypeToken
import java.lang.reflect.Type

/**
 * Created by 陈丰尧 on 2025/4/14
 */
interface IAppStoreService {
    /**
     * 获取应用市场的应用列表
     * @param region 1:国内 0:海外
     */
    @MiaoHttpGet("/api/app/store/app/whitelist")
    fun getInstallWhiteList(
        @MiaoHttpParam("region")
        region: Int,
        token: Type = object : TypeToken<List<String>>() {}.type
    ): MiaoHttpEntity<String>
}