<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="PxUsage">

    <style name="user_default_dialog" parent="@android:style/Theme.Dialog">
        <item name="android:windowFrame">@null</item>
        <!-- 边框 -->
        <item name="android:windowIsFloating">true</item>
        <!-- 是否浮现在activity之上 -->
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:padding">0px</item>
        <!-- 半透明 -->
        <item name="android:windowNoTitle">true</item>
        <!-- 无标题 -->
        <item name="android:background">@color/transparent</item>
        <!-- 背景色 -->
        <item name="android:windowBackground">@color/transparent</item>
        <!-- 背景透明 -->
        <item name="android:backgroundDimEnabled">true</item>
        <!-- 模糊 -->
        <item name="android:windowFullscreen">false</item>
        <!-- 全屏 -->
    </style>
    <!--    从中间弹出:-->
    <style name="AnimCenter" parent="@android:style/Animation.Dialog">
        <item name="android:windowEnterAnimation">@anim/dialog_center_in</item>
        <item name="android:windowExitAnimation">@anim/dialog_center_out</item>
    </style>

</resources>