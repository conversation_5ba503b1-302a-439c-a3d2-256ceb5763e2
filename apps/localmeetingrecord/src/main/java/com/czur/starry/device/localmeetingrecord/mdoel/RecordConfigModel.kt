package com.czur.starry.device.localmeetingrecord.mdoel

import android.hardware.display.VirtualDisplay
import com.czur.starry.device.localmeetingrecord.Config
import com.czur.starry.device.localmeetingrecord.RecordModel
import com.czur.starry.device.localmeetingrecord.monitor.audio.AudioMixer

data class RecordConfigModel(
    var recordMode: RecordModel = RecordModel.VIDEO_MODE,// 录制类型 video or
    var enableTimeWaterMark: Boolean = true // 是否开启水印

    , var virtualDisplay: VirtualDisplay? = null // 录制屏幕时的虚拟屏幕
    , var recordAudio: Boolean = true // 录制屏幕时 是否录制麦克风
    , var recordSubmixAudio: Boolean = true// 录制屏幕时 是否录制系统声音
    , var recordCamera: Boolean = true // 录制屏幕时 是否录制摄像头
    , var recordWidth: Int = Config.SRC_VIDEO_WIDTH_720,
    var recordHeight: Int = Config.SRC_VIDEO_HEIGHT_720
) {
    val audioMixerSource:Int by lazy {
        var audioSource = 0
        if (recordAudio) {
            audioSource = audioSource or AudioMixer.AUDIO_SOURCE_MIC
        }
        if (recordSubmixAudio) {
            audioSource = audioSource or AudioMixer.AUDIO_SOURCE_SUBMIX
        }
        audioSource
    }
}