package com.czur.starry.device.localmeetingrecord.widget

import android.animation.ObjectAnimator
import android.animation.ValueAnimator.INFINITE
import android.animation.ValueAnimator.REVERSE
import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatImageView
import com.czur.starry.device.baselib.utils.ONE_SECOND

/**
 * Created by 陈丰尧 on 2022/8/8
 */
class RecordView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : AppCompatImageView(context, attrs, defStyleAttr) {
    companion object {
        private const val RECORD_COLOR = 0xFFFC0D0D.toInt()
        private const val POINT_MAX_ALPHA = 255
        private const val POINT_MIN_ALPHA = (POINT_MAX_ALPHA * 0.5F).toInt()
        private const val ANIM_DURATION = 2 * ONE_SECOND
    }

    // 是否开启录像动画
    var recording: Boolean = false
        set(value) {
            if (field == value) return
            field = value
            if (value) {
                startAnim()
            } else {
                stopAnim()
            }
        }


    // 中心点的最大值
    private val pointMaxRadius: Float by lazy {
        width / 2.4F / 2F
    }

    // 中心点的最小值
    private val pointMinRadius: Float by lazy {
        pointMaxRadius * 0.8F
    }
    private var pointRadius: Float = 0F

    private var pointAlpha = POINT_MAX_ALPHA

    private var anim: ObjectAnimator? = null

    // 动画进度
    private var animProcess = 0F
        set(value) {
            if (field == value) return
            field = value
            pointRadius = pointMaxRadius - (pointMaxRadius - pointMinRadius) * value
            pointAlpha = POINT_MAX_ALPHA - ((POINT_MAX_ALPHA - POINT_MIN_ALPHA) * value).toInt()
            invalidate()
        }


    private val paint: Paint by lazy {
        Paint().apply {
            isAntiAlias = true
            style = Paint.Style.FILL_AND_STROKE
            color = RECORD_COLOR
        }
    }

    /**
     * 开始动画
     */
    private fun startAnim() {
        anim =
            ObjectAnimator.ofFloat(this, "animProcess", 0F, 1F).apply {
                duration = ANIM_DURATION
                repeatCount = INFINITE
                repeatMode = REVERSE
                start()
            }
    }

    private fun stopAnim() {
        anim?.cancel()
        anim = null

        animProcess = 0F
    }
}