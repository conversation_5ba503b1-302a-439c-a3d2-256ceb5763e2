package com.czur.starry.device.localmeetingrecord.monitor.audio

import android.media.MediaRecorder
import com.czur.czurutils.log.logTagI
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.channels.Channel.Factory.UNLIMITED

/**
 * Created by 陈丰尧 on 2023/7/17
 * 混音器
 */
class AudioMixer(
    private val sourceType: Int,
    private val mixerType: AudioMixerType
) {
    companion object {
        private const val TAG = "AudioMixer"

        const val AUDIO_SOURCE_MIC = MediaRecorder.AudioSource.MIC
        const val AUDIO_SOURCE_SUBMIX = MediaRecorder.AudioSource.REMOTE_SUBMIX
    }

    init {
        logTagI(TAG, "init mixerType: $sourceType - $mixerType")
    }

    private val channelMap = mutableMapOf(
        AUDIO_SOURCE_MIC to Channel<ByteArray>(capacity = UNLIMITED),
        AUDIO_SOURCE_SUBMIX to Channel(capacity = UNLIMITED)
    )

    private val micChannel
        get() = channelMap[AUDIO_SOURCE_MIC]!!
    private val submixChannel
        get() = channelMap[AUDIO_SOURCE_SUBMIX]!!

    private val useMicSource = sourceType and AUDIO_SOURCE_MIC != 0
    private val useSubmixSource = sourceType and AUDIO_SOURCE_SUBMIX != 0


    /**
     *  添加音频数据
     */
    fun addAudioFrameData(data: ByteArray, source: Int) {
        val channel = channelMap[source] ?: return
        channel.trySend(data)
    }

    fun clearAudioFrameData() {
        micChannel.close()
        submixChannel.close()
        channelMap.clear()
        channelMap[AUDIO_SOURCE_MIC] = Channel<ByteArray>(capacity = UNLIMITED)
        channelMap[AUDIO_SOURCE_SUBMIX] = Channel(capacity = UNLIMITED)
    }

    suspend fun getAudioFrameData(): ByteArray {
        val micData = if (useMicSource && !micChannel.isClosedForReceive) {
            micChannel.receive()
        } else {
            null
        }

        val submixData = if (useSubmixSource && !submixChannel.isClosedForReceive) {
            submixChannel.receive()
        } else {
            null
        }


        return mixerType.mixAudioData(micData, submixData) ?: ByteArray(0)
    }


}