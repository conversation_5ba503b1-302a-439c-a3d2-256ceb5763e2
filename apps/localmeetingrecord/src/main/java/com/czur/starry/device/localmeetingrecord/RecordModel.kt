package com.czur.starry.device.localmeetingrecord

import com.czur.starry.device.baselib.utils.getString

/**
 * Created by 陈丰尧 on 2022/8/6
 * number 请保持和实际位置一致
 */
enum class RecordModel(val recordModeName: String, val number: Int) {
    AUDIO_MODE(getString(R.string.str_function_recorder), 0),
    SCREEN_MODE(getString(R.string.str_function_screen_recorder), 1),
    VIDEO_MODE(getString(R.string.str_function_video), 2),
    CAMERA_MODE(getString(R.string.str_function_photo), 3);
}