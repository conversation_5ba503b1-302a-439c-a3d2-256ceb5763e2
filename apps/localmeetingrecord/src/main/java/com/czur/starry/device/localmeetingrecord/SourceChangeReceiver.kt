package com.czur.starry.device.localmeetingrecord

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import com.czur.starry.device.baselib.common.ACTION_BROADCAST_SOURCE_CHANGE
import com.czur.starry.device.baselib.common.KEY_SOURCE_CHANGE

class SourceChangeReceiver(private val sourceChangeListener:(source:String) -> Unit):BroadcastReceiver() {
    override fun onReceive(context: Context?, intent: Intent?) {
        if (intent?.action == ACTION_BROADCAST_SOURCE_CHANGE) {
            val source = intent.extras?.getString(KEY_SOURCE_CHANGE) ?: return
            sourceChangeListener(source)
        }
    }

    fun register(context: Context){
        val filter = IntentFilter(ACTION_BROADCAST_SOURCE_CHANGE)
        context.registerReceiver(this,filter)
    }

    fun unRegister(context: Context){
        context.unregisterR<PERSON>eiver(this)
    }

}