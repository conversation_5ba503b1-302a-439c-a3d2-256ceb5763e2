package com.czur.starry.device.localmeetingrecord.utils

import com.czur.czurutils.log.logTagW
import android.os.Environment
import android.os.StatFs
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.utils.getTimeStr

import com.czur.starry.device.localmeetingrecord.Config
import com.czur.starry.device.localmeetingrecord.Config.SIZE_ENOUGH_REC
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream

/**
 * Created by 陈丰尧 on 2022/8/9
 */

private const val TAG = "FileUtil"

// 录像文件夹
 val videoFolder: File by lazy {
    val sdCardPath = Environment.getExternalStorageDirectory().path
    File(sdCardPath, Config.VIDEO_FILE_FOLDER_NAME).apply {
        if (!exists()) {
            mkdirs()
        }
    }
}

/**
 * 生成新的录音音频地址
 * 文件格式: 年月日(序号).mp4  中途删除了, 则补充该序号
 */
suspend fun getNewAudioFile(): File = withContext(Dispatchers.IO) {

    val fileName = getTimeStr("yyyyMMdd")
    var partIndex = 1

    fun generateAudioFileName(): String =
        "$fileName(${partIndex.toString().padStart(2, '0')})${Config.AUDIO_EXTENSION}"

    fun generateVideoFileName(): String =
        "$fileName(${partIndex.toString().padStart(2, '0')})${Config.VIDEO_EXTENSION}"

    var audioFile = File(videoFolder, generateAudioFileName())
    var videoFile = File(videoFolder, generateVideoFileName())
    while (audioFile.exists() || videoFile.exists()) {
        partIndex++
        logTagW(TAG, "录像文件:${audioFile.absolutePath}存在!!")
        audioFile = File(videoFolder, generateAudioFileName())
        videoFile = File(videoFolder, generateVideoFileName())
    }
    audioFile
}

/**
 * 生成新的录像地址
 * 文件格式: 年月日(序号).mp4  中途删除了, 则补充该序号
 */
suspend fun getNewVideoFile(): File = withContext(Dispatchers.IO) {

    val fileName = getTimeStr("yyyyMMdd")
    var partIndex = 1

    fun generateAudioFileName(): String =
        "$fileName(${partIndex.toString().padStart(2, '0')})${Config.AUDIO_EXTENSION}"

    fun generateVideoFileName(): String =
        "$fileName(${partIndex.toString().padStart(2, '0')})${Config.VIDEO_EXTENSION}"

    var audioFile = File(videoFolder, generateAudioFileName())
    var videoFile = File(videoFolder, generateVideoFileName())
    while (videoFile.exists() || audioFile.exists()) {
        partIndex++
        logTagW(TAG, "录像文件:${videoFile.absolutePath}存在!!")
        audioFile = File(videoFolder, generateAudioFileName())
        videoFile = File(videoFolder, generateVideoFileName())
    }
    videoFile
}

// 确定最终可以使用的文件名称
fun determineFileName(path: String): String {
    var file = File(path)
    var newPath = path;
    var partIndex = 1
    val fileName = file.name.substringBeforeLast(".")
    val fileExtension = file.extension
    val name = fileName.substringBeforeLast("(")

    while (file.exists()) {
        newPath = "${file.parent}/$name(${partIndex.toString().padStart(2, '0')}).$fileExtension"
        file = File(newPath)
        partIndex++
    }

    return newPath
}

/**
 * 获取当前的可用空间
 */
suspend fun getFreeSize(): Long = withContext(Dispatchers.IO) {
    //    logTagI(TAG, "freeSize:${videoFolder.freeSpace.toSizeStr()}")
//    videoFolder.freeSpace // 这个方法获取的是可用空间, 相比王浩的方法会空间更大

    var mDataFileStats: StatFs? = null // 这个方法和王浩的方法一样,相比空间会更小

    mDataFileStats = StatFs(Environment.getDataDirectory().getAbsolutePath());
    val mFreeMem = mDataFileStats!!.getAvailableBlocks().toLong() *
            mDataFileStats!!.getBlockSize()
    logTagD(TAG, "=====mFreeMem=" + mFreeMem / 1000 / 1000)

    mFreeMem
}

/**
 * 是否有足够的存储空间
 */
suspend fun hasEnoughSpace(): Boolean = getSafeSpace() > 0

/**
 * 获取不需要提醒的安全空间
 */
suspend fun getSafeSpace(): Long = getFreeSize() - SIZE_ENOUGH_REC


/*
*向本地文件中写入数据
 */
fun writeTestFile(data: String) {

// 假设你有一个文件路径
    val filePath = videoFolder.absolutePath + "/test.txt"
    //写入当前时间 转换成 时分秒毫秒的格式
    var currentTime = getTimeStr("HH:mm:ss.SSS")
    currentTime += "   "
    // 写入数据
    try {
        // 创建一个 FileOutputStream 对象，第二个参数 true 表示追加模式
        val fos = FileOutputStream(filePath, true)
        // 将数据写入文件
//        fos.write(currentTime.toByteArray())
        fos.write(("$currentTime  $data\n").toByteArray())
        // 刷新缓冲区，确保数据被实际写入文件
        fos.flush()
        // 关闭文件输出流
        fos.close()
    } catch (e: Exception) {
        // 捕获异常并打印堆栈跟踪
        e.printStackTrace()
    }
}
