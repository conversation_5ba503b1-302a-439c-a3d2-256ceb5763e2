<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:ignore="PxUsage,RtlHardcoded,ContentDescription">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/background"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_bg_color"
        android:visibility="invisible">

        <com.czur.starry.device.localmeetingrecord.widget.SoundWaveView
            android:id="@+id/waveView"
            android:layout_width="match_parent"
            android:layout_height="300px"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.czur.starry.device.localmeetingrecord.widget.CameraView
            android:id="@+id/surfaceView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_centerInParent="true"
            app:cameraDisplayMode="texture" />


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/hudGroup"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.helper.widget.Flow
                android:layout_width="150px"
                android:layout_height="wrap_content"
                android:layout_marginEnd="30px"
                android:layout_marginBottom="100px"
                android:orientation="vertical"
                app:constraint_referenced_ids="flipTheLensTv,timeWaterMarkTv"
                app:flow_verticalGap="30px"
                app:flow_verticalStyle="packed"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toRightOf="parent" />

            <androidx.constraintlayout.helper.widget.Layer
                android:id="@+id/timeWaterMarkLayer"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:padding="10px"
                app:bl_corners_radius="30px"
                app:bl_solid_color="#80000000"
                app:constraint_referenced_ids="timeWaterMarkCb,timeWaterMarkTv" />

            <com.czur.uilib.choose.CZImageCheckBox
                android:id="@+id/timeWaterMarkCb"
                android:layout_width="40px"
                android:layout_height="40px"
                app:checked="false"
                app:checkedImg="@drawable/ic_time_watermark_check"
                app:layout_constraintBottom_toBottomOf="@+id/timeWaterMarkTv"
                app:layout_constraintRight_toLeftOf="@+id/timeWaterMarkTv"
                app:layout_constraintTop_toTopOf="@+id/timeWaterMarkTv"
                app:unCheckedImg="@drawable/ic_time_watermark_uncheck"
                app:withClickIDs="timeWaterMarkTv" />

            <TextView
                android:id="@+id/timeWaterMarkTv"
                style="@style/TVMainRightOtpItem"
                android:text="@string/str_time_watermark" />

            <androidx.constraintlayout.helper.widget.Layer
                android:id="@+id/flipTheLensLayer"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:padding="10px"
                app:bl_corners_radius="30px"
                app:bl_solid_color="#80000000"
                app:constraint_referenced_ids="flipTheLensCb,flipTheLensTv" />

            <com.czur.uilib.choose.CZImageCheckBox
                android:id="@+id/flipTheLensCb"
                android:layout_width="40px"
                android:layout_height="40px"
                app:checked="true"
                app:checkedImg="@drawable/ic_time_watermark_check"
                app:layout_constraintBottom_toBottomOf="@+id/flipTheLensTv"
                app:layout_constraintRight_toLeftOf="@+id/flipTheLensTv"
                app:layout_constraintTop_toTopOf="@+id/flipTheLensTv"
                app:unCheckedImg="@drawable/ic_time_watermark_uncheck"
                app:withClickIDs="flipTheLensTv" />

            <TextView
                android:id="@+id/flipTheLensTv"
                style="@style/TVMainRightOtpItem"
                android:text="@string/str_flip_lens" />


            <androidx.constraintlayout.widget.Group
                android:id="@+id/timeWaterMarkGroup"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:constraint_referenced_ids="timeWaterMarkCb,timeWaterMarkTv,timeWaterMarkLayer" />

            <androidx.constraintlayout.widget.Group
                android:id="@+id/flipTheLensGroup"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:constraint_referenced_ids="flipTheLensCb,flipTheLensTv,flipTheLensLayer" />

            <com.czur.starry.device.localmeetingrecord.widget.RecordView
                android:id="@+id/startOrStopIv"
                android:layout_width="120px"
                android:layout_height="120px"
                android:layout_marginBottom="35px"
                android:src="@drawable/ic_start_record"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent" />

            <TextView
                android:id="@+id/recordDurationTv"
                android:layout_width="200px"
                android:layout_height="60px"
                android:layout_marginRight="80px"
                android:gravity="center"
                android:textColor="@color/white"
                android:textSize="24px"
                android:textStyle="bold"
                app:bl_corners_radius="30px"
                app:bl_solid_color="#80000000"
                app:layout_constraintBottom_toBottomOf="@id/startOrStopIv"
                app:layout_constraintRight_toLeftOf="@id/startOrStopIv"
                app:layout_constraintTop_toTopOf="@id/startOrStopIv"
                tools:background="#80000000"
                tools:text="00:02:58" />

            <ImageView
                android:id="@+id/pauseOrResumeIv"
                android:layout_width="80px"
                android:layout_height="80px"
                android:layout_marginLeft="80px"
                android:src="@drawable/ic_pause"
                app:layout_constraintBottom_toBottomOf="@id/startOrStopIv"
                app:layout_constraintLeft_toRightOf="@id/startOrStopIv"
                app:layout_constraintTop_toTopOf="@id/startOrStopIv" />


            <androidx.constraintlayout.widget.Group
                android:id="@+id/select_model_group"
                android:layout_width="0px"
                android:layout_height="0px"
                android:visibility="visible"
                app:constraint_referenced_ids="record_yellow_point,slideSelectTabView" />

            <com.czur.starry.device.localmeetingrecord.widget.SlideSelectTabView
                android:id="@+id/slideSelectTabView"
                android:layout_width="match_parent"
                android:layout_height="60px"
                android:layout_marginLeft="300px"
                android:layout_marginRight="300px"
                android:layout_marginBottom="15dp"
                app:layout_constraintBottom_toTopOf="@+id/startOrStopIv" />

            <View
                android:id="@+id/record_yellow_point"
                android:layout_width="10px"
                android:layout_height="10px"
                android:background="@drawable/yellow_point"
                app:layout_constraintBottom_toBottomOf="@id/slideSelectTabView"
                app:layout_constraintEnd_toEndOf="@+id/startOrStopIv"
                app:layout_constraintStart_toStartOf="@+id/startOrStopIv"
                app:layout_constraintTop_toBottomOf="@+id/slideSelectTabView" />

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/guildline1"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:orientation="vertical"
                app:layout_constraintGuide_percent="0.5" />

            <androidx.constraintlayout.widget.Group
                android:id="@+id/screenSelectOptionsGroup"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:constraint_referenced_ids="withVideoIv,withAudioIv,withVideoTv,withAudioTv" />


            <com.czur.uilib.choose.CZImageCheckBox
                android:id="@+id/withVideoIv"
                android:layout_width="40px"
                android:layout_height="40px"
                android:layout_marginEnd="2dp"
                android:layout_marginBottom="12dp"
                app:checked="true"
                app:checkedImg="@drawable/ic_time_watermark_check"
                app:layout_constraintBottom_toTopOf="@+id/withAudioIv"
                app:layout_constraintEnd_toStartOf="@+id/guildline1"
                app:layout_constraintRight_toLeftOf="@id/timeWaterMarkTv"
                app:unCheckedImg="@drawable/ic_time_watermark_uncheck"
                app:withClickIDs="withVideoTv" />

            <com.czur.uilib.choose.CZImageCheckBox
                android:id="@+id/withAudioIv"
                android:layout_width="40px"
                android:layout_height="40px"
                android:layout_marginEnd="2dp"
                app:checked="true"
                app:checkedImg="@drawable/ic_time_watermark_check"
                app:layout_constraintBottom_toTopOf="@+id/slideSelectTabView"
                app:layout_constraintEnd_toStartOf="@+id/guildline1"
                app:layout_constraintRight_toLeftOf="@id/timeWaterMarkTv"
                app:unCheckedImg="@drawable/ic_time_watermark_uncheck"
                app:withClickIDs="withAudioTv" />


            <TextView
                android:id="@+id/withVideoTv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="2dp"
                android:shadowColor="@color/black"
                android:shadowRadius="5"
                android:text="@string/str_function_simultaneousRecording_pictureInPicture"
                android:textColor="@color/white"
                android:textSize="24px"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="@+id/withVideoIv"
                app:layout_constraintStart_toStartOf="@+id/guildline1"
                app:layout_constraintTop_toTopOf="@+id/withVideoIv" />

            <TextView
                android:id="@+id/withAudioTv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="2dp"
                android:shadowColor="@color/black"
                android:shadowRadius="5"
                android:text="@string/str_function_simultaneousRecording"
                android:textColor="@color/white"
                android:textSize="24px"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="@+id/withAudioIv"
                app:layout_constraintStart_toStartOf="@+id/guildline1"
                app:layout_constraintTop_toTopOf="@+id/withAudioIv" />

            <!--            <TextView-->
            <!--                android:id="@+id/withSubmixAudioTv"-->
            <!--                android:layout_width="wrap_content"-->
            <!--                android:layout_height="wrap_content"-->
            <!--                android:layout_marginStart="2dp"-->
            <!--                android:text="@string/str_function_simultaneousRecording_submix_audio"-->
            <!--                android:textSize="24px"-->
            <!--                android:textStyle="bold"-->
            <!--                android:textColor="@color/white"-->
            <!--                android:shadowRadius="5"-->
            <!--                android:shadowColor="@color/black"-->
            <!--                app:layout_constraintBottom_toBottomOf="@+id/withSubmixAudioIv"-->
            <!--                app:layout_constraintStart_toStartOf="@+id/guildline1"-->
            <!--                app:layout_constraintTop_toTopOf="@+id/withSubmixAudioIv" />-->
        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/coverLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/black"
        android:visibility="visible">

        <ImageView
            android:layout_width="160px"
            android:layout_height="160px"
            android:src="@drawable/ic_record_screen_uncheck"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/blurCoverIv"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <FrameLayout
        android:id="@+id/photoFlashingLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/flash_white"
        android:visibility="gone" />

    <include
        android:id="@+id/screenRecordingLayout"
        layout="@layout/screen_recording_layout"
        android:visibility="gone" />

    <ImageView
        android:id="@+id/prepareTimeBgIv"
        android:layout_width="120px"
        android:layout_height="120px"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/prepareTimeIv"
        android:layout_width="85px"
        android:layout_height="85px"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/backPlaceHolderView"
        android:layout_width="60px"
        android:layout_height="60px"
        android:layout_marginLeft="45px"
        android:layout_marginTop="35px"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/backIv"
        android:layout_width="60px"
        android:layout_height="60px"
        android:layout_marginLeft="45px"
        android:layout_marginTop="35px"
        android:src="@drawable/ic_local_back"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/narrowRemindTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="22px"
        android:shadowColor="@color/black"
        android:shadowRadius="5"
        android:text="@string/str_can_record_background"
        android:textColor="@color/white"
        android:textSize="24px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@+id/backPlaceHolderView"
        app:layout_constraintLeft_toRightOf="@+id/backIv"
        app:layout_constraintTop_toTopOf="@+id/backPlaceHolderView"
        app:layout_goneMarginLeft="45px" />
</androidx.constraintlayout.widget.ConstraintLayout>