<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/bgcl"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:elevation="10px"
    tools:ignore="PxUsage,RtlHardcoded">

    <TextView
        android:id="@+id/float_time"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/black_50_bg"
        android:clickable="false"
        android:focusable="false"
        android:focusableInTouchMode="false"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="30px" />


</FrameLayout>