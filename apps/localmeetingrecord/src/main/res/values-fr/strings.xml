<resources xmlns:tools="http://schemas.android.com/tools">
    <string name="app_name">Enregistrer une réunion locale</string>
    <string name="str_time_watermark">Filigrane temporel</string>
    <string name="toast_video_file_saved">%s fichiers d’enregistrements ont été enregistrés.</string>
    <string name="toast_video_file_save_failed">L’enregistrement du fichier d’enregistrement a échoué.</string>
    <string name="toast_storage_not_enough_start_record">Impossible d’enregistrer L’espace de stockage \nrestant est inférieur à 500 Mo.</string>
    <string name="toast_storage_not_enough_start_camera">Impossible de prendre une photo. L’espace de stockage restant est inférieur à 500 Mo.</string>
    <string name="toast_rec_storage_not_enough">L’enregistrement s’arrêtera dans 5 minutes car l’espace de stockage restant est inférieur à 500 Mo.</string>
    <string name="toast_rec_storage_stop_recording">Espace de stockage restant insuffisant. L’enregistrement s’est arrêté</string>
    <string name="toast_rec_time_not_enough">Rappel : l’enregistrement s’arrêtera dans %s minutes. Enregistrez le fichier vidéo immédiatement.</string>
    <string name="tips_camera_mode">Code de l’enregistrement vidéo</string>
    <string name="tips_screen_mode">Mode d’enregistrement de l’écran</string>
    <string name="tips_audio_mode">Mode d’enregistrement du son</string>
    <string name="toast_conflict_with_meeting">Impossible de commencer l’enregistrement d’une réunion locale car une réunion vidéo est en cours.</string>
    <string name="dialog_mic_occupy_hint">%s est en train d’utiliser ce micro. Voulez-vous arrêter cela et commencer à enregistrer ?</string>
    <string name="dialog_mic_occupy_hint_init">%s est en train d’utiliser ce micro. Voulez-vous arrêter cela et commencer une réunion locale ?</string>
    <string name="dialog_stop_record_hint"> Terminer l’enregistrement ?</string>
    <string name="dialog_refuse_eshare">Impossible de caster l’écran car l’enregistrement d’une réunion vidéo es en cours.</string>
    <string name="str_is_recording_audio">Enregistrement audio en cours.</string>
    <string name="str_is_recording_screen">Enregistrement d’écran en cours.</string>
    <string name="str_resume_record_screen">Continuer à enregistrer.</string>
    <string name="str_pause_record_screen">Mettre en pause l’enregistrement.</string>
    <string name="str_stop_record_screen">Compléter l’enregistrement.</string>
    <string name="str_can_record_background">Enregistrement en arrière-plan</string>
    <string name="toast_recording_fail">Le démarrage de l’enregistrement a échoué. Veuillez réessayer.</string>
    <string name="toast_init_camera_fail">L’activation de la caméra a échoué. Veuillez redémarrer l’appli</string>
    <string name="str_function_simultaneousRecording_pictureInPicture">Enregistrer la vidéo (incrustation)</string>
    <string name="str_function_simultaneousRecording">Enregistrer le son</string>
    <string name="str_function_recorder">Enregistrer le son</string>
    <string name="str_function_screen_recorder">Enregistrer l’écran</string>
    <string name="str_function_video">Enregistrer la vidéo</string>
    <string name="str_function_photo">Prendre une photo</string>
    <string name="str_rename">Renommer</string>
    <string name="str_rename_inputet">Saisissez le nom du fichier</string>
    <string name="toast_init_camera_fail_100">Échec du lancement de la caméra. Veuillez redémarrer cette application.</string>
    <string name="str_save_file">Renommer le fichier</string>
    <string name="str_alert_dialog_title">Notification</string>
    <string name="str_dialog_sure">OK</string>
    <string name="str_dialog_cancel">Annuler</string>
    <string name="str_flip_lens">Mise en miroir vidéo</string>
    <string name="toast_record_error">L\'enregistrement de la vidéo a échoué. Veuillez réessayer.</string>
</resources>
