<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_user_begin"
    app:startDestination="@id/listFragment">

    <fragment
        android:id="@+id/listFragment"
        android:name="com.czur.starry.device.personalcenter.account.ai.recharge.RechargeListFragment"
        android:label="RechargeListFragment"
        tools:layout="@layout/layout_ai_trans_recharge_list">
        <action
            android:id="@+id/action_list_to_qr_code"
            app:destination="@id/qrCodeFragment"
            app:enterAnim="@anim/nav_default_enter_anim"
            app:exitAnim="@anim/nav_default_exit_anim">
            <argument
                android:name="productId"
                app:argType="integer" />
            <argument
                android:name="price"
                app:argType="float" />
            <argument
                android:name="productName"
                app:argType="string" />
        </action>
    </fragment>

    <fragment
        android:id="@+id/qrCodeFragment"
        android:name="com.czur.starry.device.personalcenter.account.ai.recharge.RechargeQRCodeFragment"
        android:label="RechargeQRCodeFragment"
        tools:layout="@layout/layout_ai_trans_recharge_qr_code" >
        <argument
            android:name="productId"
            app:argType="integer" />
        <argument
            android:name="price"
            app:argType="float" />
        <argument
            android:name="productName"
            app:argType="string" />

        <action
            android:id="@+id/action_qr_code_to_success"
            app:destination="@id/rechargeSuccessFragment"
            app:enterAnim="@anim/nav_default_enter_anim"
            app:exitAnim="@anim/nav_default_exit_anim">
            <argument
                android:name="price"
                app:argType="float" />
            <argument
                android:name="productName"
                app:argType="string" />
            <argument
                android:name="orderNumber"
                app:argType="string" />
        </action>
    </fragment>

    <fragment
        android:id="@+id/rechargeSuccessFragment"
        android:name="com.czur.starry.device.personalcenter.account.ai.recharge.RechargeSuccessFragment"
        android:label="RechargeSuccessFragment"
        tools:layout="@layout/layout_ai_trans_recharge_success" >
        <argument
            android:name="price"
            app:argType="float" />
        <argument
            android:name="productName"
            app:argType="string" />
        <argument
            android:name="orderNumber"
            app:argType="string" />
    </fragment>

</navigation>