<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/base_bg_color"
    tools:ignore="RtlHardcoded,PxUsage">

    <TextView
        android:id="@+id/captchaTitleTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="98px"
        android:text="@string/title_change_bind_phone_old"
        android:textColor="@color/white"
        android:textSize="48px"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/captchaDescTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_change_bind_phone_old_hint"
        android:textColor="@color/white"
        android:textSize="30px"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/captchaTitleTv" />

    <EditText
        android:id="@+id/captchaPhoneEt"
        android:layout_width="630px"
        android:layout_height="80px"
        android:gravity="center"
        android:hint="@string/signup_bind_phone_hint"
        android:imeOptions="actionNext"
        android:inputType="number"
        android:nextFocusDown="@id/captchaEt"
        android:singleLine="true"
        android:textColor="@color/white"
        android:textSize="30px"
        app:layout_constraintVertical_chainStyle="packed"
        android:textStyle="bold"
        app:bl_corners_radius="10px"
        app:bl_solid_color="#33000000"
        app:layout_constraintBottom_toTopOf="@id/captchaEt"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/captchaAreaCodeTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="25px"
        android:text="@string/setup_bind_phone_area_hint"
        android:textColor="@color/white"
        android:textSize="30px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@+id/captchaPhoneEt"
        app:layout_constraintRight_toLeftOf="@+id/captchaPhoneEt"
        app:layout_constraintTop_toTopOf="@+id/captchaPhoneEt" />

    <EditText
        android:id="@+id/captchaEt"
        android:layout_width="540px"
        android:layout_height="80px"
        android:layout_marginTop="30px"
        android:gravity="center"
        android:hint="@string/setup_bind_phone_verification_code_hint"
        android:imeOptions="actionDone"
        android:inputType="number"
        android:maxLength="6"
        android:nextFocusDown="@id/captchaEt"
        android:singleLine="true"
        android:textColor="@color/white"
        android:textSize="30px"
        android:textStyle="bold"
        app:bl_corners_radius="10px"
        app:bl_solid_color="#33000000"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="@+id/captchaPhoneEt"
        app:layout_constraintTop_toBottomOf="@+id/captchaPhoneEt" />

    <TextView
        android:id="@+id/getCaptchaTv"
        android:layout_width="0px"
        android:layout_height="0px"
        android:gravity="center"
        android:text="@string/get_verification_code"
        android:textColor="@color/sel_text_enable"
        android:textSize="30px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@+id/captchaEt"
        app:layout_constraintLeft_toRightOf="@+id/captchaEt"
        app:layout_constraintRight_toRightOf="@id/captchaPhoneEt"
        app:layout_constraintTop_toTopOf="@+id/captchaEt" />

    <TextView
        android:id="@+id/countDownTv"
        android:layout_width="0px"
        android:layout_height="0px"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="30px"
        android:textStyle="bold"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/captchaEt"
        app:layout_constraintLeft_toRightOf="@+id/captchaEt"
        app:layout_constraintRight_toRightOf="@id/captchaPhoneEt"
        app:layout_constraintTop_toTopOf="@+id/captchaEt" />

    <com.czur.uilib.btn.CZButton
        android:id="@+id/captchaNextBtn"
        android:layout_width="300px"
        android:layout_height="80px"
        android:layout_marginLeft="30px"
        android:text="@string/step_next"
        android:textSize="30px"
        app:colorStyle="PositiveInBlue"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginBottom="60px"
        />


</androidx.constraintlayout.widget.ConstraintLayout>