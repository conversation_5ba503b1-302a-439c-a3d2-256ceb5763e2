<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:ignore="PxUsage">


    <ImageView
        android:id="@+id/registerSuccessIv"
        android:layout_width="100px"
        android:layout_height="76px"
        android:layout_marginTop="340px"
        android:src="@drawable/img_register_success"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="49px"
        android:text="@string/register_success_title"
        android:textColor="@color/white"
        android:textSize="48px"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/registerSuccessIv" />

    <View
        android:layout_width="10px"
        android:layout_height="10px"
        android:background="@drawable/circle"
        app:layout_constraintBottom_toBottomOf="@id/accountNoTv"
        app:layout_constraintLeft_toLeftOf="@id/phoneNoIndexView"
        app:layout_constraintTop_toTopOf="@id/accountNoTv" />

    <TextView
        android:id="@+id/accountNoTv"
        style="@style/register_success_text"
        android:layout_marginTop="562px"
        app:layout_constraintLeft_toLeftOf="@id/bindTargetTv"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="账号/会议号: 280900" />

    <View
        android:id="@+id/phoneNoIndexView"
        android:layout_width="10px"
        android:layout_height="10px"
        android:background="@drawable/circle"
        app:layout_constraintBottom_toBottomOf="@id/bindTargetTv"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/bindTargetTv"
        app:layout_constraintTop_toTopOf="@id/bindTargetTv" />

    <TextView
        android:id="@+id/bindTargetTv"
        style="@style/register_success_text"
        android:layout_marginLeft="10px"
        android:layout_marginTop="30px"
        app:layout_constraintLeft_toRightOf="@id/phoneNoIndexView"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/accountNoTv"
        tools:text="密保手机号: ***********" />

    <com.czur.uilib.btn.CZButton
        android:id="@+id/startBtn"
        android:layout_width="500px"
        android:layout_height="80px"
        android:layout_marginBottom="60px"
        android:text="@string/register_success_begin_to_use"
        android:textSize="30px"
        app:colorStyle="PositiveInBlue"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>