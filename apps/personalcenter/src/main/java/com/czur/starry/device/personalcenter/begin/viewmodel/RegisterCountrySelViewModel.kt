package com.czur.starry.device.personalcenter.begin.viewmodel

import com.czur.czurutils.log.logTagV
import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.MutableLiveData
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.network.HttpManager
import com.czur.starry.device.baselib.utils.*
import com.czur.starry.device.personalcenter.R
import com.czur.starry.device.personalcenter.bean.CountryInfo
import com.czur.starry.device.personalcenter.net.ICountrySelServer
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File

/**
 * Created by 陈丰尧 on 2022/7/18
 */
class RegisterCountrySelViewModel(application: Application) : AndroidViewModel(application) {
    companion object {
        private const val TAG = "RegisterCountrySelViewModel"
        private const val CACHE_FILE_NAME = "country.json"
    }

    private val gson = Gson()
    private val countrySelServer: ICountrySelServer by lazy { HttpManager.getService(Constants.PASSPORT_COUNTRY_URL) }

    val countryListLive = MutableLiveData<List<CountryInfo>>(emptyList())


    suspend fun loadCountryList() {
        val info = loadInitCountryList().blockCN()
        countryListLive.value = info
    }

    /**
     * 屏蔽中国选项
     */
    private fun List<CountryInfo>.blockCN(): List<CountryInfo> {
        var needResetSelect = false
        val blockList = filter {
            (it.countryCode != "CHN").also { notCN ->
                if (!notCN) {
                    needResetSelect = true
                }
            }
        }

        if (needResetSelect) {
            blockList.first().sel = true
        }
        return blockList
    }

    private suspend fun loadInitCountryList(): List<CountryInfo> = withContext(Dispatchers.IO) {
        // 1. 检查缓存文件
        val lang = SettingHandler.czurLang
        val type = if (lang == SettingHandler.CZURLang.EN) {
            "en-US"
        } else {
            "zh-CN"
        }
        val cacheFile = File(appContext.cacheDir, "$type-$CACHE_FILE_NAME")
        if (cacheFile.exists() && cacheFile.length() > 10L && System.currentTimeMillis() - cacheFile.lastModified() < ONE_DAY) {
            logTagV(TAG, "从缓存获取数据")
            return@withContext gson.fromJson<List<CountryInfo>>(
                cacheFile.reader(),
                object : TypeToken<List<CountryInfo>>() {}.type
            )
        }
        // 从网络获取
        val countryEntity = countrySelServer.countryCodeList(type)
        if (countryEntity.isSuccess) {
            logTagV(TAG, "网络获取国家列表成功")
            val info = countryEntity.body
            cacheFile.delete()
            cacheFile.createNewFile()
            cacheFile.writeText(gson.toJson(info.countryList))  // 写入缓存
            return@withContext info.countryList
        }

        // 网络请求失败, 使用自带缓存
        logTagV(TAG, "使用本地缓存进行获取数据")
        val reader = appContext.resources.openRawResource(R.raw.country_list).reader()
        reader.use {
            return@withContext gson.fromJson<List<CountryInfo>>(
                it,
                object : TypeToken<List<CountryInfo>>() {}.type
            )
        }
    }
}