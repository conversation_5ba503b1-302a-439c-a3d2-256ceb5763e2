package com.czur.starry.device.personalcenter.begin

import android.os.Bundle
import androidx.fragment.app.viewModels
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.personalcenter.begin.viewmodel.RegisterViewModel
import com.czur.starry.device.personalcenter.databinding.FragmentUserBeginBinding

/**
 * Created by 陈丰尧 on 2023/3/9
 */
private const val TAG = "UserBeginFragment"

class UserBeginFragment : AbsBeginFragment<FragmentUserBeginBinding>() {
    private val model: RegisterViewModel by viewModels({ requireActivity() })
    override fun FragmentUserBeginBinding.initBindingViews() {
        beginFirstTv.setOnClickListener {
            logTagV(TAG, "点击注册")
            UserBeginFragmentDirections.actionUserBeginFragmentToRegister().nav()
        }

        beginLoginTv.setOnClickListener {
            logTagV(TAG, "点击登录")
            UserBeginFragmentDirections.actionUserBeginFragmentToLogin().nav()
        }

        skipView.setOnClickListener {
            skipThisStep()
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        model.clearAccountNos()
    }
}