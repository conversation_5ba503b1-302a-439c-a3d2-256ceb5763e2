package com.czur.starry.device.personalcenter.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.view.ViewCompat;

import com.czur.starry.device.baselib.utils.ConstraintUtil;
import com.czur.starry.device.personalcenter.R;

import java.util.List;

import static androidx.constraintlayout.widget.ConstraintSet.CHAIN_PACKED;
import static androidx.constraintlayout.widget.ConstraintSet.LEFT;
import static androidx.constraintlayout.widget.ConstraintSet.PARENT_ID;

/**
 * Created by 陈丰尧 on 2/26/21
 */
public class AccountNoSelView extends ConstraintLayout implements View.OnClickListener {
    private List<String> titles;
    private static final int ID_OFF_SET = 1000;
    private ConstraintUtil constraintUtil;
    private int selIndex = 0;
    private ImageView instructionIv;

    private OnSelChangeListener listener;

    public AccountNoSelView(Context context) {
        super(context);
        init();
    }

    public AccountNoSelView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public AccountNoSelView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        constraintUtil = new ConstraintUtil(this);
        updateViews();
    }

    public void setTitles(List<String> titles) {
        this.titles = titles;
        updateViews();
        selIndex = 0;
        updateSelUI(selIndex);
    }

    public void setOnSelChangeListener(OnSelChangeListener listener) {
        this.listener = listener;
    }

    private void updateViews() {
        removeAllViews();
        if (titles == null) {
            return;
        }


        for (int i = 0; i < titles.size(); i++) {
            String title = titles.get(i);
            TextView textView = (TextView) LayoutInflater.from(getContext())
                    .inflate(R.layout.item_account_sel_view, this, false);
            textView.setText(title);
            textView.setId(i + ID_OFF_SET);
            addView(textView);
            textView.setOnClickListener(this);
            ViewCompat.setPointerIcon(textView,null);
        }

        instructionIv = (ImageView) LayoutInflater
                .from(getContext())
                .inflate(R.layout.item_account_sel_instructions_view, this, false);
        instructionIv.setId(ID_OFF_SET * 10);
        addView(instructionIv);

        ConstraintUtil.ConstraintBegin begin = constraintUtil.begin();

        begin.commit();
        for (int i = 0; i < titles.size(); i++) {
            if (i == 0) {
                begin.leftToLeftOf(getChildAt(i).getId(), PARENT_ID);

            } else {
                begin.leftToRightOf(getChildAt(i).getId(), getChildAt(i - 1).getId())
                        .setMarginLeft(getChildAt(i).getId(), 100);
            }

            if (i == titles.size() - 1) {
                begin.rightToRightOf(getChildAt(i).getId(), PARENT_ID);
            } else {
                begin.rightToLeftOf(getChildAt(i).getId(), getChildAt(i + 1).getId());
            }
        }
        begin.chainHorizontal(getChildAt(0).getId(), CHAIN_PACKED);

        begin.topToTopOf(instructionIv.getId(), PARENT_ID)
                .bottomToBottomOf(instructionIv.getId(), PARENT_ID);
        begin.commit();
    }

    public void updateSelUI(int selIndex) {
        ConstraintUtil.ConstraintBegin begin = constraintUtil.begin();
        begin.clear(instructionIv.getId(), LEFT)
                .leftToRightOf(instructionIv.getId(), selIndex + ID_OFF_SET)
                .setMarginLeft(instructionIv.getId(), 10)
                .commit();
        this.selIndex = selIndex;
    }
    public void updateSelUI(String selContent) {
        int index = titles.indexOf(selContent);
        if (index >= 0) {
            updateSelUI(index);
        }
    }


    @Override
    public void onClick(View v) {
        int id = v.getId();
        int clickIndex = id - ID_OFF_SET;
        if (clickIndex != selIndex) {
            updateSelUI(clickIndex);
            if (listener != null) {
                listener.onSelChange(clickIndex);
            }
        }
    }


    public interface OnSelChangeListener {
        void onSelChange(int selPos);
    }
}
