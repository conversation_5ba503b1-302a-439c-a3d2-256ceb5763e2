package com.czur.starry.device.personalcenter.bean

import com.google.gson.annotations.SerializedName

/**
 * Created by 陈丰尧 on 2022/7/18
 */
class CountryData(
    val countryList: List<CountryInfo>
)

class CountryInfo(
    val countryCode: String,
    val countryName: String,        // 中文
    val countryNameUs: String,      // 英文
    val countryNameTw: String?,     // 繁体中文(台湾)
    val countryNameJP: String?,     // 日语
    val countryNameRU: String?,     // 俄语
    val countryNameIT: String?,     // 意大利语
    val countryNameDE: String?,     // 德语
    val countryNameKO: String?,     // 韩语
    val countryNameFR: String?,     // 法语
    val countryNameES: String?,     // 西班牙语
    val countryNameStandard: String?,     // 按照各个国家的语言显示
    @SerializedName("defaultCountry")
    var sel: Boolean                // 是否选中
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as CountryInfo

        if (countryCode != other.countryCode) return false
        if (countryName != other.countryName) return false
        if (countryNameUs != other.countryNameUs) return false
        if (countryNameTw != other.countryNameTw) return false
        if (countryNameJP != other.countryNameJP) return false
        if (countryNameRU != other.countryNameRU) return false
        if (countryNameIT != other.countryNameIT) return false
        if (countryNameDE != other.countryNameDE) return false
        if (countryNameKO != other.countryNameKO) return false
        if (countryNameFR != other.countryNameFR) return false
        if (countryNameES != other.countryNameES) return false
        if (countryNameStandard != other.countryNameStandard) return false
        if (sel != other.sel) return false

        return true
    }

    override fun hashCode(): Int {
        var result = countryCode.hashCode()
        result = 31 * result + countryName.hashCode()
        result = 31 * result + countryNameUs.hashCode()
        result = 31 * result + (countryNameTw?.hashCode() ?: 0)
        result = 31 * result + (countryNameJP?.hashCode() ?: 0)
        result = 31 * result + (countryNameRU?.hashCode() ?: 0)
        result = 31 * result + (countryNameIT?.hashCode() ?: 0)
        result = 31 * result + (countryNameDE?.hashCode() ?: 0)
        result = 31 * result + (countryNameKO?.hashCode() ?: 0)
        result = 31 * result + (countryNameFR?.hashCode() ?: 0)
        result = 31 * result + (countryNameES?.hashCode() ?: 0)
        result = 31 * result + (countryNameStandard?.hashCode() ?: 0)
        result = 31 * result + sel.hashCode()
        return result
    }
}