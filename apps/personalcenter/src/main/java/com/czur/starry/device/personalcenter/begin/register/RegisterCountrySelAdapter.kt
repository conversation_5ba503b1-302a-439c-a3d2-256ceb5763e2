package com.czur.starry.device.personalcenter.begin.register

import android.view.ViewGroup
import com.czur.starry.device.baselib.base.BaseDifferAdapter
import com.czur.starry.device.baselib.base.BaseVH
import com.czur.starry.device.baselib.utils.SettingHandler
import com.czur.starry.device.personalcenter.R
import com.czur.starry.device.personalcenter.bean.CountryInfo

/**
 * Created by 陈丰尧 on 2022/7/18
 */
class RegisterCountrySelAdapter(
    private val czurLang: SettingHandler.CZURLang
) : BaseDifferAdapter<CountryInfo>() {

    var selectPos = -1
        set(value) {
            if (field == value) return
            val lastSelPos = field
            field = value
            if (lastSelPos >= 0) {
                notifyItemChanged(lastSelPos)
            }
            notifyItemChanged(field)
        }

    // 选中的国家编号
    val getSelCountryCode: String
        get() = getDataOrNull(selectPos)?.countryCode ?: "CHN"

    override fun areItemsTheSame(oldItem: CountryInfo, newItem: CountryInfo): Boolean {
        return oldItem.countryCode == newItem.countryCode
    }

    override fun areContentsTheSame(oldItem: CountryInfo, newItem: CountryInfo): Boolean {
        // 判断内容
        return oldItem.countryCode == newItem.countryCode &&
                oldItem.countryName == newItem.countryName &&
                oldItem.countryNameTw == newItem.countryNameTw &&
                oldItem.countryNameUs == newItem.countryNameUs &&
                oldItem.countryNameJP == newItem.countryNameJP &&
                oldItem.countryNameRU == newItem.countryNameRU &&
                oldItem.countryNameIT == newItem.countryNameIT &&
                oldItem.countryNameDE == newItem.countryNameDE &&
                oldItem.countryNameKO == newItem.countryNameKO &&
                oldItem.countryNameFR == newItem.countryNameFR &&
                oldItem.countryNameES == newItem.countryNameES &&
                oldItem.countryNameStandard == newItem.countryNameStandard
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseVH {
        return BaseVH(R.layout.item_country_sel, parent)
    }

    override fun bindViewHolder(holder: BaseVH, position: Int, itemData: CountryInfo) {
        val showName = when (czurLang) {
            SettingHandler.CZURLang.CN -> itemData.countryName
            SettingHandler.CZURLang.EN -> itemData.countryNameUs
            SettingHandler.CZURLang.TW -> itemData.countryNameTw ?: itemData.countryName
            SettingHandler.CZURLang.JP -> itemData.countryNameStandard ?: itemData.countryNameUs
            SettingHandler.CZURLang.RU -> itemData.countryNameStandard ?: itemData.countryNameUs
            SettingHandler.CZURLang.IT -> itemData.countryNameStandard ?: itemData.countryNameUs
            SettingHandler.CZURLang.DE -> itemData.countryNameStandard ?: itemData.countryNameUs
            SettingHandler.CZURLang.KO -> itemData.countryNameStandard ?: itemData.countryNameUs
            SettingHandler.CZURLang.FR -> itemData.countryNameStandard ?: itemData.countryNameUs
            SettingHandler.CZURLang.ES -> itemData.countryNameStandard ?: itemData.countryNameUs
        }
        holder.setText(showName, R.id.countryNameTv)
        holder.visible(selectPos == position, R.id.countrySelIv)

    }

}