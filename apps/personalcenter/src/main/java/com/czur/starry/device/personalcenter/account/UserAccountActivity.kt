package com.czur.starry.device.personalcenter.account

import android.content.Intent
import android.os.Bundle
import android.view.KeyEvent
import android.view.View
import androidx.activity.viewModels
import androidx.core.widget.doAfterTextChanged
import androidx.core.widget.doOnTextChanged
import com.bumptech.glide.Glide
import com.czur.czurutils.extension.platform.startActivity
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.base.v2.aty.CZViewBindingAty
import com.czur.starry.device.baselib.data.provider.UserHandler
import com.czur.starry.device.baselib.utils.ONE_HOUR
import com.czur.starry.device.baselib.utils.ONE_MIN
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.addNickNameFilter
import com.czur.starry.device.baselib.utils.addNoSpaceFilter
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.keyboard.keyboardHide
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.baselib.utils.repeatOnResume
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.baselib.utils.toast
import com.czur.starry.device.baselib.view.floating.common.DoubleBtnCommonFloat
import com.czur.starry.device.baselib.view.floating.common.DoubleBtnCommonFloat.Companion.DOUBLE_FLOAT_BTN_CONFIRM
import com.czur.starry.device.personalcenter.MainActivity
import com.czur.starry.device.personalcenter.R
import com.czur.starry.device.personalcenter.account.ai.order.AiTransOrderActivity
import com.czur.starry.device.personalcenter.account.ai.recharge.AITransRechargeActivity
import com.czur.starry.device.personalcenter.account.deactivate.DeactivateActivity
import com.czur.starry.device.personalcenter.account.phone.ChangeBindPhoneAty
import com.czur.starry.device.personalcenter.databinding.ActivityUserAccountBinding
import java.time.Instant
import java.time.ZoneId
import java.time.format.DateTimeFormatter

class UserAccountActivity : CZViewBindingAty<ActivityUserAccountBinding>() {

    companion object {
        private const val TAG = "UserAccountActivity"
    }

    private val viewModel: UserAccountViewModel by viewModels()

    private fun toggleVisibility(isEditing: Boolean) {
        binding.saveBtn.visibility = if (isEditing) View.VISIBLE else View.INVISIBLE
        binding.cancelBtn.visibility = if (isEditing) View.VISIBLE else View.INVISIBLE
    }

    override fun ActivityUserAccountBinding.initBindingViews() {

        inputField.setOnFocusChangeListener { _, hasFocus ->
            if (hasFocus) {
                toggleVisibility(true)
            }
        }
        // 昵称长度 不得超过14个字
        inputField.addNickNameFilter()
        inputField.addNoSpaceFilter()
        inputField.doAfterTextChanged {
            viewModel.updateUserInputNickName(it.toString())
        }
        inputField.setOnKeyListener { _, keyCode, event ->
            // 监听回车键
            if (event.action == KeyEvent.ACTION_DOWN && keyCode == KeyEvent.KEYCODE_ENTER) {
                saveBtn.performClick()
                inputField.keyboardHide()
                return@setOnKeyListener true
            }
            false
        }

        saveBtn.setOnClickListener {
            inputField.clearFocus()
            toggleVisibility(false)
            updateNickName()
        }

        cancelBtn.setOnClickListener {
            inputField.clearFocus()
            binding.inputField.setText(UserHandler.nickname)
            toggleVisibility(false)
        }

        // 密保手机
        editPhoneButton.setOnDebounceClickListener {
            logTagV(TAG, "用户点击密保手机页面")
            startActivity<ChangeBindPhoneAty>()
        }

        logoutClickLayer.setOnDebounceClickListener {
            logTagV(TAG, "用户点击 退出登录")
            logout()
        }

        // 发票按钮
        recordsAndInvoices.setOnDebounceClickListener {
            logTagV(TAG, "用户点击 发票")
            startActivity<AiTransOrderActivity>()
        }

        // 充值按钮
        aiSubtitleActions.setOnDebounceClickListener {
            logTagV(TAG, "用户点击 充值")
            startActivity<AITransRechargeActivity>()
        }

        // 注销按钮
        logoutButton.setOnDebounceClickListener {
            logTagV(TAG, "用户点击 注销")
            showDeactivateDialog()
        }
    }

    private fun updateNickName() {
        launch {
            withLoading {
                val result = viewModel.updateNickName()
                if (result.isFailure) {
                    logTagW(TAG, "更新用户名失败")
                    // 恢复
                    binding.inputField.setText(UserHandler.nickname)
                    toast(R.string.toast_update_nick_fail)
                }
            }
        }
    }

    private fun showDeactivateDialog() {
        DoubleBtnCommonFloat(content = getString(R.string.str_deactivate_floating_content)) { floating, pos ->
            if (pos == DOUBLE_FLOAT_BTN_CONFIRM) {
                logTagD(TAG, "用户点击确定注销")
                floating.setOnDismissListener {
                    startActivity<DeactivateActivity>()
                }
            }
            floating.dismiss()
        }.show()
    }

    private fun refreshUserUI() {
        // 头像
        if (UserHandler.headImage.isNotEmpty()) {
            Glide.with(this)
                .load(UserHandler.headImage) // 图片的 URL 或资源 ID
                .error(R.drawable.img_user_avatar)
                .into(binding.avatar)
        }

        binding.inputField.setText(UserHandler.nickname)

        binding.accountValue.text = UserHandler.accountNo
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)

        repeatOnResume {
            refreshUserUI()
        }

        repeatCollectOnResume(viewModel.aiTransTimeFlow) {
            binding.aiSubtitleValidity.text = it
        }

        repeatOnResume {
            val phone = UserHandler.mobile
            // 中间4位修改为*
            val maskedPhone = phone.replaceRange(3, 7, "****")
            binding.securityPhoneValue.text = maskedPhone
            viewModel.refreshAITransTime()
        }

        // 保存按钮
        repeatCollectOnResume(viewModel.nickNameSaveBtnEnableFlow) {
            binding.saveBtn.isEnabled = it
        }

    }

    /**
     * 退出登录
     */
    private fun logout() {
        launch {
            logTagD(TAG, "退出登录")
            UserHandler.logout()
            // 返回登录页面
            val intent = Intent(this@UserAccountActivity, MainActivity::class.java)
            startActivity(intent)
            finish()
        }

    }

}