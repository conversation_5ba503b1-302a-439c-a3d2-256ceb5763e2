package com.czur.starry.device.appstore.ui

import android.os.Bundle
import android.os.SystemClock
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.appstore.R
import com.czur.starry.device.appstore.databinding.FragmentAppListBinding
import com.czur.starry.device.appstore.entity.LocalAppInfo
import com.czur.starry.device.appstore.menu.MenuViewModel
import com.czur.starry.device.appstore.ui.adapter.UninstallAdapter
import com.czur.starry.device.appstore.ui.vm.LocalAppVM
import com.czur.starry.device.appstore.ui.vm.UninstallVM
import com.czur.starry.device.baselib.base.v2.fragment.CZViewBindingFragment
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.doOnItemClick
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.baselib.view.floating.common.DoubleBtnCommonFloat
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.withTimeoutOrNull

/**
 * Created by 陈丰尧 on 2021/10/21
 */
class UninstallFragment : CZViewBindingFragment<FragmentAppListBinding>() {
    companion object {
        const val NET_TAG_UNINSTALL = "uninstall"
        private const val LOAD_TIME_THRESHOLD = 1 * ONE_SECOND
        private const val TAG = "UninstallFragment"
    }

    private val uninstallVM: UninstallVM by viewModels()
    private val localAppVM: LocalAppVM by viewModels({ requireActivity() })

    private val uninstallAdapter = UninstallAdapter()

    private var loadJob: Job? = null

    private var lastUninstallClickTime = 0L


    override fun FragmentAppListBinding.initBindingViews() {
        emptyGroup.gone()
        allAppGroup.gone()

        appListRv.layoutManager = GridLayoutManager(requireContext(), 2)
        appListRv.adapter = uninstallAdapter

        appListRv.doOnItemClick { vh, view ->
            val pos = vh.bindingAdapterPosition
            when (view.id) {
                R.id.itemUninstallBtn -> {
                    if ((SystemClock.elapsedRealtime() - lastUninstallClickTime) < ONE_SECOND) {
                        logTagW(TAG, "点击过快, 不响应")
                        return@doOnItemClick true
                    }
                    lastUninstallClickTime = SystemClock.elapsedRealtime()
                    val uninstallApp = uninstallAdapter.getItemData(pos)
                    showUninstallConfirmDialog(uninstallApp)
                    true
                }

                else -> false
            }
        }
    }

    /**
     * 展示确认卸载对话框
     */
    private fun showUninstallConfirmDialog(app: LocalAppInfo) {
        DoubleBtnCommonFloat(
            title = getString(R.string.dialog_title_uninstall),
            content = getString(R.string.dialog_msg_uninstall, app.appName),
            outSideDismiss = true,
            onCommonClick = { dialog, pos ->
                dialog.dismiss()
                if (pos == 1) {
                    launch {
                        uninstallVM.uninstallApk(app)
                    }
                }
            }
        ).show()
    }

    override fun initData(savedInstanceState: Bundle?) {
        // 可卸载的App
        repeatCollectOnResume(localAppVM.canUninstallApps) {
            binding.onLoadFinish(it)
        }

        // 防止卡在Loading页面
        loadJob = launch {
            delay(LOAD_TIME_THRESHOLD)
            logTagV(TAG, "加载本地App超时了, 显示UI")

            binding.onLoadFinish(localAppVM.localApps.filter {
                !it.isSystemApp
            })
        }
    }

    private fun FragmentAppListBinding.onLoadFinish(localApps: List<LocalAppInfo>) {
        loadJob?.cancel()
        progressBar.gone()

        logTagD("Chen", "---------------")
        localApps.forEach {
            logTagD("Chen", "pkg:${it.pkgName} uninstalling:${it.uninstalling}")
        }
        logTagD("Chen", "--------------")

        uninstallAdapter.setData(localApps)
        if (localApps.isEmpty()) {
            emptyGroup.show()
            allAppGroup.gone()
        } else {
            emptyGroup.gone()
            allAppGroup.show()
        }

        launch {
            localAppVM.currentUninstallAppNameFlow.collect {
                logTagV(TAG, "当前要卸载的App: $it")
                localAppVM.onNavigateReset()

                val result = withTimeoutOrNull(7000) {
                    while (uninstallAdapter.itemCount == 0) {
                        delay(200)
                    }
                    true
                }
                if (result == null) {
                    // 超时处理
                    return@collect
                }
                delay(1000)
                val pos = findAppPos(it)
                if (pos == -1) return@collect
                val uninstallApp = uninstallAdapter.getItemData(pos)
                delay(200)
                showUninstallConfirmDialog(uninstallApp)
            }
        }
    }

    private fun findAppPos(appName: String): Int {
        val pos = uninstallAdapter.getItemPosition(appName)
        if (pos == -1) {
            logTagW(TAG, "未找到要卸载的App:${appName}")
            return -1
        }
        (binding.appListRv.layoutManager as LinearLayoutManager).scrollToPositionWithOffset(pos, 0)

        return pos
    }
}