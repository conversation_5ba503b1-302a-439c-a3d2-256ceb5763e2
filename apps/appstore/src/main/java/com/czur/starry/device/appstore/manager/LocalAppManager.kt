package com.czur.starry.device.appstore.manager

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.czur.czurutils.log.logIntent
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.appstore.App
import com.czur.starry.device.appstore.entity.LocalAppInfo
import com.czur.starry.device.appstore.util.installApp
import com.czur.starry.device.appstore.util.installXApkApp
import com.czur.starry.device.appstore.util.loadAppsInfo
import com.czur.starry.device.baselib.utils.AppUtil
import com.czur.starry.device.baselib.utils.DifferentLiveData
import com.czur.starry.device.baselib.utils.ONE_MIN
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.data.LiveDataDelegate
import com.czur.starry.device.baselib.utils.doWithoutCatch
import com.czur.starry.device.baselib.utils.has
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * Created by 陈丰尧 on 2021/10/26
 */
object LocalAppManager : CoroutineScope by MainScope() {
    private const val TAG = "LocalAppManager"
    const val INSTALL_TIME_OUT = 2 * ONE_MIN

    private val appContext = App.instance

    val appsLive: LiveData<List<LocalAppInfo>> = DifferentLiveData(emptyList())
    var apps by LiveDataDelegate(appsLive)
        private set

    val installingPkgListLive = MutableLiveData<MutableSet<String>>(mutableSetOf())
    private var installingPkgList by LiveDataDelegate(installingPkgListLive)
    private var waitInstallingPkgSet = mutableSetOf<String>()
    private var installFinishTime = mutableMapOf<String, Long>()

    val unInstallingAppPkgSet = mutableSetOf<String>()

    private val appChangeReceiver = AppInfoChangeReceiver()

    private val appUtil by lazy { AppUtil() }

    /**
     * 是否安装超时
     */
    private fun isInstallTimeOut(pkg: String): Boolean {
        if (pkg !in installingPkgList) return false
        val finishTime = installFinishTime[pkg] ?: return false
        return System.currentTimeMillis() - finishTime >= INSTALL_TIME_OUT
    }

    fun addWaitInstallPkg(pkgName: String) {
        synchronized(this) {
            waitInstallingPkgSet.add(pkgName)
        }
    }

    private fun removeWaitInstallPkg(pkgName: String) {
        synchronized(this) {
            waitInstallingPkgSet.remove(pkgName)
        }
    }

    fun getAllInstallingPkgName(): List<String> {
        synchronized(this) {
            return (waitInstallingPkgSet + installingPkgList).toList()
        }
    }


    /**
     * 注册App安装/卸载/升级的广播
     */
    fun registerAppReceiver() {
        logTagD(TAG, "注册App安装广播")
        val intentFilter = IntentFilter().apply {
            addAction(Intent.ACTION_PACKAGE_ADDED)      // 安装
            addAction(Intent.ACTION_PACKAGE_REPLACED)
            addAction(Intent.ACTION_PACKAGE_CHANGED)    // 升级
            addAction(Intent.ACTION_PACKAGE_REMOVED)    // 卸载

            addDataScheme("package")
        }
        appContext.registerReceiver(appChangeReceiver, intentFilter)
    }

    /**
     * 加载所有Apps
     */
    fun loadApps() {
        launch {
            val uninstallApps = loadAppsInfo()
            launch(Dispatchers.Default) {
                uninstallApps.forEach {
                    synchronized(this) {
                        installingPkgList.remove(it.pkgName)
                        installFinishTime.remove(it.pkgName)
                    }
                }
            }
            apps = uninstallApps
        }
    }

    suspend fun installApk(pkgName: String, filePath: String, isXApk: Boolean): Boolean {
        return if (isXApk) {
            installPackage({ installXApkApp(filePath, pkgName) }, pkgName)
        } else {
            installPackage({ installApp(filePath, pkgName) }, pkgName)
        }
    }

    private suspend fun installPackage(
        installFunc: suspend () -> Boolean,
        pkgName: String
    ): Boolean {
        synchronized(this) {
            removeWaitInstallPkg(pkgName)
            installingPkgList.add(pkgName)
            refreshInstallingPkgList()
        }
        return installFunc().also { success ->
            synchronized(this) {
                if (!success) {  // 安装失败
                    installingPkgList.remove(pkgName)
                    refreshInstallingPkgList()
                } else {
                    logTagV(TAG, "记录安装时间:$pkgName")
                    installFinishTime[pkgName] = System.currentTimeMillis()
                }
            }
        }
    }

    /**
     * 卸载apk
     */
    suspend fun uninstallApk(apk: LocalAppInfo) {
        withContext(Dispatchers.IO) {
            val pkgName = apk.pkgName
            if (pkgName in unInstallingAppPkgSet) {
                logTagD(TAG, "正在卸载中:${pkgName}")
                return@withContext
            }

            unInstallingAppPkgSet.add(pkgName)

            apps = apps.map {
                if (it.pkgName == pkgName) {
                    it.copy(uninstalling = true)
                } else {
                    it
                }
            }
            appUtil.uninstallApp(pkgName)
        }
    }

    /**
     * 是否正在安装
     */
    fun isInstallingThisApp(pkgName: String): Boolean {
        val cacheInstalling = pkgName in installingPkgList || pkgName in waitInstallingPkgSet
        if (isInstallTimeOut(pkgName)) {
            logTagW(TAG, "安装超时:${pkgName}")
            return false
        }
        return cacheInstalling
    }

    /**
     * 本地安装的App是否有
     */
    suspend fun hasInstallThisApp(pkgName: String): Boolean {
        return if (apps.isEmpty()) {
            hasInstallThisAppBySystem(pkgName)
        } else {
            withContext(Dispatchers.Default) {
                apps.has {
                    it.pkgName == pkgName && !it.uninstalling
                }
            }
        }
    }

    private suspend fun hasInstallThisAppBySystem(pkgName: String): Boolean {
        return withContext(Dispatchers.Default) {
            appContext.packageManager.getInstalledPackages(0).any {
                it.packageName == pkgName
            }
        }
    }

    /**
     * 判断是否可以升级
     */
    fun canUpDate(pkgName: String, versionCode: Int): Boolean {
        val appInfo = apps.find {
            it.pkgName == pkgName
        }
        return appInfo?.let {
            it.versionCode < versionCode
        } == true
    }


    /**
     * 释放资源
     */
    fun cleared() {
        doWithoutCatch {
            appContext.unregisterReceiver(appChangeReceiver)
        }
    }


    /**
     * App改变的广播,包括 安装/卸载/升级
     */
    private class AppInfoChangeReceiver : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            val action = intent?.action ?: return
            logTagV(TAG, "action:${intent.action}")
            logIntent(intent, TAG)
            launch {
                loadApps()
            }
            when (action) {
                Intent.ACTION_PACKAGE_ADDED, Intent.ACTION_PACKAGE_CHANGED, Intent.ACTION_PACKAGE_REPLACED -> {
                    // 触发了这个广播的时候, 不一定真的能从packageManage中找到这个app
                    // 所以不能代表安装完成, 但是可以触发刷新
                    launch {
                        refreshInstallingPkgList()
                        delay(3 * ONE_SECOND)   // 接到广播时刷新一次, 3s后再刷新一次
                        logTagV(TAG, "新一次应用安装情况")
                        refreshInstallingPkgList()
                        delay(5 * ONE_SECOND)   // 5s后再刷新一次
                        logTagV(TAG, "第二次刷新应用安装情况")
                        refreshInstallingPkgList()
                    }
                }

                Intent.ACTION_PACKAGE_REMOVED -> {
                    val pkgName = intent.data?.schemeSpecificPart ?: ""
                    logTagD(TAG, "卸载完成pkgName:${pkgName}")
                    synchronized(this@LocalAppManager) {
                        unInstallingAppPkgSet.remove(pkgName)
                        logTagV(TAG, "当前还在卸载中的应用: ${unInstallingAppPkgSet.joinToString()}")
                    }
                }
            }

        }
    }

    fun refreshInstallingPkgList() {
        synchronized(this) {
            installingPkgList = installingPkgList
        }
    }


}