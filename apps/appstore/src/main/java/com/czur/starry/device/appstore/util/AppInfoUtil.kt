package com.czur.starry.device.appstore.util

import android.app.PendingIntent
import android.app.usage.StorageStatsManager
import android.content.Intent
import android.content.pm.ApplicationInfo
import android.content.pm.PackageInstaller
import android.content.pm.PackageManager
import com.czur.czurutils.encryption.md5
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.appstore.App
import com.czur.starry.device.appstore.entity.LocalAppInfo
import com.czur.starry.device.appstore.manager.LocalAppManager
import com.czur.starry.device.baselib.utils.doWithoutCatch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import okhttp3.internal.closeQuietly
import java.io.File
import java.io.FileInputStream

/**
 * Created by 陈丰尧 on 2021/10/21
 */

private const val TAG = "AppInfoUtil"
private const val ACTION_INSTALL_COMPLETE = "com.czur.starry.device.appstore.INSTALL_COMPLETE"

private val appContext by lazy(LazyThreadSafetyMode.NONE) { App.instance.applicationContext }

val packageManager: PackageManager by lazy(LazyThreadSafetyMode.NONE) {
    appContext.packageManager
}

val storageStateManager: StorageStatsManager by lazy(LazyThreadSafetyMode.NONE) {
    appContext.getSystemService(StorageStatsManager::class.java)
}


/**
 * 静默安装APK
 */
suspend fun installApp(filepathApk: String, pkgName: String): Boolean {
    return withContext(Dispatchers.IO) {
        val file = File(filepathApk)
        try {
            val pi = packageManager.packageInstaller
            //给定模式，创建新的参数，创建新安装会话，返回唯一 Id
            val sessionId =
                pi.createSession(PackageInstaller.SessionParams(PackageInstaller.SessionParams.MODE_FULL_INSTALL))
            //打开现有会话，主动执行工作
            val session = pi.openSession(sessionId)
            var sizeBytes = 0L
            if (file.isFile) {
                sizeBytes = file.length()
            }
            if (!file.exists()) {
                logTagW(TAG, "没有找到apk文件:${file.absolutePath}")
                return@withContext false
            }
            val out = session.openWrite("app_store_session", 0, sizeBytes)
            val total = file.inputStream().use {
                val total = it.copyTo(out)
                session.fsync(out)
                out.closeQuietly()
                total
            }

            logTagD(TAG, "InstallApkViaPackageInstaller - Success: streamed apk $total bytes")

            val broadCastTest = PendingIntent.getBroadcast(
                appContext,
                sessionId,
                Intent(ACTION_INSTALL_COMPLETE).apply {
                    `package` = appContext.packageName
                },
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            //提交之前必须关闭所有流
            session.commit(broadCastTest.intentSender)
            session.close()
            true
        } catch (exp: Exception) {
            logTagW(TAG, "安裝apk失敗失败", tr = exp)
            false
        } finally {
            logTagV(TAG, "安装结束, 清理apk资源")
            deleteApkFile(file, pkgName)
        }
    }
}

suspend fun installXApkApp(filepathApk: String, pkgName: String): Boolean {
    return withContext(Dispatchers.IO) {
        val file = File(filepathApk)
        try {
            val pi = packageManager.packageInstaller
            val sessionId =
                pi.createSession(PackageInstaller.SessionParams(PackageInstaller.SessionParams.MODE_FULL_INSTALL))
            val session = pi.openSession(sessionId)

            if (!file.exists()) {
                logTagW(TAG, "没有找到apk文件:${file.absolutePath}")
                return@withContext false
            }

            val apkList = findApkFiles(filepathApk).map { it.absolutePath }
            for (path in apkList) {
                val xApkFile = File(path)
                val outStream = session.openWrite(xApkFile.name, 0, -1)
                FileInputStream(xApkFile).use { it.copyTo(outStream) }
                session.fsync(outStream)
                outStream.close()
            }

            val sessionIntent = Intent(ACTION_INSTALL_COMPLETE).apply {
                `package` = appContext.packageName
                putExtra("installXApk", true)
            }
            session.commit(
                PendingIntent.getBroadcast(
                    appContext,
                    sessionId,
                    sessionIntent,
                    PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
                ).intentSender
            )
            session.close()
            true

        } catch (exp: Exception) {
            logTagW(TAG, "安裝xApk失敗失败", tr = exp)
            false
        } finally {
            logTagV(TAG, "安装结束, 清理xApk资源")
            deleteDirectory(file.absolutePath.substringBeforeLast("/"))
        }
    }
}

private fun findApkFiles(directoryPath: String): List<File> {
    val directory = File(directoryPath)
    if (!directory.exists()) {
        return emptyList()
    }

    val parentFile = directory.parentFile
    parentFile?.let {
        return it.walk()
            .filter { file ->
                file.isFile && file.extension.equals(
                    "apk",
                    ignoreCase = true
                ) && !file.extension.equals("xapk", ignoreCase = true)
            }.toList()
    } ?: return emptyList()
}

/**
 * 删除已安装的apk文件
 * 会删除当前安装的apk文件
 * 同时会删除这个包名对应的其他apk文件(为了防止异常情况, 数据越下载越多)
 */
private fun deleteApkFile(apkFile: File, pkgName: String) {
    if (apkFile.exists()) {
        apkFile.delete()
    }
    // 再卸载这个包名下的所有apk
    val md5Name = runBlocking { pkgName.md5() }
    apkFile.parentFile?.let { dir ->
        dir.listFiles { _, name ->
            md5Name in name
        }?.forEach {
            it.delete()
        }
    }
}

private fun deleteDirectory(path: String): Boolean {
    val directory = File(path)
    if (directory.exists()) {
        val files = directory.listFiles()
        if (null != files) {
            for (file in files) {
                if (file.isDirectory) {
                    //递归删除子文件夹
                    deleteDirectory(file.absolutePath)
                } else {
                    //删除子文件
                    file.delete()
                }
            }
        }
    }
    //删除主文件夹
    return directory.delete()
}


/**
 * 扫描系统中安装的应用
 */
suspend fun loadAppsInfo() = withContext(Dispatchers.IO) {
    val filterIntent = Intent(Intent.ACTION_MAIN, null)
    filterIntent.addCategory(Intent.CATEGORY_LAUNCHER)
    val apps = packageManager.getInstalledPackages(0)
    //图标获取方式 获取本地定制图标
    val resolvent = packageManager.queryIntentActivities(filterIntent, PackageManager.MATCH_ALL)
    apps.mapNotNull {
        try {
            val appInfo = it.applicationInfo ?: return@mapNotNull null

            val appName = appInfo.loadLabel(packageManager).toString()
            var appIcon = resolvent.find { info ->
                info.activityInfo.packageName == appInfo.packageName
            }?.activityInfo?.loadIcon(packageManager)

            if (appIcon == null) {
                appIcon = appInfo.loadIcon(packageManager)
            }

            val packageName = it.packageName
            val versionCode = it.longVersionCode
            val versionName = it.versionName ?: versionCode.toString()
            val totalSize = queryPackageSize(packageName)
            if (totalSize == 0L) return@mapNotNull null

            if (it.packageName.contains("wemeet")) {
                logTagD(
                    "Chen",
                    "!!pkg:${it.packageName} uninstalling:${packageName in LocalAppManager.unInstallingAppPkgSet}, size:${LocalAppManager.unInstallingAppPkgSet.size}"
                )
            }

            LocalAppInfo(
                appName,
                packageName,
                appIcon!!,
                versionName,
                versionCode,
                totalSize,
                isSystemApp = appInfo.flags and ApplicationInfo.FLAG_SYSTEM != 0,
                uninstalling = packageName in LocalAppManager.unInstallingAppPkgSet
            )

        } catch (exp: Exception) {
            logTagW(TAG, "解析appInfo失败,", tr = exp)
            null
        }
    }.sortedBy {
        it.pkgName
    }.also {
        it.forEach {
            if (it.pkgName.contains("wemeet")) {
                logTagD(
                    "Chen",
                    "!!"
                )
            }
        }
    }
}


/**
 * 查询应用大小
 */
suspend fun queryPackageSize(pkgName: String): Long {
    return withContext(Dispatchers.Default) {
        val ai = packageManager.getApplicationInfo(pkgName, 0)
        val storageStats = storageStateManager.queryStatsForUid(ai.storageUuid, ai.uid)
        storageStats.appBytes
    }
}

/**
 * 启动对应App
 */
fun bootApp(pkgName: String) {
    val intent = makeIntentByPkgManager(pkgName)
    intent?.let {
        it.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        doWithoutCatch {
            appContext.startActivity(it)
        }
    }
}

fun makeIntentByPkgManager(pkgName: String) = packageManager.getLaunchIntentForPackage(pkgName)
