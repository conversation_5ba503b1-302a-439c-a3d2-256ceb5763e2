package com.czur.starry.device.appstore.download

import android.os.RemoteCallbackList
import com.czur.starry.device.appstore.IDownloadProcessCallback
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch

/**
 * Created by 陈丰尧 on 2022/9/8
 */
class DownInfoPublisher(scope: CoroutineScope) {
    private val broadcastChannel = Channel<ServicePublishTask<*>>(capacity = Channel.UNLIMITED)
    private val cbList = RemoteCallbackList<IDownloadProcessCallback>()

    init {
        scope.launch(Dispatchers.IO) {
            while (isActive) {
                val publishTask = broadcastChannel.receive()
                // 获取任务后, 发现没有注册callback, 则跳过这次任务
                if (cbList.registeredCallbackCount == 0) {
                    continue
                }
                when (publishTask) {
                    is ProcessPublishTask -> publicProcess(publishTask)
                    is DownloadTerminationPublishTask -> publishDownloadFail(publishTask)
                }
            }
        }
    }

    suspend fun publish(task: ServicePublishTask<*>) {
        broadcastChannel.send(task)
    }

    fun registerStatusCallback(cb: IDownloadProcessCallback?) {
        cb?.let { cbList.register(it) }
    }

    fun unregisterStatusCallback(cb: IDownloadProcessCallback?) {
        cb?.let { cbList.unregister(it) }
    }

    private fun publicProcess(processTask: ProcessPublishTask) {
        cbList.beginBroadcast()
        for (i in 0 until cbList.registeredCallbackCount) {
            val cb = cbList.getBroadcastItem(i)
            cb.onInfoUpdate(processTask.publishData)
        }
        cbList.finishBroadcast()
    }

    private fun publishDownloadFail(failTask: DownloadTerminationPublishTask) {
        cbList.beginBroadcast()
        for (i in 0 until cbList.registeredCallbackCount) {
            val cb = cbList.getBroadcastItem(i)
            cb.onDownloadTerminate(failTask.publishData.pkg, failTask.publishData.reason)
        }
        cbList.finishBroadcast()
    }
}