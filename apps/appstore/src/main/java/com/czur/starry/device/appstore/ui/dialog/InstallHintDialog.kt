package com.czur.starry.device.appstore.ui.dialog

import com.czur.starry.device.appstore.R
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.StarryDevLocale
import com.czur.starry.device.baselib.utils.getString
import com.czur.starry.device.baselib.view.floating.FloatShowMode
import com.czur.starry.device.baselib.view.floating.common.DoubleBtnCommonFloat

/**
 * Created by 陈丰尧 on 2023/5/22
 */
private const val MEETING_APP_TAG = "VideoMeeting"
private const val MEETING_APP_TAG_NA = "VideoMeetingNa"
private const val VIDEO_APP_TAG = "VideoPlayer"
private const val VIDEO_APP_TAG_NA = "VideoPlayerNa"

val meetingAppTag:String by lazy(LazyThreadSafetyMode.NONE) {
    when(Constants.starryHWInfo.salesLocale){
        StarryDevLocale.Mainland -> MEETING_APP_TAG
        StarryDevLocale.Overseas -> MEETING_APP_TAG_NA
    }
}

val videoAppTag:String by lazy(LazyThreadSafetyMode.NONE) {
    when(Constants.starryHWInfo.salesLocale){
        StarryDevLocale.Mainland -> VIDEO_APP_TAG
        StarryDevLocale.Overseas -> VIDEO_APP_TAG_NA
    }
}


enum class InstallHintDialogType {
    MEETING,  // 会议类
    VIDEO,  // 影音娱乐类
    OTHER;  // 其他类型
}

fun InstallHintDialog(
    type: InstallHintDialogType,
    onInstallClick: (installHintDialog: DoubleBtnCommonFloat) -> Unit
): DoubleBtnCommonFloat {
    val content = when (type) {
        InstallHintDialogType.VIDEO -> getString(R.string.dialog_msg_install_video_entertainment)
        InstallHintDialogType.OTHER -> getString(R.string.dialog_msg_install_other)
        else -> throw IllegalArgumentException("$type is not supported")
    }
    val cancelBtnText = when (type) {
        InstallHintDialogType.OTHER -> getString(R.string.dialog_normal_cancel1)
        else -> getString(R.string.dialog_normal_cancel2)
    }

    return DoubleBtnCommonFloat(
        content = content,
        cancelBtnText = cancelBtnText,
        confirmBtnText = getString(R.string.dialog_btn_install),
        showMode = FloatShowMode.SINGLE,
        outSideDismiss = true,
        onCommonClick = { dialog, position ->
            if (position == DoubleBtnCommonFloat.DOUBLE_FLOAT_BTN_CANCEL) {
                dialog.dismiss()
            } else {
                onInstallClick.invoke(dialog)
            }
        }
    )
}