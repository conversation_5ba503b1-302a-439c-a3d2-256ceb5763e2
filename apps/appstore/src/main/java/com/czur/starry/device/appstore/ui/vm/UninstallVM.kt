package com.czur.starry.device.appstore.ui.vm

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import com.czur.starry.device.appstore.entity.LocalAppInfo
import com.czur.starry.device.appstore.manager.LocalAppManager

/**
 * Created by 陈丰尧 on 2021/10/21
 */
class UninstallVM(application: Application) : AndroidViewModel(application) {
    companion object{
        private const val TAG = "UninstallVM"
    }

    suspend fun uninstallApk(apk: LocalAppInfo){
        LocalAppManager.uninstallApk(apk)
    }


}