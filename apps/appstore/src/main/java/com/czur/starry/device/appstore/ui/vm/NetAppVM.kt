package com.czur.starry.device.appstore.ui.vm

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.MediatorLiveData
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.appstore.download.DownloadUtil
import com.czur.starry.device.appstore.entity.AllAppVO
import com.czur.starry.device.appstore.entity.NetAppItem
import com.czur.starry.device.appstore.manager.AppStoreManager
import com.czur.starry.device.appstore.manager.LocalAppManager
import com.czur.starry.device.appstore.manager.LocalAppManager.INSTALL_TIME_OUT
import com.czur.starry.device.appstore.util.ToastUtil
import com.czur.starry.device.appstore.util.bootApp
import com.czur.starry.device.baselib.utils.DifferentLiveData
import com.czur.starry.device.baselib.utils.appContext
import com.czur.starry.device.baselib.utils.data.LiveDataDelegate
import com.czur.starry.device.baselib.utils.data.NullableLiveDataDelegate
import com.czur.starry.device.baselib.utils.isNetworkConnected
import com.czur.starry.device.baselib.utils.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.sample
import kotlinx.coroutines.isActive

/**
 * Created by 陈丰尧 on 2021/10/26
 */
class NetAppVM(application: Application) : AndroidViewModel(application) {
    val allAppsLive = MediatorLiveData<List<AllAppVO>>()
    private var allApps by LiveDataDelegate(allAppsLive, emptyList())

    val lastLoadNetAppResLive: DifferentLiveData<Boolean> = DifferentLiveData(true)
    var lastLoadNetAppRes: Boolean by LiveDataDelegate(lastLoadNetAppResLive)

    private val netAppsLive = DifferentLiveData<List<NetAppItem>>()
    private var netApps by NullableLiveDataDelegate(netAppsLive)

    private val appStoreManager = AppStoreManager
    private val downloadUtil = DownloadUtil(application)

    private val makeAllAppFlow = MutableStateFlow(0L)

    // 定时刷新Flow
    private val refreshFlow = MutableStateFlow(0L)

    companion object {
        private const val TAG = "NetAppVM"
    }

    init {
        downloadUtil.init {
            launch {
                makeAllAppFlow.debounce(100L).collect {
                    makeAllAppVO()
                }
            }

            allAppsLive.addSource(netAppsLive) {
                updateAllAppFlow()
            }
            allAppsLive.addSource(LocalAppManager.appsLive) {
                if (it.isNotEmpty()) {
                    updateAllAppFlow()
                }
            }
            allAppsLive.addSource(downloadUtil.getDownloadProgressLive()!!) { updateAllAppFlow() }
            allAppsLive.addSource(LocalAppManager.installingPkgListLive) { updateAllAppFlow() }

            launch {
                refreshFlow
                    .sample(INSTALL_TIME_OUT)
                    .collect {
                        logTagD(TAG, "刷新AppStore")
                        updateAllAppFlow()
                    }
            }

            launch {
                while (isActive) {
                    delay(INSTALL_TIME_OUT * 2) // 不需要咔咔刷新
                    refreshFlow.value = System.currentTimeMillis()
                }
            }
        }

    }

    private fun updateAllAppFlow() {
        makeAllAppFlow.value = System.currentTimeMillis()
        refreshFlow.value = System.currentTimeMillis()
    }

    private fun makeAllAppVO() {
        logTagD(TAG, "makeAllAppVO")
        launch(Dispatchers.IO) {
            if (netApps == null) return@launch
            allApps = (netApps ?: emptyList<NetAppItem>()).map {
                val hasInstall = LocalAppManager.hasInstallThisApp(it.packageName)
                val canUpdate =
                    if (hasInstall) LocalAppManager.canUpDate(
                        it.packageName,
                        it.versionCode
                    ) else false
                val downloading = downloadUtil.isDownloading(it.packageName)
                val downloadProgress =
                    downloadUtil.getDownloadPercent(it.packageName)
                val installing = LocalAppManager.isInstallingThisApp(it.packageName)
                AllAppVO(
                    id = it.id,
                    name = it.name,
                    versionName = it.versionName,
                    size = it.size,
                    iconUrl = it.iconUrl,
                    pkgName = it.packageName,
                    hasInstall = hasInstall,
                    canUpdate = canUpdate,
                    downloading = downloading,
                    installing = installing,
                    downloadProgress = downloadProgress,
                    tags = it.tags
                )
            }
        }

    }

    /**
     * 加载网络App
     * @param reset 是否重置历史数据
     */
    suspend fun loadNetApps(reset: Boolean = false) {
        if (reset) {
            // 因为使用DifferentLiveData, 所以如果不重置一次, 那么LiveData不会更新
            netApps = null
        }
        netApps = appStoreManager.loadAppStoreApps()
    }

    /**
     * 下载并安装Apk
     */
    fun downloadAndInstall(pkgName: String) {
        if (!isNetworkConnected(appContext)) {
            launch {
                ToastUtil.showNetErrorToast()
            }
            return
        }

        val targetApp = netApps?.find {
            it.packageName == pkgName
        } ?: return
        logTagD(TAG, "downloadAndInstall")
        downloadUtil.addDownloadReq(
            targetApp.downloadUrl,
            targetApp.packageName,
            targetApp.versionCode,
            targetApp.size,
            targetApp.downloadUrl.endsWith(".xapk")
        )
    }

    /**
     * 打开App
     */
    fun openApp(pkgName: String) {
        bootApp(pkgName)
    }

    /**
     * 取消下载App
     */
    fun cancelDownload(pkgName: String) {
        downloadUtil.cancelAllDownload(pkgName)

    }


    override fun onCleared() {
        super.onCleared()
        downloadUtil.recycler()
    }
}