package com.czur.starry.device.appstore.ui.vm

import android.app.Application
import android.content.Intent
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.asFlow
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.appstore.R
import com.czur.starry.device.appstore.entity.LocalAppInfo
import com.czur.starry.device.appstore.manager.LocalAppManager
import com.czur.starry.device.appstore.net.IFeedbackServer
import com.czur.starry.device.baselib.common.BootParam.BOOT_KEY_PAGE_MENU_NAME
import com.czur.starry.device.baselib.common.BootParam.BOOT_KEY_PAGE_MENU_NAVIGATE
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.network.HttpManager
import com.czur.starry.device.baselib.utils.getString
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext

/**
 * Created by 陈丰尧 on 2021/10/26
 */
class LocalAppVM(application: Application) : AndroidViewModel(application) {
    companion object {
        private const val TAG = "UninstallVM"
    }

    val appsLive: LiveData<List<LocalAppInfo>> = LocalAppManager.appsLive
    val localApps: List<LocalAppInfo>
        get() = LocalAppManager.apps

    private var _currentMenuPageFlow = MutableStateFlow<String?>(null)
    var currentMenuPageFlow = _currentMenuPageFlow.filterNotNull()

    private var _currentUninstallAppNameFlow = MutableStateFlow<String?>(null)
    var currentUninstallAppNameFlow = _currentUninstallAppNameFlow.filterNotNull()

    private var _currentInstallAppNameFlow = MutableStateFlow<String?>(null)
    var currentInstallAppNameFlow = _currentInstallAppNameFlow.filterNotNull()

    val canUninstallApps = appsLive.asFlow().map {
        it.filter { app ->
            !app.isSystemApp
        }.map {
            it.copy(uninstalling = it.pkgName in LocalAppManager.unInstallingAppPkgSet)
        }
    }

    private val feedbackServer: IFeedbackServer by lazy { HttpManager.getService(BASE_URL = Constants.APP_STORE_BASE_URL) }


    fun initData() {
        LocalAppManager.loadApps()
    }

    fun handleIntent(intent: Intent) {
        intent.getStringExtra(BOOT_KEY_PAGE_MENU_NAME)?.let { name ->
            intent.getStringExtra(BOOT_KEY_PAGE_MENU_NAVIGATE)?.let {

                _currentMenuPageFlow.value = it
                if (it == getString(R.string.voice_command_uninstall)) {
                    _currentUninstallAppNameFlow.value = name
                }else {
                    _currentInstallAppNameFlow.value = name
                }
            }
        }

    }

    fun onNavigateReset() {
        _currentMenuPageFlow.value = null
        _currentUninstallAppNameFlow.value = null
        _currentInstallAppNameFlow.value = null
    }

    /**
     * 添加反馈
     * @param appName String 要反馈的应用名
     */
    suspend fun addFeedBackAppName(appName: String): Boolean {
        return withContext(Dispatchers.IO) {
            logTagD(TAG, "添加反馈:${appName}")
            val entity = feedbackServer.addAppFeedback(appName)
            if (entity.isSuccess) {
                logTagI(TAG, "addFeedBackAppName success")
                true
            } else {
                logTagW(TAG, "addFeedBackAppName failed: ${entity.code} - ${entity.msg}")
                false
            }
        }
    }

}