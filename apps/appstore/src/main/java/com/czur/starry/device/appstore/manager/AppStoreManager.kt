package com.czur.starry.device.appstore.manager

import com.czur.starry.device.appstore.entity.NetAppItem
import com.czur.starry.device.appstore.net.IAppStoreMainlandService
import com.czur.starry.device.appstore.net.IAppStoreOverseasService
import com.czur.starry.device.appstore.net.IAppStoreService
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.Constants.APP_STORE_BASE_URL
import com.czur.starry.device.baselib.common.StarryDevLocale
import com.czur.starry.device.baselib.network.HttpManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Created by 陈丰尧 on 2021/10/26
 */
object AppStoreManager {
    private val appStoreService: IAppStoreService by lazy {
        when(Constants.starryHWInfo.salesLocale) {
            StarryDevLocale.Mainland -> HttpManager.getService<IAppStoreMainlandService>(BASE_URL = APP_STORE_BASE_URL)
            StarryDevLocale.Overseas -> HttpManager.getService<IAppStoreOverseasService>(BASE_URL = APP_STORE_BASE_URL)
        }
    }

    /**
     * 加载网络中的App
     */
    suspend fun loadAppStoreApps(): List<NetAppItem> {
        return withContext(Dispatchers.IO) {
            appStoreService.getCallRecords().withCheck().bodyList
        }
    }

    suspend fun installCallback(versionCode: Int, pkgName: String) {
        withContext(Dispatchers.IO) {
            appStoreService.installCallBack(versionCode.toString(), pkgName)
        }
    }

    suspend fun getTagList() = withContext(Dispatchers.IO) {
        appStoreService.getTagList()
    }


}