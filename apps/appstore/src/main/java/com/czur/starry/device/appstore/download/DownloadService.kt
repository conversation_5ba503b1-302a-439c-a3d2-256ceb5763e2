package com.czur.starry.device.appstore.download

import android.content.Intent
import android.os.IBinder
import androidx.lifecycle.LifecycleService
import androidx.lifecycle.lifecycleScope
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.appstore.DOWNLOAD_STATUS_DOWNLOADING
import com.czur.starry.device.appstore.DOWNLOAD_STATUS_INSTALLING
import com.czur.starry.device.appstore.DOWNLOAD_TERMINAL_REASON_CANCEL
import com.czur.starry.device.appstore.DOWNLOAD_TERMINATE_REASON_FAIL
import com.czur.starry.device.appstore.IAppDownload
import com.czur.starry.device.appstore.IDownloadProcessCallback
import com.czur.starry.device.appstore.R
import com.czur.starry.device.appstore.manager.AppStoreManager
import com.czur.starry.device.appstore.manager.LocalAppManager
import com.czur.starry.device.baselib.utils.DifferentLiveData
import com.czur.starry.device.baselib.utils.ONE_MIN
import com.czur.starry.device.baselib.utils.basic.otherwise
import com.czur.starry.device.baselib.utils.basic.yes
import com.czur.starry.device.baselib.utils.createUUID
import com.czur.starry.device.baselib.utils.data.LiveDataDelegate
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.onOnMainScope
import com.czur.starry.device.baselib.utils.toast
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.channels.Channel.Factory.UNLIMITED
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.ConcurrentMap
import java.util.zip.ZipFile


private const val TAG = "DownloadService"

class DownloadService : LifecycleService() {
    private val binder by lazy { DownloadBinder() }

    private var binderCount = 0
    private val client: DownloadClient = DownloadClient(this)
    private val scope = CoroutineScope(Job())

    private var finishSelfJob: Job? = null

    // apk的安装是排队进行
    private var installChannel = Channel<InstallInfo>(capacity = UNLIMITED)

    override fun onCreate() {
        super.onCreate()

        client.downloadDoneListener = {
            checkNeedStopSelf()
        }
        LocalAppManager.registerAppReceiver()

        launch {
            while (isActive) {
                val installInfo = installChannel.receive()
                logTagV(TAG, "开始安装:${installInfo.pkgName}")
                val installRes =
                    LocalAppManager.installApk(installInfo.pkgName, installInfo.filePath, installInfo.apkType == ApkType.XAPK)
                logTagV(TAG, "安装完成:${installInfo.pkgName},安装成功:${installRes}")
                if (!installRes) {
                    onOnMainScope {
                        // 显示安装失败Toast
                        toast(R.string.toast_install_app_fail)
                    }
                }
                checkNeedStopSelf()
            }

        }

        launch {
            LocalAppManager.loadApps()  // 预加载按照的应用
        }
    }

    override fun onBind(intent: Intent): IBinder {
        super.onBind(intent)
        logTagV(TAG, "onBindService")
        binderCount++
        finishSelfJob?.cancel()
        return binder
    }

    override fun onUnbind(intent: Intent?): Boolean {
        binderCount--
        logTagD(TAG, "onUnbind-binderCount:${binderCount}")
        checkNeedStopSelf()
        return super.onUnbind(intent)
    }

    /**
     * 检查是否需要结束自己
     */
    private fun checkNeedStopSelf() {
        scope.launch {
            if (canStopSelf()) {
                createStopJob()
            }

        }
    }

    private suspend fun canStopSelf(): Boolean {
        return withContext(Dispatchers.IO) {
            if (binderCount > 0) {
                logTagV(TAG, "还有Client在绑定, 不结束")
                return@withContext false
            }

            if (client.hasDowningTask()) {
                logTagV(TAG, "还有下载任务, 不结束")
                return@withContext false
            }

            if (!installChannel.isEmpty) {
                logTagV(TAG, "还有等待安装的APK, 不结束")
            }
            true
        }
    }

    private fun createStopJob() {
        finishSelfJob?.cancel()
        logTagD(TAG, "创建定时结束DownloadService任务")
        finishSelfJob = scope.launch {
            delay(3 * ONE_MIN)
            if (canStopSelf()) {
                logTagD(TAG, "没有绑定对象, 没有下载任务, 停止DownloadService")
                stopSelf()
            } else {
                logTagI(TAG, "还不能停止DownloadService")
            }
        }
    }


    inner class DownloadBinder : IAppDownload.Stub() {
        val downloadInfoUpdateTimeLive = DifferentLiveData(0L)
        private var downloadInfoUpdateTime by LiveDataDelegate(downloadInfoUpdateTimeLive)

        private val downloadInfoMap: ConcurrentMap<String, DownloadProgress> =
            ConcurrentHashMap()

        private val downloadPkgMap = mutableMapOf<String, String>()

        private val infoPublisher = DownInfoPublisher(lifecycleScope)

        init {
            downloadInfoUpdateTimeLive.observe(this@DownloadService) {

                launch(Dispatchers.IO) {
                    infoPublisher.publish(ProcessPublishTask(createPublishList()))
                }
            }
        }

        /**
         * 构建发布数据
         */
        private fun createPublishList(): List<DownloadPublishInfo> {
            val publishInfoMap = mutableMapOf<String, DownloadPublishInfo>()

            // 确定下载中的进度信息
            downloadInfoMap.forEach { (_, downloadProcess) ->
                val info = publishInfoMap.getOrPut(downloadProcess.pkgName) {
                    DownloadPublishInfo.create(downloadProcess.pkgName)
                }
                info.process = downloadProcess.progress
                info.status = DOWNLOAD_STATUS_DOWNLOADING
            }
            // 确定安装中的进度信息
            LocalAppManager.getAllInstallingPkgName().forEach { pkgName ->
                val info = publishInfoMap.getOrPut(pkgName) { DownloadPublishInfo.create(pkgName) }
                info.process = 100
                info.status = DOWNLOAD_STATUS_INSTALLING
            }
            return publishInfoMap.values.toList()
        }

        /**
         * 添加下载请求
         */
        override fun addDownloadReq(request: DownloadRequest): String {
            logTagV(TAG, "添加下载请求:${request.pkgName}")
            finishSelfJob?.cancel()
            val downloadId = createDownloadId()
            downloadInfoMap[downloadId] =
                DownloadProgress(downloadId, request.pkgName, request.versionCode, request.isXapk)

            scope.launch {
                downloadInfoMap[downloadId] =
                    DownloadProgress(downloadId, request.pkgName, request.versionCode, request.isXapk)
                refreshDownloadInfo()
                val saveFileName = request.saveFileName.isEmpty().yes {
                    if (request.isXapk) {
                        DownloadUtil.createXApkFolderName(request.pkgName)
                    } else {
                        DownloadUtil.createFileName(request.pkgName)
                    }
                }.otherwise {
                    request.saveFileName
                }
                client.addDownload(
                    request.downloadUrl,
                    downloadId,
                    saveFileName,
                    request.estimatedSize,
                    request.isXapk
                ) { max, saved, finish, filePath ->
                    val downloadProgress = downloadInfoMap.getOrPut(downloadId) {
                        DownloadProgress(downloadId, request.pkgName, request.versionCode, request.isXapk)
                    }

                    if (saved == -2L) {
                        logTagW(TAG, "下载失败")
                        scope.launch {
                            infoPublisher.publish(
                                DownloadTerminationPublishTask(
                                    request.pkgName,
                                    DOWNLOAD_TERMINATE_REASON_FAIL
                                )
                            )
                        }
                        removeDownloadInfo(downloadId)
                        refreshDownloadInfo()

                        scope.launch {
                            logTagD(TAG, "删除临时文件")
                            val filePathWithoutXAPK = filePath.replace(".xapk", "")
                            deleteDirectory(filePathWithoutXAPK)
                            onOnMainScope {
                                // 显示安装失败Toast
                                toast(R.string.toast_install_app_fail)
                            }
                        }
                        return@addDownload
                    }

                    if (finish) {
                        logTagD(TAG, "下载完成")
                        downloadProgress.progress = 100
                        downloadProgress.finish = true
                        downloadProgress.filePath = filePath
                        refreshDownloadInfo()
                        scope.launch {
                            if (downloadProgress.isXapk) {
                                installXAPK(downloadProgress)
                            } else {
                                installAPK(downloadProgress)
                            }
                        }
                    } else {
                        val nowProgress = calculateProgress(saved, max)
                        if (downloadProgress.progress != nowProgress) {
                            downloadProgress.progress = nowProgress
                            logTagV(
                                TAG,
                                "progress;${downloadProgress.pkgName}-${nowProgress} - ${downloadInfoUpdateTimeLive.value ?: 0}"
                            )
                            refreshDownloadInfo()
                        }
                    }
                }
            }
            downloadPkgMap[request.pkgName] = downloadId
            refreshDownloadInfo()
            return downloadId
        }

        private fun deleteDirectory(path: String): Boolean {
            val directory = File(path)
            if(directory.exists()) {
                val files = directory.listFiles()
                if(null!=files) {
                    for (file in files) {
                        if(file.isDirectory) {
                            //递归删除子文件夹
                            deleteDirectory(file.path)
                        } else {
                            //删除子文件
                            file.delete()
                        }
                    }
                }
            }
            //删除主文件夹
            return directory.delete()
        }

        override fun isDownloading(packageName: String): Boolean {
            val downloadId = downloadPkgMap[packageName] ?: ""
            return downloadInfoMap[downloadId]?.isDownloading ?: false
        }

        override fun getDownloadPercent(packageName: String): Int {
            val downloadId = downloadPkgMap[packageName] ?: ""
            return downloadInfoMap[downloadId]?.progress ?: 0
        }

        override fun removeDownloadInfo(downloadId: String) {
            val info = downloadInfoMap.remove(downloadId)
            info?.let {
                downloadPkgMap.remove(it.pkgName)
            }
            refreshDownloadInfo()
        }

        override fun cancelAllDownload(pkgName: String) {
            val downloadId = downloadPkgMap[pkgName]
            downloadId?.let {
                client.cancelAllDownload(it)
                downloadInfoMap.remove(it)
                refreshDownloadInfo()
                launch {
                    infoPublisher.publish(
                        DownloadTerminationPublishTask(
                            pkgName,
                            DOWNLOAD_TERMINAL_REASON_CANCEL
                        )
                    )
                }
            }
        }

        override fun registerStatusCallback(cb: IDownloadProcessCallback?) {
            infoPublisher.registerStatusCallback(cb)
        }

        override fun unregisterStatusCallback(cb: IDownloadProcessCallback?) {
            infoPublisher.unregisterStatusCallback(cb)
        }

        private fun refreshDownloadInfo() {
            downloadInfoUpdateTime = System.currentTimeMillis()
        }

        private fun calculateProgress(current: Long, max: Long): Int {
            val p = (current * 100 / max).toInt()
            if (p < 0) return 0
            if (p >= 100) return 100
            return p
        }


        private fun createDownloadId(): String = createUUID(true)

        private suspend fun installAPK(progress: DownloadProgress) {
            logTagV(TAG, "加入安装队列:${progress.pkgName}")
            LocalAppManager.addWaitInstallPkg(progress.pkgName)
            installChannel.send(InstallInfo(ApkType.APK ,progress.pkgName, progress.filePath))
            // 移除安装进度信息
            removeDownloadInfo(progress.downloadId)
            // 通知后台
            scope.launch {
                logTagD(TAG, "通知服务器 统计数据")
                AppStoreManager.installCallback(
                    progress.versionCode,
                    progress.pkgName
                )
            }
        }

        private suspend fun installXAPK(progress: DownloadProgress) {
            unzipXAPK(progress.filePath)
            LocalAppManager.addWaitInstallPkg(progress.pkgName)
            installChannel.send(InstallInfo(ApkType.XAPK, progress.pkgName, progress.filePath))
            removeDownloadInfo(progress.downloadId)
            scope.launch {
                logTagD(TAG, "通知服务器 统计数据")
                AppStoreManager.installCallback(
                    progress.versionCode,
                    progress.pkgName
                )
            }
        }

        private fun unzipXAPK(filePath: String): String? {
            val xApkFile = File(filePath)
            if (!xApkFile.exists()) {
                return null
            }

            val directoryPath = xApkFile.parent
            val directory = directoryPath?.let { File(it) }
            if (directory != null) {
                if (!directory.exists()) {
                    directory.mkdirs()
                }
            }

            ZipFile(xApkFile).use { zip ->
                zip.entries().asSequence().forEach { entry ->
                    zip.getInputStream(entry).use { input ->
                        val entryFile = File(directory, File(entry.name).name)
                        entryFile.outputStream().use { output ->
                            input.copyTo(output)
                        }
                    }
                }
            }
            return directoryPath
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        LocalAppManager.cleared()
    }

}


enum class ApkType{APK,XAPK}

/**
 * 安装信息
 */
private data class InstallInfo(
    val apkType: ApkType,
    val pkgName: String,
    val filePath: String
)

class DownloadProgress(
    val downloadId: String,
    val pkgName: String,
    val versionCode: Int,
    val isXapk:Boolean
) {
    var finish: Boolean = false
    val isDownloading: Boolean
        get() = !finish
    var progress: Int = 0
    var filePath: String = ""
}