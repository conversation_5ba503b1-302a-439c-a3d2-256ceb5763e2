package com.czur.starry.device.appstore.ui

import android.os.Bundle
import androidx.core.os.bundleOf
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.appstore.R
import com.czur.starry.device.appstore.databinding.FragmentNetAppsBinding
import com.czur.starry.device.appstore.entity.AllAppVO
import com.czur.starry.device.appstore.manager.LocalAppManager
import com.czur.starry.device.appstore.ui.adapter.NetAppAdapter
import com.czur.starry.device.appstore.ui.dialog.InstallHintDialog
import com.czur.starry.device.appstore.ui.dialog.InstallHintDialogType
import com.czur.starry.device.appstore.ui.vm.LocalAppVM
import com.czur.starry.device.appstore.ui.vm.NetAppVM
import com.czur.starry.device.baselib.base.v2.fragment.CZViewBindingFragment
import com.czur.starry.device.baselib.utils.closeDefChangeAnimations
import com.czur.starry.device.baselib.utils.doOnItemClick
import com.czur.starry.device.baselib.utils.doWithoutCatch
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.has
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.baselib.view.floating.common.DoubleBtnCommonFloat
import kotlinx.coroutines.delay
import kotlinx.coroutines.withTimeoutOrNull

/**
 * Created by 陈丰尧 on 2021/10/21
 */
class NetAppFragment : CZViewBindingFragment<FragmentNetAppsBinding>() {
    companion object {
        const val NET_TAG_ALL = "all"
        private const val TAG = "AllAppFragment"
        private const val KEY_NET_TAG = "tag"

        fun instance(tagCode: String = NET_TAG_ALL): NetAppFragment {
            return NetAppFragment().apply {
                arguments = bundleOf(
                    KEY_NET_TAG to tagCode
                )
            }
        }
    }

    private val netAppAdapter = NetAppAdapter()

    private val localAppVM: LocalAppVM by viewModels({ requireActivity() })
    private val netAppVM: NetAppVM by viewModels({ requireActivity() })
    private var tagCode = ""
    private var installHintDialog: DoubleBtnCommonFloat? = null

    override fun FragmentNetAppsBinding.initBindingViews() {
        tagCode = arguments?.getString(KEY_NET_TAG) ?: NET_TAG_ALL
        logTagV(TAG, "当前tagCode:${tagCode}")

        appListRv.layoutManager = GridLayoutManager(requireContext(), 2)
        appListRv.adapter = netAppAdapter
        appListRv.closeDefChangeAnimations()

        appListRv.doOnItemClick { vh, view ->
            val itemData = netAppAdapter.getData(vh.bindingAdapterPosition)
            when (view.id) {
                R.id.itemInstallBtn, R.id.itemUpdateBtn -> {
                    // 安装 / 升级 APK
                    doInstall(itemData)
                    true
                }

                R.id.itemOpenBtn -> {
                    // 打开APK
                    netAppVM.openApp(itemData.pkgName)
                    true
                }

                R.id.itemCancelDownBtn -> {
                    // 取消下载
                    netAppVM.cancelDownload(itemData.pkgName)
                    true
                }

                else -> false
            }
        }

        tryAgainBtn.setOnClickListener {
            loadNetApps(true)
        }
    }

    private fun FragmentNetAppsBinding.loadNetApps(reset: Boolean = false) {
        logTagV(TAG, "加载网络数据")
        launch {
            try {
                progressBar.show()
                allAppGroup.gone()
                emptyGroup.gone()

                netAppVM.loadNetApps(reset)
                netAppVM.lastLoadNetAppRes = true
            } catch (exp: Throwable) {
                logTagE(TAG, "加载网络应用列表失败", tr = exp)
                netAppVM.lastLoadNetAppRes = false
            }
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        binding.loadNetApps()

        // 防止页面刷新的时候, 先看见历史记录, 再出现加载失败
        netAppVM.lastLoadNetAppResLive.observe(viewLifecycleOwner) {
            if (it) {
                binding.loadErrorGroup.gone()
            } else {
                binding.loadErrorGroup.show()
            }
        }

        netAppVM.allAppsLive.observe(viewLifecycleOwner) {
            logTagV(TAG, "更新应用数据:${it.size}")
            val showData = if (tagCode == NET_TAG_ALL) {
                it
            } else {
                it.filter { app ->
                    app.tags.has { tag ->
                        tag.tagCode == tagCode
                    }
                }
            }
            netAppAdapter.setData(showData)
            binding.progressBar.gone()
            if (showData.isEmpty()) {
                binding.emptyGroup.show()
                binding.allAppGroup.gone()
            } else {
                binding.emptyGroup.gone()
                binding.allAppGroup.show()
            }
        }

        launch {
            //安装只在all tag页面
            if (tagCode == NET_TAG_ALL) {
                localAppVM.currentInstallAppNameFlow.collect {
                    logTagV(TAG, "当前安装App:${it}")
                    localAppVM.onNavigateReset()
                    val result = withTimeoutOrNull(7000) {
                        while (netAppAdapter.itemCount == 0) {
                            delay(200)
                        }
                        true
                    }
                    if (result == null) {
                        // 超时处理
                        return@collect
                    }
                    delay(1000)
                    val pos = findAppPos(it)
                    if (pos == -1) return@collect
                    val itemData = netAppAdapter.getData(pos)
                    val hasInstall = LocalAppManager.hasInstallThisApp(itemData.pkgName)
                    if (!hasInstall) {
                        delay(1000)
                        doInstall(itemData)
                    }

                }
            }
        }

    }

    private fun findAppPos(appName: String): Int {
        val pos = netAppAdapter.getItemPosition(appName)
        if (pos == -1) {
            logTagW(TAG, "未找到要安装的App:${appName}")
            return -1
        }
        (binding.appListRv.layoutManager as LinearLayoutManager).scrollToPositionWithOffset(pos, 1)
        return pos
    }

    private fun doInstall(itemData: AllAppVO) {
        when (itemData.hintType) {
            InstallHintDialogType.MEETING -> netAppVM.downloadAndInstall(pkgName = itemData.pkgName)    // 直接安装
            else -> {
                installHintDialog = InstallHintDialog(itemData.hintType) {
                    it.dismiss()
                    netAppVM.downloadAndInstall(pkgName = itemData.pkgName)
                }
                installHintDialog?.show()
            }
        }
    }

    override fun onPause() {
        super.onPause()
        installHintDialog?.dismiss()
    }

    fun reLoadData() {
        try {
            binding.loadNetApps(true)
        } catch (exp: Throwable) {
            logTagE(TAG, "重新加载网络应用列表失败", tr = exp)
        }
    }
}