<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    android:sharedUserId="android.uid.system">

    <uses-permission android:name="android.permission.GET_PACKAGE_SIZE" />
    <uses-permission android:name="android.permission.REQUEST_DELETE_PACKAGES" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
    <uses-permission android:name="android.permission.INSTALL_PACKAGES" />
    <uses-permission android:name="android.permission.PACKAGE_USAGE_STATS" />


    <application
        android:name=".App"
        android:icon="@mipmap/icon"
        android:label="@string/app_name"
        android:supportsRtl="true">

        <activity
            android:name=".MainActivity"
            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|uiMode|screenSize|navigation|layoutDirection"
            android:exported="true"
            android:theme="@style/CZUIAppTheme"
            android:windowSoftInputMode="adjustNothing">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <service
            android:name=".download.DownloadService"
            android:exported="true">
            <intent-filter>
                <action android:name="com.czur.starry.device.appstore.DownloadService" />
            </intent-filter>
        </service>

        <activity
            android:name=".InstallAppRemindDialogActivity"
            android:exported="true"
            android:launchMode="singleInstance"
            android:theme="@style/DialogActivity" />
    </application>

</manifest>