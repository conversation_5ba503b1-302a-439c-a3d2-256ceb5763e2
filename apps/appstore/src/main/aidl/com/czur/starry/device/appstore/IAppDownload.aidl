// IAppDownload.aidl
package com.czur.starry.device.appstore;

// Declare any non-default types here with import statements
import com.czur.starry.device.appstore.download.DownloadRequest;
import com.czur.starry.device.appstore.IDownloadProcessCallback;
interface IAppDownload {
    String addDownloadReq(in DownloadRequest request);

    boolean isDownloading(String packageName);

    int getDownloadPercent(String packageName);

    void removeDownloadInfo(String downloadId);

    void cancelAllDownload(String pkgName);

    void registerStatusCallback(IDownloadProcessCallback cb);
    void unregisterStatusCallback(IDownloadProcessCallback cb);
}