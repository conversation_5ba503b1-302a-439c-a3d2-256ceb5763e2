
plugins {
    id(libs.plugins.application.get().pluginId)
    id(libs.plugins.kotlinAndroid.get().pluginId)
    id("kotlin-parcelize")
}

private val pkgName = "com.czur.starry.device.hdmiin"
private val apkName = "CZHDMIIn"
android.buildFeatures.buildConfig = true
android {
    namespace = pkgName
    compileSdk = libs.versions.compileSdkVersion.get().toInt()

    defaultConfig {
        applicationId = namespace
        minSdk = libs.versions.minSdkVersion.get().toInt()
        targetSdk = libs.versions.targetSdkVersion.get().toInt()

        versionCode = libs.versions.appVersionCode.get().toInt()
        versionName = libs.versions.appVersionName.get()

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        renderscriptSupportModeEnabled = true

        setFlavorDimensions(listOf("constantEnv"))

        manifestPlaceholders["atyPlaceHolder"] = rootProject.ext["atyConfigChange"].toString()
    }

    // 签名
    signingConfigs {
        create("keyStore") {
            storeFile = file("${rootDir}/signature/starry.jks")
            storePassword = rootProject.ext["storePassword"].toString()
            keyAlias = rootProject.ext["keyAlias"].toString()
            keyPassword = rootProject.ext["keyPassword"].toString()
        }
    }

    buildTypes {
        val signConfig = signingConfigs.getByName("keyStore")
        debug {
            signingConfig = signConfig
            isMinifyEnabled = false
        }

        create("unsigned") {
            signingConfig = signConfig
            isDebuggable = false
            isMinifyEnabled = false
        }

    }
    productFlavors {
        create("envProduct") {
            dimension = "constantEnv"
            buildConfigField("Integer", "CONSTANT_ENV", "0")
        }
        create("envTest") {
            dimension = "constantEnv"
            buildConfigField("Integer", "CONSTANT_ENV", "1")
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }
    kotlinOptions {
        jvmTarget = "11"
    }
    android.applicationVariants.all {
        outputs.all {
            if (this is com.android.build.gradle
                .internal.api.ApkVariantOutputImpl
            ) {
                this.outputFileName = "${apkName}.apk"
            }
        }
    }
}

dependencies {
    // 测试依赖
    testImplementation(libs.junit)
    androidTestImplementation(libs.bundles.androidTest)

    implementation(project(":baselib"))
    implementation(project(":base:hdmiLib"))
    implementation(project(":base:meetlib"))
    implementation(project(":base:eShareLib"))
    implementation(project(":base:UILib"))
}
