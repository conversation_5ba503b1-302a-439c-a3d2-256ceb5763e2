package com.czur.keystone;

import android.annotation.SuppressLint;
import android.app.Application;
import android.content.Context;

import com.czur.czurutils.ProcessUtilsKt;
import com.czur.czurutils.log.CZURLogOutputSwitch;
import com.czur.czurutils.log.CZURLogStyle;
import com.czur.czurutils.log.CZURLogUtilsKt;

import java.io.File;

import kotlin.Unit;
import kotlin.jvm.functions.Function1;

/**
 * Created by 陈丰尧 on 2/8/21
 */
public class App extends Application {
    @SuppressLint("StaticFieldLeak")
    private static Context sContext;

    @Override
    public void onCreate() {
        super.onCreate();
        sContext = this;
        CZURLogUtilsKt.initLogUtil(this, czurLogConfig -> {
            czurLogConfig.setConfigTag("CZKeystone");
            czurLogConfig.logStyle(czurLogStyle -> {
                czurLogStyle.setShowArgIndex(false);
                czurLogStyle.setShowBorder(CZURLogStyle.BorderStyle.NONE);
                czurLogStyle.setShowHeader(false);
                return null;
            });
            czurLogConfig.logSaveConfig(czurLogSaveConfig -> {
                czurLogSaveConfig.setCustomLogSaveDir(
                        new File(getExternalFilesDir(null),
                                "czur/logs/" + ProcessUtilsKt.getProcessName())    // 先手动指定, 防止与项目原有的log框架冲突
                );
                czurLogSaveConfig.setMaxFileSizeInByte(512 * 1024L);
                czurLogSaveConfig.setMaxFileCount(1);
                czurLogSaveConfig.setLogFileName("CZLog");
                czurLogSaveConfig.setLogFileExtension("log");
                czurLogSaveConfig.logFileHead(czurLogFileHead -> {
                    czurLogFileHead.setAddFileHead(false);
                    return null;
                });
                return null;
            });
            return null;
        });
    }

    public static Context getContext(){
        return sContext;
    }
}
