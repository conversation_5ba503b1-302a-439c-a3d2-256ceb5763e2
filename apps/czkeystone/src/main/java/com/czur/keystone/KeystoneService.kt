package com.czur.keystone

import android.app.Service
import android.content.Intent
import android.hardware.Sensor
import android.hardware.SensorEvent
import android.hardware.SensorEventListener
import android.hardware.SensorManager
import android.os.IBinder
import com.czur.keystone.keystone.AutoCorrectionCZUR
import com.czur.starry.device.baselib.common.Constants.starryHWInfo
import com.czur.starry.device.baselib.common.hw.Q2Series
import com.czur.starry.device.baselib.common.hw.StudioSeries

class KeystoneService : Service() {

    companion object {
        const val KEYSTONE_ACTION_START: String = "com.czur.keystone.ACTION.START"
        private const val KEY_FORCE_AUTO_CORRECTION = "forceAutoCorrection"
    }

    private var mSensorManager: SensorManager? = null
    private var mSensor: Sensor? = null

    private val mAutoCorrection by lazy {
        AutoCorrectionCZUR()
    }

    override fun onCreate() {
        super.onCreate()
        if (starryHWInfo.series == StudioSeries || starryHWInfo.series == Q2Series) {
            LogUtil.d("KeystoneService - 不支持,直接退出")
            stopSelf()
        } else {
            LogUtil.d("KeystoneService - onCreate")
            mSensorManager = getSystemService(SENSOR_SERVICE) as SensorManager
            mSensor = mSensorManager!!.getDefaultSensor(Sensor.TYPE_ACCELEROMETER)
            //mSensorMagnetic = mSensorManager.getDefaultSensor(Sensor.TYPE_MAGNETIC_FIELD);
            startListenSensor()
        }
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        LogUtil.d("KeystoneService - onStartCommand")
        if (intent == null) {
            LogUtil.d("KeystoneService - onStartCommand - intent is null")
            return super.onStartCommand(intent, flags, startId)
        }

        if (starryHWInfo.series == StudioSeries || starryHWInfo.series == Q2Series) {
            LogUtil.d("KeystoneService - 不支持,直接退出")
            stopSelf()
            return super.onStartCommand(intent, flags, startId)
        }

        if (intent.getBooleanExtra(KEY_FORCE_AUTO_CORRECTION, false)) {
            LogUtil.d("强制更新一次梯形矫正")
            mAutoCorrection.doAutoCorrection()
        }
        return super.onStartCommand(intent, flags, startId)
    }

    private val mSensorEventListener: SensorEventListener = object : SensorEventListener {
        override fun onSensorChanged(sensorEvent: SensorEvent) {
            mAutoCorrection.onAccelerometerSensorChanged(sensorEvent)
        }

        override fun onAccuracyChanged(sensor: Sensor, i: Int) {
        }
    }

    private fun startListenSensor(): Boolean {
        if (mSensorManager != null && mSensor != null) {
            mSensorManager!!.registerListener(
                mSensorEventListener, mSensor,
                SensorManager.SENSOR_DELAY_GAME
            )
            LogUtil.d("KeystoneService - Start Listen Sensor Data")
            return true
        } else {
            LogUtil.d("KeystoneService - Start Listen Sensor Data Failure !")
            return false
        }
    }

    private fun stopListenSensor(): Boolean {
        if (mSensorManager != null && mSensor != null) {
            mSensorManager!!.unregisterListener(mSensorEventListener, mSensor)
            LogUtil.d("KeystoneService - Stop Listen Sensor Data")
            return true
        } else {
            LogUtil.d("KeystoneService - Stop Listen Sensor Data Failure !")
            return false
        }
    }

    override fun onBind(intent: Intent): IBinder? {
        // TODO: Return the communication channel to the service.
        throw UnsupportedOperationException("Not yet implemented")
    }

    override fun onDestroy() {
        super.onDestroy()
        LogUtil.d("KeystoneService - onDestroy")
        stopListenSensor()
    }
}
