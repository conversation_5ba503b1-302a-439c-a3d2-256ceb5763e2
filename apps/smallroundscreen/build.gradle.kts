@Suppress("DSL_SCOPE_VIOLATION") // TODO: Remove once KTIJ-19369 is fixed
plugins {
    alias(libs.plugins.application)
    alias(libs.plugins.kotlinAndroid)
    id("kotlin-parcelize")
    alias(libs.plugins.ksp)
}
android.buildFeatures.buildConfig = true
android {
    namespace = "com.czur.starry.device.smallroundscreen"
    compileSdk = libs.versions.compileSdkVersion.get().toInt()


    defaultConfig {
        applicationId = namespace
        minSdk = libs.versions.minSdkVersion.get().toInt()
        //noinspection ExpiredTargetSdkVersion
        targetSdk = libs.versions.targetSdkVersion.get().toInt()
        versionCode = libs.versions.appVersionCode.get().toInt()
        versionName = libs.versions.appVersionName.get()

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        renderscriptSupportModeEnabled = true

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        setFlavorDimensions(listOf("constantEnv"))

        manifestPlaceholders["atyPlaceHolder"] = rootProject.ext["atyConfigChange"].toString()
        manifestPlaceholders["startUpatyPlaceHolder"] = rootProject.ext["startUpatyConfigChange"].toString()
    }

    // 签名
    signingConfigs {
        create("keyStore") {
            storeFile = file("../../signature/starry.jks")
            storePassword = rootProject.ext["storePassword"].toString()
            keyAlias = rootProject.ext["keyAlias"].toString()
            keyPassword = rootProject.ext["keyPassword"].toString()
        }
    }

    packaging {
        // netty在引入的时候需要添加这个, 否则编译会报错
        resources.excludes.add("META-INF/INDEX.LIST")
        resources.excludes.add("META-INF/io.netty.versions.properties")
    }

    buildTypes {
        val signConfig = signingConfigs.getByName("keyStore")
        debug {
            signingConfig = signConfig
            isMinifyEnabled = false
        }
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
            signingConfig = signConfig
        }

        create("unsigned") {
            signingConfig = signConfig
            isDebuggable = false
            isMinifyEnabled = false
        }

    }

    productFlavors {
        create("envProduct") {
            dimension = "constantEnv"
            buildConfigField("Integer", "CONSTANT_ENV", "0")
        }
        create("envTest") {
            isDefault = true
            dimension = "constantEnv"
            buildConfigField("Integer", "CONSTANT_ENV", "1")
        }
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }
    kotlinOptions {
        jvmTarget = "11"
    }
    viewBinding {
        enable = true
    }

    android.applicationVariants.all {
        outputs.all {
            if (this is com.android.build.gradle
                .internal.api.ApkVariantOutputImpl
            ) {
                this.outputFileName = "CZSmallRoundScreen.apk"
            }
        }
    }
}

dependencies {
    implementation(fileTree(baseDir = "libs") {
        include("*.aar")
    })
    implementation(project(":baselib"))
    implementation(project(":base:hdmiLib"))
    implementation(project(":base:eShareLib"))
    implementation(files("${rootDir}/libs/hdmi.jar"))
    implementation(files("${rootDir}/libs/hdmi.jar"))

//    implementation(libs.androidx.appcompat)
//    implementation(libs.material)
//    implementation(libs.activity)
//    implementation(libs.constraintlayout)
    testImplementation(libs.junit)
    androidTestImplementation(libs.bundles.androidTest)

}