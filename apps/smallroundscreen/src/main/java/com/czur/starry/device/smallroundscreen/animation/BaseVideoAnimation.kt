package com.czur.starry.device.smallroundscreen.animation


import android.content.Context
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.smallroundscreen.widget.TextureVideoView
import androidx.core.net.toUri


open class BaseVideoAnimation (
    private val context: Context,
    private val videoView: TextureVideoView,
    private val videoPath: String, // Path to the video file
    private val looping: Boolean = false, // Whether the video should loop
    private val firstAnimation: Boolean = false, //循环播放动画 出现时是否需要淡入(消失时间不确定只能设置淡入)
    private val onAnimationEnd: (() -> Unit)? = null // Callback for when the animation ends
) : CustomAnimation {

    companion object {
        private const val TAG = "BaseHdmiAnimationDrawable"
        private const val ANIMATION_PLAY_INTERVAL = 40L //动画播放间隔 毫秒
    }

    private var isAnimationRunning = false
    private var isFirstAnimat = false

    init {
        isFirstAnimat = firstAnimation
        if (firstAnimation) {
            videoView.alpha = 0f
        } else {
            videoView.alpha = 1f
        }
    }

    override fun startAnimation() {
        val videoUri = videoPath.toUri()
        videoView.setVideoURI(videoUri)
        isAnimationRunning = true
        if (videoView.alpha < 1F){
            videoView.animate().alpha(1F).setDuration(1000).start()
        }
    }

    private fun startGradient() {
    }

    private fun stopGradient() {
    }

    override fun stopAnimation() {
        logTagD(TAG, "Stopping video animation: $videoPath")
        if (firstAnimation) {
            stopGradient()
        }
    }

    override fun endAnimation() {
        if (firstAnimation) {
            stopGradient()
        }
    }
}