package com.czur.starry.device.smallroundscreen.widget

import android.content.Context
import android.graphics.SurfaceTexture
import android.media.AudioAttributes
import android.media.AudioManager
import android.media.MediaPlayer
import android.net.Uri
import android.util.AttributeSet
import android.view.KeyEvent
import android.view.Surface
import android.view.TextureView
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.starry.device.baselib.utils.setVolume

/**
 * Created by 陈丰尧 on 2025/4/15
 */
private const val TAG = "TextureVideoView"

class TextureVideoView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : TextureView(context, attrs, defStyleAttr), TextureView.SurfaceTextureListener {
    private var mediaPlayer: MediaPlayer = MediaPlayer()
    private var videoUrl: Uri? = null

    init {
        surfaceTextureListener = this
    }

    fun setVideoURI(url: Uri) {
        videoUrl = url

        if (isAvailable) {
            prepareMediaPlayer()
        }
    }

    private fun prepareMediaPlayer() {
        logTagD(TAG, "prepareMediaPlayer: $videoUrl")
        try {
            videoUrl?.let {
                val audioAttributes = AudioAttributes.Builder()
                    .setUsage(-1)
                    .setContentType(AudioAttributes.CONTENT_TYPE_UNKNOWN)

                    .build()

                mediaPlayer.setDataSource(context, videoUrl!!)
                mediaPlayer.setSurface(Surface(surfaceTexture))
                mediaPlayer.setAudioAttributes(audioAttributes)
                mediaPlayer.setVolume(0F)
                mediaPlayer.isLooping = true
                mediaPlayer.setOnPreparedListener {
                    it.start()
                }
                mediaPlayer.prepareAsync()
            }
        } catch (e: Exception) {
            logTagE(TAG, "Error preparing media player", tr = e)
        }
    }

    override fun onSurfaceTextureAvailable(surface: SurfaceTexture, width: Int, height: Int) {
        if (videoUrl != null) {
            prepareMediaPlayer()
        }
    }

    override fun onSurfaceTextureSizeChanged(surface: SurfaceTexture, width: Int, height: Int) {
    }

    override fun onSurfaceTextureDestroyed(surface: SurfaceTexture): Boolean {
        mediaPlayer.release()
        return true
    }

    override fun onSurfaceTextureUpdated(surface: SurfaceTexture) {
    }
}