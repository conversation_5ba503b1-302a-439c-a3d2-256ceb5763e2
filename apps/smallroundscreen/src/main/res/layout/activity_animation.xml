<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:theme="@android:style/Theme.NoTitleBar.Fullscreen"
    tools:context=".AnimationActivity"
    tools:ignore="PxUsage">

    <ImageView
        android:layout_width="360px"
        android:layout_height="360px"
        android:background="@drawable/bg_image"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.czur.starry.device.smallroundscreen.widget.TextureVideoView
        android:id="@+id/animationbg1"
        android:layout_width="360px"
        android:layout_height="360px"
        android:background="@null"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/animationword1"
        android:layout_width="360px"
        android:layout_height="360px"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.czur.starry.device.smallroundscreen.widget.TextureVideoView
        android:id="@+id/animationbg2"
        android:layout_width="360px"
        android:layout_height="360px"
        android:visibility="invisible"
        android:background="@null"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/animationword2"
        android:layout_width="360px"
        android:layout_height="360px"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/singleAnimation"
        android:layout_width="360px"
        android:layout_height="360px"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>