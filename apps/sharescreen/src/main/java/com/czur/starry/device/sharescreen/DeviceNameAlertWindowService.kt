package com.czur.starry.device.sharescreen

import android.content.Intent
import android.view.View
import android.view.WindowManager
import android.widget.TextView
import androidx.core.view.postDelayed
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.base.AlertWindowService
import com.czur.starry.device.baselib.utils.SettingUtil
import com.czur.starry.device.baselib.utils.getScreenWidth
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.sharescreen.esharelib.E_SHARE_DEVICE_NAME
import com.czur.starry.device.sharescreen.esharelib.SimpleEShareCallback
import com.eshare.serverlibrary.api.EShareServerSDK
import kotlinx.coroutines.runBlocking

/**
 * Created by 陈丰尧 on 2023/5/20
 */
private const val TAG = "DeviceNameAlertWindowService"
private const val KEY_PAUSE_DISPLAY = "pauseDisplay"

private const val DEF_OFFSET = 20

class DeviceNameAlertWindowService : AlertWindowService() {
    override val layoutId: Int
        get() = R.layout.service_device_name
    override val windowWidthParam: Int
        get() = WindowManager.LayoutParams.WRAP_CONTENT
    override val windowHeightParam: Int
        get() = 60

    override val draggable: Boolean
        get() = true

    private val deviceNameTv by ViewFinder<TextView>(R.id.deviceNameTv)

    private val eShareServerSDK by lazy { EShareServerSDK.getSingleton(this.applicationContext) }
    private val callback = object : SimpleEShareCallback() {
        override fun onSettingsChanged(key: String, newValue: Any?) {
            super.onSettingsChanged(key, newValue)
            when (key) {
                E_SHARE_DEVICE_NAME -> {
                    logTagV(TAG, "onSettingsChanged: E_SHARE_DEVICE_NAME = $newValue")
                    updateDeviceName(newValue as String)
                }

                else -> {}
            }
        }
    }

    override fun View.initViews() {}

    override fun needBlockDisplay(): Boolean {
        return runBlocking { !SettingUtil.ShareScreenSetting.isNameAlertWindowEnable() }
    }

    override fun initData() {
        super.initData()
        eShareServerSDK.registerCallback(callback)
    }

    override fun onDataRefresh(intent: Intent?) {
        super.onDataRefresh(intent)

        val pauseDisplay = intent?.getBooleanExtra(KEY_PAUSE_DISPLAY, false) ?: false
        logTagD(TAG, "onDataRefresh: pauseDisplay = $pauseDisplay")
        if (pauseDisplay) {
            updateDeviceName("")
        } else {
            val deviceName = eShareServerSDK.deviceName ?: ""
            updateDeviceName(deviceName)
        }
    }

    fun updateDeviceName(deviceName: String) {
        if (deviceName.isEmpty()) {
            deviceNameTv.alpha = 0F
            deviceNameTv.postDelayed(50) {
                // 这里必须先等透明度已经改了, 再调用隐藏,否则透明度的改变也不会生效的
                // 如果不调用gone, 那么又会挡住用户的操作
                deviceNameTv.gone()
            }
            return
        }
        val text = getString(R.string.str_alert_win_device_name, deviceName)
        deviceNameTv.text = text
        // 计算文字实际宽度
        val calculateWidth = deviceNameTv.paint.measureText(text).toInt() + deviceNameTv.paddingLeft + deviceNameTv.paddingRight
        deviceNameTv.post {
            val tvWidth = deviceNameTv.width.coerceAtLeast(calculateWidth)
            updateLocation(getScreenWidth() - tvWidth - DEF_OFFSET, DEF_OFFSET)
            deviceNameTv.show()
            deviceNameTv.alpha = 1F
        }
    }

    override fun onDestroy() {
        eShareServerSDK.unregisterCallback(callback)
        super.onDestroy()
    }
}