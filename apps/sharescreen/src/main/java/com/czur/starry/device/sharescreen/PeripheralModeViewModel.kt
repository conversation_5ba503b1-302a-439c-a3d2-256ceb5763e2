package com.czur.starry.device.sharescreen

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.utils.AudioUtil
import com.czur.starry.device.baselib.utils.fw.proxy.SystemManagerProxy
import com.czur.starry.device.baselib.utils.getAppNameByPkg
import com.czur.starry.device.baselib.utils.getAppNamesByPkges
import com.czur.starry.device.baselib.utils.getProcessNameByPid
import com.czur.starry.device.baselib.utils.getString
import com.czur.starry.device.baselib.utils.hardware.CameraUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.withContext

/**
 * Created by 陈丰尧 on 2024/5/21
 */
private const val TAG = "PeripheralModeViewModel"

class PeripheralModeViewModel(application: Application) : AndroidViewModel(application) {
    private val _isGadgetModeFlow = MutableStateFlow(false)
    val isGadgetModeFlow = _isGadgetModeFlow.asStateFlow()
    private val systemManager = SystemManagerProxy()
    private val audioUtil = AudioUtil()


    suspend fun refreshGadgetMode() = withContext(Dispatchers.Default) {
        logTagV(TAG, "refreshGadgetMode")
        systemManager.isGadgetMode().let {
            _isGadgetModeFlow.value = it
        }
    }

    /**
     * 检查是否可以进入Gadget模式
     */
    suspend fun getUseCameraOrMicAppName(): String {
        return withContext(Dispatchers.IO) {
            // 1. 检查Camera是否被占用
            val cameraInUse = async {
                CameraUtil.getUseCameraPkg().also {
                    logTagD(TAG, "getUseCameraPkgName $it")
                }

            }
            // 2. 检查Mic是否被占用
            val micInUse = async {
                audioUtil.getUseMicPid().mapNotNull { pid ->
                    getProcessNameByPid(pid)?.processName
                }.toMutableSet().also {
                    logTagD(TAG, "getUseMicPkgName ${it.joinToString()}")
                }
            }

            val pkgNameSet = micInUse.await()

            pkgNameSet.add(cameraInUse.await())
            if (pkgNameSet.isEmpty()) {
                ""
            } else {
                pkgNameSet.mapNotNull {
                    getAppNameByPkg(it).ifEmpty { null }
                }.joinToString(separator = getString(R.string.comma))
            }
        }
    }

    /**
     * 设置Gadget模式
     */
    suspend fun setGadgetMode(on: Boolean) {
        logTagV(TAG, "setGadgetMode $on")
        _isGadgetModeFlow.value = on
        systemManager.setGadgetMode(on)
        refreshGadgetMode()
    }
}