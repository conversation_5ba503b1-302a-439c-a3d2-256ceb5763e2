<resources xmlns:tools="http://schemas.android.com/tools">
    <string name="app_name">무선 화면 캐스팅</string>
    <string name="toast_eShare_no_active">처음 사용 시 인터넷 연결이 필요합니다.</string>
    <string name="str_alert_win_device_name">디바이스 이름: %s</string>
    <string name="str_quick_boot_title_byom">주변장치 모드</string>
    <string name="str_byom_content">주변 장치로서 마이크 /StarryHub의 스피커 / 카메라 / 프로젝션 화면은 컴퓨터의 응용 프로그램에서 사용할 수 있습니다.</string>
    <string name="str_byom_wireless_title">무선 주변 장치 모드</string>
    <string name="app_name_army">화면 공유</string>
    <string name="byom_wireless_content1">ClickDrop(USB/Type-C)을 StarryHub에 삽입하여 페어링합니다.</string>
    <string name="byom_wireless_content2">ClickDrop(StarryHub와 페어링됨)을 컴퓨터에 삽입하여 페어링합니다.</string>
    <string name="byom_wireless_content3">일단 페어링되면 StarryHub의 카메라 / 마이크 /카메라는 컴퓨터 응용 프로그램을 통해 주변 장치로 사용할 수 있습니다.</string>
    <string name="byom_wireless_content4">ClickDrop 버튼을 눌러 StarryHub에 컴퓨터 화면을 공유합니다.</string>
    <string name="str_byom_USB_title">유선 주변 장치 모드</string>
    <string name="byom_USB_content1">유선 주변 장치 모드 사용.</string>
    <string name="byom_USB_content2">USB 2.0 인터페이스를 통해 USB 케이블을 사용하여 StarryHub에 컴퓨터를 연결합니다.</string>
    <string name="byom_USB_content3">StarryHub 마이크 선택 /카메라 / 스피커는 컴퓨터 응용 프로그램의 주변 장치로 사용됩니다.</string>
    <string name="byom_USB_content4">무선 미러링을 사용하여 컴퓨터 화면을 기기에 투영할 수 있습니다.</string>
</resources>
