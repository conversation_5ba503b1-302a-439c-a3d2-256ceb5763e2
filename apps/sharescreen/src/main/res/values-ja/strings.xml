<resources xmlns:tools="http://schemas.android.com/tools">
    <string name="app_name">ワイヤレス画面キャスト</string>
    <string name="toast_eShare_no_active">初回利用時はインターネット接続が必要です。</string>
    <string name="str_alert_win_device_name">デバイス名： %s</string>
    <string name="str_quick_boot_title_byom">周辺機器モード</string>
    <string name="app_name_army">画面の共有</string>
    <string name="str_byom_content">周辺機器として、マイク/StarryHubのスピーカー/カメラ/投影画面は、コンピュータ上のアプリケーションで使用できます。</string>
    <string name="str_byom_wireless_title">ワイヤレス周辺機器モード</string>
    <string name="byom_wireless_content1">ClickDrop（USB/Type-C）をStarryHubに挿入してペアリングします。</string>
    <string name="byom_wireless_content2">ClickDrop（StarryHubとペアリング済み）をコンピュータに挿入してペアリングします。</string>
    <string name="byom_wireless_content3">ペアリングすると、StarryHubのカメラ/マイク/カメラは、コンピュータアプリケーションを介して周辺機器として使用できます。</string>
    <string name="byom_wireless_content4">ClickDropボタンを押して、StarryHubにコンピュータ画面を共有します。</string>
    <string name="str_byom_USB_title">有線周辺機器モード</string>
    <string name="byom_USB_content1">有線周辺機器モードの有効化。</string>
    <string name="byom_USB_content2">USB 2.0インタフェースを介して、USBデータケーブルを使用してコンピュータをStarryHubに接続します。</string>
    <string name="byom_USB_content3">StarryHubマイクを選択/カメラ/スピーカーはコンピュータアプリケーション上の周辺デバイスとして機能します。</string>
    <string name="byom_USB_content4">無線ミラーリングを使用して、コンピュータのスクリーンをデバイスに投影することができます。</string>
</resources>
