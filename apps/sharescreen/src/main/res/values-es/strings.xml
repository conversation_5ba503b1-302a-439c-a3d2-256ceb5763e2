<resources xmlns:tools="http://schemas.android.com/tools">
    <string name="app_name">Presentación de pantalla inalámbrica</string>
    <string name="toast_eShare_no_active">Se requiere conexión a Internet cuando se utiliza por primera vez.</string>
    <string name="str_alert_win_device_name">Nombre del dispositivo: %s</string>
    <string name="app_name_army">Pantalla compartida</string>
    <string name="str_byom_content">Como periféricos, el micrófono Los altavoces / cámaras / pantallas de proyección de starryhub pueden ser utilizados por aplicaciones en su computadora.</string>
    <string name="str_byom_wireless_title">Modo periférico inalámbrico</string>
    <string name="byom_wireless_content1">Inserte cliccdrop (perro cifrado byom inalámbrico) en starryhub para el emparejamiento.</string>
    <string name="byom_wireless_content2">Introduzca cliccdrop (emparejado con starryhub) en la computadora para emparejar.</string>
    <string name="byom_wireless_content3">Una vez emparejado, la Cámara / micrófono de starryhub Las cámaras se pueden utilizar como dispositivos periféricos a través de aplicaciones informáticas.</string>
    <string name="byom_wireless_content4">Presione el botón clicckdrop para compartir la pantalla del ordenador con starryhub.</string>
    <string name="str_byom_USB_title">Modo periférico por cable</string>
    <string name="byom_USB_content1">Activar el modo periférico por cable</string>
    <string name="byom_USB_content2">A través de la interfaz USB 2.0, se utiliza un cable de datos USB para conectar el ordenador a starryhub.</string>
    <string name="byom_USB_content3">Elija el micrófono starryhub / Cámaras / altavoces como periféricos en aplicaciones informáticas.</string>
    <string name="str_quick_boot_title_byom">Modo periférico</string>
    <string name="byom_USB_content4">Puede utilizar la pantalla inalámbrica para proyectar la pantalla de su computadora en un dispositivo.</string>
</resources>
