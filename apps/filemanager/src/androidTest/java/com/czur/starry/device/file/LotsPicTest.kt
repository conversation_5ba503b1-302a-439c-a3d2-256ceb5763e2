package com.czur.starry.device.file

import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.async
import kotlinx.coroutines.runBlocking
import org.junit.Before
import org.junit.Test
import java.io.File
import java.util.concurrent.Executors
import kotlin.random.Random

/**
 * Created by 陈丰尧 on 2023/6/5
 */
private const val CREATE_IMG_COUNT = 7000

class LotsPicTest {
    private lateinit var rootDir: File
    private val scope = CoroutineScope(Dispatchers.Default)

    private val workDispatcher = Executors.newFixedThreadPool(6).asCoroutineDispatcher()

    @Before
    fun init() {
        // 根路径选择报名/file
//        val targetContext = InstrumentationRegistry.getInstrumentation().targetContext
//        val filesDir = targetContext.filesDir
        rootDir = File("/sdcard/Screenshots")
    }

    @Test
    fun test() {
        val deferredList = mutableListOf<Deferred<Unit>>()
        repeat(CREATE_IMG_COUNT) {
            val deferred = scope.async(workDispatcher) {
                val file = File(rootDir, "test-$it.jpg")
                if (!file.exists()) {
                    val bitmap = createRandomBitmap()
                    file.outputStream().use { fos ->
                        bitmap.compress(Bitmap.CompressFormat.JPEG, 100, fos)
                    }
                    bitmap.recycle()
                    println("create file: ${file.absolutePath}")
                } else {
                    println("file exists: ${file.absolutePath}")
                }

            }
            deferredList.add(deferred)
        }

        runBlocking {
            println("start waiting")
            deferredList.forEach {
                it.await()
            }
            println("end waiting")
        }
    }

    private fun createRandomBitmap(): Bitmap  {
        val bitmap = Bitmap.createBitmap(1920, 1080, Bitmap.Config.RGB_565)
        val canvas = Canvas(bitmap)
        val paint = Paint().apply {
            style = Paint.Style.FILL
        }

        canvas.drawColor(Color.WHITE)

        val circleCount = Random.nextInt(10, 50)
        repeat(circleCount) {
            val circleRadius = Random.nextInt(50, 500)
            val color = Random.nextInt(0xFF000000.toInt(), 0xFFFFFFFF.toInt())
            paint.color = color
            val x = Random.nextInt(50, 1920 - 50)
            val y = Random.nextInt(50, 1080 - 50)
            canvas.drawCircle(x.toFloat(), y.toFloat(), circleRadius.toFloat(), paint)
        }

        return bitmap
    }
}