<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"

    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingVertical="25px">

    <TextView
        android:id="@+id/localMeetingTransfer_fileNameTv"
        android:layout_width="500px"
        android:layout_height="wrap_content"
        android:layout_marginLeft="40px"
        android:ellipsize="middle"
        android:lines="1"
        android:text="会议纪要.txt"
        android:textColor="@color/text_white"
        android:textSize="30px"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="MissingConstraints" />

    <TextView
        android:id="@+id/localMeetingTransfer_durationTv"
        android:layout_width="420px"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:lines="1"
        android:text="01:10:00"
        android:textColor="@color/text_white"
        android:textSize="30px"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/localMeetingTransfer_fileNameTv"/>


    <LinearLayout
        android:id="@+id/status_bg_Linear"
        android:layout_width="420px"
        android:layout_height="match_parent"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        app:layout_constraintLeft_toRightOf="@id/localMeetingTransfer_durationTv">

        <ImageView
            android:id="@+id/transfer_status_Iv"
            android:layout_width="30px"
            android:layout_height="30px"
            android:layout_marginLeft="135px"
            android:scaleType="centerInside"
            android:src="@drawable/ic_transfer_list_done"/>

        <TextView
            android:id="@+id/transfer_status_Tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/str_file_transfer_list_file_transfering"
            android:lines="1"
            android:textColor="@color/text_white"
            android:textSize="30px"
            android:layout_marginLeft="15px"
            />

    </LinearLayout>







    <ImageView
        android:id="@+id/transfer_download_Iv"
        android:layout_width="40px"
        android:layout_height="40px"
        android:visibility="gone"
        android:scaleType="centerInside"
        android:src="@drawable/ic_transfer_list_download"
        android:layout_marginLeft="40px"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/status_bg_Linear"/>

    <TextView
        android:id="@+id/transfer_download_Tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_file_transfer_list_file_download"
        android:lines="1"
        android:textColor="@color/text_white"
        android:textSize="30px"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/transfer_download_Iv"/>

    <ImageView
        android:id="@+id/transfer_retry_Iv"
        android:layout_width="40px"
        android:layout_height="40px"
        android:visibility="gone"
        android:src="@drawable/ic_transfer_list_retry"
        android:scaleType="centerInside"
        android:layout_marginLeft="40px"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="@+id/transfer_download_Iv"/>

    <TextView
        android:id="@+id/transfer_retry_Tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_file_transfer_list_file_retry"
        android:lines="1"
        android:textColor="@color/text_white"
        android:textSize="30px"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/transfer_retry_Iv"/>

    <ImageView
        android:id="@+id/transfer_delete_Iv"
        android:layout_width="40px"
        android:layout_height="40px"
        android:visibility="gone"
        android:scaleType="centerInside"
        android:src="@drawable/ic_transfer_list_delete"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toLeftOf="@id/transfer_delete_Tv"/>

    <TextView
        android:id="@+id/transfer_delete_Tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_file_transfer_list_file_delete"
        android:lines="1"
        android:textColor="@color/text_white"
        android:textSize="30px"
        android:layout_marginRight="70px"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>