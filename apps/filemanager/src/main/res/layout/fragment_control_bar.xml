<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/controlBarView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/backIv"
            android:layout_width="90px"
            android:layout_height="60px"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="30px"
            android:layout_marginTop="45px"
            android:visibility="gone"
            android:src="@drawable/file_icon_control_back"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            android:id="@+id/titleBar"
            android:layout_width="0px"
            android:layout_height="@dimen/control_bar_height"
            android:gravity="center_vertical"
            android:layout_marginTop="@dimen/fragment_control_bar_margin_top"
            android:layout_marginStart="@dimen/fragment_title_bar_margin_left"
            android:orientation="horizontal"
            android:visibility="gone"
            app:layout_constraintLeft_toLeftOf="@+id/backIv"
            app:layout_constraintRight_toLeftOf="@id/viewControlBar"
            app:layout_constraintTop_toTopOf="parent">


            <ImageView
                android:id="@+id/file_icon"
                android:layout_width="40px"
                android:layout_height="32px"
                android:layout_gravity="center_vertical"
                android:src="@drawable/file_icon_folder_min" />

            <TextView
                android:id="@+id/titleTv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="30px"
                android:ellipsize="end"
                android:lines="1"
                android:paddingRight="80px"
                android:textColor="@color/text_common"
                android:textSize="30px"
                tools:text="根目录" />
        </LinearLayout>

        <com.czur.uilib.choose.CZTabBar
            android:id="@+id/selectBar"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginLeft="@dimen/fragment_title_bar_margin_left"
            android:layout_marginTop="@dimen/fragment_control_bar_margin_top"
            app:layout_constraintTop_toTopOf="@id/titleBar"
            app:layout_constraintStart_toStartOf="parent"/>


        <LinearLayout
            android:id="@+id/viewControlBar"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginEnd="60px"
            android:layout_marginTop="@dimen/fragment_control_bar_margin_top"
            android:divider="@drawable/divider_control_bar"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:showDividers="middle"
            android:visibility="visible"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/changeLayoutIv"
                style="@style/style_control_bar_iv"
                android:src="@drawable/ic_change_layout_list"
                app:float_tips="@string/float_tip_display_mode" />
           <ImageView
                android:id="@+id/sortIv"
                style="@style/style_control_bar_iv"
                android:src="@drawable/file_icon_sort_on"
                app:float_tips="@string/float_tip_sorting_way" />

            <ImageView
                android:id="@+id/addFolderIv"
                style="@style/style_control_bar_iv"
                android:src="@drawable/file_icon_new_on"
                app:float_tips="@string/float_tip_new_folder" />

            <ImageView
                android:id="@+id/selectModeIv"
                style="@style/style_control_bar_iv"
                android:src="@drawable/file_icon_mutil_pick_on"
                app:float_tips="@string/float_tip_choose" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/fragment_control_bar_margin_top">

        <include
            android:id="@+id/controlBarSelect"
            layout="@layout/control_bar_select"
            android:visibility="gone" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</FrameLayout>
