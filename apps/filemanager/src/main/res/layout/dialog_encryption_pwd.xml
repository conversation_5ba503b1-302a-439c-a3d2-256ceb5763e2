<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="880px"
    android:layout_height="630px"
    android:background="@drawable/bg_dialog_input"
    tools:ignore="PxUsage,RtlHardcoded">

    <View
        android:id="@+id/topBarView"
        android:layout_width="match_parent"
        android:layout_height="86px"
        app:bl_corners_topLeftRadius="10px"
        app:bl_corners_topRightRadius="10px"
        app:bl_solid_color="#4E6EE1"
        app:layout_constraintTop_toTopOf="parent"
        tools:background="#4E6EE1" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="55px"
        android:text="@string/title_dialog_encryption_input_pwd"
        android:textColor="@color/white"
        android:textSize="48px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/topBarView"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@id/topBarView" />

    <ImageView
        android:id="@+id/closeIv"
        style="@style/icon_copy_dialog"
        android:layout_marginRight="20px"
        android:src="@drawable/file_icon_close"
        app:layout_constraintBottom_toBottomOf="@id/topBarView"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.czur.starry.device.file.widget.EncryptionPwdGroupView
        android:id="@+id/encryptionPwdGroupView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:etBackground="@drawable/bg_auto_change_focus_et_dialog"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/topBarView" />

    <ImageView
        android:id="@+id/pwdEyeIv"
        android:layout_width="50px"
        android:layout_height="50px"
        android:layout_marginLeft="30px"
        android:tintMode="src_atop"
        app:layout_constraintBottom_toBottomOf="@id/encryptionPwdGroupView"
        app:layout_constraintLeft_toRightOf="@id/encryptionPwdGroupView"
        app:layout_constraintTop_toTopOf="@id/encryptionPwdGroupView"
        app:tint="@color/white" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="70px"
        android:text="@string/dialog_encryption_pwd_hint"
        android:textColor="@color/white"
        android:textSize="30px"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@id/encryptionPwdGroupView"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <Button
        android:id="@+id/confirmFocusBtn"
        android:layout_width="1px"
        android:layout_height="1px"
        app:layout_constraintBottom_toBottomOf="@id/enterBtn"
        app:layout_constraintLeft_toLeftOf="@id/enterBtn"
        app:layout_constraintRight_toRightOf="@id/enterBtn"
        app:layout_constraintTop_toTopOf="@id/enterBtn" />

    <com.czur.uilib.btn.CZButton
        android:id="@+id/enterBtn"
        android:layout_width="180px"
        android:layout_height="50px"
        android:layout_marginBottom="30px"
        android:text="@string/str_encryption_pwd_enter"
        android:textSize="20px"
        android:textStyle="bold"
        app:colorStyle="PositiveInBlue"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <TextView
        android:id="@+id/lockInfoTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:textSize="24px"
        android:textStyle="normal"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/enterBtn"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/encryptionPwdGroupView"
        tools:text="密码已锁定，请24小时后重试" />

</androidx.constraintlayout.widget.ConstraintLayout>