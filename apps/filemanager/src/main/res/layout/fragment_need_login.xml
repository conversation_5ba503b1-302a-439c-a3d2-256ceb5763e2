<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    tools:ignore="PxUsage">

    <TextView
        android:id="@+id/noLoginTitleTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_no_login_title"
        android:textColor="@color/text_common_light"
        android:textSize="60px"
        android:textStyle="bold" />

    <Space
        android:id="@+id/space1"
        android:layout_width="wrap_content"
        android:layout_height="20px" />

    <TextView
        android:id="@+id/noLoginInfoTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/text_common_light"
        android:textSize="30px"
        tools:text="@string/str_no_login_info_ai_trans" />

    <Space
        android:id="@+id/space2"
        android:layout_width="wrap_content"
        android:layout_height="60px" />

    <com.czur.uilib.btn.CZButton
        android:id="@+id/loginBtn"
        android:layout_width="300px"
        android:layout_height="80px"
        android:text="@string/str_login"
        android:textSize="30px" />

    <androidx.constraintlayout.helper.widget.Flow
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:constraint_referenced_ids="noLoginTitleTv,space1,noLoginInfoTv,space2,loginBtn"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>