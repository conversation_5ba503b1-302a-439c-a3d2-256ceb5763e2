<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:ignore="PxUsage,RtlHardcoded">

    <FrameLayout
        android:id="@+id/filePadRvContainer"
        android:layout_width="match_parent"
        android:layout_height="0px"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_goneMarginBottom="0px">
        <!--EmptyRecyclerView 必须外面是FrameLayout-->
        <com.czur.starry.device.file.widget.EmptyRecyclerView
            android:id="@+id/filePadRv"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </FrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout>