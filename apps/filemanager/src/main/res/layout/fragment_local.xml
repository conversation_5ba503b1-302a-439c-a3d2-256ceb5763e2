<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <FrameLayout
        android:id="@+id/infoBarContainer"
        android:layout_width="match_parent"
        android:layout_height="@dimen/fragment_info_bar_height"
        app:layout_constraintRight_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <FrameLayout
        android:id="@+id/controlBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/infoBarContainer"/>

<!--  文件列表展示  -->
    <FrameLayout
        android:id="@+id/filePad"
        android:layout_width="match_parent"
        android:layout_height="0px"
        android:layout_marginHorizontal="@dimen/fragment_file_pad_margin_left"
        android:layout_marginTop="@dimen/fragment_file_pad_margin_top"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"/>


</androidx.constraintlayout.widget.ConstraintLayout>