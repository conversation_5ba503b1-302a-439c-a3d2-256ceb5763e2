<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:ignore="PxUsage">

    <LinearLayout
        android:id="@+id/closeLayout"
        android:layout_width="130px"
        android:layout_height="60px"
        android:layout_marginRight="60px"
        android:orientation="horizontal"
        android:visibility="gone"
        app:bl_corners_radius="30px"
        app:bl_solid_color="#E7E7E7"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:layout_width="0px"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:paddingLeft="15px"
            android:text="@string/str_cz_share_off"
            android:textColor="@color/text_common"
            android:textSize="20px"
            android:textStyle="bold" />

        <ImageView
            android:layout_width="60px"
            android:layout_height="60px"
            android:src="@drawable/ic_czur_share_off" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/openLayout"
        android:layout_width="130px"
        android:layout_height="60px"
        android:layout_marginRight="60px"
        android:orientation="horizontal"
        android:visibility="gone"
        app:bl_corners_radius="30px"
        app:bl_solid_color="#E7E7E7"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:layout_width="60px"
            android:layout_height="60px"
            android:src="@drawable/ic_czur_share_on" />

        <TextView
            android:layout_width="0px"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:paddingRight="15px"
            android:text="@string/str_cz_share_on"
            android:textColor="@color/text_common"
            android:textSize="20px"
            android:textStyle="bold" />

    </LinearLayout>

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/barrier"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="left"
        app:constraint_referenced_ids="closeLayout,openLayout" />

    <TextView
        android:id="@+id/tipsTv"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toLeftOf="@id/barrier"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/baselib_bg_pop_tip"
        android:includeFontPadding="false"
        android:maxWidth="380px"
        android:layout_marginRight="20px"
        android:visibility="gone"
        android:paddingHorizontal="25px"
        android:paddingVertical="15px"
        android:text="@string/str_file_share_tip2"
        android:textColor="@color/black"
        android:textSize="20px"
        android:textStyle="normal"
        tools:viewBindingIgnore="true" />

</androidx.constraintlayout.widget.ConstraintLayout>