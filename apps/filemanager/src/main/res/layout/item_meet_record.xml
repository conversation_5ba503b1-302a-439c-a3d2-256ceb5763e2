<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingLeft="30px"
    android:paddingVertical="25px">

    <ImageView
        android:id="@+id/meetItemSelectIv"
        android:layout_width="80px"
        android:layout_height="80px"
        android:src="@drawable/file_select"
        android:visibility="invisible"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/meetIconIv"
        android:layout_width="80px"
        android:layout_height="80px"
        app:layout_constraintBottom_toBottomOf="@id/meetItemSelectIv"
        app:layout_constraintLeft_toLeftOf="@id/meetItemSelectIv"
        app:layout_constraintRight_toRightOf="@id/meetItemSelectIv"
        app:layout_constraintTop_toTopOf="@id/meetItemSelectIv" />

    <TextView
        android:id="@+id/meetTitleTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:layout_marginLeft="30px"
        android:maxWidth="260px"
        android:ellipsize="middle"
        android:singleLine="true"
        android:text="新会议记录6"
        android:textColor="@color/text_common"
        android:textSize="30px"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/meetItemSelectIv"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/meetTimeTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:layout_marginLeft="30px"
        android:ellipsize="end"
        android:lines="1"
        android:text="2019.10.29 22:10:00"
        android:textColor="@color/text_common_light"
        android:textSize="24px"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/meetTitleTv"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/meetDurationTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:layout_marginLeft="15px"
        android:ellipsize="end"
        android:lines="1"
        android:text="02:10:00"
        android:textColor="@color/text_common_light"
        android:textSize="20px"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/meetTimeTv"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/meetControlBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:divider="@drawable/divider_control_bar"
        android:orientation="horizontal"
        android:showDividers="middle"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:id="@+id/meetTextPlay"
            style="@style/style_control_layout"
            android:layout_marginRight="30px"
            android:visibility="gone">

            <ImageView
                style="@style/style_control_bar_iv"
                android:src="@drawable/icon_meet_watch_text"
                app:float_tips="@string/float_tip_text_transform" />
        </LinearLayout>

        <TextView
            android:id="@+id/meetWatchText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginRight="30px"
            android:text="@string/meeting_record_transform"
            android:textColor="@color/text_common_light"
            android:textSize="24px"
            android:visibility="gone" />

        <LinearLayout
            android:id="@+id/meet_trans_failed_lyout"
            style="@style/style_control_layout"
            android:layout_marginRight="30px"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/recordTransFailedTv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:textColor="@color/text_common_warning"
                android:text="@string/record_transform_failed"
                android:textSize="24px" />

            <ImageView
                style="@style/style_control_bar_iv"
                android:layout_marginStart="10px"
                android:visibility="gone"
                android:src="@drawable/icon_meet_trans_failed" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/meetPlay_layout"
            android:layout_marginRight="30px"
            style="@style/style_control_layout">

            <ImageView
                android:id="@+id/meetPlayIv"
                style="@style/style_control_bar_iv"
                android:src="@drawable/icon_meet_play"
                app:float_tips="@string/float_tip_play" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/meetSentToPhone_layout"
            android:layout_marginRight="30px"
            style="@style/style_control_layout">

            <ImageView
                android:id="@+id/meetSentToPhoneIv"
                style="@style/style_control_bar_iv"
                android:src="@drawable/icon_meet_send_to_phone"
                app:float_tips="@string/float_tip_share" />
        </LinearLayout>

    </LinearLayout>

    <com.czur.uilib.choose.CZCheckBox
        android:id="@+id/meetCheckIv"
        style="@style/style_select_mode_checkbox"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>