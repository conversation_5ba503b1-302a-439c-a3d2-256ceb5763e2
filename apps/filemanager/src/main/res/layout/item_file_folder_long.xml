<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:ignore="PxUsage">

    <View
        android:id="@+id/dividerViewStart"
        android:layout_width="wrap_content"
        android:layout_height="15px"
        app:layout_constraintLeft_toLeftOf="@+id/layout_tab"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/dividerLine"
        android:layout_width="wrap_content"
        android:layout_height="10px"
        android:gravity="center_vertical"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="@+id/layout_tab"
        app:layout_constraintTop_toBottomOf="@+id/dividerViewStart">

        <View
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:layout_marginStart="@dimen/margin_start_menu_icon"
            android:background="@color/text_main_menu" />
    </androidx.appcompat.widget.LinearLayoutCompat>


    <View
        android:id="@+id/dividerViewEnd"
        android:layout_width="wrap_content"
        android:layout_height="15px"
        app:layout_constraintLeft_toLeftOf="@+id/layout_tab"
        app:layout_constraintTop_toBottomOf="@+id/dividerLine" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_tab"
        android:layout_width="match_parent"
        android:layout_height="60px"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/dividerViewEnd"
        tools:ignore="PxUsage">

        <View
            android:id="@+id/viewChecked"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/drawable_menu_sel"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/viewCheckedCursor"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="invisible"
            android:background="@drawable/drawable_menu_cursor"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/iv_icon"
            android:layout_width="30px"
            android:layout_height="30px"
            android:layout_marginStart="@dimen/margin_start_menu_icon"
            android:layout_marginTop="2px"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/icon_usb_on" />

        <TextView
            android:id="@+id/tv_folder"
            android:layout_width="wrap_content"
            android:layout_height="40px"
            android:layout_marginStart="@dimen/margin_start_menu_text"
            android:gravity="left"
            android:includeFontPadding="false"
            android:textSize="30px"
            android:textStyle="bold"
            app:layout_constraintLeft_toRightOf="@id/iv_icon"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="@string/app_name" />

        <FrameLayout
            android:id="@+id/tabPoint"
            android:layout_marginLeft="6px"
            android:layout_width="14px"
            android:layout_height="14px"
            android:visibility="gone"
            app:layout_constraintLeft_toRightOf="@+id/tv_folder"
            app:layout_constraintTop_toTopOf="@id/tv_folder">

            <com.czur.starry.device.baselib.widget.CircleView
                android:layout_width="14px"
                android:layout_height="14px"
                android:layout_gravity="center"
                app:circleColor="#FFFFFF" />

            <com.czur.starry.device.baselib.widget.CircleView
                android:layout_width="10px"
                android:layout_height="10px"
                android:layout_gravity="center"
                app:circleColor="#ff5b5c" />

        </FrameLayout>


        <ImageView
            android:id="@+id/im_usb_unmount"
            android:layout_width="24px"
            android:layout_height="24px"
            android:layout_marginLeft="40px"
            android:layout_marginBottom="5px"
            android:src="@drawable/icon_usb_unmount"
            app:layout_constraintBottom_toBottomOf="@+id/tv_folder"
            app:layout_constraintLeft_toRightOf="@+id/tv_folder" />

    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>