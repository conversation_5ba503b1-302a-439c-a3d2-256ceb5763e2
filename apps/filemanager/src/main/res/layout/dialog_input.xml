<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <EditText
        android:id="@+id/inputDialogEt"
        android:layout_width="440px"
        android:layout_height="60px"
        android:layout_gravity="center"
        android:background="@drawable/bg_edittext"
        android:paddingStart="12px"
        android:paddingEnd="12px"
        android:singleLine="true"
        android:textColor="@color/white"
        android:textColorHint="@color/colorWhiteInputHint"
        android:textCursorDrawable="@drawable/dialog_input_cursor"
        android:textSize="30px"
        android:textStyle="bold"
        android:includeFontPadding="false"
        android:nextFocusDown="@id/inputDialogEt"
        android:imeOptions="actionDone"
        tools:hint="新文件1" />
</FrameLayout>