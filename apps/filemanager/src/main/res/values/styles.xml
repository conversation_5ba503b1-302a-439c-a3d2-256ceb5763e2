<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="PxUsage">

    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.AppCompat.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowFullscreen">true</item>
    </style>

    <style name="style_control_bar_iv">
        <item name="android:layout_gravity">center</item>
        <item name="android:padding">16px</item>
        <item name="android:layout_width">60px</item>
        <item name="android:layout_height">60px</item>
        <item name="android:scaleType">fitCenter</item>
    </style>

    <style name="style_trans_btn_iv">
        <item name="android:layout_gravity">center</item>
        <item name="android:layout_width">60px</item>
        <item name="android:layout_height">60px</item>
        <item name="android:scaleType">fitCenter</item>
    </style>

    <style name="style_control_layout">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:gravity">center</item>
        <item name="android:layout_gravity">center</item>
    </style>

    <style name="style_sort_pop_layout">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">60px</item>
        <item name="android:paddingLeft">28px</item>
        <item name="android:paddingRight">15px</item>
        <item name="android:orientation">horizontal</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="style_sort_pop_text">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">24px</item>
        <item name="android:layout_gravity">center_vertical</item>
        <item name="android:textColor">@color/text_common</item>
    </style>

    <style name="style_speed_pop_text">
        <item name="android:layout_width">110px</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">23px</item>
        <item name="android:textColor">@color/colorMusicSpeed</item>
    </style>

    <style name="style_sort_pop_img">
        <item name="android:layout_width">22px</item>
        <item name="android:layout_height">22px</item>
        <item name="android:layout_marginLeft">28px</item>
        <item name="android:layout_gravity">center_vertical</item>
        <item name="android:src">@drawable/icon_pick_on</item>
    </style>

    <style name="style_dialog_btn">
        <item name="android:layout_width">180px</item>
        <item name="android:layout_height">50px</item>
        <item name="android:gravity">center</item>
        <item name="android:textSize">20px</item>
    </style>

    <style name="style_dialog_file_trans_btn">
        <item name="android:layout_width">295px</item>
        <item name="android:layout_height">80px</item>
        <item name="android:gravity">center</item>
        <item name="android:textSize">30px</item>
    </style>

    <style name="ProgressBar_Mini" parent="@android:style/Widget.ProgressBar.Horizontal">
        <item name="android:maxHeight">10px</item>
        <item name="android:minHeight">10px</item>
        <item name="android:indeterminateOnly">false</item>
        <item name="android:progressDrawable">@drawable/progress_horizontal</item>
    </style>

    <style name="progressbar_music" parent="@android:style/Widget.ProgressBar.Horizontal">
        <item name="android:maxHeight">3px</item>
        <item name="android:minHeight">3px</item>
        <item name="android:indeterminateOnly">false</item>
        <item name="android:progressDrawable">@drawable/progress_horizontal_white</item>
    </style>

    <style name="MediaSeekBar" parent="Widget.AppCompat.SeekBar">
        <item name="android:maxHeight">3px</item>
        <item name="android:minHeight">3px</item>
        <item name="android:indeterminateOnly">false</item>
        <item name="android:progressDrawable">@drawable/seek_horizontal_white</item>
    </style>

    <style name="music_time_tv">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:layout_marginBottom">10px</item>
        <item name="android:textSize">23px</item>
    </style>

    <style name="icon_copy_dialog">
        <item name="android:layout_width">60px</item>
        <item name="android:layout_height">60px</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:background">@drawable/bg_icon_close</item>
        <item name="android:padding">20px</item>
        <item name="android:scaleType">fitCenter</item>
    </style>

    <style name="tv_record_qrcode_guide">
        <item name="android:layout_marginTop">10px</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:text">@string/share_scan_text</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:gravity">center</item>
        <item name="android:textSize">32px</item>
    </style>

    <style name="tv_music_speed">
        <item name="android:layout_width">140px</item>
        <item name="android:layout_height">40px</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:background">@drawable/bg_music_speed</item>
        <item name="android:gravity">center</item>
        <item name="android:text">@string/music_speed_1</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">23px</item>
    </style>

    <style name="style_info_bar_view">
        <item name="android:layout_marginRight">30px</item>
        <item name="android:layout_marginTop">20px</item>
        <item name="android:layout_width">50px</item>
        <item name="android:layout_height">50px</item>
        <item name="android:scaleType">fitCenter</item>
    </style>

    <style name="style_select_mode_checkbox">
        <item name="android:layout_marginRight">30px</item>
        <item name="android:layout_width">50px</item>
        <item name="android:layout_height">50px</item>
    </style>

    <style name="style_czur_share_switch">
        <item name="android:layout_width">94px</item>
        <item name="android:layout_height">44px</item>
        <item name="android:layout_marginTop">40px</item>
        <item name="android:layout_marginEnd">40px</item>
    </style>

    <style name="style_czur_share_switch_text">
        <item name="android:layout_width">320px</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:layout_marginEnd">5px</item>
        <item name="android:textSize">30px</item>
    </style>

    <style name="style_czur_share_sort_text">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:paddingEnd">10px</item>
        <item name="android:textSize">24px</item>
    </style>

    <style name="style_czur_share_delete_text">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">30px</item>
        <item name="android:text">@string/str_device_enable_share</item>
    </style>

    <style name="style_share_pop_layout">
        <item name="android:layout_width">300px</item>
        <item name="android:layout_height">60px</item>
        <item name="android:paddingLeft">28px</item>
        <item name="android:paddingRight">28px</item>
    </style>

    <style name="style_share_pop_text">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">24px</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:textColor">@color/text_common</item>
    </style>

    <style name="style_share_pop_img">
        <item name="android:layout_width">22px</item>
        <item name="android:layout_height">22px</item>
        <item name="android:layout_alignParentRight">true</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:src">@drawable/ic_share_pick_on</item>
    </style>

    <style name="local_info_title_tv">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginBottom">15px</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:textSize">20px</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="bottom_text_style">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginBottom">60px</item>
    </style>

    <style name="device_name_tv_style">
        <item name="android:maxWidth">800px</item>
    </style>

    <style name="et_encryption">
        <item name="android:layout_width">80px</item>
        <item name="android:layout_height">104px</item>
        <item name="android:gravity">center</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:textCursorDrawable">@null</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textSize">60px</item>
        <item name="android:inputType">numberPassword</item>
        <item name="android:cursorVisible">false</item>
    </style>

    <style name="encryption_input_et">
        <item name="android:layout_height">80px</item>
        <item name="android:background">@drawable/bg_encryption_et</item>
        <item name="android:lines">1</item>
        <item name="android:paddingHorizontal">20px</item>
        <item name="android:textColor">@color/text_common</item>
        <item name="android:textSize">30px</item>
        <item name="android:textStyle">bold</item>
        <item name="android:gravity">center_vertical</item>
    </style>
</resources>
