<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:sharedUserId="android.uid.system"
    tools:ignore="ProtectedPermissions">

    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="com.czur.starry.contentProvider.rw.sp" />
    <uses-permission android:name="android.permission.INTERNET" /> <!-- 阿里云OSS要求添加的权限 -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />
    <uses-permission android:name="android.permission.WRITE_MEDIA_STORAGE" />
    <!--  安装apk  -->
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />


    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />

    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />

    <uses-feature
        android:name="android.hardware.nfc.hce"
        android:required="true" />

    <uses-permission
        android:name="android.permission.READ_LOGS"
        tools:ignore="ProtectedPermissions" />

    <uses-permission
        android:name="android.hardware.usb.host"
        android:required="false" />

    <uses-feature
        android:name="android.hardware.usb.host"
        android:required="true" />

    <application
        android:name=".base.FileApp"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="true">

        <provider
            android:name=".share.ShareFileProvider"
            android:authorities="com.czur.starry.device.file.share.ShareFileProvider"
            android:enabled="true"
            android:exported="true" />

        <activity
            android:name=".view.activity.FileMainPageActivity"
            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|uiMode|screenSize|navigation|layoutDirection"
            android:excludeFromRecents="false"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@style/BaseAppTheme"
            android:windowSoftInputMode="adjustPan">

            <intent-filter>
                <action android:name="com.czur.starry.device.file.BOOT_APP" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity
            android:name=".view.encryption.setting.CreatePwdActivity"/>

        <activity
            android:name=".view.encryption.setting.EncryptionSettingActivity" />

        <activity
            android:name=".view.activity.WebActivity"
            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|uiMode|screenSize|navigation|layoutDirection"
            android:exported="true"
            android:label="@string/webview_name"
            android:launchMode="singleTask"
            android:theme="@style/BaseAppTheme">
            <intent-filter tools:ignore="AppLinkUrlError">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <!--ppt-->
                <data android:mimeType="application/vnd.ms-powerpoint" />
                <!--xls-->
                <data android:mimeType="application/vnd.ms-excel" />
                <!--doc、dot-->
                <data android:mimeType="application/msword" />
                <!--docx-->
                <data android:mimeType="application/vnd.openxmlformats-officedocument.wordprocessingml.document" />
                <!--potx-->
                <data android:mimeType="application/vnd.openxmlformats-officedocument.presentationml.template" />
                <!--pptx-->
                <data android:mimeType="application/vnd.openxmlformats-officedocument.presentationml.presentation" />
                <!--xlsx-->
                <data android:mimeType="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" />
                <!--xltx-->
                <data android:mimeType="application/vnd.openxmlformats-officedocument.spreadsheetml.template" />
                <!--ppsx-->
                <data android:mimeType="application/vnd.openxmlformats-officedocument.presentationml.slideshow" />
                <!--dotx-->
                <data android:mimeType="application/vnd.openxmlformats-officedocument.wordprocessingml.template" />
            </intent-filter>
        </activity>
        <activity
            android:name=".view.activity.MediaActivity"
            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|uiMode|screenSize|navigation|layoutDirection"
            android:excludeFromRecents="true"
            android:exported="true"
            android:process=":MediaActivity"
            android:theme="@style/MediaAtyTheme">
            <intent-filter>
                <action android:name="com.czur.starry.device.file.EXTERNAL_MEDIA" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity
            android:name=".view.activity.PhotoPagerActivity"
            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|uiMode|screenSize|navigation|layoutDirection"
            android:theme="@style/BaseAppTheme" />
        <activity
            android:name=".view.activity.PDFActivity"
            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|uiMode|screenSize|navigation|layoutDirection"
            android:exported="true"
            android:label="@string/pdf_view_name"
            android:theme="@style/BaseAppTheme">
            <intent-filter tools:ignore="AppLinkUrlError">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="application/pdf" />
            </intent-filter>
        </activity>

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="com.czur.starry.device.file.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_paths" />
        </provider>
        <provider
            android:name=".share.FileLiveProvider"
            android:authorities="com.czur.starry.device.livedata.fileprovider"
            android:enabled="true"
            android:exported="true" />

        <service
            android:name=".service.FileWatcherService"
            android:enabled="true"
            android:exported="true"
            android:priority="1000"
            android:process=":FileWatcherService">
            <intent-filter>
                <action android:name="android.intent.action.FileWatcherService" />
                <action android:name="com.czur.starry.device.file.FILE_WATCH_UPDATE" />
            </intent-filter>
        </service>

        <service
            android:name=".server.ServerService"
            android:enabled="true"
            android:exported="true"
            android:foregroundServiceType="dataSync"
            android:priority="1000"
            tools:ignore="ForegroundServicePermission" />

        <service
            android:name=".service.ScheduleCleanService"
            android:enabled="true"
            android:exported="true"
            android:foregroundServiceType="dataSync"
            android:priority="1000"
            android:process=":ScheduleCleanService"
            tools:ignore="ForegroundServicePermission" />
    </application>

</manifest>