package com.czur.starry.device.file.server.component.servlet.get

import com.czur.starry.device.file.manager.LocalEncryptionManager
import com.czur.starry.device.file.server.common.FileShareConstant
import com.czur.starry.device.file.server.component.anno.Servlet
import com.czur.starry.device.file.server.component.servlet.HttpServlet
import com.czur.starry.device.file.server.entity.CheckPwdEntity
import com.czur.starry.device.file.server.util.CZHttpRequest
import com.czur.starry.device.file.server.util.CZHttpResponse

/**
 * Created by 陈丰尧 on 2024/12/17
 */
private const val PARAM_KEY_PWD = "pwd"
private const val PARAM_KEY_ROOT_KEY = "rootKey"

@Servlet(path = FileShareConstant.GET_CHECK_PWD)
class CheckPwdServlet : HttpServlet() {
    override suspend fun onConnect(request: CZHttpRequest, response: CZHttpResponse) {
        val pwd = request.getPathParam(PARAM_KEY_PWD) ?: ""
        val rootKey = request.getPathParam(PARAM_KEY_ROOT_KEY) ?: ""

        if (pwd.isEmpty() || rootKey.isEmpty()) {
            // 参数错误
            response.mkMsg(FileShareConstant.COMMON_ERROR_PARAM, "param error")
            return
        }

        val needPwd = when (rootKey) {
            FileShareConstant.FILE_ENCRYPT -> true
            else -> false
        }
        val checkRes = checkPwd(pwd, rootKey)

        response.content = CheckPwdEntity(needPwd, checkRes || !needPwd)
    }

    /**
     * 检查密码
     */
    private suspend fun checkPwd(pwd: String, rootKey: String): Boolean {
        return when (rootKey) {
            FileShareConstant.FILE_ENCRYPT -> LocalEncryptionManager.checkPwd(pwd)
            else -> true
        }
    }
}