package com.czur.starry.device.file.manager.oss

import com.alibaba.sdk.android.oss.ClientException
import com.alibaba.sdk.android.oss.OSS
import com.alibaba.sdk.android.oss.ServiceException
import com.alibaba.sdk.android.oss.model.CopyObjectRequest
import com.alibaba.sdk.android.oss.model.DeleteMultipleObjectRequest
import com.alibaba.sdk.android.oss.model.GetObjectRequest
import com.alibaba.sdk.android.oss.model.ListObjectsRequest
import com.alibaba.sdk.android.oss.model.MultipartUploadRequest
import com.alibaba.sdk.android.oss.model.OSSObjectSummary
import com.alibaba.sdk.android.oss.model.PutObjectRequest
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.data.provider.UserHandler
import com.czur.starry.device.baselib.network.HttpManager
import com.czur.starry.device.baselib.network.core.NoNetworkException
import com.czur.starry.device.baselib.network.core.common.ResCode
import com.czur.starry.device.baselib.network.core.exception.MiaoHttpException
import com.czur.starry.device.file.bean.FileEntity
import com.czur.starry.device.file.exp.UploadException
import com.czur.starry.device.file.manager.oss.manager.OSSRequestManager
import com.czur.starry.device.file.manager.oss.manager.OssTaskState
import com.czur.starry.device.file.utils.isEmpty
import com.czur.starry.device.file.utils.toFileListForOss
import com.czur.starry.device.temposs.TempOSSUtil
import com.czur.starry.device.temposs.TmpOSSType
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import okhttp3.internal.closeQuietly
import java.io.File


/**
 * Created by 陈丰尧 on 1/6/21
 */
object OSSManager {
    private const val TAG = "OSSManager"
    private var _bucketName = UserHandler.bucketName
    private const val PART_COUNT = 100 * 1024L // 分片大小  阿里云下限是100k, 小一点比较不容易失败

    val ossService: IOSSService by lazy { HttpManager.getService() }

    private var ossInstance = OSSInstance()

    /**
     * 用来上传临时文件
     */
    private val tmpOssUtil = TempOSSUtil(TmpOSSType.TEMP_UPLOAD)

    private val fileOss: OSS?
        get() = ossInstance.getOSS()

    /**
     * 用来管理异步请求的
     */
    private val asyncRequestManager by lazy { OSSRequestManager(fileOss!!) }

    /**
     * 刷新BucketName
     */
    fun refreshBucketInfo() {
        logTagV(TAG,"刷新OSS信息")
        _bucketName = UserHandler.bucketName
        ossInstance = OSSInstance()
    }

    private suspend fun getBucketName(): String {
        if (_bucketName.isEmpty()) {
            logTagW(TAG, "Bucket_name为空, 尝试重新获取BucketName")
            UserHandler.updateUserInfo()
            _bucketName = UserHandler.bucketName
            if (_bucketName.isEmpty()) {
                logTagE(TAG, "重新获取BUCKET_NAME,仍然为空!!")
                throw MiaoHttpException(ResCode.RESULT_CODE_NO_LOGIN, "bucketName为空, 可能是没有登录")
            }
        }
        return _bucketName
    }


    /**
     * 上传临时文件到OSS上
     * @return 上传完成后的URL 如果上传失败, 则会返回空字符串
     */
    suspend fun uploadTmp(localFilePath: String): String = withContext(Dispatchers.IO) {
        tmpOssUtil.upload(
            localFilePath,
            randomFileName = true,
            remotePath = "${Constants.SERIAL}/tmpFile"
        )
            .ossKey
    }

    /**
     * 删除OSS上的文件
     */
    suspend fun deleteFiles(delFiles: List<FileEntity>) = withContext(Dispatchers.IO) {
        // 批量删除
        val bucketName = getBucketName()
        logTagD(TAG, "删除-Bucket:${bucketName}")
        val delListChunked = getFilePathList(delFiles)
            .chunked(1000) // OSS 最多一次删除1000个文件,所以按照1000分组

        val failedList = delListChunked.flatMap { delList ->
            logTagD(TAG, "删除List:${delList}")
            logTagD(TAG, "删除个数:${delList.size}")
            val request = DeleteMultipleObjectRequest(
                bucketName, delList, false
            )
            try {
                val result = fileOss!!.deleteMultipleObject(request)
                logTagD(TAG, "删除失败列表: ${result.failedObjects}")
                // 没有失败的文件,表示删除成功
                if (result.failedObjects.isNullOrEmpty()) {
                    emptyList<String>()
                } else {
                    result.failedObjects
                }
            } catch (exp: Throwable) {
                logTagW(TAG, "删除云端文件失败", tr = exp)
                delList
            }
        }

        failedList.isNullOrEmpty()
    }

    /**
     * 将FileEntity转换为路径, 包含文件夹下的每个文件
     */
    private suspend fun getFilePathList(fileEntities: List<FileEntity>): List<String> {
        val result = mutableListOf<String>()
        fileEntities.forEach {
            if (it.isDir()) {
                // 是文件夹
                // 列举所有包含指定前缀的文件并删除。
                val prefix = it.absPath
                val names = getSubFileSummary(prefix).map { ossSummary ->
                    ossSummary.key
                }
                result.addAll(names)
            } else {
                // 添加自身路径
                result.add(it.absPath)
            }
        }
        return result
    }

    /**
     * 获取OSS上的信息
     * @param prefix 前缀, 就是文件路径
     */
    private suspend fun getSubFileSummary(
        prefix: String,
    ): List<OSSObjectSummary> {
        logTagD(TAG, "getSubFileSummary,prefix:${prefix}")
        val bucketName = getBucketName()
        val summaryList = mutableListOf<OSSObjectSummary>()
        var nextMarker: String? = null
        do {
            val listObjectsRequest = ListObjectsRequest(
                bucketName, prefix, nextMarker, null, null
            )
            val objectListing = fileOss!!.listObjects(listObjectsRequest)
            if (objectListing.objectSummaries.size > 0) {
                for (s in objectListing.objectSummaries) {
                    logTagD(TAG, "key name: " + s.key)
                    summaryList.add(s)
                }
            }
            nextMarker = objectListing.nextMarker
        } while (objectListing.isTruncated)
        return summaryList
    }

    /**
     * 创建文件夹或空文件
     */
    private suspend fun createNewFolderOrFile(bucketName: String, ossKey: String): Boolean =
        withContext(Dispatchers.IO) {
            val put =
                PutObjectRequest(
                    bucketName, ossKey,
                    byteArrayOf()
                )
            try {
                fileOss?.putObject(put) ?: throw NoNetworkException()
                return@withContext true
            } catch (e: ClientException) {
                // 本地异常，如网络异常等。
                e.printStackTrace()
            } catch (e: ServiceException) {
                // 服务异常。
                logTagE("RequestId", e.requestId)
                logTagE("ErrorCode", e.errorCode)
                logTagE("HostId", e.hostId)
                logTagE("RawMessage", e.rawMessage)
            }
            return@withContext false
        }

    /**
     * 在指定目录新建文件夹 或文件
     * @param folderName 要新建文件夹的名字
     * @param targetDir  要在哪新建文件夹
     * @return 如果新建成功,则返回true,创建失败,返回false
     */
    suspend fun createNewFolderOrFile(folderName: String, targetDir: FileEntity): Boolean {
        return createNewFolderOrFile(getBucketName(), getOSSPath(folderName, targetDir))
    }

    /**
     * 判断当前文件夹中是否已经存在这个名字
     * @param name 文件名
     * @param targetDir 文件所在的文件夹
     * @param isDir: 这个文件是否是文件夹
     */
    suspend fun hasThisFile(name: String, targetDir: FileEntity, isDir: Boolean): Boolean =
        withContext(Dispatchers.IO) {
            val objKey = getOSSPath(name, targetDir, isDir)
            logTagD(TAG, "hasThisName:$objKey")
            fileOss?.doesObjectExist(getBucketName(), objKey) ?: throw NoNetworkException()
        }

    /**
     * 生成符合oss规范的路径名称
     * @param name 文件或文件夹名称
     * @param targetDir 文件或文件所在的路径
     * @param isDir 要生成的路径是文件夹还是文件, 默认是文件夹
     */
    private fun getOSSPath(name: String, targetDir: FileEntity, isDir: Boolean = true): String =
        File(targetDir.absPath, name).path + if (isDir) "/" else ""


    private suspend fun copyObj(from: String, to: String) = withContext(Dispatchers.IO) {
        logTagD(TAG, "复制Obj:")
        logTagD(TAG, "From:$from")
        logTagD(TAG, "TO:$to")
        val bucketName = getBucketName()
        val copyRequest = CopyObjectRequest(
            bucketName,
            from,
            bucketName,
            to
        )
        // 先复制
        fileOss!!.copyObject(copyRequest)
        true
    }

    /**
     * 获取文件大小
     */
    suspend fun getFileLength(fileEntity: FileEntity): Long = withContext(Dispatchers.IO) {
        logTagD(TAG, "getFileLength")
        if (!fileEntity.isDir()) {
            // 如果是文件,那么文件大小是直接写在fileEntity中了
            // 就不再去请求OSS了, 这样比较快
            fileEntity.fileSize
        } else {
            getSubFileSummary(fileEntity.absPath).sumOf {
                it.size
            }
        }

    }

    /**
     * 下载缓存文件
     */
    private suspend fun downloadCache(
        src: OSSObjectSummary,
        destDir: FileEntity,
        basePath: String,
    ): String {
        // eTag 会有引号
        val eTag = src.eTag.removeSurrounding("\"")
        val extension = src.key.substringAfter(basePath)
            .substringAfterLast('.', "")
        // 使用Etag作为缓存图片的文件名
        val cacheFile = File(destDir.absPath, "${eTag}.${extension}")
        if (cacheFile.exists() && cacheFile.length() == src.size) {
            // 缓存文件存在
            logTagV(TAG, "缓存文件:${cacheFile.name}存在, 跳过下载")
            return cacheFile.absolutePath
        }
        logTagV(TAG, "下载缓存文件")
        if (cacheFile.exists()) {
            logTagV(TAG, "删除之前未完成的缓存文件")
            cacheFile.delete()
        }
        return withContext(Dispatchers.IO) {
            val get = GetObjectRequest(getBucketName(), src.key)
            val getResult = fileOss!!.getObject(get)
            val inputStream = getResult.objectContent

            val cacheDir = File(destDir.absPath)
            if (!cacheDir.exists()) {
                cacheDir.mkdirs()
            }

            val fos = cacheFile.outputStream()
            logTagV(TAG, "开始下载缓存文件")
            inputStream.use {
                it.copyTo(fos)
            }
            fos.closeQuietly()
            cacheFile.absolutePath
        }
    }

    /**
     * 从OSS下载文件
     * @param src OSS 上的文件
     * @param destDir 本地路径
     * @param forCache 本次下载是否是为了数据缓存
     */
    suspend fun download(
        src: FileEntity,
        destDir: FileEntity,
        forCache: Boolean = false,
        listener: ((finishSize: Long) -> Unit)? = null,
    ): String = withContext(Dispatchers.IO) {
        logTagD(TAG, "下载OSS文件")
        val basePath = src.parentPath
        val baseLocalFile = File(destDir.absPath) // 这次要复制的本地路径

        // 获取要下载的文件列表
        val fileSummaries = getSubFileSummary(src.absPath)
        if (forCache && fileSummaries.size == 1) {
            // 如果是forCache的, 只能是下载一个文件,不能是整个文件夹
            return@withContext downloadCache(fileSummaries[0], destDir, basePath)
        }

        // 下载非缓存文件
        val downloadKeys = fileSummaries.map { it.key }
        // 进度信息
        val progressListener = listener?.let {
            SizeListener<GetObjectRequest>(it)
        }

        val taskList = downloadKeys.map { ossKey ->
            async {
                val get = GetObjectRequest(getBucketName(), ossKey)
                progressListener?.let {
                    get.setProgressListener(it)
                }
                val getResult = fileOss!!.getObject(get)
                // 创建本地文件
                // 要创建文件的相对路径,如果是文件,则是文件名 如果是路径,则是文件夹名字并且后面还带/
                val relativePaths = ossKey.substringAfter(basePath)
                val file = File(baseLocalFile, relativePaths)
                if (relativePaths.endsWith("/")) {
                    // 这是一个文件夹
                    logTagD(TAG, "在本地创建文件夹:${file.absolutePath}")
                    file.mkdirs()
                } else {
                    // 这是一个文件
                    // 先创建父文件夹
                    val parent = file.parentFile
                    parent?.let { p ->
                        if (!p.exists()) {
                            logTagV(TAG, "创建父文件夹")
                            p.mkdirs()
                        }
                    }
                    logTagV(TAG, "复制文件")

                    // 下载
                    if (file.exists()) {
                        // 如果文件存在, 则将文件删除
                        // 防止写出错
                        file.delete()
                    }
                    // 获取输入流
                    val buffer = ByteArray(2048)
                    var len = -1
                    val fos = file.outputStream()
                    val inputStream = getResult.objectContent
                    try {
                        do {
                            len = inputStream.read(buffer)
                            if (len != -1) {
                                if (isActive) {
                                    fos.write(buffer, 0, len)
                                } else {
                                    logTagD(TAG, "下载被取消")
                                    file.deleteRecursively()
                                    break
                                }
                            }
                        } while (len != -1)
                    } catch (exp: Exception) {
                        logTagD(TAG, "出错后删除临时文件")
                        file.deleteRecursively()
                        fos.closeQuietly()
                        throw exp
                    }
                    fos.closeQuietly()

                }

                file.absolutePath
            }
        }

        // 等待下载完成
        taskList.forEach {
            // 等待所有协程完成
            val localPath = it.await()
            logTagV(TAG, "下载完成:${localPath}")
        }

        logTagD(TAG, "所有下载请求全部完成")
        // 可能下载的是文件夹,所以重新计算一次路径返回
        val destFile = File(baseLocalFile, src.absPath.substringAfter(basePath))
        if (!isActive) {
            // 删除掉所有的临时文件
            destFile.deleteRecursively()
            ""
        } else {
            destFile.absolutePath
        }
    }

    suspend fun upload(
        src: FileEntity,
        destDir: FileEntity,
        needUpdateInfo: Boolean = true,
        listener: (finishSize: Long) -> Unit
    ) {
        withContext(Dispatchers.IO) {
            val bucketName = try {
                getBucketName()
            } catch (exp: Exception) {
                logTagE(TAG, "bucketName获取失败", tr = exp)
                return@withContext
            }

            val finishSize = mutableMapOf<String, Long>()

            /**
             * 更新总完成进度
             */
            fun updateFinishSize(taskId: String, size: Long) {
                finishSize[taskId] = size
                val sumSize = finishSize.values.sum()
                launch {
                    listener(sumSize)
                }
            }

            val srcDir = src.parentPath
            // 将src展开成单独的文件
            val fileList = src.toFileListForOss()
            fileList.forEach { file ->
                // 每次循环之前都进行检查
                if (!isActive) {
                    logTagI(TAG, "上传取消")
                    // 上传取消, 跳出循环
                    return@forEach
                }

                // 拼接文件名
                val ossKey =
                    getOSSPath(
                        file.absolutePath.substringAfter(srcDir),
                        destDir,
                        file.isDirectory
                    )
                logTagD(TAG, "上传:ossKey:$ossKey")
                when {
                    file.isDirectory -> {
                        // 如果是文件夹
                        logTagD(TAG, "创建文件夹")
                        // 文件夹一下就完事了, 不需要创建Task等待
                        createNewFolderOrFile(bucketName, ossKey)
                    }
                    file.isEmpty -> {
                        logTagD(TAG, "创建空文件:${ossKey}")
                        createNewFolderOrFile(bucketName, ossKey)
                    }
                    else -> {
                        logTagD(TAG, "上传文件:${ossKey}")
                        val rq: MultipartUploadRequest<MultipartUploadRequest<*>> =
                            MultipartUploadRequest(bucketName, ossKey, file.absolutePath)
                        rq.partSize = PART_COUNT
                        // 添加Task
                        val taskId = asyncRequestManager.addUploadRequest(rq, file.length())
                        logTagV(TAG, "taskID:${taskId} (${ossKey})")

                        // 循环等待 上传完成
                        doUpload(ossKey, taskId, ::updateFinishSize)
                    }
                }
                logTagV(TAG, "上传完成:${ossKey}")
            }
        }
    }

    /**
     * 执行上传操作
     */
    private suspend fun CoroutineScope.doUpload(
        ossKey: String,
        taskId: String,
        updateFinishSize: (String, Long) -> Unit
    ) {
        while (true) {
            if (!isActive) {
                logTagD(TAG, "上传已取消:${ossKey}")
                asyncRequestManager.clearTask(taskId)
                break
            }
            try {
                // 每 200ms检查一次进度
                delay(200)
            } catch (exp: Exception) {
                logTagD(TAG, "上传已取消:${ossKey}")
                asyncRequestManager.clearTask(taskId)
                break
            }
            when (asyncRequestManager.getTaskStatus(taskId)) {
                OssTaskState.RUNNING -> {
                    // Task正在运行, 获取进度
                    val finishSize = asyncRequestManager.getFinishSize(taskId)
                    updateFinishSize(taskId, finishSize)
                }
                OssTaskState.SUCCESS -> {
                    val finishSize = asyncRequestManager.getTotalSize(taskId)
                    updateFinishSize(taskId, finishSize)
                    asyncRequestManager.clearTask(taskId)
                    break
                }
                OssTaskState.FAIL -> {
                    logTagW(TAG, "上传发生异常!")
                    val exp = asyncRequestManager.getException(taskId)
                    asyncRequestManager.clearTask(taskId)
                    // 上传失败, 抛出异常
                    throw UploadException(exp)
                }
                null -> {
                    logTagW(TAG, "根据TaskID:${taskId},没有找到对应Task")
                    break
                }
            }
        }
    }


    /**
     * 复制文件
     * @param src 要复制的文件
     * @param destDir 目的地文件夹
     * @param listener 进度回调, 因为复制没有提供进度信息, 所以按照完成个数 范围0-100
     */
    suspend fun copyFile(
        src: FileEntity,
        destDir: FileEntity,
        listener: (finishSize: Long) -> Unit,
    ) = withContext(Dispatchers.IO) {
        if (!isActive) {
            logTagV(TAG, "复制被取消, 不执行")
            return@withContext
        }
        // 要复制的文件列表
        val downloadKeys = getSubFileSummary(src.absPath).map { it.key }
        // src所在的文件夹路径
        val basePath = src.parentPath
        val pre = 100F / downloadKeys.size
        var finishSize = 0

        downloadKeys.forEach { from ->
            if (!isActive) {
                logTagV(TAG, "复制被取消:${from}")
                return@forEach
            }
            val name = from.substringAfter(basePath)
            val to = destDir.absPath + name
            copyObj(from, to)
            finishSize++
            // 复制OSS没有提供进度信息
            // 所以只能大致用文件个数估算
            // 之所以没有使用文件大小, 因为文件大小还需要请求网络才能拿到
            listener(finishSize * pre.toLong())
        }
        listener(100L)
        logTagD(TAG, "复制完成")
    }

    /**
     * 获取全部文件列表
     */
    suspend fun getAllCloudFilePath(fileEntities: List<FileEntity>): List<String> {
        val pathUtil = OSSPathUtil(fileOss!!, getBucketName())
        return pathUtil.getAllCloudFilePath(fileEntities)
    }

    /**
     * 获取
     */
    suspend fun getExpectFilePathByLocal(
        localFiles: List<FileEntity>,
        cloudDir: FileEntity
    ): List<String> {
        val pathUtil = OSSPathUtil(fileOss!!, getBucketName())
        return pathUtil.getExpectFilePathByLocal(localFiles, cloudDir)
    }

    suspend fun getCopyOrMoveFilePath(
        srcFiles: List<FileEntity>,
        destFileEntity: FileEntity
    ): List<String> {
        val srcPaths = getAllCloudFilePath(srcFiles)
        val parentPath = srcFiles.first().parentPath
        val destPath = destFileEntity.absPathWithSuffix
        val exceptPath = srcPaths.map {
            it.replace(parentPath, destPath)
        }
        return srcPaths + exceptPath
    }


}