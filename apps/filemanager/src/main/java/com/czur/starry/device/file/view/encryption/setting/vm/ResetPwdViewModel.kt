package com.czur.starry.device.file.view.encryption.setting.vm

import androidx.lifecycle.ViewModel
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.utils.VerifyException
import com.czur.starry.device.file.manager.LocalEncryptionManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.map

/**
 * Created by 陈丰尧 on 2024/12/12
 */
private const val TAG = "ResetPwdViewModel"

class ResetPwdViewModel : ViewModel() {
    private val _userInputPwdFlow = MutableStateFlow("")
    private val userInputPwdFlow = _userInputPwdFlow.asStateFlow()
    val userInputPwd: String
        get() = userInputPwdFlow.value

    val finishEnableFlow = userInputPwdFlow.map { it.length >= LocalEncryptionManager.PWD_LENGTH }

    fun onUserInputPwdChanged(pwd: String) {
        _userInputPwdFlow.value = pwd
    }

    /**
     * 更新密码
     */
    suspend fun updateEncryptionInfo(email: String?, pwd: String): Result<Unit> {
        logTagV(TAG, "updateEncryptionInfo email: $email, pwd: $pwd")
        if (pwd.isEmpty() || pwd.length < LocalEncryptionManager.PWD_LENGTH) {
            return Result.failure(VerifyException(""))
        }
        if (!email.isNullOrEmpty()) {
            LocalEncryptionManager.updateEncryptionEmail(email)
        }

        LocalEncryptionManager.updatePwd(pwd)
        return Result.success(Unit)
    }
}