package com.czur.starry.device.file.server.msg

import io.netty.channel.ChannelHandlerContext
import java.util.concurrent.atomic.AtomicBoolean

/**
 *  author : <PERSON><PERSON><PERSON>
 *  time   :2023/11/02
 */

class Connection(private val context: ChannelHandlerContext) {
    private val isFileUploading = AtomicBoolean(false)

    fun getContext(): ChannelHandlerContext {
        return this.context
    }

    fun startFileUpload() {
        isFileUploading.set(true)
    }

    fun endFileUpload() {
        isFileUploading.set(false)
    }

    fun isFileUploading(): Boolean {
        return isFileUploading.get()
    }
}