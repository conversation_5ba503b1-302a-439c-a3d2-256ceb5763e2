package com.czur.starry.device.file.view.record

import androidx.fragment.app.FragmentTransaction
import androidx.fragment.app.commitNow
import com.czur.starry.device.baselib.base.v2.fragment.CZViewBindingFragment
import com.czur.starry.device.file.R
import com.czur.starry.device.file.base.RefreshAble
import com.czur.starry.device.file.databinding.FragmentMeetingRecordBinding
import com.czur.starry.device.file.view.ControlBarFragment
import com.czur.starry.device.file.view.localmeeting.LocalMeetingListFragment

/**
 * Created by 陈丰尧 on 2023/6/2
 * 会议记录
 */
private const val FRAGMENT_TAG_LOCAL_RECORD_CONTROL_BAR = "localRecordControlBar"
private const val FRAGMENT_TAG_LOCAL_RECORD_FILE_PAD = "localRecordFilePad"


class MeetingRecordFragment : CZViewBindingFragment<FragmentMeetingRecordBinding>(), RefreshAble{

    private val localMeetingFilePad = LocalMeetingListFragment()
    private val localMeetingControlBar =
        ControlBarFragment.getInstance(ControlBarFragment.TYPE_LOCAL_MEETING, localMeetingFilePad)

    override fun FragmentMeetingRecordBinding.initBindingViews() {

        localMeetingFilePad.setControlBar(localMeetingControlBar)

        childFragmentManager.commitNow {
            showOrAddFragment(FRAGMENT_TAG_LOCAL_RECORD_CONTROL_BAR)
            showOrAddFragment(FRAGMENT_TAG_LOCAL_RECORD_FILE_PAD)
        }
    }

    private fun FragmentTransaction.showOrAddFragment(tag: String) {
        val fragment = childFragmentManager.findFragmentByTag(tag)
        if (fragment == null) {
            when (tag) {

                FRAGMENT_TAG_LOCAL_RECORD_CONTROL_BAR ->
                    add(R.id.controlBar, localMeetingControlBar, tag)


                FRAGMENT_TAG_LOCAL_RECORD_FILE_PAD ->
                    add(R.id.filePad, localMeetingFilePad, tag)

                else -> throw IllegalArgumentException("unknown tag: $tag")
            }
        } else {
            show(fragment)
        }
    }

    override fun refresh(needMoveToTop: Boolean) {
        localMeetingFilePad.refresh(needMoveToTop)
    }
}