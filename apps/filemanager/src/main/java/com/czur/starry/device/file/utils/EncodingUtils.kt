package com.czur.starry.device.file.utils

import android.graphics.Bitmap
import android.graphics.BitmapShader
import android.graphics.Canvas
import android.graphics.ColorFilter
import android.graphics.Paint
import android.graphics.PixelFormat
import android.graphics.Shader
import android.graphics.drawable.Drawable

object EncodingUtils {

    class RoundImageDrawable(mBitmap: Bitmap,radius: Float) : Drawable(){
        private val mPaint: Paint
        private val mWidth: Int
        private val mRadius: Float

        init {
            val bitmapShader = BitmapShader(
                mBitmap, Shader.TileMode.CLAMP,
                Shader.TileMode.CLAMP
            )
            mPaint = Paint()
            mPaint.isAntiAlias = true
            mPaint.shader = bitmapShader
            mWidth = mBitmap.width.coerceAtMost(mBitmap.height)
            mRadius = radius
        }
        override fun draw(canvas: Canvas) {
            canvas.drawRoundRect(0f, 0f, mWidth.toFloat(), mWidth.toFloat(), mRadius, mRadius, mPaint)
        }

        override fun setAlpha(alpha: Int) {
            mWidth
        }

        override fun setColorFilter(colorFilter: ColorFilter?) {
            mPaint.colorFilter = colorFilter
        }

        override fun getOpacity(): Int {
            return PixelFormat.TRANSLUCENT
        }
        override fun getIntrinsicWidth(): Int {
            return mWidth
        }

        override fun getIntrinsicHeight(): Int {
            return mWidth
        }
    }

}