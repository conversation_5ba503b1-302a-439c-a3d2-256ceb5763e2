package com.czur.starry.device.file.server.component.servlet.post

import com.czur.czurutils.encryption.md5
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.utils.doWithoutCatch
import com.czur.starry.device.file.db.entity.UploadFile
import com.czur.starry.device.file.server.common.FileShareConstant
import com.czur.starry.device.file.server.component.anno.Servlet
import com.czur.starry.device.file.server.component.servlet.HttpServlet
import com.czur.starry.device.file.server.msg.FileResultMessage
import com.czur.starry.device.file.server.msg.ServerHandler
import com.czur.starry.device.file.server.upload.UploadHelper
import com.czur.starry.device.file.server.util.CZHttpRequest
import com.czur.starry.device.file.server.util.CZHttpResponse
import com.czur.starry.device.file.server.util.fileUploadPath
import com.czur.starry.device.file.server.util.getFileName
import com.czur.starry.device.file.server.util.syncUploadFile
import io.netty.handler.codec.http.HttpContent
import io.netty.handler.timeout.ReadTimeoutException
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import java.io.File
import java.io.RandomAccessFile

/**
 * Created by 陈丰尧 on 2024/12/17
 */
private const val TAG = "FileUploadServlet"

@Servlet(path = FileShareConstant.POST_UPLOAD_FILE)
class FileUploadServlet : HttpServlet() {
    private var uploadFile: UploadFile? = null
    private var randomF: RandomAccessFile? = null
    private var totalSize: Long = 0
    private var targetSize: Long = 0L

    override suspend fun onConnect(request: CZHttpRequest, response: CZHttpResponse) {
        //获取header信息
        val info = UploadHelper.getHeaderInfo(request)
        targetSize = info.fileSize

        //获取数据库已有信息或者创建信息
        val currentTime = System.currentTimeMillis()
        val fileNewName = getFileName(info.fileName)
        val tempName = "$fileUploadPath/$fileNewName"
        var dataFile =
            UploadHelper.getFileInfo(info.fileName, info.fileMd5, info.deviceId)?.apply {
                this.isUploading = true
                this.isContinueInterrupt = false
            } ?: UploadFile(
                info.fileName,
                info.fileMd5,
                info.deviceId,
                tempName,
                fileNewName,
                currentTime,
                info.fileSize,
                isUploading = true
            )
        val id = UploadHelper.updateFileInfoData(dataFile)
        dataFile = dataFile.copy(
            id = id
        )

        //如果临时上传文件被删除了，更新信息
        if (!File(dataFile.tempName).exists()) {
            dataFile.tempName = tempName
            dataFile.fileNewName = fileNewName
            dataFile.createTime = currentTime
            File(dataFile.tempName).createNewFile()
            runBlocking {
                UploadHelper.updateFileInfoData(dataFile)
            }
        }
        uploadFile = dataFile.copy(
            id = id
        )
        withContext(Dispatchers.IO) {
            val raf = RandomAccessFile(dataFile.tempName, "rw").also {
                randomF = it
            }
            totalSize = raf.length()

            logTagD(TAG, "${dataFile.tempName} : length=${totalSize} startByte=${info.startByte}")

            if (raf.length() == info.startByte) {
                raf.seek(raf.length())
            } else {
                response.content = FileResultMessage(
                    FileShareConstant.BYTE_START_ERROR,
                    raf.length(),
                    "byteStart error"
                )
            }
        }

    }

    override suspend fun onContentReceive(
        httpContent: HttpContent,
        response: CZHttpResponse,
        isLast: Boolean
    ) {
        super.onContentReceive(httpContent, response, isLast)
        try {
            ServerHandler.semaphore.acquire()   // 申请信号量
            val content = httpContent.content()
            val byteBuffer = content.nioBuffer()
            while (byteBuffer.hasRemaining()) {
                val length = byteBuffer.remaining()
                randomF?.channel?.write(byteBuffer, totalSize)
                totalSize += length
            }
            byteBuffer.clear()
            delay(1)
        } catch (tr: Throwable) {
            logTagE(TAG, "read upload contentError", tr = tr)
            randomF?.close()
            response.content = FileResultMessage(
                FileShareConstant.UNKNOWN_ERROR,
                totalSize,
                "read upload contentError"
            )
            return
        } finally {
            ServerHandler.semaphore.release()
        }

        logTagD(TAG, "全部完成:target:${totalSize} / $targetSize")


        if (isLast || totalSize >= targetSize) {
            doAsLast(response)
        }

    }

    override suspend fun onExceptionCaught(e: Throwable) {
        super.onExceptionCaught(e)
        if (e is ReadTimeoutException) {
            logTagW(TAG, "读取数据超时:90s")
        } else {
            logTagE(TAG, "onExceptionCaught", tr = e)
        }

        doWithoutCatch(TAG) {
            randomF?.close()
        }
        doWithoutCatch {
            uploadFile?.let {
                UploadHelper.onUploadError(it)
            }
        }

    }

    private suspend fun doAsLast(response: CZHttpResponse) = withContext(Dispatchers.IO) {
        logTagD(TAG, "doAsLast")
        randomF?.close()
        syncUploadFile()
        totalSize = 0
        val loadedFile = uploadFile
        if (loadedFile == null) {
            logTagE(TAG, "uploadFile is null")
            response.mkMsg(FileShareConstant.UNKNOWN_ERROR, "uploadFile is null")
            return@withContext
        }
        val file = File(loadedFile.tempName)
        if (loadedFile.fileSize == file.length()) {
            onTransFinish(loadedFile, file, response)
        } else {
            if (loadedFile.fileSize < file.length()) {
                val result = UploadHelper.delFileInfoFromData(loadedFile.id)
                delay(200)
                logTagD(TAG, "===丢弃，重试====result===${result}")
                response.mkMsg(FileShareConstant.FILE_MISS, "上传文件超出指定文件大小，丢弃，重试")
                file.delete()
            } else {
                //待续传的中断标记
                loadedFile.isContinueInterrupt = true
                UploadHelper.updateFileInfoData(loadedFile)
                logTagD(TAG, "===文件缺失,待续传===")
                response.mkMsg(FileShareConstant.FILE_MISS, "文件缺失,待续传 ")
            }

        }
    }

    private suspend fun onTransFinish(
        loadedFile: UploadFile,
        tempFile: File,
        response: CZHttpResponse
    ) {
        logTagV(TAG, "onTransFinish: ${loadedFile.tempName}")
        if (loadedFile.md5 == tempFile.md5()) {
            UploadHelper.delFileInfoFromData(loadedFile.id)
            delay(200)
            logTagD(
                TAG,
                "===删除旧数据====result===id:${loadedFile.id}"
            )
            uploadFile = null
            response.mkMsg(FileShareConstant.TRANS_SUCCESS, "successful finish")
            UploadHelper.sendBroadCastToFileWatch(tempFile.name)
        } else {
            logTagW(TAG, "md5 verification failed")
            UploadHelper.delFileInfoFromData(loadedFile.id)
            delay(200)
            response.mkMsg(FileShareConstant.MD5_FAILED, "md5 verification failed")
            tempFile.delete()
        }
    }

    suspend fun endFileUploadOrDownload() {
        logTagD(TAG, "===endFileUploadOrDownload=")
        uploadFile?.let {
            doWithoutCatch(TAG) {
                if (it.isUploading) {
                    it.isUploading = false
                    UploadHelper.updateFileInfoData(it)
                }
            }
        }

        randomF?.let {
            doWithoutCatch(TAG) {
                it.close()
            }
        }
    }
}