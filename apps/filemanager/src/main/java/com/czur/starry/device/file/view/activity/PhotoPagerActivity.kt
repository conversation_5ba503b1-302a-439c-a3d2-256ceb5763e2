package com.czur.starry.device.file.view.activity

import android.os.Bundle
import android.view.ContextMenu
import android.view.KeyEvent
import android.view.MenuItem
import android.view.View
import androidx.activity.viewModels
import androidx.core.view.MenuCompat
import androidx.viewpager2.widget.ViewPager2
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.baselib.view.dialog.LoadingDialog
import com.czur.starry.device.file.R
import com.czur.starry.device.file.bean.FileEntity
import com.czur.starry.device.file.bean.PhotoInfo
import com.czur.starry.device.file.databinding.ActivityPhotoPagerBinding
import com.czur.starry.device.file.filelib.AccessType
import com.czur.starry.device.file.view.adapter.PhotoViewVPAdapter
import com.czur.starry.device.file.view.vm.PhotoViewModel

class PhotoPagerActivity : BasePAVActivity<ActivityPhotoPagerBinding>() {
    companion object {
        private const val TAG = "PhotoActivity"
    }

    override val pavViewModel: PhotoViewModel by viewModels()
    private val loadingDialog by lazy { LoadingDialog() }

    // 上一页下一页共用一个debounce时间, 因为按键也能翻页
    private var clickTime = 0L

    private val vpAdapter by lazy {
        PhotoViewVPAdapter()
    }

    // 当前旋转角度
    private var currentDegree = 0
    private var currentInfo: PhotoInfo? = null


    override fun onCreateContextMenu(
        menu: ContextMenu,
        v: View?,
        menuInfo: ContextMenu.ContextMenuInfo?
    ) {
        menuInflater.inflate(R.menu.menu_photo, menu)

        // 检查上一张和下一张 是否可用
        menu.getItem(0).apply {
            isEnabled = binding.photoPreIv.isEnabled
        }
        menu.getItem(1).apply {
            isEnabled = binding.photoNextIv.isEnabled
        }


        MenuCompat.setGroupDividerEnabled(menu, true)

        super.onCreateContextMenu(menu, v, menuInfo)
    }


    override fun onContextItemSelected(item: MenuItem): Boolean {

        when (item.itemId) {
            R.id.menuPhotoPre -> {
                if (binding.photoPreIv.isEnabled) {
                    binding.photoPreIv.performClick()
                }
            }

            R.id.menuPhotoNext -> {
                if (binding.photoNextIv.isEnabled) {
                    binding.photoNextIv.performClick()
                }
            }

            R.id.menuRotateLeft -> {
                currentInfo?.let {
                    if (!it.error) {
                        currentDegree -= 90
                        vpAdapter.setPhotoInfoDegree(pavViewModel.currentIndex, currentDegree)
                    }
                }
            }

            R.id.menuRotateRight -> {
                currentInfo?.let {
                    if (!it.error) {
                        currentDegree += 90
                        vpAdapter.setPhotoInfoDegree(pavViewModel.currentIndex, currentDegree)
                    }
                }
            }

            R.id.menuExit -> {
                finish()
            }
        }

        return super.onContextItemSelected(item)
    }

    override fun ActivityPhotoPagerBinding.initBindingViews() {

        photoPreIv.setOnDebounceClickListener {
            pavViewModel.currentIndex--
            // 上一张图片
            currentDegree = 0
            var targetIndex = viewpager.currentItem - 1
            if (targetIndex < 0 || targetIndex == 0) {
                targetIndex = 0
            }
            refreshPreAndNextBtnState()
            vpAdapter.resetPhotoInfo()
            viewpager.currentItem = targetIndex

            clickTime = System.currentTimeMillis()
        }
        photoNextIv.setOnDebounceClickListener {
            pavViewModel.currentIndex++
            // 下一张图片
            currentDegree = 0
            var targetIndex = viewpager.currentItem + 1
            if (targetIndex > pavViewModel.fileEntities.size - 1 || targetIndex == pavViewModel.fileEntities.size - 1) {
                targetIndex = pavViewModel.fileEntities.size - 1
            }
            refreshPreAndNextBtnState()
            vpAdapter.resetPhotoInfo()
            viewpager.currentItem = targetIndex
            clickTime = System.currentTimeMillis()
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)

        launch {
            pavViewModel.photoPathInfoFlow.collect {
                logTagD(TAG, "更新显示图片:${it}")
                currentInfo = it

                if (it.error) {
                    logTagW(TAG, "图片请求出错")
                    vpAdapter.refreshPhotoWithInfo(pavViewModel, it)
                    loadingDialog.dismiss()
                    return@collect
                }
                if (it.isNotEmpty()) {
                    if (it.filePath.isEmpty() && it.entityPath.isEmpty()) {
                        return@collect
                    }
                    vpAdapter.refreshPhotoWithInfo(pavViewModel, it)
                }
            }
        }

        pavViewModel.uiShowLive.observe(this) {
            if (it) {
                if (binding.photoPreIv.isEnabled) {
                    binding.photoPreIv.show()
                } else {
                    binding.photoPreIv.gone()
                }
                if (binding.photoNextIv.isEnabled) {
                    binding.photoNextIv.show()
                } else {
                    binding.photoNextIv.gone()
                }
            } else {
                binding.photoUIGroup.gone()
            }
        }


        binding.viewpager.registerOnPageChangeCallback(object :
            ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                pavViewModel.currentIndex = position
                vpAdapter.resetPhotoInfo()
                pavViewModel.loadPhotoPath(position)
                pavViewModel.loadNeighborPhotoPath(position)
                logTagD(
                    TAG,
                    "onPageSelected currentIndex ${binding.viewpager.currentItem}"
                )
                refreshPreAndNextBtnState()
            }
        })

        val formatDataList = formatDataList(pavViewModel.fileEntities)
        refreshPreAndNextBtnState()
        binding.viewpager.offscreenPageLimit = 1
        binding.viewpager.adapter = vpAdapter
        vpAdapter.setContext(this)
        vpAdapter.setData(formatDataList)
        binding.viewpager.setCurrentItem(pavViewModel.currentIndex, false)

    }

    override fun onInterceptKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        // 拦截音量键,
        return when (keyCode) {
            KeyEvent.KEYCODE_ZOOM_IN -> {
                vpAdapter.scaleUp(pavViewModel.currentIndex)
                true
            }

            KeyEvent.KEYCODE_ZOOM_OUT -> {
                vpAdapter.scaleDown(pavViewModel.currentIndex)
                true
            }

            KeyEvent.KEYCODE_DPAD_LEFT -> {
                if (pavViewModel.preEnable) {
                    pavViewModel.switchPre()
                }
                true
            }

            KeyEvent.KEYCODE_DPAD_RIGHT -> {
                if (pavViewModel.nextEnable) {
                    pavViewModel.switchNext()
                }
                true
            }

            else -> super.onInterceptKeyDown(keyCode, event)

        }
    }


    private fun formatDataList(fileEntities: List<FileEntity>): List<PhotoInfo> {
        val photoInfoList = mutableListOf<PhotoInfo>()
        // 循环fileEntities
        for (i in fileEntities.indices) {
            val filePath = if (fileEntities[i].belongTo == AccessType.LOCAL) {
                fileEntities[i].absPath
            } else {
                ""
            }
            photoInfoList.add(PhotoInfo(filePath, 1920, 1080, 0, fileEntities[i].absPath, false, i))
        }

        return photoInfoList
    }

    fun refreshPreAndNextBtnState() {
        if (pavViewModel.fileEntities.size == 1) {
            binding.photoPreIv.isEnabled = false
            binding.photoPreIv.visibility = View.GONE
            binding.photoNextIv.isEnabled = false
            binding.photoNextIv.visibility = View.GONE
        } else if (pavViewModel.currentIndex == 0) {
            binding.photoPreIv.isEnabled = false
            binding.photoPreIv.visibility = View.GONE
            binding.photoNextIv.isEnabled = true
            binding.photoNextIv.visibility = View.VISIBLE
        } else if (pavViewModel.currentIndex == pavViewModel.fileEntities.size - 1) {
            binding.photoPreIv.isEnabled = true
            binding.photoNextIv.isEnabled = false
            binding.photoPreIv.visibility = View.VISIBLE
            binding.photoNextIv.visibility = View.GONE
        } else {
            binding.photoPreIv.isEnabled = true
            binding.photoNextIv.isEnabled = true
            binding.photoPreIv.visibility = View.VISIBLE
            binding.photoNextIv.visibility = View.VISIBLE
        }
    }

}
