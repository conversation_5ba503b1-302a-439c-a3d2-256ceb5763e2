package com.czur.starry.device.file.manager.transfer.inter

import com.czur.starry.device.file.bean.FileEntity
import com.czur.starry.device.file.manager.oss.OSSManager
import com.czur.starry.device.file.utils.sizeRecursive
import java.io.File

/**
 * Created by 陈丰尧 on 3/25/21
 */
interface IComputeSumSize {
    var sizeMap: Map<String, Long>

    /**
     * 计算总容量
     * @param srcFiles: 要复制的文件list
     * @return 所需要的字节数 Key: 文件路径, value:文件大小
     */
    suspend fun computeSumSize(srcFiles: List<FileEntity>): Map<String, Long>
}

/**
 * 文件在本地, 计算所需空间
 */
class LocalComputeSumSize : IComputeSumSize {
    override var sizeMap: Map<String, Long> = mutableMapOf()
    override suspend fun computeSumSize(srcFiles: List<FileEntity>): Map<String, Long> {
        sizeMap = srcFiles.associate {
            it.absPath to File(it.absPath).sizeRecursive()
        }
        return sizeMap
    }
}

/**
 * 文件在云盘, 计算所需空间
 */
class CloudComputeSumSize : IComputeSumSize {
    override var sizeMap: Map<String, Long> = mutableMapOf()
    override suspend fun computeSumSize(srcFiles: List<FileEntity>): Map<String, Long> {
        // 转换成Map
        sizeMap = srcFiles.associate {
            it.absPath to OSSManager.getFileLength(it)
        }
        return sizeMap
    }
}