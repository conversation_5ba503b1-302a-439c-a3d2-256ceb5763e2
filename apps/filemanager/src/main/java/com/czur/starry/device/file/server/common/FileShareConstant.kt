package com.czur.starry.device.file.server.common

/**
 *  author : <PERSON><PERSON><PERSON>
 *  time   :2023/09/27
 */


object FileShareConstant {
    const val DEFAULT_PORT = 8652
    const val NEXT_PORT = 8687
    const val ONE_CHUNK_SIZE = 100 * 1024 * 1024L //100M

    const val API_VERSION = 1

    const val GET_VERIFY_VERIFY = "/api/starry/fileShare/getVerifyCode"
    const val GET_ROOT_LIST = "/api/starry/fileShare/getRootList"
    const val GET_FILE_LIST = "/api/starry/fileShare/getFileList"
    const val GET_THUMB = "/api/starry/fileShare/getThumb"
    const val GET_CHECK_PWD = "/api/starry/fileShare/checkPwd"
    const val GET_DOWNLOAD_FILE = "/api/starry/fileShare/download"
    const val GET_DELETE_TEMP_FILE = "/api/starry/fileShare/deleteTempFile"
    const val GET_START_BYTES = "/api/starry/fileShare/getStartBytes"
    const val GET_CHECK_DOWNLOAD_FILE = "/api/starry/fileShare/checkFile"
    const val GET_FUNCTIONS = "/api/starry/fileShare/functions"             // 功能列表
    const val GET_STARRY_STATUS = "/api/starry/fileShare/getStarryStatus"   // 获取设备状态

    const val POST_UPLOAD_FILE = "/api/starry/fileShare/uploadFile"

    /**
     * http header 参数
     */
    const val HEADER_KEY_STARRY_API_VERSION = "starry-api-version"
    const val HEADER_KEY_CLIENT_API_VERSION = "client-api-version"

    const val FILE_NAME = "File-Name"
    const val FILE_MD5 = "File-MD5"
    const val FILE_SIZE = "File-SIZE"
    const val BYTES_START = "Bytes-Start"
    const val DEVICE_ID = "Device-ID"

    const val FILE_PATH = "File-Path"
    const val FILE_LOCAL = "Root-Local"
    const val FILE_ENCRYPT = "Root-Encrypt"     // 加密文件夹
    const val FILE_DOWNLOAD = "Root-Download"
    const val FILE_SHARE = "Root-Share"
    const val FILE_MEETING = "Root-Meeting"
    const val FILE_PICTURE = "Root-Picture"
    const val CHUNK_CURRENT_SIZE = "Current-Chunk-Size"
    const val CHUNK_NUMBER = "Chunk-Number"
    const val CHUNK_TOTAL_NUMBER = "Total-Chunk-Number"


    const val FUNC_UPLOAD = "upload"
    const val FUNC_DOWNLOAD = "download"

    const val TRANS_SUCCESS = 200           //正常
    const val VERIFY_CODE = 202             //校验码
    const val COMMON_ERROR_PARAM = 203      //参数错误
    const val MD5_FAILED = 205              //md5校验失败
    const val FILE_MISS = 206               //文件缺失
    const val DISK_SPACE_NOT_ENOUGH = 210   //空间不足
    const val SERVICE_IS_CLOSE = 220        //服务关闭
    const val CPU_LOAD_HIGH = 230           //cpu 高负荷
    const val BYTE_START_ERROR = 300        //起始字节错误
    const val UNKNOWN_ERROR = 400           //未知错误
    const val FILE_NOT_EXIST = 404          //文件不存在
    const val FILE_IS_LOCKED = 405          //文件加锁
    const val PWD_ERROR = 406               //密码错误

}