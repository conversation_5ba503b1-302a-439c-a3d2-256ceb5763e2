package com.czur.starry.device.file.server.util

import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.file.manager.CZURShareFileAccess
import com.czur.starry.device.file.manager.EncryptFileAccess
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.RandomAccessFile
import java.math.BigInteger
import java.security.MessageDigest


/**
 *  author : WangHao
 *  time   :2023/10/13
 */
val fileUploadPath by lazy { CZURShareFileAccess.getRootEntity().absPath }

/**
 * 命名规则：有同名文件时,后面+"（序列）.后缀"；
 * 临时上传文件在文件名后加后缀".upload"
 */
fun getFileName(fileName: String): String {
    val nameFirst = fileName.substringBeforeLast(".")
    val nameLast = fileName.substringAfterLast('.', "")
    var name = fileName
    var index = 1
    while (File(fileUploadPath, name).exists()) {
        name = "${nameFirst}(${index})"
        if (nameLast.isNotEmpty()) {
            // 有扩展名才添加扩展名
            name = "$name.$nameLast"
        }
        index++
    }

    return name
}

fun syncUploadFile() {
    try {
        val process = Runtime.getRuntime().exec("sync")
        val result = process.waitFor()
    } catch (e: Exception) {
        logTagW("syncUploadFile", "syncUploadFile error", tr = e)
    }
}

suspend fun getBlockMd5(file: File, start: Long, end: Long): String? {
    return withContext(Dispatchers.IO) {
        try {
            RandomAccessFile(file, "r").use { raf ->
                val md = MessageDigest.getInstance("MD5")
                val buffer = ByteArray(8192)
                var read: Int
                raf.seek(start)
                var bytesToRead = buffer.size.toLong().coerceAtMost(end - start).toInt()
                while (bytesToRead > 0) {
                    read = raf.read(buffer, 0, bytesToRead)
                    if (read == -1) break
                    md.update(buffer, 0, read)
                    bytesToRead = buffer.size.toLong().coerceAtMost(end - raf.filePointer).toInt()
                }
                BigInteger(1, md.digest()).toString(16).padStart(32, '0')
            }
        } catch (e: Exception) {
            logTagW("getBlockMd5", "getBlockMd5 error", tr = e)
            null
        }
    }
}

/**
 * 是否需要检查密码
 */
fun needCheckPwd(filePath: String): Boolean {
    return filePath.startsWith(EncryptFileAccess.getRootEntity().absPath)
}