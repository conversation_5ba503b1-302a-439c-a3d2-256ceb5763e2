package com.czur.starry.device.file.base

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.viewbinding.ViewBinding
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.base.BaseDialog
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.file.R
import com.czur.starry.device.file.databinding.DialogBaseBinding
import kotlin.properties.Delegates

/**
 * Created by 陈丰尧 on 12/29/20
 */
abstract class CustomDialog<VB : ViewBinding> : BaseDialog() {
    var title: String = ""

    var confirmText: String = ""
    var cancelText: String = ""

    var onConfirmListener: (() -> Unit)? = null
    var onCancelListener: (() -> Unit)? = null

    var hideCancelBtn = false
    var hideConfirmBtn = false
    var outDismiss = true
    var autoDismiss = true

    private var rootbinding: DialogBaseBinding by Delegates.notNull()
    protected var binding: VB? = null  // 给子类用的

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        rootbinding = DialogBaseBinding.inflate(inflater, container, false)
        rootbinding.dialogChildView.setOnInflateListener { stub, inflated ->
            binding = createSubViewBinding(inflated)
        }
        return rootbinding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        rootbinding.dialogChildView.layoutResource = getLayout()
        rootbinding.dialogChildView.inflate()
        rootbinding.dialogTitle.text = title

        rootbinding.dialogBg.setOnClickListener {
            if (outDismiss) {
                dismiss()
            }
        }

        rootbinding.dialogConfirmBtn.text = confirmText
        rootbinding.dialogConfirmBtn.setOnClickListener {
            if (autoDismiss) {
                dismiss()
            }
            onConfirmListener?.let { it() }
            onConfirmClick()
        }


        rootbinding.dialogCancelBtn.text = cancelText
        rootbinding.dialogCancelBtn.setOnClickListener {
            if (autoDismiss) {
                dismiss()
            }
            onCancelListener?.let { it() }
            onCancelClick()
        }

        rootbinding.dialogCancelBtn.visibility = if (hideCancelBtn) {
            View.GONE
        } else {
            View.VISIBLE
        }

        rootbinding.dialogConfirmBtn.visibility = if (hideConfirmBtn) {
            View.GONE
        } else {
            View.VISIBLE
        }

        initView(rootbinding.root)
    }

    abstract fun getLayout(): Int
    abstract fun createSubViewBinding(container: View): VB
    abstract fun initView(view: View)
    open fun onConfirmClick() {}
    open fun onCancelClick() {}

    abstract class CustomDialogBuilder<D : CustomDialog<*>> {
        val dialog: D

        init {
            val res = FileApp.getApp().resources
            dialog = setDialog()
            dialog.confirmText = res.getString(R.string.dialog_confirm)
            dialog.cancelText = res.getString(R.string.dialog_cancel)
        }

        abstract fun setDialog(): D

        open fun setTitle(title: String): CustomDialogBuilder<D> {
            dialog.title = title
            return this
        }

        open fun setTitle(titleId: Int): CustomDialogBuilder<D> {
            val title = FileApp.getApp().resources.getString(titleId)
            return setTitle(title)
        }

        open fun setOutDismiss(outDismiss: Boolean): CustomDialogBuilder<D> {
            dialog.outDismiss = outDismiss
            return this
        }

        open fun setConfirmText(confirmText: String): CustomDialogBuilder<D> {
            dialog.confirmText = confirmText
            return this
        }

        open fun setAutoDismiss(autoDismiss: Boolean): CustomDialogBuilder<D> {
            dialog.autoDismiss = autoDismiss
            return this
        }

        open fun setConfirmText(confirmTextId: Int): CustomDialogBuilder<D> {
            val confirmText = FileApp.getApp().resources.getString(confirmTextId)
            return setConfirmText(confirmText)
        }

        open fun setCancelText(cancelText: String): CustomDialogBuilder<D> {
            dialog.cancelText = cancelText
            return this
        }

        open fun setCancelText(cancelTextId: Int): CustomDialogBuilder<D> {
            val cancelText = FileApp.getApp().resources.getString(cancelTextId)
            return setCancelText(cancelText)
        }

        open fun setOnConfirmListener(
            listener: () -> Unit
        ): CustomDialogBuilder<D> {
            dialog.onConfirmListener = listener
            return this
        }

        open fun setOnCancelListener(listener: () -> Unit): CustomDialogBuilder<D> {
            dialog.onCancelListener = listener
            return this
        }

        fun hideCancelBtn(hidden: Boolean = true): CustomDialogBuilder<D> {
            dialog.hideCancelBtn = hidden
            return this
        }

        fun hideConfirmBtn(hidden: Boolean = true): CustomDialogBuilder<D> {
            dialog.hideConfirmBtn = hidden
            return this
        }

        open fun build(): D {
            return dialog
        }

        open fun buildAndShow(tag: String = "baseDialog"): D {
            dialog.show(tag)
            return build()
        }
    }
}