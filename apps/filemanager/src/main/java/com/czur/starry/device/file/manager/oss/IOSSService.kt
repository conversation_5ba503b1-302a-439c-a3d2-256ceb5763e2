package com.czur.starry.device.file.manager.oss

import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.data.provider.UserHandler
import com.czur.starry.device.baselib.network.core.MiaoHttpEntity
import com.czur.starry.device.baselib.network.core.MiaoHttpGet
import com.czur.starry.device.baselib.network.core.MiaoHttpParam
import com.czur.starry.device.baselib.network.core.MiaoHttpPost
import com.czur.starry.device.file.bean.OSSTokenEntity
import com.czur.starry.device.file.bean.SpaceInfo

/**
 * Created by 陈丰尧 on 1/6/21
 * 请求OSS相关数据
 */

interface IOSSService {
    /**
     * 获取同步空间
     */
    @MiaoHttpPost("/api/deviceFileRest/getUsage")
    fun getUsage(
        @MiaoHttpParam("sn") sn: String = Constants.SERIAL,
        clazz: Class<SpaceInfo> = SpaceInfo::class.java
    ): MiaoHttpEntity<SpaceInfo>

    @MiaoHttpPost("/api/deviceFileRest/oss/token")
    fun getFileToken(
        @MiaoHttpParam("sn") sn: String = Constants.SERIAL,
        @MiaoHttpParam("u_id") uId: String = UserHandler.accountNo.toString(),
        clazz: Class<OSSTokenEntity> = OSSTokenEntity::class.java
    ): MiaoHttpEntity<OSSTokenEntity>

    @MiaoHttpGet("/api/starry/translate/oss/token")
    fun getTransferOsstoken(clazz: Class<OSSTokenEntity> = OSSTokenEntity::class.java): MiaoHttpEntity<OSSTokenEntity>
}