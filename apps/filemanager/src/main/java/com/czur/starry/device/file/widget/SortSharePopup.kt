package com.czur.starry.device.file.widget

import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.RelativeLayout
import com.czur.starry.device.file.R
import com.czur.starry.device.file.manager.FileAccess
import com.wanglu.lib.BasePopup
import com.wanglu.lib.WPopParams
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 *  author : <PERSON><PERSON><PERSON>
 *  time   :2023/10/18
 */


private val mainScope = MainScope()
private const val SEL_CHANGE_SHOW_TIME = 120L

/**
 * 构建一个排序
 */
fun createShareSortPopup(
    context: Context,
    onPopupItemClick: (sortType: FileAccess.SortTime) -> Unit,
    defSortType: FileAccess.SortTime = FileAccess.SortTime.ITEM_2,
): BasePopup {
    val sortPopup = BasePopup(
        WPopParams(
            R.layout.file_share_pop,
            context,
            false,
            cancelable = true,
            width = ViewGroup.LayoutParams.WRAP_CONTENT,
            height = ViewGroup.LayoutParams.WRAP_CONTENT,
            isShowShadow = true
        )
    )
    sortPopup.defaultMargin = 0
    val popItems = mapOf(
        FileAccess.SortTime.ITEM_1 to sortPopup.getContentView()
            .findViewById(R.id.firstItem) as RelativeLayout,
        FileAccess.SortTime.ITEM_2 to sortPopup.getContentView()
            .findViewById(R.id.secondItem) as RelativeLayout,
        FileAccess.SortTime.ITEM_3 to sortPopup.getContentView()
            .findViewById(R.id.thirdItem) as RelativeLayout,
        FileAccess.SortTime.ITEM_4 to sortPopup.getContentView()
            .findViewById(R.id.fourthItem) as RelativeLayout,
        FileAccess.SortTime.ITEM_5 to sortPopup.getContentView()
            .findViewById(R.id.fiveItem) as RelativeLayout,
    )

    fun changeSelect(targetTime: FileAccess.SortTime) {
        var index = 0
        popItems.forEach { (time, item) ->
            val isNotSelected = time != targetTime
            item.hideIv(isNotSelected)
            index++
        }
    }

    popItems.forEach { (time, item) ->
        changeSelect(defSortType)
        item.setOnClickListener {
            // 选中的显示
            changeSelect(time)
            // 回调
            onPopupItemClick(time)
            mainScope.launch {
                delay(SEL_CHANGE_SHOW_TIME)
                sortPopup.dismiss()
            }
        }
    }


    return sortPopup
}

private fun RelativeLayout.hideIv(isNotSelected: Boolean = true) {
    val imageView = getChildAt(1)
    if (imageView is ImageView) {
        imageView.visibility = if (isNotSelected) View.INVISIBLE else View.VISIBLE
    }

}


