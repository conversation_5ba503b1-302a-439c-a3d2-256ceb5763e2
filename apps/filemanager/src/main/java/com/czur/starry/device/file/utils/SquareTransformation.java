package com.czur.starry.device.file.utils;

import android.graphics.Bitmap;

import androidx.annotation.NonNull;
import com.bumptech.glide.load.engine.bitmap_recycle.BitmapPool;
import com.bumptech.glide.load.resource.bitmap.BitmapTransformation;
import com.bumptech.glide.load.resource.bitmap.CenterCrop;
import com.bumptech.glide.load.resource.bitmap.TransformationUtils;

import java.security.MessageDigest;


/**
 * Created by 陈丰尧 on 3/21/21
 * 将图片处理成圆角矩形
 */
public class SquareTransformation extends BitmapTransformation {
    private static final String ID = "com.czur.starry.device.file.utils.SquareTransformation";
    private static final byte[] ID_BYTES = ID.getBytes(CHARSET);

    @Override
    protected Bitmap transform(
            @NonNull BitmapPool pool, @NonNull Bitmap toTransform, int outWidth, int outHeight) {
        int edge = Math.min(outHeight,outWidth);
        Bitmap bmp = TransformationUtils.centerCrop(pool, toTransform, edge, edge);
        return TransformationUtils.roundedCorners(pool,bmp,10);
    }
    @Override
    public boolean equals(Object o) {
        return o instanceof CenterCrop;
    }

    @Override
    public int hashCode() {
        return ID.hashCode();
    }
    @Override
    public void updateDiskCacheKey(@NonNull MessageDigest messageDigest) {
        messageDigest.update(ID_BYTES);
    }
}

