package com.czur.starry.device.file.manager

import android.os.Environment
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.network.HttpManager
import com.czur.starry.device.file.bean.FileEntity
import com.czur.starry.device.file.bean.MusicEntity
import com.czur.starry.device.file.filelib.AccessType
import com.czur.starry.device.file.filelib.FileType
import com.czur.starry.device.file.manager.transfer.ai.AIDownloadTask
import com.czur.starry.device.file.server.AITransServer
import com.czur.starry.device.file.server.entity.AITransFileEntity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.InputStream
import java.io.OutputStream
import java.util.concurrent.atomic.AtomicBoolean

/**
 * Created by 宋清君 on 2025/3/4
 */
object AITransAccess : FileAccess {
    private val TAG = "AITransAccess"


    const val AI_TRANS_FAKE_FILE = "aiTransFakeFile"
    private val transferServer: AITransServer by lazy { HttpManager.getService() }

    private val delFlag: AtomicBoolean = AtomicBoolean(false)
    override val isDeleting: Boolean
        get() = delFlag.get()

    private val rootDir: File by lazy {
        val sdCardPath = Environment.getExternalStorageDirectory().path
        File(sdCardPath, AI_TRANS_FAKE_FILE).apply {
            if (!exists()) {
                mkdirs()
            }
        }
    }

    // 获取下载文件的根目录
    override fun getRootEntity() = FileEntity(
        rootDir.absolutePath,
        FileType.ROOT, AI_TRANS_FAKE_FILE,
        0, "", AccessType.AI_TRANS, 0L
    )

    suspend fun delAITransFile(fileList: List<AITransFileEntity>?): Pair<Boolean, Int> {
        if (fileList.isNullOrEmpty()) return Pair<Boolean, Int> (false, -1)

        return withContext(Dispatchers.IO) {
            try {
                val idList = fileList.mapNotNull { it?.id }
                if (idList.isEmpty()) return@withContext Pair<Boolean, Int> (false, -1)

                val postBatchDelete = transferServer.postBatchDelete(idList)
                logTagD(TAG,"postBatchDelete.msg ${postBatchDelete.msg} postBatchDelete.body ${postBatchDelete.body} postBatchDelete.code ${postBatchDelete.isNetError}")
                Pair<Boolean, Int> (postBatchDelete.isSuccess, postBatchDelete.code)
            } catch (e: Exception) {
                Pair<Boolean, Int> (false, -1)
            }
        }
    }

    // 调用接口删除文件
    override suspend fun delFiles(
        delFiles: List<FileEntity>,
        reqCloudLock: Boolean
    ): FileAccess.Result {
        return withContext(Dispatchers.IO) {
            val delFlag = AtomicBoolean(false)
            delFlag.set(true)
            val delResult = delFiles.map {
                // 调用接口删除文件
                delFiles(delFiles)
            }
            delFlag.set(false)
            delResult.firstOrNull { it != FileAccess.Result.SUCCESS }?.let {
                return@withContext it
            }
            return@withContext FileAccess.Result.SUCCESS
        }
    }

    suspend fun renameTransFile(
        selFile: AITransFileEntity,
        name: String
    ): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val postRename = transferServer.postRenameRecord(selFile.id.toString(), name)
                postRename.isSuccess
            } catch (e: Exception) {
                false
            }
        }
    }

    // 重命名文件名称
    override suspend fun renameFile(
        target: FileEntity,
        newName: String,
        targetDir: FileEntity
    ): FileAccess.Result {
        return withContext(Dispatchers.IO) {
//            val file = File(target.absPath)
//            val newFile = File(targetDir.absPath + File.separator + newName)
//            file.renameTo(newFile).yes {
//                FileAccess.Result.SUCCESS
//            }.otherwise {
//                FileAccess.Result.SYSTEM_ERR
//            }
            FileAccess.Result.SUCCESS
        }
    }


    suspend fun getRecordInfo(id: String): AITransFileEntity {
        return withContext(Dispatchers.IO) {
            try {
                val recordInfo = transferServer.getRecordInfo(id)
                if (recordInfo.isSuccess) {
                    recordInfo.body
                } else {
                    logTagW(TAG, "获取文件信息失败 ${recordInfo.msg.toString()}")
                    AITransFileEntity(id = -9527)
                }
            } catch (e: Exception) {
                logTagW(TAG, "获取文件信息失败 ${e.message.toString()}")
                AITransFileEntity(id = -9527)
            }
        }
    }

    // 获取列表
    suspend fun getAITransFileList(
        pageNo: Int,
        sortType: FileAccess.SortType,
        onNetworkError: () -> Unit = {}
    ): List<AITransFileEntity> {
        return withContext(Dispatchers.IO) {
            val sortAlgo = when (sortType) {
                FileAccess.SortType.TYPE_NAME_ASC,
                FileAccess.SortType.TYPE_TIME_ASC -> {
                    "ASC"
                }

                FileAccess.SortType.TYPE_NAME_DESC,
                FileAccess.SortType.TYPE_TIME_DESC -> {
                    "DESC"
                }
            }

            val sortField = when (sortType) {
                FileAccess.SortType.TYPE_TIME_DESC,
                FileAccess.SortType.TYPE_TIME_ASC -> {
                    "create_time"
                }

                FileAccess.SortType.TYPE_NAME_DESC,
                FileAccess.SortType.TYPE_NAME_ASC -> {
                    "name"
                }
            }
            try {
                val transferRecord = transferServer.getTransferRecord(
                    page = pageNo,
                    sortAlgo = sortAlgo,
                    sortField = sortField
                )
                if (transferRecord.isSuccess) {
                    val body = transferRecord.withCheck().body
                    body.data
                } else {
                    // 请求失败，通知显示ErrorNetworkFragment
                    withContext(Dispatchers.Main) {
                        onNetworkError()
                    }
                    listOf()
                }
            } catch (e: Exception) {
                // 发生异常，通知显示ErrorNetworkFragment
                withContext(Dispatchers.Main) {
                    onNetworkError()
                }
                listOf()
            }
        }
    }

    // 获取列表
    suspend fun checkTransferRecord(list: List<Int>): List<AITransFileEntity> {
        return withContext(Dispatchers.IO) {
            try {
                val transferRecord = transferServer.postCheckTransferRecord(list)
                if (transferRecord.isSuccess) {
                    val body = transferRecord.withCheck().body
                    body.data
                } else {
                    listOf()
                }
            } catch (e: Exception) {
                listOf()
            }
        }
    }


    private fun parseFileName(fileName: String): Pair<String, Int?> {
        val regex = Regex("""(.*)\((\d+)\)$""")
        return regex.matchEntire(fileName)?.destructured?.let { (name, number) ->
            name to number.toInt()
        } ?: (fileName to null)
    }

    // 从输入流读取数据并写入到输出流
    private fun copyStream(inputStream: InputStream, outputStream: OutputStream) {
        val buffer = ByteArray(1024)
        var bytesRead = inputStream.read(buffer)
        while (bytesRead != -1) {
            outputStream.write(buffer, 0, bytesRead)
            bytesRead = inputStream.read(buffer)
        }
    }


    suspend fun startDownload(
        selFiles: List<AITransFileEntity>,
        processListener: (process: Int, max: Int) -> Unit
    ) {
        withContext(Dispatchers.IO) {
            // 找出sleFiles中所有summaryPath和asrPath不为空的数量
            val summaryPathCount = selFiles.count { it.summaryPath?.isNotEmpty() == true }
            val asrPathCount = selFiles.count { it.asrPath?.isNotEmpty() == true }

            val maxProgress = summaryPathCount + asrPathCount + selFiles.size
            logTagV(TAG, "download AI maxProgress:${maxProgress}")
            var currentProgress = 0
            for (file in selFiles) {
                val recordInfo = getRecordInfo(file.id.toString())
                if (recordInfo.id == -9527){
                    continue  // 跳过当前file
                }
                logTagD(TAG,"recordInfo--${recordInfo.id} asr--${recordInfo.asrUrl} summary--${recordInfo.summaryUrl}")

                val tasks = AIDownloadTask(file,recordInfo)
                tasks.start { progress ->
                    if (progress > 0) {
                        currentProgress += progress
                        processListener(currentProgress, maxProgress)
                    } else if (progress < 0){
                        // 失败
                        logTagW(TAG, "下载失败")
                        processListener(-1, -1)
                    }
                    logTagV(TAG, "download AI currentProgress:${currentProgress}")

                }
            }
            logTagI(TAG, "下载完成")
            logTagV(TAG, "download AI Progress:${currentProgress}")

            if (currentProgress < maxProgress){
                withContext(Dispatchers.Main) {
                    processListener(maxProgress, maxProgress)
                }
            }
        }
    }


    override suspend fun getItemsByTarget(
        targetDir: FileEntity,
        start: Int,
        count: Int,
        filterMode: FileAccess.FilterMode,
        sortType: FileAccess.SortType
    ): List<FileEntity> {
        TODO("Not yet implemented")
    }

    override suspend fun getNextFolder(
        folderName: String,
        targetDir: FileEntity,
        targetDirFileNames: List<String>
    ): String {
        TODO("Not yet implemented")
    }

    override suspend fun createNewFolder(
        folderName: String,
        targetDir: FileEntity,
        targetDirFileNames: List<String>
    ): String? {
        TODO("Not yet implemented")
    }


    override fun getMusicList(target: List<FileEntity>): ArrayList<MusicEntity> {
        TODO("Not yet implemented")
    }


}