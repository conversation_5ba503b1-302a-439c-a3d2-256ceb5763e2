package com.czur.starry.device.file.utils

import android.webkit.MimeTypeMap
import com.czur.starry.device.file.base.MIME_TYPE_APK
import com.czur.starry.device.file.base.MIME_TYPE_KEY_AUDIO
import com.czur.starry.device.file.base.MIME_TYPE_KEY_IMG
import com.czur.starry.device.file.base.MIME_TYPE_KEY_OGG
import com.czur.starry.device.file.base.MIME_TYPE_KEY_VIDEO
import com.czur.starry.device.file.filelib.FileType
import java.io.File

/**
 * Created by 陈丰尧 on 2021/5/19
 */
// 这是根据后端的代码 添加的这些
private val documentList: List<String>
    get() = listOf(
        "rtf",
        "wps",
        "txt",
        "js",
        "json",
        "css",
        "sql",
        "xml",
        "java",
        "cs",
        "html",
        "xmind",
        "sh",
        "bat"
    )

//支持本地打开的document类型
val supportList = listOf(
    "wps", "rtf", "txt", "xml", "Java", "bat", "html"
)
private val zipList: List<String>
    get() = listOf("zip", "rar", "7z")

private val IMG_EXCLUDE = listOf("tiff")

/**
 * 会议录音的扩展名
 * 会议录音包含音频文件和文本文件, 所以将其放到一个文件夹中
 */
const val MEET_RECORD_EXTENSION = "czmr"



/**
 * 根据传入的文件, 获取文件类型
 * 使用的是文件扩展名进行判断
 * 有误判的可能, 但是速度较快
 */
fun File.getFileType(): FileType {
    return when {
        extension == MEET_RECORD_EXTENSION -> FileType.MEET_RECORD
        isDirectory -> FileType.FOLDER
        isImage() -> FileType.IMAGE
        isDocument() -> FileType.DOCUMENT
        isApk() -> FileType.APK
        isZip() -> FileType.ZIP
        isAudio() -> FileType.AUDIO
        isVideo() -> FileType.VIDEO
        isWord() -> FileType.DOC
        isPDF() -> FileType.PDF
        isPpt() -> FileType.PPT
        isExcel() -> FileType.EXCEL
        isXApk() -> FileType.X_APK
        //针对 NTFS格式硬盘下 有个别文件夹情况
        !isFile -> FileType.FOLDER
        else -> FileType.OTHER
    }
}

/**
 * 判断文件类型
 */
fun File.isApk(): Boolean = mimeTypeContains(MIME_TYPE_APK)
fun File.isImage(): Boolean = mimeTypeContains(MIME_TYPE_KEY_IMG, excludeList = IMG_EXCLUDE)
fun File.isAudio(): Boolean =
    mimeTypeContains(MIME_TYPE_KEY_AUDIO, MIME_TYPE_KEY_OGG) and (!extension.contains("wma", true))

fun File.isVideo(): Boolean = mimeTypeContains(MIME_TYPE_KEY_VIDEO)
fun File.isDocument() = extension in documentList
fun File.isWord() = "docx".equals(extension, true) ||
        "doc".equals(extension, true)

fun File.isPpt() = "ppt".equals(extension, true) ||
        "pptx".equals(extension, true)

fun File.isExcel() = "xls".equals(extension, true) ||
        "xlsx".equals(extension, true)

fun File.isPDF() = "pdf".equals(extension, true)
private fun File.isZip() = extension in zipList
fun File.isXApk() = extension.equals("xapk", ignoreCase = true)


/**
 * 获取文件的MimeType
 */
val File.mimeType: String?
    get() {
        return MimeTypeMap.getSingleton().getMimeTypeFromExtension(extension.lowercase())
    }


/**
 * 文件的MimeType是否包含指定字符串
 */
private fun File.mimeTypeContains(
    vararg targetMime: String,
    excludeList: List<String> = emptyList()
): Boolean {
    val type = mimeType ?: ""
    targetMime.forEach {
        if (type.contains(it, true)
            && !excludeList.targetInElement(type, true)
        ) {
            return true
        }
    }
    return false
}


private fun List<String>.targetInElement(target: String, ignoreCase: Boolean): Boolean {
    this.forEach {
        if (target.contains(it, ignoreCase)) {
            return true
        }
    }
    return false
}