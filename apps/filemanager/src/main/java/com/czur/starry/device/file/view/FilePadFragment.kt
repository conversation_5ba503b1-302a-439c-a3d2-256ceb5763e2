package com.czur.starry.device.file.view

import android.content.pm.PackageManager
import android.os.Bundle
import android.os.FileObserver
import android.view.KeyEvent
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import com.czur.czurutils.global.globalAppCtx
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.base.CZURAtyManager
import com.czur.starry.device.baselib.base.CZURAtyManager.appContext
import com.czur.starry.device.baselib.base.listener.KeyDownListener
import com.czur.starry.device.baselib.base.v2.fragment.CZViewBindingFragment
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.ServerLocationOversea
import com.czur.starry.device.baselib.common.hw.Q1Series
import com.czur.starry.device.baselib.common.hw.Q2Series
import com.czur.starry.device.baselib.common.hw.StarryModel
import com.czur.starry.device.baselib.common.hw.StudioSeries
import com.czur.starry.device.baselib.network.core.NoNetworkException
import com.czur.starry.device.baselib.network.core.exception.MiaoMainlandRejectExp
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.SettingUtil
import com.czur.starry.device.baselib.utils.closeDefChangeAnimations
import com.czur.starry.device.baselib.utils.doOnItemClick
import com.czur.starry.device.baselib.utils.doOnItemLongClick
import com.czur.starry.device.baselib.utils.doOnItemRightClick
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.lifecycle.AutoRemoveLifecycleObserver
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.baselib.utils.toast
import com.czur.starry.device.baselib.utils.toastFail
import com.czur.starry.device.baselib.view.dialog.LoadingDialog
import com.czur.starry.device.baselib.view.floating.common.DoubleBtnCommonFloat
import com.czur.starry.device.baselib.view.floating.common.InstallLoadingFloat
import com.czur.starry.device.baselib.view.floating.common.SingleBtnCommonFloat
import com.czur.starry.device.file.R
import com.czur.starry.device.file.adapter.FilePadAdapter
import com.czur.starry.device.file.adapter.MODE_GRID
import com.czur.starry.device.file.adapter.MODE_LIST
import com.czur.starry.device.file.base.CustomDialog
import com.czur.starry.device.file.base.RefreshAble
import com.czur.starry.device.file.bean.FileEntity
import com.czur.starry.device.file.bean.MainTabKey.SHOW_TYPE_ENCRYPTION
import com.czur.starry.device.file.bean.MainTabKey.SHOW_TYPE_LOCAL
import com.czur.starry.device.file.bean.MainTabKey.SHOW_TYPE_LOCAL_CZUR_SHARE
import com.czur.starry.device.file.bean.MainTabKey.SHOW_TYPE_LOCAL_DOWNLOAD
import com.czur.starry.device.file.bean.MainTabKey.SHOW_TYPE_LOCAL_SCREEN_SHOT
import com.czur.starry.device.file.bean.MainTabKey.SHOW_TYPE_USB
import com.czur.starry.device.file.databinding.FragmentFilePadBinding
import com.czur.starry.device.file.db.entity.UploadFile
import com.czur.starry.device.file.exp.CloudFileInUserExp
import com.czur.starry.device.file.exp.NoSpaceException
import com.czur.starry.device.file.filelib.AccessType
import com.czur.starry.device.file.filelib.EXT_TYPE_LOCAL_CZUR_SHARE
import com.czur.starry.device.file.filelib.EXT_TYPE_LOCAL_DOWNLOAD
import com.czur.starry.device.file.filelib.EXT_TYPE_LOCAL_ENCRYPTION
import com.czur.starry.device.file.filelib.EXT_TYPE_LOCAL_SCREEN_SHOT
import com.czur.starry.device.file.filelib.FileHandlerLive
import com.czur.starry.device.file.filelib.FileHandlerLive.fileTabUpdate
import com.czur.starry.device.file.filelib.FileType
import com.czur.starry.device.file.manager.CZURShareFileAccess
import com.czur.starry.device.file.manager.DownloadFileAccess
import com.czur.starry.device.file.manager.FileAccess
import com.czur.starry.device.file.manager.FileManager
import com.czur.starry.device.file.manager.FileShowInfoManager
import com.czur.starry.device.file.manager.LocalFileAccess
import com.czur.starry.device.file.manager.LocalInfoManager
import com.czur.starry.device.file.manager.ScreenShotFileAccess
import com.czur.starry.device.file.manager.USBFileAccess
import com.czur.starry.device.file.manager.UnReadFileManager
import com.czur.starry.device.file.manager.transfer.FileTransfer
import com.czur.starry.device.file.manager.usb.UsbHelper
import com.czur.starry.device.file.service.FileWatcherService
import com.czur.starry.device.file.utils.InstallXApkCallback
import com.czur.starry.device.file.utils.TargetFileObserver
import com.czur.starry.device.file.utils.deleteDirectory
import com.czur.starry.device.file.utils.getStrRes
import com.czur.starry.device.file.utils.getXApkName
import com.czur.starry.device.file.utils.initRegisterSessionCallback
import com.czur.starry.device.file.utils.installXApk
import com.czur.starry.device.file.utils.isTypeOf
import com.czur.starry.device.file.utils.open
import com.czur.starry.device.file.utils.unzipXAPK
import com.czur.starry.device.file.view.activity.FileMainPageActivity
import com.czur.starry.device.file.view.activity.FileMainPageActivity.Companion.usbOperationPath
import com.czur.starry.device.file.view.dialog.CopyFileDialog
import com.czur.starry.device.file.view.dialog.NewFolderDialog
import com.czur.starry.device.file.view.dialog.ProgressDialog
import com.czur.starry.device.file.view.dialog.RenameDialog
import com.czur.starry.device.file.view.dialog.showCopyFileRepeatDialog
import com.czur.starry.device.file.view.dialog.showCopyNoSpaceDialog
import com.czur.starry.device.file.view.dialog.showHintDialog
import com.czur.starry.device.file.view.dialog.showOtherCopyError
import com.czur.starry.device.file.view.vm.FilePadViewModel
import com.czur.starry.device.file.view.vm.MainViewModel
import com.czur.starry.device.file.widget.EmptyRecyclerView
import com.czur.starry.device.file.widget.SpacesFileGrid
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.sample
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.util.Stack
import java.util.concurrent.atomic.AtomicBoolean


/**
 * Created by 陈丰尧 on 12/28/20
 * 用于显示文件列表
 */
private const val TAG = "FilePadFragment"

class FilePadFragment : CZViewBindingFragment<FragmentFilePadBinding>(), IFilePad, RefreshAble,
    KeyDownListener {
    private val fileAccess: FileAccess by lazy {
        createFileAccess()
    }
    override var copyJob: Job? = null
    private val fileAdapter = FilePadAdapter()
    private var controlBar: IControlBar? = null
    private var fileItems = listOf<FileEntity>()
    private lateinit var layoutManager: GridLayoutManager
    private var gridSpace = SpacesFileGrid(55) // 行间距
    private var filterMode = FileAccess.FilterMode.ALL
    private var selectMode = false // 是否是选择模式

    val path = Stack<FileEntity>()

    private val loadingDialog by lazy { LoadingDialog() }

    // 记录是否正在刷新页面
    private val refreshing: AtomicBoolean = AtomicBoolean(false)

    private val showTypeKey by lazy {
        requireArguments().getInt(SHOW_TYPE_KEY)
    }

    private val fileChangeFlow = MutableStateFlow(0L)


    private val packageManager: PackageManager by lazy(LazyThreadSafetyMode.NONE) {
        appContext.packageManager
    }
    private var unknownSourceFloat: DoubleBtnCommonFloat? = null
    private var unknownSourceSecondFloat: DoubleBtnCommonFloat? = null
    private var installLoadingFloat: InstallLoadingFloat? = null
    private var installXApkSuccessFloat: DoubleBtnCommonFloat? = null
    private var unableDialog: SingleBtnCommonFloat? = null

    /**
     * 扩展数据
     * 给USB用的
     */
    private val extData: Bundle? by lazy {
        requireArguments().getBundle(KEY_EXT_DATA)
    }

    private var newFolderName: String? = null

    private val filePadRv: EmptyRecyclerView by lazy {
        requireView().findViewById(R.id.filePadRv)!!
    }

    private val targetFileObserver: TargetFileObserver by lazy {
        initRootFileWatch()
    }

    private var isOpeningAudioOrVideo: Boolean = false  // 正在打开音视频文件


    /**
     * 当前页面属于哪个分类
     */
    private val pageBelongTo: AccessType by lazy {
        when (fileAccess) {
            is LocalFileAccess, DownloadFileAccess, ScreenShotFileAccess -> AccessType.LOCAL
            is USBFileAccess -> AccessType.USB
            else -> AccessType.RECORD
        }
    }

    private val mainViewModel: MainViewModel by activityViewModels()
    private val filePadViewModel: FilePadViewModel by viewModels()

    val needFileObserver: Boolean
        get() = showTypeKey == SHOW_TYPE_LOCAL_DOWNLOAD || showTypeKey == SHOW_TYPE_LOCAL_SCREEN_SHOT
                || showTypeKey == SHOW_TYPE_LOCAL || showTypeKey == SHOW_TYPE_LOCAL_CZUR_SHARE
                || showTypeKey == SHOW_TYPE_ENCRYPTION

    private val openFileEventFlow = MutableSharedFlow<Pair<FileEntity, Boolean>>()


    companion object {
        const val SPAN_COUNT_PAD = 6 // 默认一行显示6个item
        const val SPAN_COUNT_LIST = 1

        const val SHOW_TYPE_KEY = "showType"

        private const val KEY_EXT_DATA = "extData"

        //文件传输状态
        const val FILE_NORMAL_STATUS = 0
        const val FILE_UPLOADING_STATUS = 1
        const val FILE_UPLOAD_INTERRUPT_STATUS = 2


        var updateUploadIconJob: Job? = null
        var dialogCustom: CustomDialog<*>? = null
        var progressDialog: ProgressDialog? = null
        var copyDialog: CopyFileDialog? = null
        var copysrPath: String = ""
        var index = 0

        fun getInstance(showType: Int, extData: Bundle? = null): FilePadFragment {
            val instance = FilePadFragment()
            val bundle = Bundle().apply {
                putInt(SHOW_TYPE_KEY, showType)
                putBundle(KEY_EXT_DATA, extData)
            }
            instance.arguments = bundle
            return instance
        }

    }

    override fun FragmentParams.initFragmentParams() {
        this.viewLifecycleObserver = object : AutoRemoveLifecycleObserver {
            override fun onResume(owner: LifecycleOwner) {
                super.onResume(owner)
                logTagV(TAG, "页面onResume: 重新刷新一次")
                refresh(false)
                isOpeningAudioOrVideo = false
            }

            override fun onStop(owner: LifecycleOwner) {
                super.onStop(owner)
                loadingDialog.dismiss()
            }
        }
    }

    override fun FragmentFilePadBinding.initBindingViews() {
        path.push(fileAccess.getRootEntity())

        layoutManager = GridLayoutManager(requireContext(), SPAN_COUNT_PAD)
        filePadRv.layoutManager = layoutManager
        filePadRv.adapter = fileAdapter
        launch {
            // 加载显示模式
            val mode = FileShowInfoManager.loadShowMode()
            changeLayoutMode(mode)
        }

        filePadRv.addItemDecoration(gridSpace)
        // 关闭刷新时的闪烁动画
        filePadRv.closeDefChangeAnimations()

        fileAdapter.selectCount.observe(viewLifecycleOwner) {
            controlBar?.updateSelectCount(
                it, fileAdapter.getSelectModeCount(),
                fileAdapter.hasSelectUpload(),
            )
        }

        FileHandlerLive.unReadFileNameLive.observe(viewLifecycleOwner) {
            // 强调一下现在已经选择的页面,可以消除在当前页面发生的新文件产生的红点
            logTagD(TAG, "捕捉到了新文件名字 = $it showTypeKey = $showTypeKey isHidden = $isHidden")
            if (!isHidden && CZURAtyManager.czurAppState == CZURAtyManager.CZURAppState.VISIBLE) {
                fileTabUpdate = showTypeKey
            }
        }

        // 添加点击事件的监听
        filePadRv.doOnItemClick(true) { vh, view ->
            val pos = vh.bindingAdapterPosition
            if (pos < 0) {
                logTagW(TAG, "FilePad Pos index < 0")
                return@doOnItemClick true
            }

            if (selectMode) {
                if (showTypeKey == SHOW_TYPE_LOCAL_CZUR_SHARE) {
                    if (fileAdapter.getData(pos).isUploadStatus == FILE_UPLOADING_STATUS) {
                        toast(R.string.str_select_upload_tip)
                        return@doOnItemClick true
                    }

                }

                fileAdapter.changeSelect(pos)
            } else {
                //数据上传中
                when (fileAdapter.getData(pos).isUploadStatus) {
                    FILE_UPLOADING_STATUS -> {
                        toast(R.string.str_select_upload_tip)
                        return@doOnItemClick true
                    }

                    FILE_UPLOAD_INTERRUPT_STATUS -> {
                        toast(R.string.toast_can_not_open)
                        return@doOnItemClick true
                    }
                }

                val item = fileAdapter.getData(pos).fileEntity
                if (item isTypeOf FileType.FOLDER) {
                    // 如果是文件夹, 就打开
                    openFolder(item)
                } else {
                    openFile(item)
                }
            }
            true
        }
        // 长按监听
        filePadRv.doOnItemLongClick { vh, view ->
            val pos = vh.layoutPosition
            if (pos < 0) {
                logTagW(TAG, "FilePad Pos index < 0")
                return@doOnItemLongClick true
            }
            val data = fileAdapter.getData(pos)

            // 检查上传状态
            if (showTypeKey == SHOW_TYPE_LOCAL_CZUR_SHARE) {
                when (data.isUploadStatus) {
                    FILE_UPLOADING_STATUS -> {
                        toast(R.string.str_select_upload_tip)
                        return@doOnItemLongClick true
                    }

                    FILE_UPLOAD_INTERRUPT_STATUS -> {
                        toast(R.string.toast_can_not_open)
                        return@doOnItemLongClick true
                    }
                }
            }

            // 进入选择模式并选中当前项
            if (!selectMode) {
                controlBar?.enterSelMode()
                fileAdapter.changeSelect(pos)
                // 更新控制栏的选择状态
                val selectCount = fileAdapter.getSelectCount()
                val totalCount = fileAdapter.getSelectModeCount()
                controlBar?.updateSelectCount(
                    selectCount,
                    totalCount,
                    fileAdapter.hasSelectUpload()
                )
            }
            true
        }

        // 鼠标右键监听
        filePadRv.doOnItemRightClick { vh, view ->
            val pos = vh.layoutPosition
            if (pos < 0) {
                logTagW(TAG, "FilePad Pos index < 0")
                return@doOnItemRightClick true
            }
            val data = fileAdapter.getData(pos)
            val item = data.fileEntity

            if (showTypeKey == SHOW_TYPE_LOCAL_CZUR_SHARE) {
                when (data.isUploadStatus) {
                    FILE_UPLOADING_STATUS -> {
                        toast(R.string.str_select_upload_tip)
                        return@doOnItemRightClick true
                    }

                    FILE_UPLOAD_INTERRUPT_STATUS -> {
                        toast(R.string.toast_can_not_open)
                        return@doOnItemRightClick true
                    }
                }
            }

            if (!(item isTypeOf FileType.FOLDER)) {
                openFile(item, true)
            }
            true
        }
        // 没有文件时的布局
        if (showTypeKey == SHOW_TYPE_USB) {
            filePadRv.setEmptyView(isUsb = true)
        } else {
            filePadRv.setEmptyView()
        }

        if (showTypeKey == SHOW_TYPE_LOCAL_CZUR_SHARE) {
            launch {
                mainViewModel.uploadFileFlow.collect { item ->
                    if (!isHidden && CZURAtyManager.czurAppState == CZURAtyManager.CZURAppState.VISIBLE) {
                        updateUploadFileUI(item)
                        fileTabUpdate = showTypeKey
                    } else {
                        synchronized(this) {
                            if (item.isNotEmpty()) {
                                //更新UI列表
                                mainViewModel.uploadFileList = item.filter {
                                    it.isUploading
                                }.map {
                                    it.fileNewName
                                } as MutableList<String>
                            } else {
                                mainViewModel.uploadFileList.clear()
                            }
                        }
                    }

                }
            }
        }

    }

    private fun updateUploadFileUI(list: List<UploadFile>) {
        logTagD(TAG, "===start==updateUploadFileUI=")
        synchronized(this) {
            if (list.isNotEmpty()) {
                //更新UI列表
                mainViewModel.uploadFileList = list.filter {
                    it.isUploading
                }.map {
                    it.fileNewName
                } as MutableList<String>

                //异常中断状态
                mainViewModel.uploadFilePauseList = list.filter {
                    !it.isUploading
                }.map {
                    it.fileNewName
                } as MutableList<String>
                //待续传中断
                val continueList = list.filter {
                    it.isContinueInterrupt
                }.map {
                    it.fileNewName
                }
                logTagD(TAG, "====continue: ${continueList.size}")
                if (continueList.isNotEmpty()) {
                    refreshUploadIcon(true)
                } else {
                    refreshUploadIcon()
                }

            } else {
                mainViewModel.uploadFileList.clear()
                mainViewModel.uploadFilePauseList?.clear()
                refreshUploadIcon()
            }

            logTagD(TAG, "===end==updateUploadFileUI")
        }
    }

    private fun refreshUploadIcon(delay: Boolean = false) {
        updateUploadIconJob?.cancel()
        updateUploadIconJob = launch {
            if (delay) {
                //待续传中断等待3秒后更新UI
                delay(ONE_SECOND * 3)
            }
            fileAdapter.changeUploadUI(
                mainViewModel.uploadFileList,
                mainViewModel.uploadFilePauseList!!
            )
            if (showTypeKey == SHOW_TYPE_LOCAL_CZUR_SHARE) {
                val selectCount = fileAdapter.getSelectCount()
                val totalCount = fileAdapter.getSelectModeCount()
                controlBar?.updateSelectCount(
                    selectCount,
                    totalCount,
                    fileAdapter.hasSelectUpload()
                )
            }
        }
    }


    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        refresh()

        repeatCollectOnResume(filePadRv.isEmptyFlow) {
            controlBar?.onEmptyDataChanged(it)
        }

        repeatCollectOnResume(mainViewModel.currentTabKeyFlow) {
            filePadViewModel.updateSelCurrent(showTypeKey == it)
        }
        //语音打开最近上传文件
        if (showTypeKey == SHOW_TYPE_LOCAL_CZUR_SHARE) {
            repeatCollectOnResume(mainViewModel.voiceOpenFileName.debounce(1000)) { it ->
                logTagD(TAG, "voiceOpenFileName: $it")
                mainViewModel.voiceOpenFileName.value = ""
                if (fileAdapter.fileEntities.isEmpty()) {
                    delay(2000)
                }
                when (it) {
                    "recent" -> {
                        openFileIfExists(fileAdapter.getRecentUploadFile())
                        mainViewModel.voiceOpenFileName.value = ""
                    }

                    "" -> {}
                    else -> openFileIfExists(fileAdapter.getVoiceFile(it))
                }
            }
        }
        repeatCollectOnResume(filePadViewModel.selCurrentFlow) { selCurrent ->
            if (selCurrent) {
                refresh(false)
                /**
                 * 监控本地文件的改变
                 */
                if (needFileObserver) {
                    logTagI(TAG, "开始监控本地文件的改变:${showTypeKey} - ${path.peek().absPath}")
                    targetFileObserver.startWatch()
                }
            } else {
                if (needFileObserver) {
                    logTagI(
                        TAG,
                        "停止监控本地文件的改变:${showTypeKey} - ${path.peek().absPath}"
                    )
                    targetFileObserver.stopWatch()
                }
            }
        }

        repeatCollectOnResume(fileChangeFlow.debounce(300L)) {
            if (it > 0L) {
                logTagD(TAG, "文件改变,刷新UI")
                refresh(false)
            }
        }

        // 打开文件,增加防抖
        repeatCollectOnResume(openFileEventFlow.sample(500)) {
            doOpenFile(it.first, it.second)
        }

        repeatCollectOnResume(FileShowInfoManager.padModeFlow) {
            launch {

            }
            changeLayoutMode(it)
        }

        //注册安装xapk监听
        initRegisterSessionCallback()
    }


    private fun initRootFileWatch(): TargetFileObserver {
        logTagD(TAG, "初始化FileWatch,target:${fileAccess.getRootEntity().absPath}")
        return TargetFileObserver(
            fileAccess.getRootEntity().absPath,
            FileObserver.DELETE or FileObserver.CLOSE_WRITE or FileObserver.ATTRIB or FileObserver.CREATE
        ) { event: Int,
            _: String ->
            // 只有在根路径才刷新
            if (path.size > 1) {
                return@TargetFileObserver
            }
            //成者妙传分段上传不刷新
            if (showTypeKey == SHOW_TYPE_LOCAL_CZUR_SHARE) {
                launch {
                    logTagD(TAG, "刷新当前容量信息")
                    delay(ONE_SECOND)
                    fileAccess.updateUsage()
                }
                if (event == FileObserver.CLOSE_WRITE || event == FileObserver.ATTRIB) {
                    return@TargetFileObserver
                }
            } else {
                launch {
                    delay(ONE_SECOND)
                    fileAccess.updateUsage()
                }
                if (event == FileObserver.CREATE) {
                    return@TargetFileObserver
                }
            }
            logTagV(TAG, "event:$event")
            fileChangeFlow.value = System.currentTimeMillis()
        }
    }

    private var lastSortType: FileAccess.SortType? = null

    /**
     * 加载当前文件夹的数据
     */
    override fun refresh(needMoveToTop: Boolean) {
        if (refreshing.get()) {
            logTagW(TAG, "正在刷新,ignore")
            return
        }
        if (path.isEmpty()) {
            // 控制条会直接回调一次刷新, 此时数据还没有准备好, 忽略就行了
            logTagV(TAG, "path还没有准备好, 取消刷新")
            return
        }
        if (fileAccess.isDeleting) {
            logTagW(TAG, "正在删除,ignore")
            return
        }
        logTagV(TAG, "refresh:${path.peek().belongTo}")
        refreshing.set(true)
        launch {
            // 刷新时 取消选中状态
            controlBar?.resetSelMode()
            // 根据path中保存当当前路径进行加载
            try {
                if (path.peek().belongTo == AccessType.USB) {
                    USBFileAccess.isCopyFlag = false
                }

                val sortType = FileShowInfoManager.loadSortType(path.peek().belongTo)
                fileItems = fileAccess.getItemsByTarget(
                    path.peek(),
                    filterMode = filterMode,
                    sortType = sortType
                )
                if (fileItems.count() >= 500 && lastSortType != sortType && lastSortType != null) {
                    fileAdapter.updateAllData(emptyList())
                }
                lastSortType = sortType

                fileAdapter.updateAllData(fileItems)
                if (showTypeKey == SHOW_TYPE_LOCAL_CZUR_SHARE) {
                    fileAdapter.changeUploadUI(
                        mainViewModel.uploadFileList,
                        mainViewModel.uploadFilePauseList!!
                    )
                    val selectCount = fileAdapter.getSelectCount()
                    val totalCount = fileAdapter.getSelectModeCount()
                    controlBar?.updateSelectCount(
                        selectCount,
                        totalCount,
                        fileAdapter.hasSelectUpload()
                    )
                }

                // adapter 刷新完成后, 根据情况移动到指定位置
                refreshCurrentPos(newFolderName, needMoveToTop)
            } catch (exp: Exception) {
                logTagE(TAG, "文件夹刷新失败", tr = exp)

                when (exp) {
                    is MiaoMainlandRejectExp -> {
                        logTagW(TAG, "大陆设备, 在海外访问")
                        if (mainViewModel.serverLo !is ServerLocationOversea) {
                            // 手动将其定位到海外
                            mainViewModel.updateToOverseasServer()
                        }
                        (requireActivity() as FileMainPageActivity).reSelCurrentFragment()
                    }

                    else -> {}
                }

                try {
                    fileAdapter.updateAllData(emptyList())
                } catch (e: Exception) {
                    e.printStackTrace()
                }

            } finally {
                newFolderName = null
                loadingDialog.dismissImmediate()
                refreshing.set(false)
                //去除当前tab红点
                if (mainViewModel.currentTabKey == showTypeKey) {
                    logTagD(TAG, "===showTypeKey==$showTypeKey=")
                    fileTabUpdate = showTypeKey
                }
            }

            if (controlBar == null) {
                logTagW(TAG, "controlBar 为空,退出")
            } else {
                if (!isDetached) {
                    controlBar?.updateFileEntity(path.peek())
                }
            }
        }
    }

    /**
     * 移动内容到指定位置
     */
    private fun refreshCurrentPos(name: String?, needMoveToTop: Boolean) {
        if (name != null) {
            // 新建文件夹
            val newFolderPos = fileItems.indexOfFirst {
                it.name == name
            }

            if (newFolderPos >= 0) {
                launch {
                    // 等待200ms是为了插入动画执行完成
                    delay(200)
                    filePadRv.smoothScrollToPosition(newFolderPos)
                }
            }
        } else if (needMoveToTop) {
            // 回到顶端
            filePadRv.scrollToPosition(0)
        }
    }

    /**
     * 设置ControlBar
     */
    override fun setControlBar(iControlBar: IControlBar) {
        controlBar = iControlBar
    }


    /**
     * 返回上一级文件夹
     */
    override fun backToTop() {
        // 重置选择模式
        controlBar?.resetSelMode()
        if (refreshing.get()) {
            logTagD(TAG, "正在刷新,ignore")
            return
        }
        path.pop() // 移除目前的路径
        refresh()
    }

    /**
     * 过滤显示
     */
    override fun showFilter(mode: FileAccess.FilterMode) {
        filterMode = mode
        refresh(true)
    }

    /**
     * 切换布局模式
     */
    private fun changeLayoutMode(padMode: Boolean) {
        val spanCount = if (padMode) SPAN_COUNT_PAD else SPAN_COUNT_LIST
        layoutManager.spanCount = spanCount
        if (spanCount == SPAN_COUNT_LIST) {
            gridSpace.space = 1
        } else {
            gridSpace.space = 72
        }
        val showMode = if (padMode) MODE_GRID else MODE_LIST
        fileAdapter.changeLayoutType(showMode)
    }

    /**
     * 按照指定排序规则排序显示
     * @param sortType 排序规则
     */
    override fun sortBy(sortType: FileAccess.SortType) {
        refresh(true)
    }

    /**
     * 新建文件夹
     */
    override fun createNewFolder() {
        logTagV(TAG, "新建文件夹")
        NewFolderDialog {
            createNewFolder(it)
        }.show()
    }

    override fun changeUIMode(selectMode: Boolean) {
        this.selectMode = selectMode
        fileAdapter.setCurrentMode(selectMode)
        if (!selectMode) {
            fileAdapter.clearSelect()
        }
    }

    override fun selectAll() {
        fileAdapter.selectAll()
        // 更新控制栏的选择状态
        val selectCount = fileAdapter.getSelectCount()
        val totalCount = fileAdapter.getSelectModeCount()
        controlBar?.updateSelectCount(selectCount, totalCount, fileAdapter.hasSelectUpload())
    }

    override fun selectNone() {
        fileAdapter.clearSelect()
        controlBar?.updateSelectCount(0, fileAdapter.itemCount)
    }

    /**
     * 删除选中的文件
     */
    override suspend fun deleteSelect() {
        // 显示Loading
        loadingDialog.isShowAllTime = true
        loadingDialog.show()

        if (fileAdapter.getSelectCount() > 0) {
            val delItems = fileAdapter.getSelectFiles()
            when (fileAccess.delFiles(delItems, true)) {
                FileAccess.Result.SUCCESS -> {
                    logTagD(TAG, "删除成功")
                    // 定向删除指定Item
                    fileAdapter.delSelect()
                }

                FileAccess.Result.FILE_CONFLICT -> {
                    // 文件冲突
                    toast(R.string.toast_conflict)
                }

                else -> {
                    logTagD(TAG, "删除失败")
                    // 有删除失败的文件
                    // 所以Adapter不知道应该如何更新UI
                    // 重新加载数据
                    refresh()
                }
            }
        } else {
            // 临时添加的提示
            toast(R.string.toast_choose_no_file)
        }
        // 取消loading
        loadingDialog.isShowAllTime = false
        loadingDialog.dismiss()
    }

    /**
     * 重命名选中的文件
     */
    override fun renameSelect() {
        // 显示重命名对话框
        // 可以保证选择的文件只有一个
        val selFile = fileAdapter.getSelectFiles().first()
        RenameDialog(
            hint = getString(R.string.dialog_rename_hint),
            inputText = selFile.name.substringBeforeLast("."),
            title = getString(R.string.dialog_rename_title)
        ) {
            when {
                // 文件名没有变化时,不做任何操作
                it == selFile.name.substringBeforeLast(".") -> {
                    logTagI(TAG, "文件名没有改变")
                }

                it.isNotBlank() -> {
                    doRename(it, selFile)
                }

                else -> {
                    // 没有输入文件名对话框
                    showHintDialog(getString(R.string.str_info_no_file_name))
                }
            }
        }.show()
    }

    // 选择打开方式
    override fun openMethod() {
        val selFile = fileAdapter.getSelectFiles().first()
        if (selFile.fileType == FileType.FOLDER) {
            openFolder(selFile)
        } else {
            openFile(selFile, true)
        }
    }

    /**
     * 执行重命名操作
     */
    private fun doRename(newName: String, selFile: FileEntity) {
        launch {
            loadingDialog.show()
            // 为文件名添加扩展名
            var name = newName
            if (selFile.extension.isNotBlank()) {
                name += ".${selFile.extension}"
            }

            val result = fileAccess.renameFile(selFile, name, path.peek())
            loadingDialog.dismiss()
            if (result == FileAccess.Result.SUCCESS) {
                // 重新更新UI
                refresh()
            } else {
                showErrorDialog(result)
            }
        }
    }

    /**
     * 复制选中的文件
     */
    override fun copyFiles() {
        val selectFiles = fileAdapter.getSelectFiles()
        if (selectFiles.isEmpty()) {
            logTagW(TAG, "没有选中的文件, 忽略操作")
            return
        }
        copysrPath = selectFiles[0].absPath

        copyDialog = CopyFileDialog.Builder(selectFiles)
            .setOnConfirmListener { path, moveMode ->
                doCopy(path, selectFiles, moveMode)
            }
            .buildAndShow()
    }

    /**
     * 移动选中的文件
     */
    override fun moveFiles() {
        val selectFiles = fileAdapter.getSelectFiles()
        copysrPath = selectFiles[0].absPath
        copyDialog = CopyFileDialog.Builder(selectFiles, true)
            .setOnConfirmListener { path, moveMode ->
                doCopy(path, selectFiles, moveMode)
            }
            .buildAndShow()

    }

    override fun cancelJob() {
        copyJob?.cancel()
    }

    /**********复制相关**********/

    /**
     * 执行复制操作
     */
    private fun doCopy(
        targetDir: FileEntity,
        srcFiles: List<FileEntity>,
        isMove: Boolean,
    ) {

        val destBelong = targetDir.belongTo

        launch {
            usbOperationPath.add(targetDir.absPath)
            usbOperationPath.add(srcFiles[0].absPath)

            // 这里接收job 是为了能在对话框中取消
            copyJob = launch {
                val transfer = fileAccess.getTransfer(targetDir, srcFiles, isMove)
                    .apply {
                        progressListener = ::onCopyProcess
                        needUserListener = ::onNeedUserHandle
                        finishedListener = { successFiles ->
                            onCopyFinish(destBelong, isMove)
                            checkAndNoticeCZURShare(targetDir, successFiles)
                        }
                        onErrorListener = {
                            // 先刷新一次当前文件夹
                            onCopyFinish(destBelong, isMove)
                            when (it) {
                                is NoSpaceException -> showCopyNoSpaceDialog(it)
                                is CloudFileInUserExp -> toast(R.string.toast_conflict)
                                else -> showOtherCopyError(isMove) {}
                            }
                        }
                    }

                transfer.transfer()

            }


            // 显示进度对话框
            val progressDialogBuilder = ProgressDialog.Builder()
            if (isMove) {
                progressDialogBuilder.setTitle(R.string.str_dialog_progress_title_move)
            } else {
                progressDialogBuilder.setTitle(R.string.str_dialog_progress_title_copy)
            }
            progressDialogBuilder.dialog.cancelText = getString(R.string.float_tip_cancel_choose)
            progressDialog = progressDialogBuilder
                .setOnCancelListener {
                    logTagD(TAG, "取消协程")
                    copyJob?.cancel() // 取消复制
                    doCopyFinishRefresh(destBelong, isMove)
                    usbOperationPath.clear()
                }
                .buildAndShow("copyProcessDialog")
        }


    }

    /**
     * 检查并通知成者妙传
     */
    private fun checkAndNoticeCZURShare(destDir: FileEntity, srcFiles: List<FileEntity>) {
        if (showTypeKey == SHOW_TYPE_LOCAL_CZUR_SHARE) {
            return  // 当前页面就是成者妙传的页面, 不需要通知
        }
        if (destDir.absPath != CZURShareFileAccess.getRootEntity().absPath) {
            // 没有复制到成者妙传根路径,不需要通知(与其他tab的复制效果一样)
            return
        }
        if (srcFiles.isEmpty()) {
            logTagV(TAG, "没有复制的文件, 不需要通知")
            return
        }
        val name = srcFiles.last().name
        FileWatcherService.manualUpdate(
            context ?: globalAppCtx,
            UnReadFileManager.PathType.TYPE_SHARE_FILE,
            name
        )
    }

    /**
     * 当复制/移动时发生错误的回调
     */
    private fun onNeedUserHandle(
        file: FileEntity,
        error: FileTransfer.NeedUserHandleType,
        mode: (FileTransfer.HandleMode) -> Unit,
    ) {
        // 接收错误信息
        when (error) {
            FileTransfer.NeedUserHandleType.FILE_NAME_REPEAT -> dialogCustom =
                showCopyFileRepeatDialog(file, mode)
        }

    }

    /**
     * 当复制/移动时更新进度的回调
     * @param process: 进度信息 0-100
     */
    private fun onCopyProcess(process: Int) {
        // 接收进度信息
        progressDialog?.progress = process
    }

    /**
     * 当复制/移动完成后的回调
     * @param isMove :  是否是移动
     */
    private fun onCopyFinish(destBelong: AccessType, isMove: Boolean) {
        // 当完成时
        progressDialog?.let {
            logTagD(TAG, "复制对话框消失")
            it.dismissAllowingStateLoss()
            doCopyFinishRefresh(destBelong, isMove)
            usbOperationPath.clear()
        }
        lifecycleScope.launch {
            delay(ONE_SECOND)
            logTagV(TAG, "通过tag再检查一次")
            requireActivity().supportFragmentManager.findFragmentByTag("copyProcessDialog")?.let {
                logTagW(TAG, "复制对话框消失-通过TAG寻找到的")
                (it as ProgressDialog).dismissAllowingStateLoss()
                doCopyFinishRefresh(destBelong, isMove)
                usbOperationPath.clear()
            }
        }

        LocalInfoManager.refreshUsageOnce()

    }

    /**
     * 根据不同的情况来决定是否需要
     */
    private fun doCopyFinishRefresh(dest: AccessType, isMove: Boolean) {
        // 移动操作
        if (isMove) {
            logTagV(TAG, "取消移动, 刷新当前页面")
            launch {
                // 刷新当前文件夹 延迟1s, 来让存储容量稳定
                delay(ONE_SECOND)
                refresh()
            }
            return
        }

        // 复制操作
        if (dest != pageBelongTo && dest.takeUpStorageLocation != pageBelongTo.takeUpStorageLocation) {
            logTagD(TAG, "取消复制, 目的地不是当前页面, 不需要刷新")
            return
        }

        // 复制操作, 切目的地是当前页面
        // 因为不支持 在当前页面中, 直接进行复制操作, 所以, 只需要刷新存储空间即可
        launch {
            // 刷新当前文件夹 延迟1s, 来让存储容量稳定
            logTagD(TAG, "刷新当前容量信息")
            delay(ONE_SECOND)
            fileAccess.updateUsage()
        }

    }

    /**********复制相关**********/

    /**
     * 展示错误对话框
     */
    private fun showErrorDialog(error: FileAccess.Result) {
        when (error) {
            FileAccess.Result.FILE_NAME_REPETITION ->
                // 文件名重复对话框
                showHintDialog(getString(R.string.str_info_filename_repeat))

            FileAccess.Result.SYSTEM_ERR ->
                showHintDialog(getString(R.string.str_info_system_err))
            // 成功, 正常不会走到这个分支
            FileAccess.Result.SUCCESS -> {
            }

            FileAccess.Result.FILE_CONFLICT -> {
                toast(R.string.toast_conflict)
            }

            FileAccess.Result.NO_NET_WORK -> {
                toast(R.string.toast_operation_failure_by_net)
                fileAdapter.updateAllData(emptyList())
            }

            FileAccess.Result.CN_DEVICES_IN_OVERSEAS -> {
                logTagW(TAG, "国内设备在海外使用")
                mainViewModel.updateToOverseasServer()
                toastFail()
                (requireActivity() as FileMainPageActivity).reSelCurrentFragment()
            }

            else -> {}
        }
    }


    /**
     * 创建新文件夹
     */
    private fun createNewFolder(folderName: String) {
        launch {
            // 创建文件夹
            loadingDialog.show()
            try {
                val fileNames = fileAdapter.fileEntities
                    .filter { it.isDir() }.map {
                        it.name
                    }
                val result = fileAccess.createNewFolder(folderName, path.peek(), fileNames)
                when {
                    result == "" -> {
                        // 有冲突
                        toast(R.string.toast_conflict)
                    }

                    result != null -> {
                        newFolderName = result
                        // 更新UI
                        refresh()
                    }

                    else -> {
                        showHintDialog(R.string.dialog_create_folder_fail.getStrRes())
                    }
                }
            } catch (exp: Exception) {
                when (exp) {
                    is NoNetworkException -> {
                        toast(R.string.toast_no_network_retry)
                        fileAdapter.updateAllData(emptyList())
                    }

                    else -> {
                        logTagD(TAG, "新建文件夹失败", tr = exp)
                    }
                }
            }
            loadingDialog.dismiss()
        }
    }

    override fun needLogin(): Boolean = false

    override fun getBelongTo(): AccessType = path.peek().belongTo


    /**
     * 打开文件夹
     */
    private fun openFolder(folder: FileEntity) {
        path.push(folder)
        refresh()
    }

    /**
     * 语音打开文件
     */
    private fun openFileIfExists(file: FileEntity?) {
        file?.let {
            openFile(it)
        }
    }

    /**
     * 打开文件
     */
    private fun openFile(file: FileEntity, isRightClick: Boolean = false) {
        launch {
            logTagV(TAG, "add Open File")
            openFileEventFlow.emit(file to isRightClick)
        }
    }

    private fun doOpenFile(file: FileEntity, isRightClick: Boolean = false) {
        logTagD(TAG, "doOpenFile:${file.name} - isRightClick:${isRightClick}")
        isOpeningAudioOrVideo = file.fileType == FileType.VIDEO || file.fileType == FileType.AUDIO
        launch {

            if (file.fileType == FileType.APK || file.fileType == FileType.X_APK) {
                if (!canInstallApp()
                    && !SettingUtil.SystemSetting.isApkInstallRestrictionRemoved()
                ) {
                    showUnableInstallDialog()
                    return@launch
                }
            }

            if (isOpeningAudioOrVideo) {
                loadingDialog.show()
            } else {
                loadingDialog.showDelay()
            }

            try {
                if (file.fileType == FileType.X_APK) {
                    if (loadingDialog.isShowAllTime) return@launch
                    loadingDialog.isShowAllTime = true
                    val xApkName = File(file.absPath).nameWithoutExtension
                    val isUnzipSuccess = withContext(Dispatchers.IO) {
                        unzipXAPK(file.absPath, xApkName)
                    }
                    if (isUnzipSuccess) {
                        showUnknownSourceFloat(file)
                    } else {
                        deleteDirectory(xApkName)
                    }
                    loadingDialog.isShowAllTime = false
                } else {
                    logTagV(TAG, "showTypeKey = $showTypeKey")
                    if (showTypeKey == SHOW_TYPE_ENCRYPTION) {
                        // 加密文件只能打开一个文件,没有列表
                        file.open(listOf(file), requireContext(), isRightClick)
                    } else {
                        file.open(fileAdapter.fileEntities, requireContext(), isRightClick)
                    }
                }

            } catch (exp: Exception) {
                logTagE(TAG, "文件打开失败:${file}", tr = exp)
                toast(R.string.toast_open_file_err)
            }
            if (!isOpeningAudioOrVideo) {
                loadingDialog.dismissImmediate()
            }
        }
    }

    /**
     * 是否允许安装应用
     */
    private fun canInstallApp(): Boolean {
        return when (val s = Constants.starryHWInfo.series) {
            is Q1Series -> when (s.model) {
                StarryModel.Q1Model.Q1 -> false
                else -> true
            }

            Q2Series -> when (s.model) {
                StarryModel.Q2Model.Q2 -> false
                StarryModel.Q2Model.Q2Pro -> false
                else -> true
            }

            StudioSeries -> when (s.model) {
                StarryModel.StudioModel.Studio -> false
                StarryModel.StudioModel.StudioPro -> false
                else -> true
            }
        }
    }


    // 显示“该应用无法安装”dialog
    private fun showUnableInstallDialog() {
        if (unableDialog != null) return

        unableDialog =
            SingleBtnCommonFloat(content = getString(R.string.dialog_file_unable_install_xapk)) { commonFloat ->
                unableDialog?.dismiss()
                unableDialog = null
            }
        unableDialog?.show()
    }

    // 显示未知来源dialog
    private fun showUnknownSourceFloat(file: FileEntity) {
        unknownSourceFloat = DoubleBtnCommonFloat(
            content = getString(R.string.dialog_file_unknown_source_install_tips1),
            confirmBtnText = getString(R.string.dialog_file_btn_continue)
        ) { commonFloat, position ->
            commonFloat.dismiss()
            val filePath = File(file.absPath)
            val xApkName = filePath.nameWithoutExtension
            when (position) {
                DoubleBtnCommonFloat.DOUBLE_FLOAT_BTN_CANCEL -> {
                    deleteDirectory(xApkName)
                }

                DoubleBtnCommonFloat.DOUBLE_FLOAT_BTN_CONFIRM -> {
                    val fileName = getXApkName(xApkName)
                    showUnknownSourceSecondFloat(file, fileName)
                }
            }
        }.apply {
            show()
            setOnDismissListener {
                unknownSourceFloat = null
            }
        }
    }

    // 显示风险提示，是否继续dialog
    private fun showUnknownSourceSecondFloat(file: FileEntity, fileName: String) {
        unknownSourceSecondFloat = DoubleBtnCommonFloat(
            content = getString(R.string.dialog_file_unknown_source_install_tips2, fileName),
            confirmBtnText = getString(R.string.dialog_file_btn_still_install)
        ) { commonFloat, position ->
            commonFloat.dismiss()
            when (position) {
                DoubleBtnCommonFloat.DOUBLE_FLOAT_BTN_CANCEL -> {
                    val xApkName = File(file.absPath).nameWithoutExtension
                    deleteDirectory(xApkName)
                }

                DoubleBtnCommonFloat.DOUBLE_FLOAT_BTN_CONFIRM -> {
                    showInstallLoadingFloat(file)
                }
            }
        }.apply {
            show()
            setOnDismissListener {
                unknownSourceSecondFloat = null
            }
        }
    }

    // 安装loading dialog
    private fun showInstallLoadingFloat(file: FileEntity) {
        installLoadingFloat = InstallLoadingFloat(outSideDismiss = false).apply {
            show()
        }
        launch {
            installXApk(file.absPath, object : InstallXApkCallback {
                override fun onInstallSuccess(packageName: String) {
                    installLoadingFloat?.dismiss()
                    installLoadingFloat = null
                    showInstallXApkSuccessFloat(packageName)
                }

                override fun onInstallFailure(e: Exception) {
                    logTagD(TAG, "安装xApk失败：${e}")
                    installLoadingFloat?.dismiss()
                    installLoadingFloat = null
                    toast(R.string.toast_file_install_xapk_failed)
                }
            })
        }
    }

    private fun showInstallXApkSuccessFloat(packageName: String) {
        if (!isAdded || installXApkSuccessFloat != null) {
            return
        }
        installXApkSuccessFloat = DoubleBtnCommonFloat(
            content = getString(R.string.dialog_file_content_installed_application),
            confirmBtnText = getString(R.string.dialog_file_btn_open_file),
            cancelBtnText = getString(R.string.dialog_file_btn_done),
        ) { commonFloat, position ->
            commonFloat.dismiss()
            when (position) {
                DoubleBtnCommonFloat.DOUBLE_FLOAT_BTN_CANCEL -> {}
                DoubleBtnCommonFloat.DOUBLE_FLOAT_BTN_CONFIRM -> {
                    val intent = packageManager.getLaunchIntentForPackage(packageName)
                    if (intent != null) {
                        context?.startActivity(intent)
                    }
                }
            }
        }.apply {
            show()
            setOnDismissListener {
                installXApkSuccessFloat = null
            }
        }
    }


    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        return if (keyCode == KeyEvent.KEYCODE_BACK) {
            if (isOpeningAudioOrVideo) {
                logTagV(TAG, "正在打开音视频文件, 不能取消")
                return true // 正在打开音视频的时候, 不能取消,否则会推出主页面
            }
            if (path.size > 1) {
                backToTop()
                return true
            }
            false
        } else {
            false
        }

    }

    /**
     * 初始化各个FileAccess
     */
    private fun createFileAccess() = when (showTypeKey) {
        SHOW_TYPE_USB ->
            FileManager.createFileAccess(
                AccessType.USB,
                usbPath = extData?.getString(UsbHelper.KEY_USB_PATH) ?: ""
            )

        SHOW_TYPE_LOCAL_DOWNLOAD ->
            FileManager.createFileAccess(AccessType.LOCAL, EXT_TYPE_LOCAL_DOWNLOAD)

        SHOW_TYPE_LOCAL_SCREEN_SHOT ->
            FileManager.createFileAccess(AccessType.LOCAL, EXT_TYPE_LOCAL_SCREEN_SHOT)

        SHOW_TYPE_LOCAL_CZUR_SHARE ->
            FileManager.createFileAccess(AccessType.LOCAL, EXT_TYPE_LOCAL_CZUR_SHARE)

        SHOW_TYPE_ENCRYPTION -> // 加密文件
            FileManager.createFileAccess(AccessType.LOCAL, EXT_TYPE_LOCAL_ENCRYPTION)

        else ->
            FileManager.createFileAccess(AccessType.LOCAL)
    }

}