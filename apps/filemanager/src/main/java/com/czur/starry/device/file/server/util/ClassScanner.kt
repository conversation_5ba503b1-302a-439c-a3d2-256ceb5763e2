package com.czur.starry.device.file.server.util

import android.content.Context
import android.text.TextUtils
import com.czur.czurutils.global.globalAppCtx
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.file.server.component.ComponentRepository
import com.czur.starry.device.file.server.component.anno.Servlet
import com.czur.starry.device.file.server.component.servlet.HttpServlet
import dalvik.system.DexFile
import java.lang.reflect.Constructor


/**
 * Created by 陈丰尧 on 2024/12/16
 */
private const val TAG = "ClassScanner"

class ClassScanner {
    /**
     * Get all classes from package
     *
     * @param pack      Package path
     * @return
     */
    private fun getClasses(pack: String): Set<Class<*>> {
        val context: Context = globalAppCtx
        // Set of the first class class
        val classes: MutableSet<Class<*>> = LinkedHashSet()
        try {
            val df =
                DexFile(context.packageCodePath) //Find the executable file in the current APK through dexfile
            //Get the element in DF, which contains all executable class names. The class name contains the way of package name + class name
            val enumeration = df.entries()
            while (enumeration.hasMoreElements()) {
                val className = enumeration.nextElement()

                if (className.contains(pack)) {
                    classes.add(Class.forName(className))
                }
            }
        } catch (tr: Throwable) {
            logTagW(TAG, "getClasses error:package: $pack", tr = tr)
        }


        return classes
    }


    private fun getClassesByAnno(
        pkgName: String,
        vararg componentAnnos: Class<out Annotation?>
    ): Set<Class<HttpServlet>> {
        if (componentAnnos.isEmpty()) {
            return HashSet()
        }
        val classes = getClasses(pkgName)
        val clazzSet = mutableSetOf<Class<HttpServlet>>()
        for (aClass in classes) {
            for (componentAnno in componentAnnos) {
                if (aClass.isAnnotationPresent(componentAnno) && HttpServlet::class.java.isAssignableFrom(aClass)) {
                    clazzSet.add(aClass as Class<HttpServlet>)
                    break
                }
            }
        }
        return clazzSet
    }


    /**
     * Scan all servlets and filters and index them
     *
     * @param pkgName Package name to scan
     */
    fun scannerAllServlet(pkgName: String): Map<String, Class<HttpServlet>> {
        val result = mutableMapOf<String, Class<HttpServlet>>()
        val servletClasses = getClassesByAnno(
            pkgName,
            Servlet::class.java,
        )
        logTagD(TAG, "servletClasses.size():" + servletClasses.size)
        for (servletClass in servletClasses) {
            if (servletClass.isAnnotationPresent(Servlet::class.java)) {
                val servletClassAnno = servletClass.getDeclaredAnnotation(Servlet::class.java)
                if (servletClassAnno == null) {
                    logTagD(TAG, "servletClassAnno is null")
                    continue
                }
                val path = servletClassAnno.path
                logTagD(TAG, path)
                if (TextUtils.isEmpty(path)) {
                    continue
                }
                try {
                    result[path] = servletClass
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }
        return result
    }

}