package com.czur.starry.device.file.view.dialog

import android.view.KeyEvent
import com.czur.starry.device.baselib.R
import com.czur.starry.device.baselib.base.listener.KeyDownListener
import com.czur.starry.device.baselib.base.v2.fragment.floating.CZVBFloatingFragment
import com.czur.starry.device.baselib.widget.CommonButton
import com.czur.starry.device.file.databinding.PromptCommonFloatDoubleBtnBinding

class PromptDoubleBtnCommonFloat(
    // 标题
    private val title: String = com.czur.starry.device.baselib.utils.getString(R.string.dialog_normal_title_tips),
    // 内容
    private val content: String = "",
    private val askCheckBoxStatus: Boolean = true,
    // 按钮
    private val cancelBtnText: String = com.czur.starry.device.baselib.utils.getString(R.string.dialog_normal_cancel),
    private val confirmBtnText: String = com.czur.starry.device.baselib.utils.getString(R.string.dialog_normal_confirm),
    private val confirmTheme: CommonButton.Theme = CommonButton.Theme.WHITE2,
    private val outSideDismiss: Boolean = false,
    // 确定按钮点击事件
    private val onCommonClick: ((commonFloat: PromptDoubleBtnCommonFloat, position: Int, isCheck: Boolean) -> Unit)? = null,

    ) : CZVBFloatingFragment<PromptCommonFloatDoubleBtnBinding>(),
    KeyDownListener {
    companion object {
        const val DOUBLE_FLOAT_BTN_CONFIRM = 1
        const val DOUBLE_FLOAT_BTN_CANCEL = 0
    }

    override fun FloatingFragmentParams.initFloatingParams() {
        floatingBgMode = FloatingBgMode.Dark
        outSideDismiss = <EMAIL>
    }

    override fun PromptCommonFloatDoubleBtnBinding.initBindingViews() {
        promptDoubleBtnFloatTitleTv.text = title
        promptDoubleBtnFloatContentTv.text = content
        noPromptTipsTv.text = getString(R.string.baselib_alert_dialog_no_prompt)
        promptDoubleBtnFloatCancelBtn.text = cancelBtnText
        promptDoubleBtnFloatConfirmBtn.text = confirmBtnText
        promptDoubleBtnFloatConfirmBtn.changeTheme(confirmTheme)
        noPromptCheckBox.isChecked = askCheckBoxStatus

        promptDoubleBtnFloatCancelBtn.setOnClickListener {
            onCommonClick?.invoke(
                this@PromptDoubleBtnCommonFloat,
                DOUBLE_FLOAT_BTN_CANCEL,
                noPromptCheckBox.isChecked
            )
        }
        promptDoubleBtnFloatConfirmBtn.setOnClickListener {
            onCommonClick?.invoke(
                this@PromptDoubleBtnCommonFloat,
                DOUBLE_FLOAT_BTN_CONFIRM,
                noPromptCheckBox.isChecked
            )
        }
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        return if (keyCode == KeyEvent.KEYCODE_BACK) {
            dismiss()
            true
        } else {
            false
        }
    }
}