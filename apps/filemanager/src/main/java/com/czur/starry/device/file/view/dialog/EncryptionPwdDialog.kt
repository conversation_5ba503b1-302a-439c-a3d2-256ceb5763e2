package com.czur.starry.device.file.view.dialog

import android.graphics.Bitmap
import android.os.Bundle
import androidx.fragment.app.viewModels
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.base.v2.fragment.floating.CZVBFloatingFragment
import com.czur.starry.device.baselib.utils.basic.otherwise
import com.czur.starry.device.baselib.utils.basic.yes
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.baselib.utils.toast
import com.czur.starry.device.file.R
import com.czur.starry.device.file.databinding.DialogEncryptionPwdBinding
import com.czur.starry.device.file.view.encryption.pwd.PwdEnterViewModel
import com.czur.starry.device.file.widget.EncryptionPwdGroupView

/**
 * Created by 陈丰尧 on 2024/12/13
 */
private const val TAG = "EncryptionPwdDialog"

class EncryptionPwdDialog(
    private val bgImg: Bitmap,
) : CZVBFloatingFragment<DialogEncryptionPwdBinding>() {
    private val enterViewModel: PwdEnterViewModel by viewModels()

    override fun FloatingFragmentParams.initFloatingParams() {
        this.floatingBgMode = FloatingBgMode.Image(bgImg, true)
        this.floatingRepeatMode = FloatingRepeatMode.Single("EncryptionPwdDialog")
    }

    override fun DialogEncryptionPwdBinding.initBindingViews() {
        encryptionPwdGroupView.onPwdChangeCallback = {
            enterViewModel.setUserSetPwd(it)
        }

        encryptionPwdGroupView.setTextColor(resources.getColor(R.color.white, null))

        encryptionPwdGroupView.bindWithEyeImageView(
            pwdEyeIv, R.drawable.ic_pwd_eye_open, R.drawable.ic_pwd_eye_close
        )
        encryptionPwdGroupView.eyeStatus = EncryptionPwdGroupView.EyeStatus.CLOSE // 默认不显示密码
        encryptionPwdGroupView.setPwdFinishFocusView(confirmFocusBtn)
        confirmFocusBtn.setOnClickListener { enterBtn.performClick() }

        // 确认按钮
        enterBtn.setOnDebounceClickListener {
            launch {
                enterViewModel.checkPwd().also {
                    enterViewModel.saveInputRes(it)
                }.yes {
                    logTagV(TAG, "密码正确")
                    dismiss()
                } otherwise {
                    val count = enterViewModel.getRemainingInputCount()
                    logTagV(TAG, "密码错误，剩余次数$count")
                    if (count > 0) {
                        toast(R.string.toast_encryption_pwd_error, count)
                    }
                    encryptionPwdGroupView.clearAllPwd()
                }
            }
        }

        closeIv.setOnDebounceClickListener {
            dismiss()
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        repeatCollectOnResume(enterViewModel.enterBtnEnableFlow) {
            binding.enterBtn.isEnabled = it
        }

        // 输入锁定
        repeatCollectOnResume(enterViewModel.lockInputFlow) { lock ->
            binding.encryptionPwdGroupView.isEnabled = !lock
            binding.pwdEyeIv.isEnabled = !lock
            binding.lockInfoTv.gone(!lock)
        }

        // 提示时间
        repeatCollectOnResume(enterViewModel.remainingLockTimeFlow) {
            binding.lockInfoTv.text =
                getString(
                    R.string.str_encryption_lock_input_info,
                    enterViewModel.formatLockTime(it)
                )
        }
    }
}