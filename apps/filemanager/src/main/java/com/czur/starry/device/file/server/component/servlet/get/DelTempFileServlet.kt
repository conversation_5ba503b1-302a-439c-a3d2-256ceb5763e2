package com.czur.starry.device.file.server.component.servlet.get

import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.file.server.common.FileShareConstant
import com.czur.starry.device.file.server.component.anno.Servlet
import com.czur.starry.device.file.server.component.servlet.HttpServlet
import com.czur.starry.device.file.server.upload.UploadHelper
import com.czur.starry.device.file.server.util.CZHttpRequest
import com.czur.starry.device.file.server.util.CZHttpResponse
import com.czur.starry.device.file.server.util.syncUploadFile
import kotlinx.coroutines.delay
import java.io.File

/**
 * Created by 陈丰尧 on 2024/12/17
 */
private const val TAG = "DelTempFile"

@Servlet(path = FileShareConstant.GET_DELETE_TEMP_FILE)
class DelTempFileServlet : HttpServlet() {
    override suspend fun onConnect(request: CZHttpRequest, response: CZHttpResponse) {
        logTagV(TAG, "DelTempFileServlet onConnect")
        try {
            val info = UploadHelper.getHeaderInfo(request)

            syncUploadFile()
            delay(200)
            UploadHelper.getFileInfo(info.fileName, info.fileMd5, info.deviceId)?.let {
                File(it.tempName).delete()
                UploadHelper.delFileInfoFromData(it.id)
            }
            response.mkMsg(FileShareConstant.TRANS_SUCCESS, "delete temp file success")
        } catch (throwable: Throwable) {
            logTagE(TAG, "DelTempFileServlet onConnect error", tr = throwable)
            response.mkMsg(FileShareConstant.UNKNOWN_ERROR, throwable.message ?: "unknown error")
        }


    }
}