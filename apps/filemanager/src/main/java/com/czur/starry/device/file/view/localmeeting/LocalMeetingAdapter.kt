package com.czur.starry.device.file.view.localmeeting

import android.view.MotionEvent
import android.view.ViewGroup
import com.czur.starry.device.baselib.base.BaseDifferAdapter
import com.czur.starry.device.baselib.base.BaseVH
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.StarryDevLocale
import com.czur.starry.device.baselib.utils.basic.otherwise
import com.czur.starry.device.baselib.utils.basic.yes
import com.czur.starry.device.baselib.utils.getString
import com.czur.starry.device.baselib.utils.getTimeStr
import com.czur.starry.device.file.R
import com.czur.starry.device.file.bean.FileEntity
import com.czur.starry.device.file.utils.KEY_DURATION_STR
import com.czur.uilib.choose.CZCheckBox
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Created by 陈丰尧 on 2022/8/10
 */

class LocalMeetingAdapter : BaseDifferAdapter<FileEntity>() {
    companion object {
        private const val PATTERN = "yyyy.MM.dd HH:mm:ss"
        private const val FILE_TYPE_AAC = ".aac"
    }

    //选择模式
    var selectMode = false
        set(value) {
            field = value
            selectPos.clear()//切换选择模式后, 同时将所有的选择记录清空
            notifyDataSetChanged()
        }

    // 选中的item
    private var selectPos = mutableSetOf<Int>()

    /**
     * 选中的数量
     */
    val selectCount: Int
        get() = selectPos.size

    private var lastHoverInHolder: BaseVH? = null


    /**
     * 选中全部
     */
    fun selectAll() {
        for (pos in 0 until itemCount) {
            selectPos.add(pos)
        }
        notifyDataSetChanged()
    }

    /**
     * 全不选
     */
    fun selectNone() {
        if (selectPos.isEmpty()) return // 本身就啥也没选, 就取消了
        selectPos.clear()
        notifyDataSetChanged()
    }

    /**
     * 切换对应项的选中状态
     */
    fun changeSel(pos: Int) {
        selectPos.contains(pos)
            .yes {
                selectPos.remove(pos)
            }.otherwise {
                selectPos.add(pos)
            }
        notifyItemChanged(pos)
    }

    /**
     * 获取选中的文件
     */
    fun getSelFiles(): List<FileEntity> {
        return selectPos.isEmpty()
            .yes { emptyList<FileEntity>() }
            .otherwise {
                selectPos.map { pos ->
                    getData(pos)
                }
            }
    }

    /**
     * 删除选中东
     */
    suspend fun delSelect(delFiles: List<FileEntity>) = withContext(Dispatchers.IO) {
        val newData = getDataList().toMutableList()
        newData.removeAll(delFiles)
        setData(newData)
    }

    private fun getTransferShowStatus(itemData: FileEntity): Boolean {
        val isMainland = Constants.starryHWInfo.salesLocale == StarryDevLocale.Mainland
        val isShow = itemData.extension != "txt"
        return isMainland && isShow
    }

    override fun bindViewHolder(holder: BaseVH, position: Int, itemData: FileEntity) {


        //背景颜色
        holder.itemView.setOnHoverListener { view, motionEvent ->
            when (motionEvent.action) {
                MotionEvent.ACTION_HOVER_ENTER -> {
                    if (lastHoverInHolder != holder) {
                        lastHoverInHolder = holder
                    }
                    // 鼠标悬停进入
                    view.setBackgroundResource(R.drawable.bg_color_transfer_list_hover)
                    true
                }

                MotionEvent.ACTION_HOVER_EXIT -> {
                    // 鼠标悬停离开
                    view.setBackgroundResource(R.color.color_trans)
                    true

                }

                else -> false
            }
        }

        //显示勾选
        holder.visible(selectMode, R.id.localMeetCheckIv)
        val meetCheckIv = holder.getView<CZCheckBox>(R.id.localMeetCheckIv)
        meetCheckIv.setChecked(position in selectPos) // 选中对勾

        holder.setText(itemData.name, R.id.localMeetingFileNameTv)
        val timeStr = getTimeStr(PATTERN, itemData.lastModifyTime)
        holder.setText(timeStr, R.id.localMeetingTimeTv)
        val duration = itemData.getAttribute(KEY_DURATION_STR)
        holder.setText(
            "${getString(R.string.meeting_record_duration)}$duration",
            R.id.localMeetingDurationTv
        )

        if (itemData.name.endsWith(FILE_TYPE_AAC)) {
            holder.setImgResource(R.drawable.icon_meet_voice, R.id.local_meeting_iv)
        } else if (itemData.extension == "txt") {
            holder.setImgResource(R.drawable.file_icon_txt, R.id.local_meeting_iv)
            holder.setText("", R.id.localMeetingDurationTv)
        } else {
            holder.setImgResource(R.drawable.ic_local_meeting, R.id.local_meeting_iv)
        }
    }

    override fun areItemsTheSame(oldItem: FileEntity, newItem: FileEntity): Boolean {
        return oldItem.absPath == newItem.absPath
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseVH {
        return BaseVH(R.layout.item_local_meeting, parent)
    }
}