package com.czur.starry.device.file.service

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.FileObserver
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.preferencesDataStore
import androidx.lifecycle.LifecycleService
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.handler.createDefCorruptionHandler
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.file.filelib.FileHandlerLive
import com.czur.starry.device.file.filelib.FileHandlerLive.unReadFileName
import com.czur.starry.device.file.manager.AITransAccess
import com.czur.starry.device.file.manager.CZURShareFileAccess
import com.czur.starry.device.file.manager.DownloadFileAccess
import com.czur.starry.device.file.manager.LocalFileAccess
import com.czur.starry.device.file.manager.LocalMeetingAccess
import com.czur.starry.device.file.manager.ScreenShotFileAccess
import com.czur.starry.device.file.manager.UnReadFileManager
import com.czur.starry.device.file.manager.UnReadFileManager.addUnreadFile
import com.czur.starry.device.file.manager.UnReadFileManager.getLastFileName
import com.czur.starry.device.file.manager.UnReadFileManager.getUnreadFile
import com.czur.starry.device.file.manager.UnReadFileManager.refreshUnRead
import com.czur.starry.device.file.utils.TargetFileObserver
import kotlinx.coroutines.delay
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import java.io.File
import java.io.IOException


/**
 * created by wangh on 23.0516
 * 监控未读文件：
 * 本地文件；下载文件；拍照/截图；本地会议录制
 */


class FileWatcherService : LifecycleService() {
    companion object {
        private const val TAG = "FileWatcherService"
        private const val UNREAD_DATA_STORE_NAME = "UnReadFile"
        const val UPLOAD_FILE_NAME_KEY = "fileName"
        const val KEY_UPDATE_FILE_TYPE = "updateFileType"
        const val ACTION_UPLOAD_FILE_COMPLETE = "com.czur.starry.device.file.UPLOAD_FILE_COMPLETE"
        const val ACTION_FILE_WATCH_UPDATE = "com.czur.starry.device.file.FILE_WATCH_UPDATE"

        val localPath = LocalFileAccess.getRootEntity().absPath
        val localScreenShort = ScreenShotFileAccess.getRootEntity().absPath
        val localDownload = DownloadFileAccess.getRootEntity().absPath
        val localShare = CZURShareFileAccess.getRootEntity().absPath
        val localMeeting = LocalMeetingAccess.getRootEntity().absPath
        val aiTrans = AITransAccess.getRootEntity().absPath

        var localState = false
        var downloadState = false
        var czurShareState = false
        var screenShortState = false
        var meetingState = false
        var aiTransState = false

        lateinit var remindDataStore: DataStore<Preferences>

        private val Context.dataStore: DataStore<Preferences> by preferencesDataStore(
            name = UNREAD_DATA_STORE_NAME,
            corruptionHandler = createDefCorruptionHandler(UNREAD_DATA_STORE_NAME)
        )

        fun getDSInstance(context: Context) {
            if (!::remindDataStore.isInitialized) {
                remindDataStore = context.dataStore
            }
        }

        /**
         * 手动更新文件
         * @param fileType 文件类型
         * @param fileName 文件名称
         */
        fun manualUpdate(context: Context, fileType: UnReadFileManager.PathType, fileName: String) {
            val intent = Intent(context, FileWatcherService::class.java)
            intent.action = ACTION_FILE_WATCH_UPDATE
            intent.putExtra(KEY_UPDATE_FILE_TYPE, fileType.name)
            intent.putExtra(UPLOAD_FILE_NAME_KEY, fileName)
            context.startService(intent)
        }
    }


    private var localFileObserver: TargetFileObserver? = null
    private var screenShortObserver: TargetFileObserver? = null
    private var downloadOberver: TargetFileObserver? = null
    private var czurShareOberver: TargetFileObserver? = null
    private var meetingObserver: TargetFileObserver? = null
    private var aiTransObserver: TargetFileObserver? = null
    private val mutexAdd = Mutex()
    private val mutexClear = Mutex()

    private val uploadReceiver = UploadFileReceiver()
    private val intentFilter = IntentFilter().apply {
        addAction(ACTION_UPLOAD_FILE_COMPLETE)        //监听上传完成的文件
        addAction(ACTION_FILE_WATCH_UPDATE)           //监听文件更新
    }

    override fun onCreate() {
        super.onCreate()
        logTagD("Chen", "onCreate")

        launch {
            logTagD(TAG, "======onCreate")
            getDSInstance(this@FileWatcherService)

            /**获取最新未读名称*/
            updateLastName(getLastFileName() ?: "null")

            /**获取未读*/
            UnReadFileManager.PathType.values().forEach {
                getUnreadFile(pathType = it)
            }
            /**初始化监听*/
            addRootFileWatch()
            /**开始监听*/
            startWatch()
            /**监听点击文件列表后刷新红点*/
            FileHandlerLive.fileTabUpdate = 0   // 每次启动都恢复默认
            delay(ONE_SECOND) // 等待数据刷新
            FileHandlerLive.fileTabUpdateLive.observe(this@FileWatcherService) {
                launch {
                    logTagD(TAG, "====fileTabUpdateLive==$it")
                    mutexClear.withLock {
                        refreshUnRead(it)
                        updateLastName(getLastFileName() ?: "null")
                    }
                }
            }
        }

        // 添加Notification
        showNotification()

        this.registerReceiver(uploadReceiver, intentFilter)

    }

    override fun onTaskRemoved(rootIntent: Intent?) {
        super.onTaskRemoved(rootIntent)
        logTagD("Chen", "onTaskRemoved")
        startService(Intent(this, FileWatcherService::class.java)) // 重启服务
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        super.onStartCommand(intent, flags, startId)
        intent?.let {
            handleIntent(it)
        }
        return START_STICKY
    }


    private fun handleIntent(intent: Intent) {
        val action = intent.action ?: ""
        logTagD(TAG, "action: $action")
        when (action) {
            ACTION_FILE_WATCH_UPDATE -> {
                val typeStr = intent.getStringExtra(KEY_UPDATE_FILE_TYPE) ?: ""
                val name = intent.getStringExtra(UPLOAD_FILE_NAME_KEY) ?: ""
                logTagV(TAG, "typeStr = $typeStr, name = $name")
                val type = try {
                    UnReadFileManager.PathType.valueOf(typeStr)
                } catch (tr: Throwable) {
                    null
                } ?: return

                if (name.isEmpty()) {
                    return
                }

                val path = when (type) {
                    UnReadFileManager.PathType.TYPE_LOCAL_FILE -> localPath
                    UnReadFileManager.PathType.TYPE_SCREEN_SHORT_FILE -> localScreenShort
                    UnReadFileManager.PathType.TYPE_DOWNLOAD_FILE -> localDownload
                    UnReadFileManager.PathType.TYPE_SHARE_FILE -> localShare
                    UnReadFileManager.PathType.TYPE_MEETING_FILE -> localMeeting
                    UnReadFileManager.PathType.TYPE_AI_TRANS_FILE -> aiTrans // 使用本地文件夹作为AI互译文件的路径
                }
                launch {
                    mutexAdd.withLock {
                        handleEvents(
                            type,
                            1000,
                            path,
                            name
                        )
                    }
                }

                launch {
                    getUnreadFile(type)
                }
            }
        }
    }


    override fun onDestroy() {
        super.onDestroy()
        logTagD("Chen", "======onDestroy")
        stopWatch()
        this.unregisterReceiver(uploadReceiver)
        // StartSelf
        startService(Intent(this, FileWatcherService::class.java))
    }

    private fun startWatch() {
        localFileObserver?.startWatch()
        screenShortObserver?.startWatch()
        downloadOberver?.startWatch()
//        czurShareOberver?.startWatch()
        meetingObserver?.startWatch()
        aiTransObserver?.startWatch()
    }

    private fun stopWatch() {
        localFileObserver?.stopWatch()
        screenShortObserver?.stopWatch()
        downloadOberver?.stopWatch()
//        czurShareOberver?.stopWatch()
        meetingObserver?.stopWatch()
        aiTransObserver?.stopWatch()
    }


    private fun addRootFileWatch() {
        localFileObserver =
            TargetFileObserver(
                localPath, FileObserver.CREATE or FileObserver.CLOSE_WRITE or FileObserver.MOVED_TO
            ) { event, path ->
                launch {
                    mutexAdd.withLock {
                        handleEvents(
                            UnReadFileManager.PathType.TYPE_LOCAL_FILE,
                            event,
                            localPath,
                            path
                        )
                    }
                }
            }
        screenShortObserver =
            TargetFileObserver(
                localScreenShort,
                FileObserver.CREATE or FileObserver.CLOSE_WRITE or FileObserver.MOVED_TO
            ) { event, path ->
                launch {
                    mutexAdd.withLock {
                        handleEvents(
                            UnReadFileManager.PathType.TYPE_SCREEN_SHORT_FILE,
                            event,
                            localScreenShort,
                            path
                        )
                    }
                }
            }
        downloadOberver =
            TargetFileObserver(
                localDownload,
                FileObserver.CREATE or FileObserver.CLOSE_WRITE or FileObserver.MOVED_TO
            ) { event, path ->
                launch {
                    mutexAdd.withLock {
                        handleEvents(
                            UnReadFileManager.PathType.TYPE_DOWNLOAD_FILE,
                            event,
                            localDownload,
                            path
                        )
                    }
                }
            }

        czurShareOberver =
            TargetFileObserver(
                localShare,
                FileObserver.CREATE or FileObserver.CLOSE_WRITE or FileObserver.MOVED_TO
            ) { event, path ->
                launch {
                    mutexAdd.withLock {
                        handleEvents(
                            UnReadFileManager.PathType.TYPE_SHARE_FILE,
                            event,
                            localShare,
                            path
                        )
                    }
                }
            }

        meetingObserver =
            TargetFileObserver(
                localMeeting,
                FileObserver.CREATE or FileObserver.CLOSE_WRITE or FileObserver.MOVED_TO
            ) { event, path ->
                launch {
                    mutexAdd.withLock {
                        handleEvents(
                            UnReadFileManager.PathType.TYPE_MEETING_FILE,
                            event,
                            localMeeting,
                            path
                        )
                    }
                }

            }



        aiTransObserver =
            TargetFileObserver(
                aiTrans,
                FileObserver.CREATE or FileObserver.CLOSE_WRITE or FileObserver.MOVED_TO
            ) { event, path ->
                launch {
                    mutexAdd.withLock {
                        handleEvents(
                            UnReadFileManager.PathType.TYPE_AI_TRANS_FILE,
                            event,
                            aiTrans,
                            path
                        )
                    }
                }

            }

    }

    private suspend fun handleEvents(
        pathType: UnReadFileManager.PathType,
        event: Int,
        path: String,
        name: String
    ) {
        logTagV(TAG, "name = $name path = $path event = $event")
        if (name.isEmpty()) return
        if (event == 1000) {
            //修改名称
            launch {
                addUnreadFile(
                    pathType,
                    UnReadFileManager.UnReadFileEntity(
                        name,
                        pathType,
                        System.currentTimeMillis()
                    )
                )
                updateLastName(name)
                logTagD(TAG, "==upload==${name}")
            }
        } else if ((event and FileObserver.CREATE) != 0) {
            val createdFileOrDir = File(path, name)
            if (createdFileOrDir.isDirectory && !name.endsWith(".tmp")) {
                //创建文件夹
                addUnreadFile(
                    pathType,
                    UnReadFileManager.UnReadFileEntity(
                        name,
                        pathType,
                        System.currentTimeMillis()
                    )
                )
                updateLastName(name)
                logTagD(TAG, "===CREATE folder==${name}")
            }
        } else if ((event and FileObserver.CLOSE_WRITE) != 0) {
            val file = File(path, name)
            if (!file.isDirectory && !name.endsWith(".tmp")) {
                try {
                    val reader = file.inputStream()
                    reader.close()
                    // 文件存在且可以读
                    // 创建文件,过滤本地会议录像临时的.tmp文件,临时的upload文件
                    addUnreadFile(
                        pathType,
                        UnReadFileManager.UnReadFileEntity(
                            name,
                            pathType,
                            System.currentTimeMillis()
                        )
                    )
                    updateLastName(name)
                } catch (e: IOException) {
                    // 文件可能不完整
                    logTagD(TAG, "FileObserver.CLOSE_WRITE 文件可能不完整 = ${e}")
                }
                logTagD(TAG, "==CLOSE_WRITE==${name}")
            }

        } else if ((event and FileObserver.MOVED_TO) != 0) {
            //修改名称
            launch {
                addUnreadFile(
                    pathType,
                    UnReadFileManager.UnReadFileEntity(
                        name,
                        pathType,
                        System.currentTimeMillis()
                    )
                )
                updateLastName(name)
                logTagD(TAG, "==MOVED_TO==${name}")
            }
        }

    }

    private fun updateLastName(name: String) {
        unReadFileName = name
    }

    private inner class UploadFileReceiver : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent) {
            val action = intent.action ?: ""
            logTagD(TAG, "receiver#action: $action")
            val name = intent.getStringExtra(UPLOAD_FILE_NAME_KEY) ?: ""
            logTagD(TAG, "name#==: $name")
            launch {
                mutexAdd.withLock {
                    handleEvents(
                        UnReadFileManager.PathType.TYPE_SHARE_FILE,
                        1000,
                        localShare,
                        name
                    )
                }
            }
        }

    }
}