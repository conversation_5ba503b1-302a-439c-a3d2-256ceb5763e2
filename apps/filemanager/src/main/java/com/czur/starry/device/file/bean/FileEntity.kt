package com.czur.starry.device.file.bean

import android.os.Parcel
import android.os.Parcelable
import com.czur.starry.device.file.filelib.AccessType
import com.czur.starry.device.file.filelib.FileType
import com.czur.starry.device.file.utils.getFileType
import com.czur.starry.device.file.utils.isTypeOf
import com.github.promeg.pinyinhelper.Pinyin
import java.io.File

// 打开文件使用的地址
const val ATTR_KEY_OPEN_PATH = "openPath"
const val ATTR_KEY_IMG_THUMB = "thumb"
const val ATTR_KEY_OFFICE_365 = "office365"
const val ATTR_KEY_OSS_FILE_KEY = "ossFileKey"

const val TAG_KEY_ROOT_ABS_PATH = "rootAbsPath"
const val TAG_KEY_LOCAL_TYPE = "localType"

data class FileEntity(
    val absPath: String, //文件绝对路径
    val fileType: FileType, // 文件类型
    val name: String, // 文件名
    var lastModifyTime: Long, // 修改时间
    val extension: String, // 扩展名
    val belongTo: AccessType, //属于哪个类别
    var fileSize: Long,//文件大小 (文件和size为0的文件,大小固定为1)
) : Parcelable {
    val parentPath: String
        get() = absPath.substringBeforeLast(name)

    // 附加属性, 不同的文件类型, 该属性略有不同
    private var extAttributes: HashMap<String, String> = hashMapOf()
    private val tag: HashMap<String, Any> = hashMapOf()

    // 拼音文件名
    val pinyinName: String by lazy {
        Pinyin.toPinyin(name, "").lowercase()
    }

    /**
     * 带斜杠的后缀
     */
    val absPathWithSuffix: String
        get() {
            return if (isDir()) {
                if (absPath.endsWith("/")) absPath else "${absPath}/"
            } else {
                absPath
            }
        }

    // 使用File构建FileEntity
    constructor(file: File) : this(
        file.absolutePath,
        file.getFileType(),
        file.name,
        file.lastModified(),
        file.extension,
        AccessType.LOCAL, //模式是Local的
        file.length()
    )

    fun isDir() = isTypeOf(FileType.FOLDER + FileType.ROOT)

    /**
     * 获取扩展属性
     */
    fun getAttribute(key: String): String = extAttributes[key] ?: ""
    fun putAttribute(key: String, value: String) {
        extAttributes[key] = value
    }

    fun putTag(key: String, value: Any) {
        tag[key] = value
    }

    fun <T> getTag(key: String): T {
        return tag[key] as T
    }

    fun <T> getTagWithDefault(key: String, def: T): T {
        return if (tag.containsKey(key)) {
            tag[key] as T
        } else {
            def
        }
    }

    fun containsAttr(key: String): Boolean = extAttributes.containsKey(key)
    fun containsTag(key: String): Boolean = tag.containsKey(key)

    constructor(parcel: Parcel) : this(
        parcel.readString()!!,
        FileType.valueOf(parcel.readString()!!),
        parcel.readString()!!,
        parcel.readLong(),
        parcel.readString()!!,
        AccessType.valueOf(parcel.readString()!!),
        parcel.readLong()
    ) {
        val map = parcel.readHashMap(HashMap::class.java.classLoader) ?: emptyMap<String, String>()
        map.forEach {
            extAttributes[it.key.toString()] = it.value.toString()
        }

    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(absPath)
        parcel.writeString(fileType.name)
        parcel.writeString(name)
        parcel.writeLong(lastModifyTime)
        parcel.writeString(extension)
        parcel.writeString(belongTo.name)
        parcel.writeLong(fileSize)
        // 写map
        parcel.writeMap(extAttributes as Map<*, *>)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<FileEntity> {
        override fun createFromParcel(parcel: Parcel): FileEntity {
            return FileEntity(parcel)
        }

        override fun newArray(size: Int): Array<FileEntity?> {
            return arrayOfNulls(size)
        }
    }


}
