package com.czur.starry.device.file.view.aitranscription

import androidx.fragment.app.FragmentTransaction
import androidx.fragment.app.commitNow
import com.czur.starry.device.baselib.base.v2.fragment.CZViewBindingFragment
import com.czur.starry.device.file.R
import com.czur.starry.device.file.base.RefreshAble
import com.czur.starry.device.file.databinding.FragmentAiTransRecordContentBinding
import com.czur.starry.device.file.view.ControlBarFragment

private const val FRAGMENT_TAG_AI_TRANS_RECORD_CONTROL_BAR = "aiTransRecordControlBar"
private const val FRAGMENT_TAG_AI_TRANS_RECORD_FILE_PAD = "aiTransRecordFilePad"


class AITransRecordContentFragment : CZViewBindingFragment<FragmentAiTransRecordContentBinding>(),
    RefreshAble {

    private val aiTransListFilePad = AITransListFragment()
    private val aiTransControlBar =
        ControlBarFragment.getInstance(ControlBarFragment.TYPE_AI_TRANS, aiTransListFilePad)

    // 显示错误网络页面
    fun showErrorNetworkFragment() {
        // 将错误网络事件传递给AITransRecordFragment
        val parentFragment = parentFragment
        if (parentFragment is AITransRecordFragment) {
            parentFragment.showErrorNetworkFragment()
        }
    }

    override fun FragmentAiTransRecordContentBinding.initBindingViews() {

        aiTransListFilePad.setControlBar(aiTransControlBar)

        childFragmentManager.commitNow {
            showOrAddFragment(FRAGMENT_TAG_AI_TRANS_RECORD_CONTROL_BAR)
            showOrAddFragment(FRAGMENT_TAG_AI_TRANS_RECORD_FILE_PAD)
        }
    }

    private fun FragmentTransaction.showOrAddFragment(tag: String) {
        val fragment = childFragmentManager.findFragmentByTag(tag)
        if (fragment == null) {
            when (tag) {

                FRAGMENT_TAG_AI_TRANS_RECORD_CONTROL_BAR ->
                    add(R.id.controlBar, aiTransControlBar, tag)


                FRAGMENT_TAG_AI_TRANS_RECORD_FILE_PAD ->
                    add(R.id.filePad, aiTransListFilePad, tag)

                else -> throw IllegalArgumentException("unknown tag: $tag")
            }
        } else {
            show(fragment)
        }
    }

    public fun refresh() {
        aiTransListFilePad.refresh(true, true)
    }

    public fun selNone() {
        aiTransListFilePad.getControlBar().resetSelMode()
        aiTransListFilePad.changeUIMode(false)
        aiTransListFilePad.selectNone()
    }

    override fun refresh(needMoveToTop: Boolean) {

    }
}