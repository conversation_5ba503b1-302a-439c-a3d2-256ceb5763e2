package com.czur.starry.device.file.widget

import android.content.Context
import android.text.method.HideReturnsTransformationMethod
import android.text.method.PasswordTransformationMethod
import android.text.method.TransformationMethod
import android.util.AttributeSet
import android.view.KeyEvent
import android.view.View
import android.widget.EditText
import android.widget.ImageView
import androidx.annotation.ColorInt
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.widget.doAfterTextChanged
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.findViewTreeLifecycleOwner
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.utils.clearContentText
import com.czur.starry.device.baselib.utils.keyboard.focusAndShowKeyboard
import com.czur.starry.device.baselib.utils.keyboard.hideKeyboard
import com.czur.starry.device.baselib.utils.lifecycle.AutoRemoveLifecycleObserver
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.file.R
import com.czur.starry.device.file.databinding.WidgetEncryptionPwdGroupBinding

/**
 * Created by 陈丰尧 on 2024/12/10
 */
class EncryptionPwdGroupView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    enum class EyeStatus {
        OPEN,
        CLOSE
    }

    val binding: WidgetEncryptionPwdGroupBinding by lazy {
        WidgetEncryptionPwdGroupBinding.bind(this)
    }

    var onPwdChangeCallback: (pwd: String) -> Unit = {}

    var eyeStatus: EyeStatus = EyeStatus.CLOSE
        set(value) {
            field = value
            changeEditStatus()
            changeEyeIvStatus()
        }

    private val pwdEditTextList by lazy {
        listOf(
            binding.pwd0Et,
            binding.pwd1Et,
            binding.pwd2Et,
            binding.pwd3Et,
            binding.pwd4Et,
            binding.pwd5Et
        )
    }
    private var eyeIv: BindEyeIvData? = null

    private val lifecycleOwner: LifecycleOwner? by lazy {
        findViewTreeLifecycleOwner()
    }

    private val lifecycleListener = object : AutoRemoveLifecycleObserver {
        override fun onStop(owner: LifecycleOwner) {
            super.onStop(owner)
            // 清空密码
            clearAllPwd()
            binding.clickView.requestFocus()
            val focusView = pwdEditTextList.firstOrNull { it.hasFocus() } ?: binding.pwd0Et
            focusView.clearFocus()
            hideKeyboard(focusView.windowToken)
        }

        override fun onResume(owner: LifecycleOwner) {
            super.onResume(owner)
            val focusView = pwdEditTextList.firstOrNull { it.hasFocus() } ?: binding.pwd0Et
            focusView.clearFocus()
            binding.clickView.requestFocus()
        }
    }

    init {
        inflate(context, R.layout.widget_encryption_pwd_group, this)

        attrs?.let {
            val typedArray = context.obtainStyledAttributes(it, R.styleable.EncryptionPwdGroupView)
            val etBackground =
                typedArray.getResourceId(R.styleable.EncryptionPwdGroupView_etBackground, 0)
            if (etBackground != 0) {
                pwdEditTextList.forEach { et ->
                    et.setBackgroundResource(etBackground)
                }
            }
            typedArray.recycle()
        }

        initPwdEditText()


        binding.clickView.setOnDebounceClickListener {
            val focusView = pwdEditTextList.firstOrNull { it.hasFocus() }
            if (focusView == null) {
                clearAllPwd()
                pwdEditTextList.first().focusAndShowKeyboard()
            } else {
                focusView.focusAndShowKeyboard()
            }

        }
    }

    fun setPwdFinishFocusView(view: View) {
        view.isFocusable = true
        view.isFocusableInTouchMode = true

        pwdEditTextList.last().nextFocusDownId = view.id
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        lifecycleOwner?.lifecycle?.addObserver(lifecycleListener)
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        lifecycleOwner?.lifecycle?.removeObserver(lifecycleListener)
    }

    private fun initPwdEditText() {
        pwdEditTextList.forEachIndexed { index, autoChangeFocusEditText ->
            autoChangeFocusEditText.autoClearText = true    // 不自动清空文本

            autoChangeFocusEditText.doAfterTextChanged {
                onPwdChangeCallback.invoke(onPwdChange())
            }

            // 监听删除键
            autoChangeFocusEditText.setOnKeyListener { _, keyCode, event ->
                // 防止光标跨范围移动
                if (keyCode == KeyEvent.KEYCODE_DPAD_LEFT || keyCode == KeyEvent.KEYCODE_DPAD_RIGHT || keyCode == KeyEvent.KEYCODE_DPAD_UP || keyCode == KeyEvent.KEYCODE_DPAD_DOWN) {
                    return@setOnKeyListener true
                }
                if (keyCode == KeyEvent.KEYCODE_ENTER) {
                    return@setOnKeyListener true
                }

                if (keyCode == KeyEvent.KEYCODE_DEL && event.action == KeyEvent.ACTION_DOWN) {
                    autoChangeFocusEditText.clearContentText()
                    if (index > 0) {
                        pwdEditTextList[index - 1].requestFocus()
                    }
                    true
                } else {
                    false
                }
            }
        }
    }

    /**
     * 设置文字颜色
     */
    fun setTextColor(@ColorInt color: Int) {
        pwdEditTextList.forEach {
            it.setTextColor(color)
        }
    }

    fun bindWithEyeImageView(eyeIv: ImageView, openImageRes: Int, closeImageRes: Int) {
        eyeIv.setOnClickListener {
            eyeStatus = if (eyeStatus == EyeStatus.OPEN) {
                EyeStatus.CLOSE
            } else {
                EyeStatus.OPEN
            }
        }
        this.eyeIv = BindEyeIvData(eyeIv, openImageRes, closeImageRes)
        changeEyeIvStatus()
    }

    private fun changeEditStatus() {
        pwdEditTextList.forEach {
            when (eyeStatus) {
                EyeStatus.OPEN -> {
                    it.changeEditTextMethod(HideReturnsTransformationMethod.getInstance())
                }

                EyeStatus.CLOSE -> {
                    it.changeEditTextMethod(PasswordTransformationMethod.getInstance())
                }
            }
        }
    }

    private fun changeEyeIvStatus() {
        eyeIv?.let {
            it.eyeIv.setImageResource(
                if (eyeStatus == EyeStatus.OPEN) {
                    it.openImageRes
                } else {
                    it.closeImageRes
                }
            )
        }
    }

    private fun EditText.changeEditTextMethod(method: TransformationMethod) {
        val index = selectionStart // 记录光标位置
        transformationMethod = method // 切换密码显示模式
        setSelection(index)            // 恢复光标位置
    }

    private fun onPwdChange(): String {
        return buildString {
            pwdEditTextList.forEach {
                append(it.text.toString())
            }
        }
    }

    private data class BindEyeIvData(
        val eyeIv: ImageView,
        val openImageRes: Int,
        val closeImageRes: Int
    )

    override fun setEnabled(enabled: Boolean) {
        super.setEnabled(enabled)
        pwdEditTextList.forEach {
            it.isEnabled = enabled
            it.alpha = if (enabled) 1f else 0.5f
        }
        eyeIv?.eyeIv?.let {
            it.isEnabled = enabled
            it.alpha = if (enabled) 1f else 0.5f
        }

    }

    /**
     * 清空所有密码
     */
    fun clearAllPwd() {
        pwdEditTextList.forEach {
            it.setText("")
        }
        pwdEditTextList.first().requestFocus()
    }
}