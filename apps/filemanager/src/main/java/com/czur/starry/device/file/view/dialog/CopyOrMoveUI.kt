package com.czur.starry.device.file.view.dialog

import com.czur.starry.device.baselib.base.v2.fragment.CZBaseFragment
import com.czur.starry.device.baselib.utils.isNetworkConnected
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.toast
import com.czur.starry.device.file.R
import com.czur.starry.device.file.base.CustomDialog
import com.czur.starry.device.file.bean.FileEntity
import com.czur.starry.device.file.exp.NoSpaceException
import com.czur.starry.device.file.manager.LocalInfoManager
import com.czur.starry.device.file.manager.oss.OSSManager
import com.czur.starry.device.file.manager.transfer.FileTransfer
import com.czur.starry.device.file.utils.getStrRes

/**
 * Created by 陈丰尧 on 2022/10/9
 */

/**
 * 显示复制时遇到文件重复的Dialog
 * @param file: 复制重复的文件
 * @param mode: 要处理的方式
 */
fun CZBaseFragment.showCopyFileRepeatDialog(
    file: FileEntity,
    mode: (FileTransfer.HandleMode) -> Unit,
): CustomDialog<*> {
    return InfoDialog.Builder()
        .setInfo(getString(R.string.dialog_copy_info_msg, file.name), true)
        .setCancelText(R.string.dialog_copy_skip.getStrRes())
        .setConfirmText(R.string.dialog_copy_cover.getStrRes())
        .setTitle(R.string.dialog_copy_info_title.getStrRes())
        .setOutDismiss(false)
        .setOnCancelListener {
            // 跳过
            mode(FileTransfer.HandleMode.SKIP)
        }
        .setOnConfirmListener {
            // 覆盖
            mode(FileTransfer.HandleMode.Cover)
        }
        .buildAndShow()
}

/**
 * 显示复制/移动时没有空间的对话框
 */
fun CZBaseFragment.showCopyNoSpaceDialog(exp: NoSpaceException) {
    val info =
            getString(R.string.str_space_not_enough_local)
    LocalInfoManager.refreshUsageOnce()
    InfoDialog.Builder()
        .setInfo(info, false)
        .setTitle(com.czur.starry.device.baselib.R.string.dialog_normal_title_tips)
        .hideCancelBtn(true)
        .setConfirmText(R.string.str_space_dialog_ok)
        .setAutoDismiss(true)
        .buildAndShow()
}


fun CZBaseFragment.showOtherCopyError(isMove: Boolean, onNoNetWork: (() -> Unit)? = null) {
    // 再提示用户
    if (!isNetworkConnected()) {
        // 无网络连接
        toast(R.string.toast_no_network_operation_failure)
        onNoNetWork?.invoke()
    } else {
        if (isMove) {
            toast(R.string.toast_error_move)
        } else {
            toast(R.string.toast_error_copy)
        }
    }
}