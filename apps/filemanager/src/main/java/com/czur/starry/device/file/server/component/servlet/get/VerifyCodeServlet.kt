package com.czur.starry.device.file.server.component.servlet.get

import com.czur.czurutils.log.logTagV
import com.czur.starry.device.file.filelib.FileHandlerLive
import com.czur.starry.device.file.filelib.FileHandlerLive.fileShareCodeEnable
import com.czur.starry.device.file.server.common.FileShareConstant
import com.czur.starry.device.file.server.component.anno.Servlet
import com.czur.starry.device.file.server.component.servlet.HttpServlet
import com.czur.starry.device.file.server.msg.ResultMessage
import com.czur.starry.device.file.server.util.CZHttpRequest
import com.czur.starry.device.file.server.util.CZHttpResponse

/**
 * Created by 陈丰尧 on 2024/12/16
 */
private const val TAG = "VerifyCode"

@Servlet(path = FileShareConstant.GET_VERIFY_VERIFY)
class VerifyCodeServlet : HttpServlet() {
    override suspend fun onConnect(request: CZHttpRequest, response: CZHttpResponse) {
        logTagV(TAG, "VerifyCodeServlet onConnect")
        response.content = ResultMessage(
            FileShareConstant.VERIFY_CODE,
            content = if (fileShareCodeEnable) FileHandlerLive.fileShareCodeStatus else "null"
        )
    }
}