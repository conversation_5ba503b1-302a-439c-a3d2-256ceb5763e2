package com.czur.starry.device.file.db

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import com.czur.starry.device.file.base.FileApp
import com.czur.starry.device.file.db.dao.LocalEncryptionDao
import com.czur.starry.device.file.db.dao.ShareConfigDao
import com.czur.starry.device.file.db.dao.UploadDao
import com.czur.starry.device.file.db.entity.EncryptionPwdErrorEntity
import com.czur.starry.device.file.db.entity.ShareConfig
import com.czur.starry.device.file.db.entity.UploadFile

/**
 *  author : <PERSON><PERSON>ao
 *  time   :2023/10/13
 */

@Database(
    entities = [UploadFile::class, ShareConfig::class, EncryptionPwdErrorEntity::class],
    version = 8
)
abstract class FileDataBase : RoomDatabase() {
    abstract fun uploadDao(): UploadDao
    abstract fun configDao(): ShareConfigDao
    abstract fun localEncryptionDao(): LocalEncryptionDao

    companion object {
        val MIGRATION_6_7: Migration = object : Migration(6, 7) {
            override fun migrate(db: SupportSQLiteDatabase) {
                // 在存在的表中添加新的字段
                db.execSQL(
                    "ALTER TABLE config "
                            + " ADD COLUMN enable_meetingVideo INTEGER NOT NULL DEFAULT 1"
                )
            }
        }

        val MIGRATION_7_8: Migration = object : Migration(7, 8) {
            override fun migrate(db: SupportSQLiteDatabase) {
                // 在存在的表中添加新的字段
                db.execSQL(
                    """
            CREATE TABLE IF NOT EXISTS `tab_encryption_pwd_error` (
                `errorInputTime` INTEGER NOT NULL,
                `id` INTEGER NOT NULL,
                PRIMARY KEY(`id`)
            )
        """
                )
            }
        }

        val instance = Single.sin
    }

    private object Single {
        val sin: FileDataBase = Room.databaseBuilder(
            FileApp.instance,
            FileDataBase::class.java,
            "File.db"
        )
            .addMigrations(MIGRATION_6_7)
            .addMigrations(MIGRATION_7_8)
            .build()
    }

}