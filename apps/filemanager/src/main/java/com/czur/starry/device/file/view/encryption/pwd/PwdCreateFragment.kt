package com.czur.starry.device.file.view.encryption.pwd

import android.os.Bundle
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.base.v2.fragment.CZViewBindingFragment
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.file.R
import com.czur.starry.device.file.bean.MainTabKey
import com.czur.starry.device.file.databinding.FragmentEncryptionPwdCreateBinding
import com.czur.starry.device.file.view.encryption.setting.CreatePwdActivity
import com.czur.starry.device.file.view.vm.MainViewModel
import com.czur.starry.device.file.widget.EncryptionPwdGroupView.EyeStatus

/**
 * Created by 陈丰尧 on 2024/12/10
 */
private const val TAG = "PwdCreateFragment"
class PwdCreateFragment : CZViewBindingFragment<FragmentEncryptionPwdCreateBinding>() {
    private val viewModel: PwdCreateViewModel by viewModels()
    private val mainViewModel:MainViewModel by activityViewModels()

    override fun FragmentEncryptionPwdCreateBinding.initBindingViews() {
        pwdGroupView.onPwdChangeCallback = {
            viewModel.setUserSetPwd(it)
        }

        pwdGroupView.bindWithEyeImageView(
            binding.pwdEyeIv,
            R.drawable.ic_pwd_eye_open,
            R.drawable.ic_pwd_eye_close
        )
        pwdGroupView.eyeStatus = EyeStatus.OPEN // 创建密码页面默认显示密码

        pwdGroupView.setPwdFinishFocusView(binding.confirmFocusBtn)
        confirmFocusBtn.setOnDebounceClickListener {
            confirmBtn.performClick()
        }

        confirmBtn.setOnDebounceClickListener {
            CreatePwdActivity.start(requireContext(), viewModel.userSetPwd)
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        repeatCollectOnResume(viewModel.nextBtnEnableFlow) {
            binding.confirmBtn.isEnabled = it
        }

        repeatCollectOnResume(mainViewModel.currentTabKeyFlow) {
            if (it != MainTabKey.SHOW_TYPE_ENCRYPTION) {
                logTagV(TAG, "当前不在加密页面，清空密码")
                binding.pwdGroupView.clearAllPwd()
            }
        }
    }
}