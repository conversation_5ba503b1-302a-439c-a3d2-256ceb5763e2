package com.czur.starry.device.file.share

import android.content.ContentValues
import android.database.Cursor
import android.graphics.BitmapFactory
import android.net.Uri
import android.util.Size
import androidx.core.content.FileProvider
import com.czur.czurutils.base.CZURContentProvider
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.base.CZURAtyManager
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.StarryDevLocale
import com.czur.starry.device.baselib.common.VersionIndustry
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.PATH_RESULT_TRUE
import com.czur.starry.device.baselib.utils.entityToCursorBlob
import com.czur.starry.device.baselib.utils.stringToCursor
import com.czur.starry.device.file.bean.ATTR_KEY_OSS_FILE_KEY
import com.czur.starry.device.file.bean.FileEntity
import com.czur.starry.device.file.bean.TAG_KEY_LOCAL_TYPE
import com.czur.starry.device.file.bean.presetFileList
import com.czur.starry.device.file.filelib.AccessType
import com.czur.starry.device.file.filelib.EXT_TYPE_LOCAL_ENCRYPTION
import com.czur.starry.device.file.filelib.EXT_TYPE_NORMAL
import com.czur.starry.device.file.filelib.FileHandler
import com.czur.starry.device.file.filelib.FileHandler.KEY_COUNT
import com.czur.starry.device.file.filelib.FileHandler.KEY_FILE_SIZE_TYPE
import com.czur.starry.device.file.filelib.FileHandler.KEY_SHARE_FILE
import com.czur.starry.device.file.filelib.FileHandler.KEY_SORT_TYPE
import com.czur.starry.device.file.filelib.FileHandler.KEY_START
import com.czur.starry.device.file.filelib.FileHandler.PATH_COPY_EXPLAIN_FILE
import com.czur.starry.device.file.filelib.FileHandler.PATH_FILE_SIZE
import com.czur.starry.device.file.filelib.FileHandler.PATH_SHARE_FILE_OPEN
import com.czur.starry.device.file.filelib.FileHandler.PATH_SHARE_FILE_ROOT
import com.czur.starry.device.file.filelib.FileHandler.PATH_SHARE_FILE_TARGET
import com.czur.starry.device.file.filelib.FileHandler.uriMatcher
import com.czur.starry.device.file.filelib.FileSize
import com.czur.starry.device.file.filelib.FileSizeItem
import com.czur.starry.device.file.filelib.FileType
import com.czur.starry.device.file.filelib.ShareFile
import com.czur.starry.device.file.filelib.SizeFileType
import com.czur.starry.device.file.manager.CZURShareFileAccess
import com.czur.starry.device.file.manager.DownloadFileAccess
import com.czur.starry.device.file.manager.FileAccess
import com.czur.starry.device.file.manager.FileManager
import com.czur.starry.device.file.manager.LocalFileAccess
import com.czur.starry.device.file.manager.LocalInfoManager
import com.czur.starry.device.file.manager.LocalMeetingAccess
import com.czur.starry.device.file.manager.ScreenShotFileAccess
import com.czur.starry.device.file.utils.getOffice365URL
import com.czur.starry.device.file.utils.isApk
import com.czur.starry.device.file.utils.isAudio
import com.czur.starry.device.file.utils.isDocument
import com.czur.starry.device.file.utils.isExcel
import com.czur.starry.device.file.utils.isImage
import com.czur.starry.device.file.utils.isPDF
import com.czur.starry.device.file.utils.isPpt
import com.czur.starry.device.file.utils.isVideo
import com.czur.starry.device.file.utils.isWord
import com.czur.starry.device.file.utils.isXApk
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import java.io.File

private const val TAG = "ShareFileProvider"

class ShareFileProvider : CZURContentProvider() {
    private val gson by lazy { Gson() }
    private val shareDir by lazy {
        File(context!!.cacheDir, "shareDir").apply {
            if (!this.exists()) {
                mkdirs()
            }
        }
    }

    private val explainFileNameMap = presetFileList.associateBy { it.fileName }

    override fun delete(uri: Uri, selection: String?, selectionArgs: Array<String>?): Int {
        TODO("Implement this to handle requests to delete one or more rows")
    }

    override fun getType(uri: Uri): String? {
        TODO(
            "Implement this to handle requests for the MIME type of the data" +
                    "at the given URI"
        )
    }

    override fun insert(uri: Uri, values: ContentValues?): Uri? {
        TODO("Implement this to handle requests to insert a new row.")
    }

    override fun onCreate(): Boolean {
        CZURAtyManager.appContext = context!!.applicationContext
        LocalInfoManager.refreshUsageOnce()
        return true
    }

    override fun query(
        uri: Uri, projection: Array<String>?, selection: String?,
        selectionArgs: Array<String>?, sortOrder: String?
    ): Cursor? {
        logTagD(TAG, "query:${uri}")
        return when (uriMatcher.match(uri)) {
            PATH_SHARE_FILE_ROOT.code -> {
                val list = FileManager.roots.filter {
                    when (it.belongTo) {
                        AccessType.RECORD -> false
                        AccessType.LOCAL_MEETING -> false
                        AccessType.LOCAL -> when (it.getTag<Int>(TAG_KEY_LOCAL_TYPE)) {
                            EXT_TYPE_LOCAL_ENCRYPTION -> false  // 不显示加密文件夹
                            else -> true
                        }

                        else -> true
                    }
                }
                val shareList = transToShare(list)
                logTagD(TAG, "root:${shareList}")
                entityToCursorBlob(shareList)
            }

            PATH_SHARE_FILE_TARGET.code -> {
                val shareFileJson = uri.getQueryParameter(KEY_SHARE_FILE)!!
                val start = uri.getQueryParameter(KEY_START)!!.toInt()
                val count = uri.getQueryParameter(KEY_COUNT)!!.toInt()

                // 排序规则
                val sortType =
                    when (FileHandler.SortType.valueOf(
                        uri.getQueryParameter(KEY_SORT_TYPE) ?: FileHandler.SortType.TIME_DESC.name
                    )) {
                        FileHandler.SortType.TIME_ASC -> FileAccess.SortType.TYPE_TIME_ASC
                        FileHandler.SortType.NAME_ASC -> FileAccess.SortType.TYPE_NAME_ASC
                        FileHandler.SortType.TIME_DESC -> FileAccess.SortType.TYPE_TIME_DESC
                        FileHandler.SortType.NAME_DESC -> FileAccess.SortType.TYPE_NAME_DESC
                    }

                val shareFile = gson.fromJson(shareFileJson, ShareFile::class.java)
                val fileEntity = transToFileEntity(shareFile)
                val extType = shareFile.extType
                val fileAccess = FileManager.createFileAccess(fileEntity.belongTo, extType)
                val fileEntities = runBlocking {
                    fileAccess.getItemsByTarget(fileEntity, start, count, sortType = sortType)
                }
                val shareFiles = transToShare(fileEntities)
                entityToCursorBlob(shareFiles)
            }

            PATH_SHARE_FILE_OPEN.code -> {
                val shareFileJson = uri.getQueryParameter(KEY_SHARE_FILE)!!
                logTagV(TAG, shareFileJson)
                val shareFile = gson.fromJson(shareFileJson, ShareFile::class.java)
                val openUrl = when (shareFile.fileType) {
                    FileType.IMAGE -> getImageUri(shareFile)
                    FileType.DOC, FileType.PDF, FileType.PPT, FileType.EXCEL, FileType.DOCUMENT
                        -> getOpenUrl(shareFile)

                    else -> null
                }

                return openUrl?.let {
                    stringToCursor(it)
                }
            }

            PATH_FILE_SIZE.code -> {
                logTagD(TAG, "查询文件大小")
                val shareFileJson = uri.getQueryParameter(KEY_FILE_SIZE_TYPE) ?: return null
                val queryTypes = gson.fromJson<List<SizeFileType>>(
                    shareFileJson,
                    object : TypeToken<List<SizeFileType>>() {}.type
                )
                val sizeMap =
                    queryTypes.associateWith { FileSize(it, 0L, mutableListOf()) }.toMutableMap()
                // 本地文件
                val localFile = File(LocalFileAccess.getRootEntity().absPath)
                getFileSize(sizeMap, localFile)
                // 截图文件
                val screenshotFile = File(ScreenShotFileAccess.getRootEntity().absPath)
                getFileSize(sizeMap, screenshotFile)
                // 下载文件
                val downloadFile = File(DownloadFileAccess.getRootEntity().absPath)
                getFileSize(sizeMap, downloadFile)
                // 成者秒传
                val shareFile = File(CZURShareFileAccess.getRootEntity().absPath)
                getFileSize(sizeMap, shareFile)
                // 本地会议录像
                val localMeetting = File(LocalMeetingAccess.getRootEntity().absPath)
                getFileSize(sizeMap, localMeetting)

                entityToCursorBlob(sizeMap.values.toList())
            }

            else -> return null
        }
    }

    private fun getFileSize(sizeMap: MutableMap<SizeFileType, FileSize>, rootFile: File) {
        rootFile.walkTopDown().forEach { file ->
            if (!file.isDirectory) {
                val sizeType = getFileSizeType(file)
                sizeType?.let { sizeFileType ->
                    sizeMap[sizeFileType]?.let { fileSize ->
                        fileSize.size += file.length()
                        if (sizeFileType == SizeFileType.APK) {
                            // 只有apk需要记录文件信息
                            fileSize.fileItems.add(
                                FileSizeItem(
                                    file.name,
                                    file.absolutePath,
                                    file.length()
                                )
                            )
                        }
                    }
                }
            }
        }
    }

    private fun getFileSizeType(file: File): SizeFileType? {
        return when {
            file.isImage() -> SizeFileType.IMAGE
            file.isAudio() || file.isVideo() -> SizeFileType.MEDIA
            file.isApk() || file.isXApk() -> SizeFileType.APK
            // 文档类型需要参考 FileUtils.kt中filterMap
            file.isDocument() || file.isExcel() || file.isWord() || file.isPpt() || file.isPDF() -> SizeFileType.DOCUMENT
            else -> null
        }
    }

    /**
     * 获取图片的打开地址
     * 网络图片需要先下载到本地, 然后提供Uri
     */
    private fun getImageUri(shareFile: ShareFile): String {
        return runBlocking {
            getLocalImgUri(shareFile)
        }
    }

    /**
     * 获取本地图片的分享Uri
     * 将图片复制到分享文件夹后, 进行共享
     */
    private suspend fun getLocalImgUri(shareFile: ShareFile): String = withContext(Dispatchers.IO) {
        val srcFile = File(shareFile.absPath)
        val destFile = File(shareDir, shareFile.name)
        srcFile.copyTo(destFile, true)
        val fileUri = FileProvider.getUriForFile(
            context!!,
            "com.czur.starry.device.file.fileprovider",
            destFile
        )
        val size = decodeBitmapSize(destFile.absolutePath)
        val buildUpon = fileUri.buildUpon()
        buildUpon.appendQueryParameter("w", size.width.toString())
            .appendQueryParameter("h", size.height.toString()).build().toString()
    }

    private fun decodeBitmapSize(imgFilePath: String): Size {
        val options = BitmapFactory.Options()
        options.inJustDecodeBounds = true
        BitmapFactory.decodeFile(imgFilePath, options)
        return Size(options.outWidth, options.outHeight)
    }

    private fun getOpenUrl(shareFile: ShareFile): String? {
        if (!shareFile.canPreview) {
            logTagD(TAG, "不能生成网址:${shareFile}")
            return null
        }
        return try {
            runBlocking {
                getOffice365URL(shareFile.absPath)
            }
        } catch (exp: Exception) {
            logTagE(TAG, "获取文件地址失败", tr = exp)
            null
        }
    }

    private fun transToFileEntity(shareFile: ShareFile): FileEntity {
        return FileEntity(
            shareFile.absPath, shareFile.fileType, shareFile.name, 0L, "",
            shareFile.belongTo, 0L
        )
    }

    private fun transToShare(list: List<FileEntity>): List<ShareFile> {
        return list.map {
            val ossKey = if (it.containsAttr(ATTR_KEY_OSS_FILE_KEY)) {
                it.getAttribute(ATTR_KEY_OSS_FILE_KEY)
            } else {
                null
            }
            val extType = it.getTagWithDefault(TAG_KEY_LOCAL_TYPE, EXT_TYPE_NORMAL)
            ShareFile(
                it.absPath,
                it.name,
                it.fileType,
                it.belongTo,
                ossKey = ossKey,
                extType = extType
            )
        }
    }


    override fun update(
        uri: Uri, values: ContentValues?, selection: String?,
        selectionArgs: Array<String>?
    ): Int {
        when (uriMatcher.match(uri)) {
            PATH_COPY_EXPLAIN_FILE.code -> {
                runBlocking {
                    if (Constants.versionIndustry != VersionIndustry.DEVICE_INDUSTRY_ARMY_BUILD) {
                        copyPreFile()
                    }
                }
                return PATH_RESULT_TRUE.code
            }
        }

        return 1
    }

    /**
     * 复制预制文件
     */
    private suspend fun copyPreFile() {
        logTagD(TAG, "数据复制预制文件")

        val srcFile = getLocalFileDir().also {
            if (!it.exists()) {
                it.mkdirs()
            }
        }

        logTagV(TAG, "copy开始")
        // 根据版本复制不同的文件
        // TODO 是否逻辑都整合到PresetFile中实现, 目前海外分支也在修改文件, 暂时不修改
        val dir = when (Constants.starryHWInfo.salesLocale) {
            StarryDevLocale.Mainland -> "prefiles"
            StarryDevLocale.Overseas -> "prefiles_os"
        }
        logTagV(TAG, "dir:${dir}")
        context!!.assets.list(dir)?.forEach { fileName ->
            val presetFile = explainFileNameMap[fileName]
            if (presetFile?.doCopy != false) {
                val destFileName = presetFile?.showName ?: fileName
                logTagV(TAG, "copy:${destFileName}")

                //Bug #26387 海外版本需要添加一个文件夹 
                val userGuideDir =
                    if (Constants.starryHWInfo.salesLocale == StarryDevLocale.Overseas) {
                        File(srcFile, "User Guide").apply {
                            if (!exists()) {
                                mkdirs()
                            }
                        }
                    } else {
                        srcFile
                    }

                val newFile = File(userGuideDir, destFileName).also {
                    it.createNewFile()
                }
                newFile.outputStream().use { outStream ->
                    context!!.assets.open("${dir}/${fileName}").use {
                        it.copyTo(outStream)
                    }
                }
            }
        }

        logTagV(TAG, "copy结束")
    }

    private suspend fun getLocalFileDir(): File {
        fun getFilePath(): String? {
            return try {
                LocalFileAccess.getRootEntity().absPath
            } catch (exp: Exception) {
                logTagW(TAG, "获取本地文件失败", tr = exp)
                null
            }
        }
        logTagV(TAG, "getLocalFileDir")
        var filePath = getFilePath()
        if (filePath == null) {
            delay(ONE_SECOND)
            filePath = getFilePath()
        }
        return if (filePath == null) {
            logTagW(TAG, "获取本地文件失败Again")
            File(context!!.filesDir, "LocalFileRoot")
        } else {
            logTagV(TAG, "使用filePath:${filePath}")
            File(filePath)
        }
    }
}