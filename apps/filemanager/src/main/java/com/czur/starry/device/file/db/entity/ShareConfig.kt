package com.czur.starry.device.file.db.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlin.random.Random

/**
 *  author : <PERSON><PERSON><PERSON>
 *  time   :2023/10/19
 */


@Entity(tableName = "config")
data class ShareConfig(
    @PrimaryKey @ColumnInfo(name = "id") val id: Int = 0,
    @ColumnInfo(name = "enable_share") var enableShare: Boolean = false,
    @ColumnInfo(name = "enable_code") var enableCode: Boolean = false,
    @ColumnInfo(name = "device_name") var deviceName: String,
    @ColumnInfo(name = "device_code") var deviceCode: String,
    @ColumnInfo(name = "auto_code") var autoCode: Boolean,
    @ColumnInfo(name = "enable_meetingVideo") var enableMeetingVideo: Boolean = true

) {
    companion object {
        fun createDefault(deviceName: String): ShareConfig {
            return ShareConfig(
                0,
                enableShare = true,
                enableCode = true,
                deviceName = deviceName,
                deviceCode = Random.nextInt(1000, 10000).toString(),
                autoCode = true,
                enableMeetingVideo = true
            )
        }
    }
}