package com.czur.starry.device.file.server.component

import com.czur.czurutils.global.globalAppCtx
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.file.server.component.servlet.HttpServlet
import com.czur.starry.device.file.server.util.ClassScanner

/**
 * Created by 陈丰尧 on 2024/12/16
 */
private const val TAG = "ComponentRepository"

class ComponentRepository {
    private val servletMap = mutableMapOf<String, Class<HttpServlet>>()
    private val classScanner = ClassScanner()

    fun scannerAllComponent() {
        logTagD(TAG, "scannerAllComponent")
        val scanRes = classScanner.scannerAllServlet(getServerAnnoPackage())
        servletMap.clear()
        servletMap.putAll(scanRes)

        logTagD(TAG, "servletMap:${servletMap.keys.joinToString()}")
    }

    fun createHttpServlet(path: String): HttpServlet? {
        val servletClass = servletMap[path] ?: return null
        val constructor = servletClass.getConstructor()
        return constructor.newInstance()
    }

    private fun getServerAnnoPackage(): String {
        return globalAppCtx.packageName + ".server.component"
    }

}