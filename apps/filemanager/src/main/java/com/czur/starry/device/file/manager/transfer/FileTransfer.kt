package com.czur.starry.device.file.manager.transfer

import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.utils.toSizeStr
import com.czur.starry.device.file.bean.FileEntity
import com.czur.starry.device.file.exp.CloudFileInUserExp
import com.czur.starry.device.file.exp.NoSpaceException
import com.czur.starry.device.file.filelib.AccessType
import com.czur.starry.device.file.manager.transfer.inter.CopyTask
import com.czur.starry.device.file.manager.transfer.task.LocalTask
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlin.math.roundToInt

/**
 * Created by 陈丰尧 on 1/4/21
 * 负责复制文件用的
 */
private const val TAG = "FileTransfer"
private const val LOCAL = 0
private const val RECORD = 2

private const val MAX_PROGRESS = 1000

class FileTransfer constructor(
    private val targetDir: FileEntity,
    private val srcFiles: List<FileEntity>,
    private val delOnFinish: Boolean,
) {
    /**
     * 需要用户处理的类型
     */
    enum class NeedUserHandleType {
        FILE_NAME_REPEAT, // 文件名重复
    }

    // 出现错误时的处理方式
    enum class HandleMode {
        // 根据UI, 复制文件时,只有两种选择: 覆盖/跳过
        // 并没有涉及文件夹复制的 "合并"
        // 所以如果是文件夹存在的话, 也是单纯的覆盖即可
        SKIP, // 跳过
        Cover // 覆盖
    }

    private val mainScope by lazy { MainScope() }

    /**
     * 判断文件是本地还是云端
     */
    private fun FileEntity.locate(): Int =
        when (belongTo) {
            AccessType.LOCAL, AccessType.USB -> LOCAL    // USB设备同本地一样
            else -> RECORD
        }


    /**
     * 本地 -> 本地
     * 本地 -> OSS (上传)
     * OSS -> OSS
     * OSS -> 本地 (下载)
     * RECORD -> 本地
     * RECORD -> OSS
     */
    private var copyTask: CopyTask = when (srcFiles[0].locate() to targetDir.locate()) {
        (LOCAL to LOCAL) -> LocalTask() // 本地 -> 本地
        else -> TODO()
    }


    /**
     * 已完成的任务个数
     */
    private var finishedTask = 0
    private val perProgress = MAX_PROGRESS / srcFiles.size.toFloat()

    /**
     * 用户处理错误的方式
     */
    private fun handlingErrors(mode: HandleMode) {
        logTagD(TAG, "用户回调了")
        userMode = mode
        logTagD(TAG, "notify前请求锁")
        synchronized(userLock) {
            logTagD(TAG, "notifyAll")
            userLock.notifyAll()
        }
    }

    // 进度的监听
    var progressListener: ((Int) -> Unit)? = null

    var finishedListener: (suspend (successFiles: List<FileEntity>) -> Unit)? = null

    // 复制发生错误时的回调
    var needUserListener: ((file: FileEntity, error: NeedUserHandleType, mode: (HandleMode) -> Unit) -> Unit)? =
        null

    var onErrorListener: ((exp: Throwable) -> Unit)? = null

    private val userLock = Object()
    private var userMode = HandleMode.SKIP
        get() {
            // 每次使用后 自动还原
            val result = field
            userMode = HandleMode.SKIP
            return result
        }

    /**
     * 开始传输
     */
    suspend fun transfer() = withContext(Dispatchers.IO) {
        try {
            val successFiles = mutableListOf<FileEntity>()
            // 请求文件锁
            val lock = copyTask.applyCloudLock(srcFiles, targetDir, delOnFinish)
            if (!lock) {
                logTagW(TAG, "请求文件锁失败")
                throw CloudFileInUserExp()
            }
            // 1. 检测空间是否够用
            // 1.1 获取总大小 用来计算进度
            val sumSizeMap = copyTask.computeSumSize(srcFiles)
            val sumSize = sumSizeMap.values.sum()
            logTagD(TAG, "共需要:${sumSize.toSizeStr()} 空间")
            // 2. 遍历要复制的文件列表
            for (srcFile in srcFiles) {
                if (!isActive) {
                    // 复制被用户取消 不再继续复制
                    logTagD(TAG, "复制被用户取消")
                    return@withContext
                }
                // 2.1 检查是否有足够的空间
                val hasSpace = copyTask.hasEnoughSpace(
                    srcFile,
                    sumSizeMap[srcFile.absPath] ?: 1L,
                    targetDir,
                    delOnFinish
                )
                if (!hasSpace) {
                    logTagD(TAG, "剩余空间不足, 提示用户")
                    // 抛出异常
                    throw NoSpaceException()
                }

                // 2.1 检测文件是否存在
                val exist = copyTask.checkExist(srcFile, targetDir)
                if (exist) {
                    // 2.1.1 文件存在, 等待用户确认
                    logTagD(TAG, "文件已存在,等待用户点击")
                    // 文件已存在
                    val userMode = notifyUserGetResult(srcFile, NeedUserHandleType.FILE_NAME_REPEAT)
                    if (userMode == HandleMode.SKIP) {
                        logTagD(TAG, "用户点击了跳过")
                        onOneFileFinish()
                        continue
                    }
                }

                // 2.2 文件不冲突,或者用户点击了覆盖
                logTagD(TAG, "开始复制>>:${srcFile.name}")
                if (!isActive) {
                    // 复制被用户取消 不再继续复制
                    logTagD(TAG, "复制被用户取消")
                    return@withContext
                }
                copyTask.doCopy(
                    srcFile,
                    targetDir,
                    sumSizeMap[srcFile.absPath] ?: 0L,
                    delOnFinish
                ) {
                    // 更新进度信息
                    updateProgress(it)
                }
                logTagD(TAG, "<<复制完成:${srcFile.name}")
                successFiles.add(srcFile)
                onOneFileFinish()
            }

            //完成
            logTagD(TAG, "全部复制完成")
            withContext(Dispatchers.Main) {
                finishedListener?.invoke(successFiles)
            }
        } catch (exp: Throwable) {
            logTagE(TAG, "复制文件出错!", tr = exp)
            withContext(Dispatchers.Main) {
                onErrorListener?.invoke(exp)
            }

        } finally {
            // 释放文件锁
            copyTask.releaseCloudLock()
        }

    }

    private var lastProgress = -1

    /**
     * 更新进度条
     * 百分比逻辑: 首先根据要复制的文件,将任务进行拆分,
     *  比如要复制2个文件,那么每个文件就占50%
     *
     * @param taskProgress : 当前任务的进度 范围是[0,1]
     */
    private fun updateProgress(taskProgress: Float) {
        // 计算已完成的百分比
        val progress = if (finishedTask < srcFiles.size) {
            val finishProgress = finishedTask * perProgress
            // 当前任务进度
            val current = taskProgress * perProgress
            // 结果四舍五入 取整
            (finishProgress + current).roundToInt()
        } else {
            MAX_PROGRESS
        }

        if (lastProgress == progress) {
            return
        }
        lastProgress = progress
        logTagD(TAG, "进度:${progress}")


        progressListener?.let {
            // 使用MainScope将任务发送到主线程中
            mainScope.launch {
                it(progress)
            }
        }
    }

    /**
     * 复制完成一个文件, 更新进度
     */
    private fun onOneFileFinish() {
        finishedTask++
        updateProgress(0f)
    }


    // 通知主线程
    private suspend fun notifyUserGetResult(
        current: FileEntity,
        error: NeedUserHandleType
    ): HandleMode {
        needUserListener?.let {
            withContext(Dispatchers.Main) {
                logTagD(TAG, "通知用户")
                it(current, error, ::handlingErrors)
            }
            // 暂停
            logTagD(TAG, "暂停前请求锁")
            synchronized(userLock) {
                logTagD(TAG, "暂停")
                userLock.wait() // 线程暂停,等待用户输入
            }
        }

        return userMode //如果没指定回调, 默认是跳过
    }


}