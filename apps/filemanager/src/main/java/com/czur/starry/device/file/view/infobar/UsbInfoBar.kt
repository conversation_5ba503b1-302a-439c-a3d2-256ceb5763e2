package com.czur.starry.device.file.view.infobar

import android.os.Bundle
import com.czur.starry.device.file.R
import com.czur.starry.device.file.databinding.FragmentUsbInfoBarBinding
import com.czur.starry.device.file.manager.usb.UsbHelper

/**
 * Created by 陈丰尧 on 2022/1/17
 */
class UsbInfoBar : BaseInfoBar<FragmentUsbInfoBarBinding>() {

    companion object {
        private const val KEY_PATH = "path"

        fun getInstance(usbPath: String): UsbInfoBar {
            return UsbInfoBar().apply {
                arguments = Bundle().apply {
                    putString(KEY_PATH, usbPath)
                }
            }
        }
    }

    private val usbPath: String by lazy {
        arguments?.getString(KEY_PATH) ?: ""
    }


    override fun updateInfo(info: String) {
        binding.usbInfoTv.text = info
    }


    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        UsbHelper.getUsbSpaceLive(usbPath).observe(this) {
            val infoStr = it.toString(R.string.str_local_space_info)
            updateInfo(infoStr)
        }
    }
}