package com.czur.starry.device.file.view.encryption.pwd

import androidx.lifecycle.ViewModel
import com.czur.starry.device.file.manager.LocalEncryptionManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.map

/**
 * Created by 陈丰尧 on 2024/12/10
 */
class PwdCreateViewModel : ViewModel() {
    private val userSetPwdFlow = MutableStateFlow("")
    val userSetPwd
        get() = userSetPwdFlow.value
    val nextBtnEnableFlow = userSetPwdFlow.map { it.length >= 6 }

    fun setUserSetPwd(pwd: String) {
        userSetPwdFlow.value = pwd
    }
}