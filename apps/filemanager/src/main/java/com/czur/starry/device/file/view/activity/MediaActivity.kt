package com.czur.starry.device.file.view.activity

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.util.Size
import android.view.ContextMenu
import android.view.InputDevice
import android.view.MenuItem
import android.view.View
import android.widget.SeekBar
import androidx.activity.viewModels
import androidx.core.view.MenuCompat
import androidx.media3.common.Player
import com.czur.czurutils.extension.platform.startActivity
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.base.CZURAtyManager
import com.czur.starry.device.baselib.base.v2.aty.CZViewBindingAty
import com.czur.starry.device.baselib.tips.TipsPool
import com.czur.starry.device.baselib.tips.setCustomFloatTip
import com.czur.starry.device.baselib.utils.doWithoutCatch
import com.czur.starry.device.baselib.utils.fw.proxy.SystemManagerProxy
import com.czur.starry.device.baselib.utils.getScreenHeight
import com.czur.starry.device.baselib.utils.getScreenWidth
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.baselib.utils.setDebounceTouchClickListener
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.baselib.view.dialog.LoadingDialog
import com.czur.starry.device.file.R
import com.czur.starry.device.file.databinding.ActivityMediaBinding
import com.czur.starry.device.file.filelib.FileHandlerLive.fileMediaStateLive
import com.czur.starry.device.file.filelib.FileType
import com.czur.starry.device.file.manager.FileAccess
import com.czur.starry.device.file.utils.RkUtil
import com.czur.starry.device.file.utils.toDuration
import com.czur.starry.device.file.view.vm.MediaViewModel
import java.io.File
import kotlin.system.exitProcess

/**
 * Created by 陈丰尧 on 2021/9/18
 *
 */

class MediaActivity : CZViewBindingAty<ActivityMediaBinding>() {

    private val systemManager: SystemManagerProxy by lazy {
        SystemManagerProxy()
    }

    companion object {
        private const val TAG = "MediaActivity"

        private const val KEY_TARGET_NAME = "targetName"
        private const val KEY_SRC_DATA_TYPE = "srcDataType"     // 数据类型
        private const val VALUE_SRC_DATA_TYPE_FOLDER = "folder" // 文件夹内的所有文件
        private const val VALUE_SRC_DATA_TYPE_SINGLE = "single" // 单个文件
        private const val KEY_SRC_DATA = "srcData"              // 数据文件
        private const val KEY_EXTERNAL_SORT_ORDER = "sortType" // 扩展数据: 排序方式
        private const val KEY_BOOT_FROM = "bootFrom"           // 启动来源
        private const val VALUE_BOOT_FROM_INTERNAL = "internal" // 内部启动
        private const val VALUE_BOOT_FROM_EXTERNAL = "external" // 外部启动
        private const val KEY_EXTERNAL_AUTO_FINISH = "autoFinish"               // 播放完成后自动结束

        /**
         * 从内部启动
         */
        fun bootFromInternal(
            fromContext: Context,
            targetName: String,
            folderPath: String,
            sortType: FileAccess.SortType,
            playSingle: Boolean
        ) {
            val intent = Intent(fromContext, MediaActivity::class.java).apply {
                putExtra(KEY_TARGET_NAME, targetName)     // 当前索引
                putExtra(
                    KEY_SRC_DATA_TYPE,
                    if (playSingle) VALUE_SRC_DATA_TYPE_SINGLE else VALUE_SRC_DATA_TYPE_FOLDER
                ) // 文件夹内的所有 或 单独的
                putExtra(KEY_SRC_DATA, folderPath)  // 文件夹路径
                putExtra(KEY_EXTERNAL_SORT_ORDER, sortType) // 排序方式
                putExtra(KEY_BOOT_FROM, VALUE_BOOT_FROM_INTERNAL) // 内部启动
            }
            logTagD(TAG, "内部启动播放页面")
            fromContext.startActivity(intent)
        }
    }

    enum class FileMediaState {
        PAUSE,
        PLAY,
        FORWARD,
        BACKWARD,
    }

    private val loadingDialog by lazy { LoadingDialog() }
    val pavViewModel: MediaViewModel by viewModels()
    private var bootFrom = VALUE_BOOT_FROM_INTERNAL


    private var seekBarInOperation = false
    private var seekBarOperationTime = -1L

    private val videoViewSize: Size by lazy { Size(getScreenWidth(), getScreenHeight()) }
    private val videoSizeVerticalScreen: Size by lazy {
        Size(
            videoViewSize.height,
            videoViewSize.height * videoViewSize.height / videoViewSize.width
        )
    }


    override fun AtyParams.initAtyParams() {
        keepScreenOn = true                 // 保持屏幕常亮
        onScreenOffListener = ::onScreenOFF // 屏幕关闭时的回调
    }

    override fun handlePreIntent(preIntent: Intent) {
        super.handlePreIntent(preIntent)
        pavViewModel.autoFinish = preIntent.getBooleanExtra(KEY_EXTERNAL_AUTO_FINISH, false)
        bootFrom = preIntent.getStringExtra(KEY_BOOT_FROM) ?: VALUE_BOOT_FROM_EXTERNAL

        preIntent.getStringExtra(KEY_SRC_DATA_TYPE)?.let {
            if (bootFrom == VALUE_BOOT_FROM_EXTERNAL) {
                pavViewModel.setExternalRepeatMode()
            }
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun ActivityMediaBinding.initBindingViews() {
        // 每一次SurfaceView从Gone状态转变为可见状态时
        // 都会执行该回调
        pavViewModel.setSurface(videoSurface)

        registerForContextMenu(mediaGroupView)
        mediaGroupView.setOnTouchListener { _, event ->
            // 吸收触屏事件, 这样只有鼠标能触发菜单
            event.source == InputDevice.SOURCE_TOUCHSCREEN
        }

        playOrPauseIv.onPlayListener = {
            pavViewModel.play()
        }
        playOrPauseIv.onPauseListener = {
            pavViewModel.pause()
        }

        mediaPreIv.setOnClickListener {
            pavViewModel.switchPre()
        }
        mediaNextIv.setOnClickListener {
            pavViewModel.switchNext()
        }

        rotationModeIv.setDebounceTouchClickListener {
            pavViewModel.changeRotationMode()
        }
        repeatModeIv.setDebounceTouchClickListener {
            pavViewModel.changeRepeatMode()
        }

        mediaSeekBar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser && !seekBarInOperation) {
                    // 响应了键盘上的方向键
                    pavViewModel.seekTo(progress)
                    seekBarOperationTime = System.currentTimeMillis()
                }
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {
                seekBarInOperation = true
            }

            override fun onStopTrackingTouch(seekBar: SeekBar) {
                val progress = seekBar.progress
                pavViewModel.seekTo(progress)
                seekBarOperationTime = System.currentTimeMillis()
                seekBarInOperation = false
            }

        })
    }

    override fun onCreateContextMenu(
        menu: ContextMenu,
        v: View?,
        menuInfo: ContextMenu.ContextMenuInfo?
    ) {
        menuInflater.inflate(R.menu.menu_media, menu)

        // 检查上一曲和下一曲 是否可用
        menu.getItem(0).apply {
            isEnabled = binding.mediaPreIv.isEnabled
        }
        menu.getItem(1).apply {
            isEnabled = binding.mediaNextIv.isEnabled
        }


        MenuCompat.setGroupDividerEnabled(menu, true)
        super.onCreateContextMenu(menu, v, menuInfo)
    }

    override fun onContextItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            R.id.menuMediaPre -> binding.mediaPreIv.takeIf {
                it.isEnabled
            }?.performClick()

            R.id.menuMediaNext -> binding.mediaNextIv.takeIf {
                it.isEnabled
            }?.performClick()

            R.id.menuExit -> finish()

        }
        return super.onContextItemSelected(item)
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        launch {
            setDataSource()
            pavViewModel.prepareAndPlayCurrent()
        }

        repeatCollectOnResume(pavViewModel.fileTypeFlow) { fileType ->
            logTagV(TAG, "当前文件类型:${fileType}")
            when (fileType) {
                FileType.VIDEO -> {
                    binding.videoGroup.show()
                    binding.musicInfoGroup.gone()
                    binding.videoActionBg.show()
                    binding.rotationModeIv.show()
                    binding.mediaVideoNameTv.show()
                }

                FileType.AUDIO -> {
                    window.setBackgroundDrawable(ColorDrawable(getColor(R.color.base_bg_color)))
                    binding.musicInfoGroup.show()
                    binding.videoGroup.gone()
                    binding.videoActionBg.gone()
                    binding.rotationModeIv.gone()
                    binding.mediaVideoNameTv.gone()
                }

                else -> {
                    logTagW(TAG, "错误的文件类型:${fileType}")
                    // 默认显示音乐信息
                    binding.musicInfoGroup.show()
                    binding.videoGroup.gone()
                    binding.videoActionBg.gone()
                    binding.rotationModeIv.gone()
                    binding.mediaVideoNameTv.gone()
                }
            }
        }

        // 是否显示控制条UI
        repeatCollectOnResume(pavViewModel.showUIFlow) {
            if (it) {
                binding.videoUIGroup.show()
                if (pavViewModel.fileType == FileType.VIDEO) {
                    binding.videoActionBg.show()
                } else {
                    binding.videoActionBg.gone()
                }
            } else {
                binding.videoUIGroup.gone()
                binding.videoActionBg.gone()
                TipsPool.clearPop() // 隐藏所有的tips
            }
        }


        // 上一曲/下一曲是否可用
        repeatCollectOnResume(pavViewModel.preEnableFlow) {
            binding.mediaPreIv.isEnabled = it
        }
        repeatCollectOnResume(pavViewModel.nextEnableFlow) {
            binding.mediaNextIv.isEnabled = it
        }

        pavViewModel.forceCloseLive.observe(this) {
            if (it) {
                logTagV(TAG, "页面停止")
                finish()
            }
        }

        // 各种状态的监听
        pavViewModel.mediaStatusLive.observe(this) { mediaStatus ->
            logTagD(TAG, "mediaStatus:${mediaStatus}")
            when (mediaStatus!!) {
                MediaViewModel.MediaStatus.PLAY -> {
                    binding.playOrPauseIv.change2Pause()
                }

                MediaViewModel.MediaStatus.PAUSE -> {
                    binding.playOrPauseIv.change2Play()
                }
            }
        }

        // 音乐标题
        repeatCollectOnResume(pavViewModel.mediaTitleFlow) {
            binding.musicFileNameTv.text = it
            binding.mediaVideoNameTv.text = it
        }
        // 艺术家
        repeatCollectOnResume(pavViewModel.artistFlow) {
            binding.singerNameTv.text = it
        }

        pavViewModel.durationLive.observe(this) {
            binding.mediaDurationTv.text = it.toDuration()
            binding.mediaSeekBar.max = it.toInt()
        }

        repeatCollectOnResume(pavViewModel.mediaPosStrFlow) {
            binding.mediaPosTv.text = it
        }

        repeatCollectOnResume(pavViewModel.mediaPosFlow) {
            if (!seekBarInOperation) {
                if (System.currentTimeMillis() - seekBarOperationTime > 200) {
                    binding.mediaSeekBar.progress = it.toInt()
                }
            }
        }

        // 加载中监听
        pavViewModel.isLoadingLive.observe(this) {
            if (it) {
                loadingDialog.showDelay(500L)
            } else {
                loadingDialog.dismissImmediate()
            }
        }

        pavViewModel.rotationModeLive.observe(this) { rotationMode ->
            changeVideoRotation(rotationMode)
            when (rotationMode) {
                MediaViewModel.MediaRotationMode.ROTATION_0 -> binding.rotationModeIv.setImageResource(
                    R.drawable.ic_rotation_mode_0
                )

                MediaViewModel.MediaRotationMode.ROTATION_90 -> binding.rotationModeIv.setImageResource(
                    R.drawable.ic_rotation_mode_90
                )

                MediaViewModel.MediaRotationMode.ROTATION_180 -> binding.rotationModeIv.setImageResource(
                    R.drawable.ic_rotation_mode_180
                )

                MediaViewModel.MediaRotationMode.ROTATION_270 -> binding.rotationModeIv.setImageResource(
                    R.drawable.ic_rotation_mode_270
                )

                else -> {}
            }
        }

        repeatCollectOnResume(pavViewModel.repeatModeFlow) { repeatMode ->
            when (repeatMode) {
                Player.REPEAT_MODE_ALL -> binding.repeatModeIv.apply {
                    setImageResource(R.drawable.ic_repeat_mode_list)
                    setCustomFloatTip(getString(R.string.float_tip_list_cycle))
                }

                Player.REPEAT_MODE_ONE -> binding.repeatModeIv.apply {
                    setImageResource(R.drawable.ic_repeat_mode_single)
                    setCustomFloatTip(getString(R.string.float_tip_single_cycle))
                }

                Player.REPEAT_MODE_OFF -> binding.repeatModeIv.apply {
                    logTagD(TAG, "不循环")
                    gone()
                }
                else -> {
                    logTagW(TAG, "错误的循环模式:${repeatMode}")
                    pavViewModel.changeRepeatMode()
                }
            }
        }

        if (pavViewModel.autoFinish) {
            logTagD(TAG, "播放完成后自动结束, 不显示播放模式按钮")
            binding.repeatModeIv.gone()

            pavViewModel.noNextLive.observe(this) {
                finish()
            }
        }

        fileMediaStateLive.observe(this) {
            logTagD(TAG, "======== fileMediaState:${it}")
            when (it) {
                FileMediaState.PAUSE.name -> {
                    pavViewModel.pause()
                }

                FileMediaState.PLAY.name -> {
                    pavViewModel.play()
                }

                FileMediaState.FORWARD.name -> {
                    seekForward()
                }

                FileMediaState.BACKWARD.name -> {
                    seekBackward()
                }
            }
        }
    }

    private fun seekForward() {
        val durationMs = pavViewModel.duration
        val currentPos = binding.mediaSeekBar.progress
        val delta = 10000 // 10秒
        val newProgress = (currentPos + delta).coerceAtMost(durationMs.toInt())
        binding.mediaSeekBar.progress = newProgress
        pavViewModel.seekTo(newProgress)
        seekBarOperationTime = System.currentTimeMillis()
    }

    private fun seekBackward() {
        val currentPos = binding.mediaSeekBar.progress
        val delta = 10000
        val newProgress = (currentPos - delta).coerceAtLeast(0)
        binding.mediaSeekBar.progress = newProgress
        pavViewModel.seekTo(newProgress)
        seekBarOperationTime = System.currentTimeMillis()
    }


    private suspend fun setDataSource() {
        val srcDataType = intent.getStringExtra(KEY_SRC_DATA_TYPE)
        when (srcDataType) {
            VALUE_SRC_DATA_TYPE_FOLDER -> {
                val folderPath = intent.getStringExtra(KEY_SRC_DATA) ?: ""
                val sortOrder =
                    intent.getSerializableExtra(KEY_EXTERNAL_SORT_ORDER) as FileAccess.SortType
                val targetName = intent.getStringExtra(KEY_TARGET_NAME)
                pavViewModel.setDataSource(File(folderPath), targetName, sortOrder)
            }

            VALUE_SRC_DATA_TYPE_SINGLE -> {
                if (bootFrom == VALUE_BOOT_FROM_INTERNAL) {
                    logTagV(TAG, "内部启动, 从内部获取数据源")
                    val folderPath = intent.getStringExtra(KEY_SRC_DATA) ?: ""
                    val targetName = intent.getStringExtra(KEY_TARGET_NAME) ?: ""
                    val targetFile = File(folderPath, targetName)
                    logTagI(TAG, "targetFile:${targetFile}")
                    pavViewModel.setDataSource(targetFile)
                } else {
                    logTagV(TAG, "外部启动, 从外部获取数据源")
                    val fileUri = intent.getStringExtra(KEY_SRC_DATA) ?: return
                    pavViewModel.setDataSource(fileUri)
                }
            }

            else -> {
                logTagW(TAG, "没有找到数据源")
                finish()
            }
        }
    }

    private fun changeVideoRotation(rotationMode: MediaViewModel.MediaRotationMode) {
        val currentWidth = binding.videoSurface.width
        val currentHeight = binding.videoSurface.height

        val (targetWidth, targetHeight) = when (rotationMode) {
            MediaViewModel.MediaRotationMode.ROTATION_0,
            MediaViewModel.MediaRotationMode.ROTATION_180
                -> videoViewSize.width to videoViewSize.height

            MediaViewModel.MediaRotationMode.ROTATION_90,
            MediaViewModel.MediaRotationMode.ROTATION_270 ->
                videoSizeVerticalScreen.width to videoSizeVerticalScreen.height
        }

        if (currentWidth != targetWidth || currentHeight != targetHeight) {
            val layoutParams = binding.videoSurface.layoutParams
            layoutParams.width = targetWidth
            layoutParams.height = targetHeight
            binding.videoSurface.layoutParams = layoutParams
        }

        if (rotationMode.value == 0F && binding.videoSurface.rotation == 270F) {
            binding.videoSurface.animate().rotation(360F).apply {
                setListener(object : AnimatorListenerAdapter() {
                    override fun onAnimationEnd(animation: Animator) {
                        super.onAnimationEnd(animation)
                        binding.videoSurface.rotation = 0F
                        setListener(null)
                    }
                })
            }
        } else {
            binding.videoSurface.animate().rotation(rotationMode.value)
        }
    }

    override fun onResume() {
        super.onResume()
        if (RkUtil.setFixFrequency) {
            doWithoutCatch {
                RkUtil.setGovernorMode(RkUtil.USERSPACE_MODE)
            }
            doWithoutCatch {
                RkUtil.setSpeedFreq(RkUtil.FIX_FREQUENCY_VALUE)
            }
        }

        doWithoutCatch {
            logTagV(TAG, "提升CPU频率")
            systemManager.perfCpuGov()
        }
    }

    private fun onScreenOFF() {
        logTagD(TAG, "屏幕关闭")
        finish()
    }

    override fun onUserInteraction() {
        super.onUserInteraction()
        pavViewModel.onUserOperation()
    }

    override fun finish() {
        logTagV(TAG, "finish页面")
        pavViewModel.releaseResource()

        if (bootFrom == VALUE_SOURCE_TYPE_INTERNAL
            && !CZURAtyManager.hasActivity(FileMainPageActivity::class.java.simpleName)
        ) {
            logTagW(TAG, "没有主页面, 启动主页面")
            startActivity<FileMainPageActivity>()
        }

        super.finish()

        overridePendingTransition(
            R.anim.slide_in_bottom,
            R.anim.slide_out_top
        )
    }

    override fun onPause() {
        super.onPause()
        if (RkUtil.setFixFrequency) {
            doWithoutCatch {
                RkUtil.setGovernorMode(RkUtil.INTERACTIVE_MODE)
            }
        }

        doWithoutCatch {
            logTagV(TAG, "恢复CPU频率")
            systemManager.restoreCpuGov()
        }

        // 页面即将不见时自动暂停播放
        doWithoutCatch {
            if (!isFinishing) {
                logTagD(TAG, "暂停播放")
                pavViewModel.pause()
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        logTagI(TAG, "页面销毁(MediaActivity)")
        exitProcess(0)
    }

}


