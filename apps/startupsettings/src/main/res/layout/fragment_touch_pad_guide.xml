<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/guideTitleTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="98px"
        android:text="@string/title_touch_pad_guide"
        android:textColor="@color/white"
        android:textSize="48px"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/touchGuidIv"
        android:layout_width="1000px"
        android:layout_height="560px"
        android:layout_marginTop="10px"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/guideTitleTv"
        tools:src="@drawable/img_touch_guid_anim" />

    <TextView
        android:id="@+id/guideHintTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="43px"
        android:text="@string/str_touch_pad_guide_hint"
        android:textColor="@color/white"
        android:textSize="30px"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/touchGuidIv" />

    <com.czur.starry.device.startupsettings.widget.TouchSliderView
        android:id="@+id/touchSliderView"
        android:layout_width="500px"
        android:layout_height="100px"
        android:layout_marginBottom="92px"
        android:paddingHorizontal="10px"
        app:bl_corners_radius="50px"
        app:bl_solid_color="#33000000"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>