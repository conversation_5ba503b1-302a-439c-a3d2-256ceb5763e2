<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/dialog_bg_color"
    tools:ignore="PxUsage">

    <ProgressBar
        android:id="@+id/progressView"
        android:layout_width="100px"
        android:layout_height="100px"
        android:layout_marginBottom="50px"
        android:indeterminateTint="@color/white"
        app:layout_constraintBottom_toTopOf="@id/progressHintTv"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/progressHintTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_startup_finish_hint"
        android:textColor="@color/white"
        android:textSize="48px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/progressView" />

</androidx.constraintlayout.widget.ConstraintLayout>