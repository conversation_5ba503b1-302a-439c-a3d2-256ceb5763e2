<resources xmlns:tools="http://schemas.android.com/tools">
    <string name="app_name">Start up settings</string>
    <string name="welcome">Welcome</string>
    <string name="str_startup_finish_hint">System configuration in progress, please do not turn off.</string>
    <string name="title_touch_pad_guide">Drag to complete the setting</string>
    <string name="str_touch_pad_guide_hint">Drag target: Double-click then slide finger to drag the target. (Do not release finger after double click.)</string>
    <string name="str_touch_pad_guide_hint_v2">Drag target: Tab the target then slide to drag/move it. (Do not release after tabbing the target.)</string>
    <string name="str_touch_pad_guide_slide_hint">Drag to right side.</string>
    <string name="title_connect_touch_pad">Please Connect the TouchBoard.</string>
    <string name="connect_touch_pad_hint">1. Switch the power button on the back of TouchBoard to ON status. \n2. Long press the [TouchControl] on the upper left corner for 3 seconds, put the TouchBoard back to charging dock when the blue indicator flahses. \n3. Wait until there is a prompt on StarryHub screen showing \"Successfully Paired!\".</string>
    <string name="startup_next_step">Next</string>
    <string name="connect_touch_pad_hint_mainland">1. Turn the TouchBoard power button (on the back side) to ON status. \n2. Long press the [TouchControl] button on upper left corner for 5 seconds, when blue light blinks, put the TouchBoard back to charging position. \n3. Wait untill there\'s a pop-up window on the StarryHub projection screen stating \'Successfully Paired!\'.</string>
</resources>
