package com.czur.starry.device.starrypad.hardware.trans.netty

import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.utils.logEnableAll
import com.czur.starry.device.baselib.utils.logEnableD
import com.czur.starry.device.baselib.utils.logEnableE
import com.czur.starry.device.baselib.utils.logEnableI
import com.czur.starry.device.baselib.utils.logEnableV
import com.czur.starry.device.baselib.utils.logEnableW
import io.netty.util.internal.logging.AbstractInternalLogger
import io.netty.util.internal.logging.InternalLogger
import io.netty.util.internal.logging.InternalLoggerFactory

/**
 * Created by 陈丰尧 on 2023/12/26
 */
object LogcatLoggerFactory : InternalLoggerFactory() {
    /**
     * Creates a new logger instance with the specified name.
     */
    override fun newInstance(name: String): InternalLogger {
        return LogcatNettyLogger(name)
    }
}

class LogcatNettyLogger(name: String?) : AbstractInternalLogger(name) {
    companion object {
        private const val TAG = "LogcatNettyLogger"
    }

    /**
     * Is the logger instance enabled for the TRACE level?
     *
     * @return True if this Logger is enabled for the TRACE level,
     * false otherwise.
     */
    override fun isTraceEnabled(): Boolean {
        return logEnableAll ?: logEnableV
    }

    /**
     * Log a message at the TRACE level.
     *
     * @param msg the message string to be logged
     */
    override fun trace(msg: String?) {
        logTagV(TAG, "$msg")
    }

    /**
     * Log a message at the TRACE level according to the specified format
     * and argument.
     *
     *
     *
     * This form avoids superfluous object creation when the logger
     * is disabled for the TRACE level.
     *
     * @param format the format string
     * @param arg    the argument
     */
    override fun trace(format: String?, arg: Any?) {
        val ft = MsgFormatter.format(format, arg)
        logTagV(TAG, "${ft.message}", tr = ft.throwable)
    }

    /**
     * Log a message at the TRACE level according to the specified format
     * and arguments.
     *
     *
     *
     * This form avoids superfluous object creation when the logger
     * is disabled for the TRACE level.
     *
     * @param format the format string
     * @param argA   the first argument
     * @param argB   the second argument
     */
    override fun trace(format: String?, argA: Any?, argB: Any?) {
        val ft = MsgFormatter.format(format, argA, argB)
        logTagV(TAG, "${ft.message}", tr = ft.throwable)
    }

    /**
     * Log a message at the TRACE level according to the specified format
     * and arguments.
     *
     *
     *
     * This form avoids superfluous string concatenation when the logger
     * is disabled for the TRACE level. However, this variant incurs the hidden
     * (and relatively small) cost of creating an `Object[]` before invoking the method,
     * even if this logger is disabled for TRACE. The variants taking [one][.trace] and
     * [two][.trace] arguments exist solely in order to avoid this hidden cost.
     *
     * @param format    the format string
     * @param arguments a list of 3 or more arguments
     */
    override fun trace(format: String?, vararg arguments: Any?) {
        val ft = MsgFormatter.arrayFormat(format, arguments)
        logTagV(TAG, "${ft.message}", tr = ft.throwable)
    }

    /**
     * Log an exception (throwable) at the TRACE level with an
     * accompanying message.
     *
     * @param msg the message accompanying the exception
     * @param t   the exception (throwable) to log
     */
    override fun trace(msg: String?, t: Throwable?) {
        logTagV(TAG, "$msg", tr = t)
    }

    /**
     * Is the logger instance enabled for the DEBUG level?
     *
     * @return True if this Logger is enabled for the DEBUG level,
     * false otherwise.
     */
    override fun isDebugEnabled(): Boolean {
        return logEnableAll ?: logEnableD
    }

    /**
     * Log a message at the DEBUG level.
     *
     * @param msg the message string to be logged
     */
    override fun debug(msg: String?) {
        logTagD(TAG, "$msg")
    }

    /**
     * Log a message at the DEBUG level according to the specified format
     * and argument.
     *
     *
     *
     * This form avoids superfluous object creation when the logger
     * is disabled for the DEBUG level.
     *
     * @param format the format string
     * @param arg    the argument
     */
    override fun debug(format: String?, arg: Any?) {
        val ft = MsgFormatter.format(format, arg)
        logTagD(TAG, "format:${ft.message}", tr = ft.throwable)
    }

    /**
     * Log a message at the DEBUG level according to the specified format
     * and arguments.
     *
     *
     *
     * This form avoids superfluous object creation when the logger
     * is disabled for the DEBUG level.
     *
     * @param format the format string
     * @param argA   the first argument
     * @param argB   the second argument
     */
    override fun debug(format: String?, argA: Any?, argB: Any?) {
        val ft = MsgFormatter.format(format, argA, argB)
        logTagD(TAG, "format:${ft.message}", tr = ft.throwable)
    }

    /**
     * Log a message at the DEBUG level according to the specified format
     * and arguments.
     *
     *
     *
     * This form avoids superfluous string concatenation when the logger
     * is disabled for the DEBUG level. However, this variant incurs the hidden
     * (and relatively small) cost of creating an `Object[]` before invoking the method,
     * even if this logger is disabled for DEBUG. The variants taking
     * [one][.debug] and [two][.debug]
     * arguments exist solely in order to avoid this hidden cost.
     *
     * @param format    the format string
     * @param arguments a list of 3 or more arguments
     */
    override fun debug(format: String?, vararg arguments: Any?) {
        val ft = MsgFormatter.arrayFormat(format, arguments)
        logTagD(TAG, "format:${ft.message}", tr = ft.throwable)
    }

    /**
     * Log an exception (throwable) at the DEBUG level with an
     * accompanying message.
     *
     * @param msg the message accompanying the exception
     * @param t   the exception (throwable) to log
     */
    override fun debug(msg: String?, t: Throwable?) {
        logTagD(TAG, "$msg", tr = t)
    }

    /**
     * Is the logger instance enabled for the INFO level?
     *
     * @return True if this Logger is enabled for the INFO level,
     * false otherwise.
     */
    override fun isInfoEnabled(): Boolean {
        return logEnableAll ?: logEnableI
    }

    /**
     * Log a message at the INFO level.
     *
     * @param msg the message string to be logged
     */
    override fun info(msg: String?) {
        logTagI(TAG, "$msg")
    }

    /**
     * Log a message at the INFO level according to the specified format
     * and argument.
     *
     *
     *
     * This form avoids superfluous object creation when the logger
     * is disabled for the INFO level.
     *
     * @param format the format string
     * @param arg    the argument
     */
    override fun info(format: String?, arg: Any?) {
        val ft = MsgFormatter.format(format, arg)
        logTagI(TAG, "format:${ft.message}", tr = ft.throwable)
    }

    /**
     * Log a message at the INFO level according to the specified format
     * and arguments.
     *
     *
     *
     * This form avoids superfluous object creation when the logger
     * is disabled for the INFO level.
     *
     * @param format the format string
     * @param argA   the first argument
     * @param argB   the second argument
     */
    override fun info(format: String?, argA: Any?, argB: Any?) {
        val ft = MsgFormatter.format(format, argA, argB)
        logTagI(TAG, "format:${ft.message}", tr = ft.throwable)
    }

    /**
     * Log a message at the INFO level according to the specified format
     * and arguments.
     *
     *
     *
     * This form avoids superfluous string concatenation when the logger
     * is disabled for the INFO level. However, this variant incurs the hidden
     * (and relatively small) cost of creating an `Object[]` before invoking the method,
     * even if this logger is disabled for INFO. The variants taking
     * [one][.info] and [two][.info]
     * arguments exist solely in order to avoid this hidden cost.
     *
     * @param format    the format string
     * @param arguments a list of 3 or more arguments
     */
    override fun info(format: String?, vararg arguments: Any?) {
        val ft = MsgFormatter.arrayFormat(format, arguments)
        logTagI(TAG, "format:${ft.message}", tr = ft.throwable)
    }

    /**
     * Log an exception (throwable) at the INFO level with an
     * accompanying message.
     *
     * @param msg the message accompanying the exception
     * @param t   the exception (throwable) to log
     */
    override fun info(msg: String?, t: Throwable?) {
        logTagI(TAG, "$msg", tr = t)
    }

    /**
     * Is the logger instance enabled for the WARN level?
     *
     * @return True if this Logger is enabled for the WARN level,
     * false otherwise.
     */
    override fun isWarnEnabled(): Boolean {
        return logEnableAll ?: logEnableW
    }

    /**
     * Log a message at the WARN level.
     *
     * @param msg the message string to be logged
     */
    override fun warn(msg: String?) {
        logTagW(TAG, "$msg")
    }

    /**
     * Log a message at the WARN level according to the specified format
     * and argument.
     *
     *
     *
     * This form avoids superfluous object creation when the logger
     * is disabled for the WARN level.
     *
     * @param format the format string
     * @param arg    the argument
     */
    override fun warn(format: String?, arg: Any?) {
        val ft = MsgFormatter.format(format, arg)
        logTagW(TAG, "format:${ft.message}", tr = ft.throwable)
    }

    /**
     * Log a message at the WARN level according to the specified format
     * and arguments.
     *
     *
     *
     * This form avoids superfluous string concatenation when the logger
     * is disabled for the WARN level. However, this variant incurs the hidden
     * (and relatively small) cost of creating an `Object[]` before invoking the method,
     * even if this logger is disabled for WARN. The variants taking
     * [one][.warn] and [two][.warn]
     * arguments exist solely in order to avoid this hidden cost.
     *
     * @param format    the format string
     * @param arguments a list of 3 or more arguments
     */
    override fun warn(format: String?, vararg arguments: Any?) {
        val ft = MsgFormatter.arrayFormat(format, arguments)
        logTagW(TAG, "format:${ft.message}", tr = ft.throwable)
    }

    /**
     * Log a message at the WARN level according to the specified format
     * and arguments.
     *
     *
     *
     * This form avoids superfluous object creation when the logger
     * is disabled for the WARN level.
     *
     * @param format the format string
     * @param argA   the first argument
     * @param argB   the second argument
     */
    override fun warn(format: String?, argA: Any?, argB: Any?) {
        val ft = MsgFormatter.format(format, argA, argB)
        logTagW(TAG, "format:${ft.message}", tr = ft.throwable)
    }

    /**
     * Log an exception (throwable) at the WARN level with an
     * accompanying message.
     *
     * @param msg the message accompanying the exception
     * @param t   the exception (throwable) to log
     */
    override fun warn(msg: String?, t: Throwable?) {
        logTagW(TAG, "$msg", tr = t)
    }

    /**
     * Is the logger instance enabled for the ERROR level?
     *
     * @return True if this Logger is enabled for the ERROR level,
     * false otherwise.
     */
    override fun isErrorEnabled(): Boolean {
        return logEnableAll ?: logEnableE
    }

    /**
     * Log a message at the ERROR level.
     *
     * @param msg the message string to be logged
     */
    override fun error(msg: String?) {
        logTagE(TAG, "$msg")
    }

    /**
     * Log a message at the ERROR level according to the specified format
     * and argument.
     *
     *
     *
     * This form avoids superfluous object creation when the logger
     * is disabled for the ERROR level.
     *
     * @param format the format string
     * @param arg    the argument
     */
    override fun error(format: String?, arg: Any?) {
        val ft = MsgFormatter.format(format, arg)
        logTagE(TAG, "format:${ft.message}", tr = ft.throwable)
    }

    /**
     * Log a message at the ERROR level according to the specified format
     * and arguments.
     *
     *
     *
     * This form avoids superfluous object creation when the logger
     * is disabled for the ERROR level.
     *
     * @param format the format string
     * @param argA   the first argument
     * @param argB   the second argument
     */
    override fun error(format: String?, argA: Any?, argB: Any?) {
        val ft = MsgFormatter.format(format, argA, argB)
        logTagE(TAG, "format:${ft.message}", tr = ft.throwable)
    }

    /**
     * Log a message at the ERROR level according to the specified format
     * and arguments.
     *
     *
     *
     * This form avoids superfluous string concatenation when the logger
     * is disabled for the ERROR level. However, this variant incurs the hidden
     * (and relatively small) cost of creating an `Object[]` before invoking the method,
     * even if this logger is disabled for ERROR. The variants taking
     * [one][.error] and [two][.error]
     * arguments exist solely in order to avoid this hidden cost.
     *
     * @param format    the format string
     * @param arguments a list of 3 or more arguments
     */
    override fun error(format: String?, vararg arguments: Any?) {
        val ft = MsgFormatter.arrayFormat(format, arguments)
        logTagE(TAG, "format:${ft.message}", tr = ft.throwable)
    }

    /**
     * Log an exception (throwable) at the ERROR level with an
     * accompanying message.
     *
     * @param msg the message accompanying the exception
     * @param t   the exception (throwable) to log
     */
    override fun error(msg: String?, t: Throwable?) {
        logTagE(TAG, "$msg", tr = t)
    }

}