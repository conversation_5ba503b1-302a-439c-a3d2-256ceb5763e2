package com.czur.starry.device.starrypad.ui.window

import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.view.KeyEvent
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.base.AlertWindowService
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.getScreenHeight
import com.czur.starry.device.baselib.utils.getScreenWidth
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.repeatCollectOnStart
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.baselib.widget.CustomProgressBar
import com.czur.starry.device.starrypad.R
import com.czur.starry.device.starrypad.db.entity.PaintEntity
import com.czur.starry.device.starrypad.vm.UploadViewModel
import com.czur.starry.writepadlib.proto.WPTransferData
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive

/**
 * Created by 陈丰尧 on 2024/11/18
 */
private const val TAG = "ShareAlertWindow"

class ShareAlertWindow : AlertWindowService() {
    override val layoutId: Int = R.layout.window_share
    override val windowWidthParam: Int = getScreenWidth()
    override val windowHeightParam: Int = getScreenHeight()

    private val cancelUploadBtn: View by ViewFinder(R.id.cancelUploadBtn)
    private val transmissionProcessBar: CustomProgressBar by ViewFinder(R.id.transmissionProcessBar)
    private val transmissionTitleTv: TextView by ViewFinder(R.id.transmissionTitleTv)
    private val qrCodeIv: ImageView by ViewFinder(R.id.qrCodeIv)
    private val shareMoreInfoTv: TextView by ViewFinder(R.id.shareMoreInfoTv)
    private val confirmErrorBtn: View by ViewFinder(R.id.confirmErrorBtn)
    private val finishBtn: View by ViewFinder(R.id.finishBtn)

    private val uploadProcessLayout: View by ViewFinder(R.id.uploadProcessLayout)
    private val qrCodeLayout: View by ViewFinder(R.id.qrCodeLayout)
    private val shareErrorLayout: View by ViewFinder(R.id.shareErrorLayout)

    private val uploadViewModel: UploadViewModel by viewModels()

    private var sharePaintEntities: List<PaintEntity> = emptyList()
    private val paintCount: Int
        get() = sharePaintEntities.size

    private var uploadJob: Job? = null

    override val careKeyEvent: Boolean
        get() = true


    companion object {

        fun sharePaint(context: Context, sharePaintEntities: List<PaintEntity>) {
            val intent = Intent(context, ShareAlertWindow::class.java)
            intent.putParcelableArrayListExtra("sharePaintEntities", ArrayList(sharePaintEntities))
            context.startService(intent)
        }
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            logTagV(TAG, "KeyBack")
            dismiss()
            return true
        }
        return super.onKeyDown(keyCode, event)
    }

    override fun View.initViews() {
        cancelUploadBtn.setOnDebounceClickListener {
            logTagV(TAG, "取消上传")
            dismiss()
        }
        confirmErrorBtn.setOnDebounceClickListener {
            logTagV(TAG, "确定")
            dismiss()
        }
        finishBtn.setOnDebounceClickListener {
            logTagV(TAG, "二维码展示完成")
            dismiss()
        }

    }

    override fun onDataRefresh(intent: Intent?) {
        super.onDataRefresh(intent)

        sharePaintEntities =
            intent?.getParcelableArrayListExtra("sharePaintEntities") ?: emptyList()


        repeatCollectOnStart(uploadViewModel.maxSizeFlow) {
            transmissionProcessBar.setMax(it)
        }

        repeatCollectOnStart(uploadViewModel.finishSizeFlow) {
            transmissionProcessBar.setProgress(it)
        }

        repeatCollectOnStart(uploadViewModel.uploadIndexFlow) {
            transmissionTitleTv.text = getString(R.string.str_upload_title, it, paintCount)
        }
        launch {
            delay(ONE_SECOND)   // 等待一秒, 等待serverMode变回鼠标模式
            repeatCollectOnStart(uploadViewModel.serverModelFlow) {
                if (it != WPTransferData.Mode.MODE_MOUSE) {
                    logTagD(TAG, "当前不在鼠标模式, dismiss")
                    dismiss()
                }
            }
        }


        uploadJob = launch {
            val qrCode = try {
                uploadViewModel.startUpload(sharePaintEntities)
            } catch (exp: Exception) {
                logTagE(TAG, "上传失败!", tr = exp)
                null
            }
            if (isActive) {
                onUploadFinish(qrCode)
            }
        }
    }

    private fun onUploadFinish(qrCode: Bitmap?) {
        uploadProcessLayout.gone()

        if (qrCode == null) {
            logTagE(TAG, "没有成功获取二维码")
            shareErrorLayout.show()
        } else {
            qrCodeLayout.show()

            if (paintCount > 0) {
                shareMoreInfoTv.text = getString(R.string.dialog_msg_share_count, paintCount)
            }

            qrCodeIv.setImageBitmap(qrCode)
        }
    }

    override fun onDestroy() {
        uploadJob?.cancel()
        uploadViewModel.reset()
        super.onDestroy()
    }
}