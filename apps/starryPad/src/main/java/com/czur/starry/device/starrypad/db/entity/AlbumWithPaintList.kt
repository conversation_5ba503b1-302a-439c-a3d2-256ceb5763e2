package com.czur.starry.device.starrypad.db.entity

import androidx.room.Embedded
import androidx.room.Relation
import com.czur.starry.writepadlib.proto.WPTransferData

/**
 * Created by 陈丰尧 on 2023/1/10
 */
data class AlbumWithPaintList(
    @Embedded
    val album: AlbumEntity,
    @Relation(
        parentColumn = "albumId",
        entityColumn = "albumCreatorId"
    )
    val paintList: List<PaintEntity>
) {
    // 是否是多张图片
    val isMutablePaint: Boolean
        get() = paintList.size > 1

    val albumMode: WPTransferData.Mode
        get() = paintList.firstOrNull()?.paintMode ?: WPTransferData.Mode.UNRECOGNIZED

    fun isContentSame(other: AlbumWithPaintList): Boolean {
        if (album.albumId != other.album.albumId) {
            return false
        }
        // adapter上是不会展示内容的, 所以只要数量不一样就行
        // 数量可能会影响封面图的显示
        return paintList.size == other.paintList.size && album.lastOpenTime == other.album.lastOpenTime
    }
}
