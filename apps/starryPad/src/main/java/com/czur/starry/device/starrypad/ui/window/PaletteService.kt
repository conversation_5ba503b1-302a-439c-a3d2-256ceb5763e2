package com.czur.starry.device.starrypad.ui.window

import android.content.Context
import android.content.Intent
import com.czur.czurutils.extension.platform.startService
import com.czur.czurutils.log.logTagV
import com.czur.starry.writepadlib.proto.WPTransferData

/**
 * Created by 陈丰尧 on 2024/7/29
 */
class PaletteService : BaseDrawService() {

    companion object {
        private const val TAG = "PaletteService"
        fun start(context: Context) {
            context.startService<PaletteService>(
                KEY_FROM_HISTORY to false
            )
        }

        fun startHistory(fromContext: Context, albumID: Long, currentIndex: Int) {
            logTagV(TAG, "启动画板Activity(历史记录) $albumID:$currentIndex")
            fromContext.startService<PaletteService>(
                KEY_HISTORY_ALBUM to albumID,
                KEY_HISTORY_CURRENT_INDEX to currentIndex,
                KEY_FROM_HISTORY to true
            )
        }
    }

    override val mode: WPTransferData.Mode
        get() = WPTransferData.Mode.MODE_PALETTE
}