package com.czur.starry.device.starrypad.ui.settings

import android.graphics.drawable.Drawable
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableString
import android.widget.TextView
import androidx.activity.viewModels
import androidx.core.content.ContextCompat
import androidx.fragment.app.commit
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.base.v2.aty.CZViewBindingAty
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.StarryDevLocale
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.baselib.utils.toast
import com.czur.starry.device.baselib.view.CenteredImageSpan
import com.czur.starry.device.starrypad.R
import com.czur.starry.device.starrypad.common.WPFuncOption
import com.czur.starry.device.starrypad.ui.settings.detail.WritePadMultiDeviceFragment
import com.czur.starry.device.starrypad.ui.settings.detail.WritePadNoDeviceFragment
import com.czur.starry.device.starrypad.databinding.ActivitySettingsBinding
import com.czur.starry.device.starrypad.devinfo.WPDeviceInfoManager
import com.czur.starry.device.starrypad.ui.settings.add.AddWPDevFragment
import com.czur.starry.device.starrypad.ui.settings.opt.WPOptionFloatFragment

/**
 * Created by 陈丰尧 on 2023/12/19
 * 设置页面
 */
private const val TAG = "SettingsActivity"

class SettingsActivity : CZViewBindingAty<ActivitySettingsBinding>() {
    private val devInfoViewMode: WritePadDevInfoViewModel by viewModels()

    private var connectDeviceSize = 0

    override fun ActivitySettingsBinding.initBindingViews() {

        if (Constants.starryHWInfo.salesLocale == StarryDevLocale.Overseas) {
            buyWpHintBg.gone()
        } else {
            buyWpHintBg.show()
        }

        addWpDevBtn.setOnDebounceClickListener {
            if (connectDeviceSize >= WPFuncOption.FUNC_MULTIPLE_DEVICE_COUNT) {
                toast(R.string.toast_add_dev_limit)
                logTagV(TAG, "超过最大连接数量")
                return@setOnDebounceClickListener
            }

            if (WPDeviceInfoManager.hasWPConnecting()) {
                toast(R.string.toast_add_dev_connecting)
                logTagV(TAG, "有设备正在连接")
                return@setOnDebounceClickListener
            }

            launch {
                WPDeviceInfoManager.clearUnSavedBTDevice()
                AddWPDevFragment().show()
            }

        }

        val imgDrawable0 =
            ContextCompat.getDrawable(this@SettingsActivity, R.drawable.ic_wp_device_detail_note)
                ?.apply {
                    setBounds(0, 0, minimumWidth, minimumHeight)
                }
        val imgDrawable1 =
            ContextCompat.getDrawable(this@SettingsActivity, R.drawable.ic_wp_device_detail_comment)
                ?.apply {
                    setBounds(0, 0, minimumWidth, minimumHeight)
                }
        val imgDrawables = arrayOf(imgDrawable0, imgDrawable1)
        val placeholders = arrayOf("[DETAIL0]", "[DETAIL1]")
        setForceImages(
            imgDrawables,
            wpHintContentTv,
            getString(R.string.str_write_pad_hint),
            placeholders
        )

        // 设置手写板选项
        optWpDevBtn.setOnDebounceClickListener {
            WPOptionFloatFragment().show()
        }
    }

    private fun setForceImages(
        imgDrawables: Array<Drawable?>,
        tv: TextView,
        text: String,
        placeholders: Array<String>
    ) {
        val span = SpannableString(text)
        for (i in imgDrawables.indices) {
            val imgDrawable = imgDrawables[i]
            val placeholder = placeholders[i]
            val index = span.indexOf(placeholder)
            if (index != -1) {
                val imgSpan = CenteredImageSpan(imgDrawable!!)
                span.setSpan(
                    imgSpan,
                    index,
                    index + placeholder.length,
                    Spannable.SPAN_INCLUSIVE_EXCLUSIVE
                )
            }
        }

        tv.text = span
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)

        launch {
            val bmp = devInfoViewMode.getBuyQrCode()
            binding.buyQrCodeIv.setImageBitmap(bmp)
        }

        // 刷新一次手写板的信息
        launch {
            devInfoViewMode.refreshWritePadInfoList()
        }

        repeatCollectOnResume(devInfoViewMode.writePadInfoListFlow) {
            connectDeviceSize = it.size
            logTagV(TAG, "记录的手写板数量: ${it.size}")
            changeDevFragment(it.size)
        }
    }

    private fun changeDevFragment(size: Int) {
        val fragment = supportFragmentManager.findFragmentById(binding.wpDeviceContainer.id)
        val targetClass = when (size) {
            0 -> WritePadNoDeviceFragment::class.java
            else -> WritePadMultiDeviceFragment::class.java
        }
        if (fragment != null && fragment::class.java == targetClass) {
            return
        }
        supportFragmentManager.commit {
            replace(binding.wpDeviceContainer.id, targetClass, null)
        }
    }
}