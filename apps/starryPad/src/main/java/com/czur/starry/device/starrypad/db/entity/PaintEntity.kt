package com.czur.starry.device.starrypad.db.entity

import android.os.Parcelable
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.czur.starry.writepadlib.proto.WPTransferData
import kotlinx.parcelize.Parcelize

/**
 * Created by 陈丰尧 on 2022/4/26
 */
@Parcelize
@Entity(tableName = "tab_paint")
data class PaintEntity(
    val paintPath: String,              // 绘画保存的路径
    val bgImgPath: String?,             // 背景图片路径
    val drawImgPath: String,            // 绘画图片路径
    val contentImgPath: String,             // 内容图片路径 在完整图片中剪裁内容部分
    val createTime: Long,               // 创建时间
    var albumCreatorId: Long = 0L,      // 对应的相册ID
    @PrimaryKey(autoGenerate = true)
    val paintId: Long = 0L,
) : Parcelable {
    // 绘画的Mode
    val paintMode: WPTransferData.Mode
        get() = if (bgImgPath.isNullOrEmpty()) {
            WPTransferData.Mode.MODE_PALETTE
        } else {
            WPTransferData.Mode.MODE_MARK
        }
}