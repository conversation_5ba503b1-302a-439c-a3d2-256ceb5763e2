package com.czur.starry.device.starrypad.db.dao

import androidx.lifecycle.LiveData
import androidx.room.*
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.starrypad.db.entity.AlbumEntity
import com.czur.starry.device.starrypad.db.entity.AlbumWithPaintList
import com.czur.starry.device.starrypad.db.entity.PaintEntity
import com.czur.starry.device.starrypadlib.StarryPadPaintHandler

/**
 * Created by 陈丰尧 on 2023/1/10
 */
private const val TAG = "AlbumDao"
@Dao
abstract class AlbumDao {
    /**
     * 获取全部的相册信息
     * 根据创建时间的降序进行排列
     */
    @Transaction
    @Query("SELECT * FROM tab_album order by createTime desc")
    abstract fun getAllAlbums(): LiveData<List<AlbumWithPaintList>>

    @Transaction
    @Query("SELECT * FROM tab_album where albumId = :albumId")
    abstract fun getAlbumsByID(albumId: Long): AlbumWithPaintList

    /**
     * 获取对应AlbumId对应的笔记
     */
    @Query("SELECT * FROM tab_paint where albumCreatorId = :albumId")
    abstract fun getPaintsByAlbumId(albumId: Long): List<PaintEntity>

    @Query("SELECT count(*) FROM tab_paint where albumCreatorId = :albumId")
    abstract fun getCountInAlbum(albumId: Long): Int


    @Transaction
    @Query("select * from tab_album order by lastOpenTime desc limit :limitCount")
    abstract fun getRecentAlbums(limitCount: Int): LiveData<List<AlbumWithPaintList>>

    /**
     * 插入PaintList
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract fun insertOrUpdatePaints(paintEntities: List<PaintEntity>)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract fun insertOrUpdatePaint(paintEntity: PaintEntity)

    /**
     * 更新指定相册, 主要是更新最后打开时间
     */
    @Update
    abstract fun updateAlbum(album: AlbumEntity)

    /**
     * 添加一个新的相册
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract fun insertOrUpdate(album: AlbumEntity): Long

    /**
     * 插入/更新一个新的相册并附带相册信息
     * @return 画册和当前插入的绘画信息
     *          注意: 该方法只会返回当前插入的绘画信息, 不会返回相册中所有的绘画信息!
     */
    @Transaction
    open fun insertOrCreateAlbumWithPaint(
        album: AlbumEntity,
        paintEntities: List<PaintEntity>
    ): AlbumWithPaintList {
        val albumId = insertOrUpdate((album))
        val newAlbum = album.copy(albumId = albumId)
        paintEntities.forEach {
            it.albumCreatorId = albumId
        }
        insertOrUpdatePaints(paintEntities)
        return AlbumWithPaintList(
            newAlbum,
            paintEntities
        ).also {
            logTagD(TAG, "更新未读文件名: ${newAlbum.createTimeStr}")
            StarryPadPaintHandler.newPaintFileName = newAlbum.createTimeStr
        }
    }

    @Delete
    abstract fun delPaintList(paintList: List<PaintEntity>)

    @Delete
    abstract fun delAlbumOnly(album: AlbumEntity)

    @Query("delete from tab_album where albumId = :albumId")
    abstract fun delAlbumOnlyById(albumId: Long)

    /**
     * 删除整个相册
     */
    @Transaction
    open fun delAlbum(album: AlbumEntity): List<PaintEntity> {
        val paintList = getPaintsByAlbumId(albumId = album.albumId)
        delPaintList(paintList)
        delAlbumOnly(album)
        return paintList
    }

    /**
     * 删除数据库中的一张笔记
     * 如果是相册中的最后一张笔记, 则会将相册联通删除掉
     */
    @Transaction
    open fun delPaint(paintEntity: PaintEntity) {
        val countInAlbum = getCountInAlbum(paintEntity.albumCreatorId)
        delPaintList(listOf(paintEntity)) // 删除对应的笔记
        if (countInAlbum <= 1) {
            // 删除对应的Album
            delAlbumOnlyById(paintEntity.albumCreatorId)
        }
    }
}