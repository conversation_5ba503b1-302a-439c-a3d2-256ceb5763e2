package com.czur.starry.device.starrypad.ui.fragment.side

import android.os.Bundle
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.czur.starry.device.baselib.base.v2.fragment.floating.CZVBFloatingFragment
import com.czur.starry.device.baselib.utils.doOnItemClick
import com.czur.starry.device.starrypad.databinding.SideFragmentPaintTimePickerBinding
import com.czur.starry.device.starrypad.ui.adapter.AlbumTimeAdapter
import com.czur.starry.device.starrypad.ui.adapter.PaintTimePickerVO
import com.czur.starry.device.starrypad.vm.PaintDataViewModel

/**
 * Created by 陈丰尧 on 2022/5/6
 */
class PaintTimePickerSideFragment(
    private val onDataClick: (fragment: PaintTimePickerSideFragment, paintTimePickerVO: PaintTimePickerVO) -> Unit
) : CZVBFloatingFragment<SideFragmentPaintTimePickerBinding>() {
    private val paintDataVM: PaintDataViewModel by viewModels({ requireActivity() })
    private val timeAdapter = AlbumTimeAdapter()

    override fun FloatingFragmentParams.initFloatingParams() {
        keyBackDismiss = false
    }

    override fun SideFragmentPaintTimePickerBinding.initBindingViews() {
        timePickerRv.layoutManager = LinearLayoutManager(requireContext())
        timePickerRv.adapter = timeAdapter

        timePickerRv.doOnItemClick { vh, view ->
            val pos = vh.bindingAdapterPosition
            val data = timeAdapter.getData(pos)
            onDataClick(this@PaintTimePickerSideFragment, data)
            true
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        paintDataVM.paintTimeLive.observe(this) {
            timeAdapter.setData(it)
        }
    }
}