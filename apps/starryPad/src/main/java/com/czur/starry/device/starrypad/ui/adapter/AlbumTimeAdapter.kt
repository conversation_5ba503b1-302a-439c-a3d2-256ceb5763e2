package com.czur.starry.device.starrypad.ui.adapter

import android.view.ViewGroup
import com.czur.starry.device.baselib.base.BaseDifferAdapter
import com.czur.starry.device.baselib.base.BaseVH
import com.czur.starry.device.starrypad.R

/**
 * Created by 陈丰尧 on 2022/5/6
 */
class AlbumTimeAdapter : BaseDifferAdapter<PaintTimePickerVO>() {
    companion object {
        private const val ITEM_TYPE_NORMAL = 0
        private const val ITEM_TYPE_SEL = 1
    }

    override fun areItemsTheSame(oldItem: PaintTimePickerVO, newItem: PaintTimePickerVO): Boolean {
        return oldItem.time == newItem.time
    }

    override fun areContentsTheSame(
        oldItem: PaintTimePickerVO,
        newItem: PaintTimePickerVO
    ): Boolean {
        return oldItem == newItem
    }

    override fun bindViewHolder(holder: BaseVH, position: Int, itemData: PaintTimePickerVO) {
        holder.setText(itemData.time + "(${itemData.countStr})", R.id.normalTimeTv)

    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseVH {
        return if (viewType == ITEM_TYPE_NORMAL) {
            BaseVH(R.layout.item_time_picker_normal, parent)
        } else {
            BaseVH(R.layout.item_time_picker_sel, parent)
        }
    }

    override fun getItemViewType(position: Int): Int {
        return if (getData(position).isSelect) ITEM_TYPE_SEL else ITEM_TYPE_NORMAL
    }

}