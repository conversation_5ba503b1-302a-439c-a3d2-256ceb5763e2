package com.czur.starry.device.starrypad.ui.settings.add

import android.os.Bundle
import androidx.fragment.app.viewModels
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.RecyclerView
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.base.v2.fragment.floating.CZVBFloatingFragment
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.addItemDecoration
import com.czur.starry.device.baselib.utils.closeDefChangeAnimations
import com.czur.starry.device.baselib.utils.doOnItemClick
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.lifecycle.AutoRemoveLifecycleObserver
import com.czur.starry.device.baselib.utils.repeatCollectOnCreate
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.starrypad.databinding.FloatFragmentAddWpDevBinding
import com.czur.starry.device.starrypad.hardware.WritePadDeviceModeManager
import com.czur.starry.device.starrypad.hardware.trans.bt.BTServer
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.debounce

/**
 * Created by 陈丰尧 on 2024/2/26
 */
private const val TAG = "AddWPDevFragment"

class AddWPDevFragment : CZVBFloatingFragment<FloatFragmentAddWpDevBinding>() {
    private val foundDevAdapter = FoundWPAdapter()
    private val viewModel: AddWPDevViewModel by viewModels()

    override fun CZVBFloatingFragment<FloatFragmentAddWpDevBinding>.FloatingFragmentParams.initFloatingParams() {
        this.viewLifecycleObserver = object : AutoRemoveLifecycleObserver {
            override fun onCreate(owner: LifecycleOwner) {
                super.onCreate(owner)
                logTagD(TAG, "onCreate")
                BTServer.startListener(autoClose = true)
            }

            override fun onDestroy(owner: LifecycleOwner) {
                super.onDestroy(owner)
                try {
                    BTServer.stop()
                } catch (e: Exception) {
                    logTagW(TAG, "Exception when stopping btServer = $e")
                }
            }
        }
    }

    override fun FloatFragmentAddWpDevBinding.initBindingViews() {
        closeIv.setOnDebounceClickListener {
            logTagV(TAG, "点击关闭按钮")
            dismiss()
        }
        // 手写板设备RecyclerView
        wpDevRv.apply {
            closeDefChangeAnimations()
            addItemDecoration(RecyclerView.VERTICAL, 40)
            adapter = foundDevAdapter

            doOnItemClick { vh, _ ->
                val pos = vh.bindingAdapterPosition
                val data = foundDevAdapter.getData(pos)
                viewModel.updateSelDev(data)
                true
            }
        }

        // 确定按钮
        addDevConfirmBtn.setOnDebounceClickListener {
            logTagD(TAG, "点击连接按钮")
            viewModel.connectSelDev()
        }

        // 重新配对按钮
        noWpDevRePairTv.setOnDebounceClickListener {
            logTagD(TAG, "点击重新配对按钮")
            foundDevAdapter.setData(listOf())
            launch {
                viewModel.startScan()
            }
        }
    }

    @FlowPreview
    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)

        // 切换显示的View
        logTagD(TAG, "initData")
        repeatCollectOnCreate(viewModel.viewStateFlow) {
            logTagD(TAG, "viewShareFlow_${it.name}")
            when (it) {
                AddWPDevViewModel.AddWpViewState.SCANNING -> {
                    binding.scanConstraintLayout.show()
                    binding.scanningGroup.show()
                    binding.connectConstraintLayout.gone()
                    binding.devListGroup.gone()
                    binding.noWpDevGroup.gone()
                }

                AddWPDevViewModel.AddWpViewState.CONNECTING -> {
                    binding.connectConstraintLayout.show()
                    binding.scanConstraintLayout.gone()
                    binding.scanningGroup.gone()
                    binding.devListGroup.gone()
                    binding.noWpDevGroup.gone()
                }

                AddWPDevViewModel.AddWpViewState.CONNECT_TIMEOUT -> {
                    foundDevAdapter.setData(listOf())
                    logTagD(TAG, "noWpDevGroup show")
                    binding.noWpDevGroup.show()
                    binding.scanConstraintLayout.show()
                    binding.devListGroup.gone()
                    binding.scanningGroup.gone()
                    binding.connectConstraintLayout.gone()
                }
            }
        }

        repeatCollectOnCreate(viewModel.foundWPDevFlow) {
            foundDevAdapter.setData(it)
            if (it.isNotEmpty()) {
                binding.devListGroup.show()
                binding.scanningGroup.gone()
                binding.noWpDevGroup.gone()
            } else if (viewModel.isScanning) {
                binding.devListGroup.gone()
                binding.scanningGroup.show()
            }
        }

        repeatCollectOnCreate(viewModel.isScanningFlow.debounce(100)) { isScanning ->
            logTagV(TAG, "蓝牙正在扫描: $isScanning")
            if (!isScanning) {
                // 扫描结束
                if (foundDevAdapter.itemCount == 0) {
                    logTagD(TAG, "bt 没有扫描到")
                    // 没有扫描到设备
                    viewModel.clearUnBoundDev()
                    binding.scanningGroup.gone()
                    binding.devListGroup.gone()
                    binding.noWpDevGroup.show()
                    binding.scanConstraintLayout.show()
                    binding.connectConstraintLayout.gone()
                }
            }
        }

        repeatCollectOnCreate(WritePadDeviceModeManager.clientInfoFlow) {
            if (it.name == viewModel.connectingDevice?.name && it.connected) {
                logTagD(TAG, "连接成功, 关闭Loading框")
                dismiss()
            }
        }
        launch {
            viewModel.startScan()   // 开始扫描
        }
    }
}