<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingHorizontal="45px"
    tools:ignore="PxUsage,RtlHardcoded">

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_found_wp_dev" />

    <TextView
        android:id="@+id/wpFoundDeviceNameTv"
        android:layout_width="0px"
        android:layout_height="wrap_content"
        android:layout_marginLeft="17px"
        android:layout_weight="1"
        android:includeFontPadding="false"
        android:textColor="@color/black"
        android:textSize="30px"
        android:textStyle="bold"
        tools:text="WritePad-2" />

    <ImageView
        android:id="@+id/wpFoundDevSelectedIv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_found_wp_sel" />

</LinearLayout>