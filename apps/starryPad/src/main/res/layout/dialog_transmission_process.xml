<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="600px"
    android:layout_height="300px"
    app:bl_corners_radius="10px"
    app:bl_solid_color="@color/white"
    tools:ignore="PxUsage">


    <TextView
        android:id="@+id/transmissionTitleTv"
        style="@style/no_network_text_style"
        android:textColor="#3D3D3D"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="正在上传2/10,请稍后" />

    <com.czur.starry.device.baselib.widget.CustomProgressBar
        android:id="@+id/transmissionProcessBar"
        android:layout_width="450px"
        android:layout_height="10px"
        app:bg_color="#D8D8D8"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:progress_color="#036CFE" />

    <TextView
        android:id="@+id/cancelBtn"
        android:layout_width="180px"
        android:layout_height="50px"
        android:layout_marginBottom="25px"
        android:gravity="center"
        android:includeFontPadding="false"
        android:text="@string/dialog_normal_cancel"
        android:textColor="#3D3D3D"
        android:textSize="20px"
        android:textStyle="bold"
        app:bl_corners_radius="10px"
        app:bl_solid_color="#E7E7E7"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        tools:background="#E7E7E7" />

</androidx.constraintlayout.widget.ConstraintLayout>