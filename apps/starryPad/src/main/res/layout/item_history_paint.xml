<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="256px"
    android:layout_height="144px"
    tools:ignore="PxUsage,RtlHardcoded">

    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/itemHistoryIv"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="2px"
        android:scaleType="fitXY"
        app:round="10px" />

    <ImageView
        android:id="@+id/itemSelMarkIv"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="fitXY" />

    <TextView
        android:id="@+id/itemHistoryIndexTv"
        android:layout_width="wrap_content"
        android:layout_height="15px"
        android:layout_marginRight="7px"
        android:layout_marginBottom="8px"
        android:gravity="center"
        android:includeFontPadding="false"
        android:minWidth="22px"
        android:textColor="#ABABAB"
        android:textSize="12px"
        android:textStyle="bold"
        app:bl_corners_radius="8px"
        app:bl_solid_color="#F4F4F4"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        tools:background="#F4F4F4" />


</androidx.constraintlayout.widget.ConstraintLayout>