<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingHorizontal="60px"
    tools:background="@color/bg_main"
    tools:ignore="PxUsage,RtlHardcoded">

    <TextView
        android:id="@+id/usingTagTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="75px"
        android:text="@string/str_screen_backdrop_using"
        android:textColor="@color/text_content_title"
        android:textSize="30px"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/usingImgIv"
        android:layout_width="400px"
        android:layout_height="225px"
        android:layout_marginTop="30px"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/usingTagTv"
        app:round="10px"
        android:background="@color/base_bg_color"
        tools:src="@drawable/img_screen_backdrop_def" />

    <com.czur.uilib.choose.CZTabBar
        android:id="@+id/backdropTabBar"
        android:layout_width="wrap_content"
        android:layout_height="60px"
        android:layout_marginTop="60px"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/usingImgIv" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/backdropRv"
        android:layout_width="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        android:layout_height="0px"
        android:layout_marginTop="40px"
        android:layout_marginRight="100px"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/backdropTabBar" />

</androidx.constraintlayout.widget.ConstraintLayout>