<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="500px"
    android:layout_height="360px"
    app:bl_corners_radius="10px"
    app:bl_solid_color="#5879FC"
    tools:background="#5879FC"
    tools:ignore="PxUsage">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="25px"
        android:includeFontPadding="false"
        android:text="@string/dialog_normal_title_tips"
        android:textColor="@color/white"
        android:textSize="36px"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <TextView
        android:id="@+id/contentTv"
        android:layout_width="0px"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:lineSpacingExtra="10px"
        android:lineSpacingMultiplier="1"
        android:textColor="@color/white"
        android:textSize="24px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintBottom_toTopOf="@id/withPairDeviceCb"
        app:layout_constraintLeft_toLeftOf="@id/cancelBtn"
        app:layout_constraintRight_toRightOf="@id/confirmBtn"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="您的账号已在其他设备登录, 请确认账号安全。" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/withPairDeviceGroup"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="withPairDeviceCb,withPairDeviceTv" />

    <com.czur.uilib.choose.CZImageCheckBox
        android:id="@+id/withPairDeviceCb"
        style="@style/CBSubSize"
        android:layout_marginTop="20px"
        app:checked="false"
        app:checkedImg="@drawable/file_icon_share_checked"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/withPairDeviceTv"
        app:layout_constraintTop_toBottomOf="@id/contentTv"
        app:unCheckedImg="@drawable/file_icon_share_unchecked"
        app:withClickIDs="withPairDeviceTv" />

    <TextView
        android:id="@+id/withPairDeviceTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10px"
        android:includeFontPadding="false"
        android:textColor="@color/white"
        android:textSize="20px"
        android:textStyle="normal"
        app:layout_constraintBottom_toBottomOf="@id/withPairDeviceCb"
        app:layout_constraintLeft_toRightOf="@id/withPairDeviceCb"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/withPairDeviceCb"
        tools:ignore="RtlHardcoded"
        tools:text="开启对应麦克风" />

    <com.czur.starry.device.baselib.widget.CommonButton
        android:id="@+id/cancelBtn"
        android:layout_width="180px"
        android:layout_height="50px"
        android:layout_marginBottom="30px"
        android:ellipsize="end"
        android:maxLines="1"
        android:padding="4px"
        android:text="@string/dialog_normal_cancel"
        android:textSize="20px"
        android:textStyle="bold"
        app:autoSizeMaxTextSize="20px"
        app:autoSizeMinTextSize="12px"
        app:autoSizeTextType="uniform"
        app:baselib_theme="dark"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/confirmBtn" />

    <com.czur.starry.device.baselib.widget.CommonButton
        android:id="@+id/confirmBtn"
        android:layout_width="180px"
        android:layout_height="50px"
        android:ellipsize="end"
        android:maxLines="1"
        android:text="@string/dialog_normal_confirm"
        android:textSize="20px"
        android:textStyle="bold"
        app:autoSizeMaxTextSize="20px"
        app:autoSizeMinTextSize="12px"
        app:autoSizeTextType="uniform"
        app:baselib_theme="white2"
        app:layout_constraintLeft_toRightOf="@+id/cancelBtn"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/cancelBtn" />
</androidx.constraintlayout.widget.ConstraintLayout>