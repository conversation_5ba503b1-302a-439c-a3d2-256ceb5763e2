<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="1020px"
        android:layout_height="130px"
        android:layout_gravity="center"
        app:bl_corners_radius="10px"
        app:bl_solid_color="#0D000000">

        <TextView
            android:id="@+id/title_tv"
            android:layout_width="wrap_content"
            android:layout_height="40px"
            android:layout_marginLeft="70px"
            android:layout_marginTop="20px"
            android:gravity="center"
            android:text="@string/memory_remaind_time_tv"
            android:textSize="24px"
            app:layout_constraintHorizontal_chainStyle="spread_inside"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@id/tv_1day"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/title_last_memory"
            android:layout_width="wrap_content"
            android:layout_height="40px"
            android:layout_marginTop="10px"
            android:gravity="center"
            android:paddingLeft="70px"
            android:text="@string/memory_remain_memory"
            android:textSize="24px"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/title_tv" />

        <TextView
            android:id="@+id/tv_1day"
            style="@style/memory_tvremind_style"
            android:text="@string/memory_remaind_everyday"
            app:layout_constraintLeft_toRightOf="@+id/title_tv"
            app:layout_constraintRight_toLeftOf="@id/tv_3day"
            app:layout_constraintTop_toTopOf="@+id/title_tv" />

        <TextView
            android:id="@+id/tv_300m"
            style="@style/memory_tvremind_style"
            android:text="@string/memory_remain_300"
            app:layout_constraintLeft_toLeftOf="@+id/tv_1day"
            app:layout_constraintTop_toTopOf="@+id/title_last_memory" />

        <TextView
            android:id="@+id/tv_3day"
            style="@style/memory_tvremind_style"
            android:text="@string/memory_remaind_3day"
            app:layout_constraintLeft_toRightOf="@+id/tv_1day"
            app:layout_constraintRight_toLeftOf="@id/tv_7day"
            app:layout_constraintTop_toTopOf="@+id/title_tv" />

        <TextView
            android:id="@+id/tv_500m"
            style="@style/memory_tvremind_style"
            android:text="@string/memory_remain_500"
            app:layout_constraintLeft_toLeftOf="@+id/tv_3day"
            app:layout_constraintTop_toTopOf="@+id/title_last_memory" />

        <TextView
            android:id="@+id/tv_7day"
            style="@style/memory_tvremind_style"
            android:layout_marginRight="70px"
            android:text="@string/memory_remaind_7day"
            app:layout_constraintLeft_toRightOf="@+id/tv_3day"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@+id/title_tv" />

        <TextView
            android:id="@+id/tv_1g"
            style="@style/memory_tvremind_style"
            android:text="@string/memory_remain_1g"
            app:layout_constraintLeft_toLeftOf="@+id/tv_7day"
            app:layout_constraintTop_toTopOf="@+id/title_last_memory" />

        <ImageView
            android:id="@+id/im_select1"
            style="@style/memory_iv_remind_style"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="@+id/tv_1day"
            app:layout_constraintLeft_toRightOf="@+id/tv_1day"
            app:layout_constraintTop_toTopOf="@+id/tv_1day" />

        <ImageView
            android:id="@+id/im_select300"
            style="@style/memory_iv_remind_style"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="@+id/tv_300m"
            app:layout_constraintLeft_toRightOf="@+id/tv_300m"
            app:layout_constraintTop_toTopOf="@+id/tv_300m" />

        <ImageView
            android:id="@+id/im_select3"
            style="@style/memory_iv_remind_style"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="@+id/tv_3day"
            app:layout_constraintLeft_toRightOf="@+id/tv_3day"
            app:layout_constraintTop_toTopOf="@+id/tv_3day" />

        <ImageView
            android:id="@+id/im_select500"
            style="@style/memory_iv_remind_style"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="@+id/tv_500m"
            app:layout_constraintLeft_toRightOf="@+id/tv_500m"
            app:layout_constraintTop_toTopOf="@+id/tv_500m" />

        <ImageView
            android:id="@+id/im_select7"
            style="@style/memory_iv_remind_style"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="@+id/tv_7day"
            app:layout_constraintLeft_toRightOf="@+id/tv_7day"
            app:layout_constraintTop_toTopOf="@+id/tv_7day" />

        <ImageView
            android:id="@+id/im_select1g"
            style="@style/memory_iv_remind_style"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="@+id/tv_1g"
            app:layout_constraintLeft_toRightOf="@+id/tv_1g"
            app:layout_constraintTop_toTopOf="@+id/tv_1g" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</FrameLayout>