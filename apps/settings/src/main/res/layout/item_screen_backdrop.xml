<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="400px"
    android:layout_height="225px"
    tools:ignore="PxUsage">

    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_margin="1px"
        android:background="@color/base_bg_color"
        app:round="10px" />


    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/screenBackdropIv"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="fitXY"
        app:round="10px" />
</androidx.constraintlayout.widget.ConstraintLayout>