<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:ignore="PxUsage">

    <ImageView
        android:id="@+id/uploadLogoIv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/img_touch_pad_upload"
        app:layout_constraintBottom_toTopOf="@id/updateProgress"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <com.czur.uilib.CZProgressBar
        android:id="@+id/updateProgress"
        android:layout_width="820px"
        android:layout_height="6px"
        android:layout_marginTop="119px"
        app:czPbBgColor="@color/cz_progress_bar_bg_white"
        app:czPbMax="100"
        app:czPbProgressColor="@color/cz_progress_bar_progress_white"
        app:layout_constraintBottom_toTopOf="@id/updateNumTv"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/uploadLogoIv"
        app:max="100" />

    <TextView
        android:id="@+id/updateNumTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="65px"
        android:includeFontPadding="false"
        android:text="00"
        android:textColor="@color/white"
        android:textSize="60px"
        android:textStyle="normal"
        app:layout_constraintBottom_toTopOf="@id/warnHintTv"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/percentTv"
        app:layout_constraintTop_toBottomOf="@id/updateProgress"
        tools:text="50" />

    <TextView
        android:id="@+id/percentTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="5px"
        android:text="@string/suffix_percentage"
        android:textColor="@color/white"
        android:textSize="24px"
        app:layout_constraintBaseline_toBaselineOf="@id/updateNumTv"
        app:layout_constraintLeft_toRightOf="@id/updateNumTv"
        app:layout_constraintRight_toRightOf="parent" />


    <TextView
        android:id="@+id/warnHintTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="15px"
        android:gravity="center_horizontal"
        android:text="@string/srt_update_warn_hint"
        android:textColor="@color/white"
        android:textSize="30px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/updateNumTv" />
</androidx.constraintlayout.widget.ConstraintLayout>