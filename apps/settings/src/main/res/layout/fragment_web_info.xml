<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/webInfoLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.noober.background.view.BLView
        android:layout_width="0px"
        android:layout_height="0px"
        app:bl_corners_radius="10px"
        app:bl_solid_color="#F1F3FE"
        app:layout_constraintLeft_toLeftOf="@id/webview"
        app:layout_constraintRight_toRightOf="@id/webview"
        app:layout_constraintTop_toTopOf="@id/webview"
        app:layout_constraintBottom_toBottomOf="@id/webview"
        />

    <WebView
        android:id="@+id/webview"
        android:layout_width="900px"
        android:layout_height="900px"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:overScrollMode="never"/>
    
    <com.czur.uilib.btn.CZBackBtn
        android:id="@+id/backBtn"
        android:layout_width="90px"
        android:layout_height="60px"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_margin="30px"/>

    <ProgressBar
        android:id="@+id/progress_bar"
        style="@style/Widget.AppCompat.ProgressBar"
        android:layout_width="100px"
        android:layout_height="100px"
        android:layout_gravity="center"
        android:indeterminateTint="@color/bg_main_blue"
        android:indeterminateTintMode="src_atop"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/errorLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginTop="100px"
        android:visibility="gone"
        android:background="@color/bg_main">
        <ImageView
            android:id="@+id/errorIv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_load_fail"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/errorTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/str_webview_error"
            android:textColor="@color/text_common"
            android:layout_marginTop="30px"
            android:alpha="0.7"
            android:textSize="30px"
            android:textStyle="bold"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/errorIv" />
    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>