<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/bg_main"
    tools:ignore="PxUsage">

    <TextView
        style="@style/TVContentTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="40px"
        android:text="@string/air_control_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.helper.widget.Flow
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="250px"
        app:constraint_referenced_ids="itemPortraitTrackIv,itemZoomMoveIv,itemWhiteboardModeIv"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/itemPortraitTrackIv"
        android:layout_width="400px"
        android:layout_height="320px"
        android:src="@drawable/img_air_control_portait_track" />

    <ImageView
        android:id="@+id/itemZoomMoveIv"
        android:layout_width="400px"
        android:layout_height="320px"
        android:src="@drawable/img_air_control_zoom_move" />

    <ImageView
        android:id="@+id/itemWhiteboardModeIv"
        android:layout_width="400px"
        android:layout_height="320px"
        android:src="@drawable/img_air_control_whiteboard_mode" />

    <TextView
        android:id="@+id/itemPortraitTrackTitleTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="25px"
        android:text="@string/air_control_item_portrait_track"
        android:textColor="@color/text_content_title"
        android:textSize="30px"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="@id/itemPortraitTrackIv"
        app:layout_constraintRight_toRightOf="@id/itemPortraitTrackIv"
        app:layout_constraintTop_toBottomOf="@id/itemPortraitTrackIv" />

    <TextView
        android:id="@+id/itemZoomMoveTitleTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="25px"
        android:text="@string/air_control_item_zoom_move"
        android:textColor="@color/text_content_title"
        android:textSize="30px"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="@id/itemZoomMoveIv"
        app:layout_constraintRight_toRightOf="@id/itemZoomMoveIv"
        app:layout_constraintTop_toBottomOf="@id/itemZoomMoveIv" />

    <TextView
        android:id="@+id/itemWhiteboardModeTitleTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="25px"
        android:text="@string/air_control_item_whiteboard_mode"
        android:textColor="@color/text_content_title"
        android:textSize="30px"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="@id/itemWhiteboardModeIv"
        app:layout_constraintRight_toRightOf="@id/itemWhiteboardModeIv"
        app:layout_constraintTop_toBottomOf="@id/itemWhiteboardModeIv" />

    <TextView
        android:layout_width="0px"
        android:layout_height="wrap_content"
        android:layout_marginTop="25px"
        android:paddingHorizontal="15px"
        android:text="@string/air_control_item_portrait_track_hint"
        android:textColor="@color/text_common_light"
        android:textSize="20px"
        android:textStyle="normal"
        app:layout_constraintLeft_toLeftOf="@id/itemPortraitTrackIv"
        app:layout_constraintRight_toRightOf="@id/itemPortraitTrackIv"
        app:layout_constraintTop_toBottomOf="@id/itemPortraitTrackTitleTv" />

    <TextView
        android:layout_width="0px"
        android:layout_height="wrap_content"
        android:layout_marginTop="25px"
        android:paddingHorizontal="15px"
        android:text="@string/air_control_item_zoom_move_hint"
        android:textColor="@color/text_common_light"
        android:textSize="20px"
        android:textStyle="normal"
        app:layout_constraintLeft_toLeftOf="@id/itemZoomMoveIv"
        app:layout_constraintRight_toRightOf="@id/itemZoomMoveIv"
        app:layout_constraintTop_toBottomOf="@id/itemZoomMoveTitleTv" />

    <TextView
        android:layout_width="0px"
        android:layout_height="wrap_content"
        android:layout_marginTop="25px"
        android:paddingHorizontal="15px"
        android:text="@string/air_control_item_whiteboard_mode_hint"
        android:textColor="@color/text_common_light"
        android:textSize="20px"
        android:textStyle="normal"
        app:layout_constraintLeft_toLeftOf="@id/itemWhiteboardModeIv"
        app:layout_constraintRight_toRightOf="@id/itemWhiteboardModeIv"
        app:layout_constraintTop_toBottomOf="@id/itemWhiteboardModeTitleTv" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/air_control_enable_hint"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>


</androidx.constraintlayout.widget.ConstraintLayout>