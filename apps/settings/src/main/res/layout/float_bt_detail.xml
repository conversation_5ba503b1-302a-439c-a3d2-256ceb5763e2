<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="650px"
    android:layout_height="800px"
    tools:ignore="PxUsage,RtlHardcoded">

    <com.noober.background.view.BLView
        android:id="@+id/bgTopView"
        android:layout_width="match_parent"
        android:layout_height="100px"
        app:bl_corners_topLeftRadius="10px"
        app:bl_corners_topRightRadius="10px"
        app:bl_solid_color="#4f6de3"
        app:layout_constraintTop_toTopOf="parent"
        tools:background="#4f6de3" />

    <com.noober.background.view.BLView
        android:layout_width="match_parent"
        android:layout_height="0px"
        app:bl_corners_bottomLeftRadius="10px"
        app:bl_corners_bottomRightRadius="10px"
        app:bl_solid_color="@color/bg_main_blue"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/bgTopView" />

    <ImageView
        android:id="@+id/closeBtn"
        android:layout_width="60px"
        android:layout_height="60px"
        android:layout_marginRight="20px"
        android:src="@drawable/ic_dialog_close"
        app:float_tips="@string/float_tip_close"
        app:layout_constraintBottom_toBottomOf="@id/bgTopView"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/bgTopView" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="58px"
        android:includeFontPadding="false"
        android:text="@string/str_bt_detail_title"
        android:textColor="@color/white"
        android:textSize="48px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/bgTopView"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@id/bgTopView" />

    <androidx.constraintlayout.helper.widget.Flow
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:constraint_referenced_ids="btImgIv,batteryGroup,deviceNameTv"
        app:layout_constraintBottom_toBottomOf="@id/ignoreBtn"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/bgTopView"
        app:layout_constraintVertical_chainStyle="packed" />

    <ImageView
        android:id="@+id/btImgIv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:src="@drawable/img_bt_mouse" />

    <LinearLayout
        android:id="@+id/batteryGroup"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingTop="20px">

        <TextView
            android:id="@+id/batteryTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            android:textSize="24px"
            android:textStyle="normal"
            tools:text="100%" />

        <com.czur.starry.device.settings.widget.BatteryView
            android:id="@+id/batteryView"
            android:layout_width="48px"
            android:layout_height="24px"
            android:layout_marginLeft="5px" />
    </LinearLayout>


    <TextView
        android:id="@+id/deviceNameTv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="center"
        android:includeFontPadding="false"
        android:lines="1"
        android:paddingHorizontal="20px"
        android:paddingTop="30px"
        android:textColor="@color/white"
        android:textSize="36px"
        android:textStyle="bold"
        tools:text="CZUR9007" />

    <ImageView
        android:id="@+id/detailHintIv"
        android:layout_width="30px"
        android:layout_height="30px"
        android:layout_marginRight="15px"
        android:src="@drawable/ic_bt_detail_hint"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/detailHintTv"
        app:layout_constraintTop_toTopOf="@id/detailHintSpaceTv"
        app:layout_constraintBottom_toBottomOf="@+id/detailHintSpaceTv"/>

    <TextView
        android:id="@+id/detailHintSpaceTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:includeFontPadding="false"
        android:text="@string/str_hint_bt_disconnect"
        android:textColor="@color/transparent"
        android:textSize="24px"
        android:textStyle="bold"
        android:singleLine="true"
        app:layout_constraintLeft_toRightOf="@id/detailHintIv"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/detailHintTv" />

    <TextView
        android:id="@+id/detailHintTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:includeFontPadding="false"
        android:text="@string/str_hint_bt_disconnect"
        android:textColor="@color/white"
        android:textSize="@dimen/detail_hint_tv_size"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/detailHintIv"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ignoreBtn" />

    <com.czur.uilib.btn.CZButton
        android:id="@+id/ignoreBtn"
        android:layout_width="450px"
        android:layout_height="80px"
        android:layout_marginBottom="90px"
        android:text="@string/str_bt_ignore"
        android:textSize="30px"
        app:colorStyle="AlphaWhiteWhite"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>