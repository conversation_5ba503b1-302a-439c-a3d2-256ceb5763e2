<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/bg_main">


    <TextView
        android:id="@+id/tv_download"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/text_common"
        android:textSize="100px"
        android:textStyle="normal"
        app:layout_constraintBottom_toTopOf="@id/dialogProgressBar"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="00" />

    <TextView
        style="@style/TVSettingItemContentBlack"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="bottom"
        android:text="@string/suffix_percentage"
        app:layout_constraintBaseline_toBaselineOf="@id/tv_download"
        app:layout_constraintLeft_toRightOf="@id/tv_download" />


    <com.czur.uilib.CZProgressBar
        android:id="@+id/dialogProgressBar"
        android:layout_width="820px"
        android:layout_height="6px"
        android:layout_marginTop="30px"
        app:czPbMax="100"
        app:czPbProgress="0"
        app:layout_constraintBottom_toTopOf="@id/tv_download_size"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_download" />

    <TextView
        android:id="@+id/tv_download_size"
        style="@style/TVSettingItemTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="30px"
        android:gravity="center"
        app:layout_constraintBottom_toTopOf="@id/downloadingTv"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/dialogProgressBar"
        tools:text="0MB/0MB" />

    <TextView
        android:id="@+id/downloadingTv"
        style="@style/TVSettingItemTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="50px"
        android:gravity="center"
        android:text="@string/downloading_ota_title"
        app:layout_constraintBottom_toTopOf="@id/downloadHintTv"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_download_size" />

    <TextView
        android:id="@+id/downloadHintTv"
        style="@style/TVSettingItemContent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10px"
        android:gravity="center"
        android:text="@string/download_hint_title"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/downloadingTv" />

    <com.czur.uilib.btn.CZBackBtn
        android:id="@+id/cancelDownloadBtn"
        android:layout_width="90px"
        android:layout_height="60px"
        android:layout_margin="30px"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>