<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    tools:background="@color/white"
    android:animateLayoutChanges="true"
    tools:ignore="PxUsage,RtlHardcoded,RtlSymmetry">

    <TextView
        android:id="@+id/shortcutChooseTitleTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingLeft="5px"
        android:textColor="@color/text_content_title"
        android:textSize="30px"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="点触快捷键" />

    <View
        android:id="@+id/borderView"
        android:layout_width="300px"
        android:layout_height="224px"
        android:layout_marginTop="25px"
        app:bl_corners_radius="10px"
        app:bl_solid_color="#5879FC"
        android:visibility="invisible"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/shortcutChooseTitleTv"
        tools:background="#5879FC" />

    <View
        android:id="@+id/bgView"
        android:layout_width="0px"
        android:layout_height="0px"
        android:layout_margin="5px"
        app:bl_corners_radius="10px"
        app:bl_solid_color="#F1F3FE"
        app:layout_constraintBottom_toBottomOf="@id/borderView"
        app:layout_constraintLeft_toLeftOf="@id/borderView"
        app:layout_constraintRight_toRightOf="@id/borderView"
        app:layout_constraintTop_toTopOf="@id/borderView"
        tools:background="#F1F3FE" />

    <ImageView
        android:id="@+id/emptyIv"
        android:layout_width="100px"
        android:layout_height="100px"
        android:src="@drawable/img_shortcut_empty"
        app:layout_constraintBottom_toBottomOf="@id/bgView"
        app:layout_constraintLeft_toLeftOf="@id/bgView"
        app:layout_constraintRight_toRightOf="@id/bgView"
        app:layout_constraintTop_toTopOf="@id/bgView" />

    <ImageView
        android:id="@+id/iconIv"
        android:layout_width="100px"
        android:layout_height="100px"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/nameTv"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintLeft_toLeftOf="@id/bgView"
        app:layout_constraintRight_toLeftOf="@id/delIconIv"
        app:layout_constraintTop_toTopOf="@id/bgView"
        app:layout_constraintVertical_chainStyle="packed"
        tools:background="#F88" />

    <TextView
        android:id="@+id/nameTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="15px"
        android:textColor="#393939"
        android:textSize="24px"
        android:textStyle="normal"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/bgView"
        app:layout_constraintLeft_toLeftOf="@id/iconIv"
        app:layout_constraintRight_toRightOf="@id/iconIv"
        app:layout_constraintTop_toBottomOf="@id/iconIv"
        tools:text="应用商店" />

    <ImageView
        android:id="@+id/delIconIv"
        android:layout_width="50px"
        android:layout_height="50px"
        android:layout_marginLeft="20px"
        android:src="@drawable/ic_fav_app_del"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/iconIv"
        app:layout_constraintLeft_toRightOf="@id/iconIv"
        app:layout_constraintRight_toRightOf="@id/bgView"
        app:layout_constraintTop_toTopOf="@id/iconIv" />

    <View
        android:id="@+id/clickView"
        android:layout_width="0px"
        android:layout_height="0px"
        app:layout_constraintBottom_toBottomOf="@id/borderView"
        app:layout_constraintLeft_toLeftOf="@id/borderView"
        app:layout_constraintRight_toRightOf="@id/borderView"
        app:layout_constraintTop_toTopOf="@id/borderView" />

</androidx.constraintlayout.widget.ConstraintLayout>