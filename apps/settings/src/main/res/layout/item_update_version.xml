<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <TextView
        android:id="@+id/versionTitleTv"
        style="@style/TVSettingItemTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="最新版本: v9.7.30-cv"
        app:layout_constraintBottom_toTopOf="@id/versionSubTv"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/versionSubTv"
        style="@style/TVSettingItemContent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16px"
        app:layout_constraintBottom_toTopOf="@id/updateVersionInfoView"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/versionTitleTv"
        tools:text="当前版本: v9.0" />


    <com.czur.starry.device.settings.widget.SettingItemBg
        android:id="@+id/updateVersionInfoView"
        android:layout_width="900px"
        android:layout_height="80px"
        android:layout_marginTop="20px"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/versionSubTv" />

    <TextView
        style="@style/TVSettingItemTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30px"
        android:includeFontPadding="false"
        android:text="@string/check_update_log"
        app:layout_constraintBottom_toBottomOf="@id/updateVersionInfoView"
        app:layout_constraintLeft_toLeftOf="@id/updateVersionInfoView"
        app:layout_constraintTop_toTopOf="@id/updateVersionInfoView" />

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginRight="30px"
        android:src="@drawable/ic_setting_item_next_small"
        app:layout_constraintBottom_toBottomOf="@id/updateVersionInfoView"
        app:layout_constraintRight_toRightOf="@id/updateVersionInfoView"
        app:layout_constraintTop_toTopOf="@id/updateVersionInfoView" />


    <com.czur.uilib.btn.CZButton
        android:id="@+id/bt_updateBtn"
        android:layout_width="300px"
        android:layout_height="80px"
        android:layout_marginBottom="50px"
        android:text="升级"
        android:textSize="30px"
        app:colorStyle="blueWhite"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>