<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="70px"
    tools:ignore="PxUsage"
    tools:viewBindingIgnore="true">

    <View
        android:id="@+id/menuSubSelViewCursor"
        android:layout_width="match_parent"
        android:layout_height="60px"
        android:background="@drawable/drawable_menu_cursor"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/menuGroupIconIv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="20px"
        app:layout_constraintBottom_toBottomOf="@id/menuGroupTitleTv"
        app:layout_constraintRight_toLeftOf="@id/menuGroupTitleTv"
        app:layout_constraintTop_toTopOf="@id/menuGroupTitleTv"
        tools:src="@drawable/ic_group_menu_projector" />

    <TextView
        android:id="@+id/menuGroupTitleTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="100px"
        android:ellipsize="end"
        android:maxWidth="252px"
        android:maxLines="1"
        android:textColor="@color/white"
        android:textSize="30px"
        android:textStyle="bold"
        app:float_tips="投影仪"
        app:float_tips_theme="lightBlue"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="投影仪" />

    <TextView
        android:id="@+id/menuGroupTitleSubTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:textSize="20px"
        android:textStyle="normal"
        app:layout_constraintBaseline_toBaselineOf="@id/menuGroupTitleTv"
        app:layout_constraintLeft_toRightOf="@id/menuGroupTitleTv" />

    <FrameLayout
        android:id="@+id/tabPoint"
        android:layout_width="14px"
        android:layout_height="14px"
        android:layout_marginLeft="6px"
        android:visibility="gone"
        app:layout_constraintLeft_toRightOf="@+id/menuGroupTitleTv"
        app:layout_constraintTop_toTopOf="@id/menuGroupTitleTv">

        <com.czur.starry.device.baselib.widget.CircleView
            android:layout_width="14px"
            android:layout_height="14px"
            android:layout_gravity="center"
            app:circleColor="#FFFFFF" />

        <com.czur.starry.device.baselib.widget.CircleView
            android:layout_width="10px"
            android:layout_height="10px"
            android:layout_gravity="center"
            app:circleColor="#ff5b5c" />
    </FrameLayout>

    <ImageView
        android:id="@+id/menuGroupArrowIv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10px"
        android:src="@drawable/ic_menu_group_unsel"
        app:layout_constraintBottom_toBottomOf="@id/menuGroupTitleTv"
        app:layout_constraintLeft_toRightOf="@id/menuGroupTitleSubTv"
        app:layout_constraintTop_toTopOf="@id/menuGroupTitleTv" />

</androidx.constraintlayout.widget.ConstraintLayout>