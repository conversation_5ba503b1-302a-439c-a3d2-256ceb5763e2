<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/memory_clean_super"
    android:layout_width="match_parent"
    android:layout_height="60px"
    android:descendantFocusability="blocksDescendants">

    <com.czur.uilib.choose.CZMultiStateCheckBox
        android:id="@+id/categoryCb"
        android:layout_width="40px"
        android:layout_height="40px"
        android:layout_marginLeft="40px"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/categoryNameTv"
        style="@style/TVSettingItemTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="15px"
        app:layout_constraintBottom_toBottomOf="@id/categoryCb"
        app:layout_constraintLeft_toRightOf="@+id/categoryCb"
        app:layout_constraintTop_toTopOf="@+id/categoryCb"
        tools:text="应用缓存" />

    <ImageView
        android:id="@+id/expendIv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingLeft="7px"
        android:src="@drawable/ic_clean_storage_group_expand"
        app:layout_constraintBottom_toBottomOf="@id/categoryCb"
        app:layout_constraintLeft_toRightOf="@id/categoryNameTv"
        app:layout_constraintTop_toTopOf="@id/categoryCb" />


    <TextView
        android:id="@+id/categorySizeTv"
        style="@style/TVSettingItemTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/memory_remain_300"
        android:layout_marginRight="40px"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ProgressBar
        android:id="@+id/progressbar"
        style="@style/Widget.AppCompat.ProgressBar"
        android:layout_width="50px"
        android:layout_height="50px"
        android:layout_gravity="center"
        android:indeterminateTint="@color/cz_progress_bar_progress"
        android:indeterminateTintMode="src_atop"
        android:layout_marginRight="40px"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>