<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="320px"
    android:layout_height="80px"
    android:orientation="horizontal"
    tools:ignore="PxUsage,RtlHardcoded">

    <com.czur.starry.device.settings.widget.SettingItemBg
        android:id="@+id/itemSecurityBg"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <TextView
        android:id="@+id/itemSecurityTv"
        style="@style/TVSettingItemTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30px"
        android:gravity="center_vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="无" />

    <ImageView
        android:id="@+id/itemSecurityIv"
        android:layout_width="30px"
        android:layout_height="30px"
        android:layout_marginLeft="70px"
        android:layout_marginRight="30px"
        android:src="@drawable/icon_selected"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>