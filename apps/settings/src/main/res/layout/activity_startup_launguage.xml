<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/primary_blue">

    <TextView
        android:id="@+id/startupLanguageTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="70px"
        android:text="@string/setup_lang_title"
        android:textColor="@color/white"
        android:textSize="48px"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ScrollView
        android:id="@+id/startupScrollView"
        android:layout_width="480px"
        android:layout_height="wrap_content"
        android:fillViewport="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:id="@+id/startupLangLl"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="10px"
            android:paddingBottom="10px"
            app:bl_corners_radius="10px"
            app:bl_solid_color="#0d000000"
            android:orientation="vertical" />

    </ScrollView>

    <com.czur.uilib.btn.CZButton
        android:id="@+id/startupLanguageNextTv"
        style="@style/start_up_btn"
        android:text="@string/startup_next_step"
        android:layout_marginBottom="60px"
        app:colorStyle="PositiveInBlue"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>