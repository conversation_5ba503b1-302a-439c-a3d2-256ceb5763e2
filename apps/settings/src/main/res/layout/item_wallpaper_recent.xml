<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:descendantFocusability="afterDescendants">


    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/imTemplate"
        android:layout_width="396px"
        android:layout_height="223px"
        android:layout_margin="1px"
        android:background="@color/base_bg_color"
        android:scaleType="fitXY"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:round="10px" />

    <ImageView
        android:id="@+id/imShader"
        android:layout_width="398px"
        android:layout_height="225px"
        android:src="@drawable/icon_wallpaper_shader"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvStop"
        android:layout_width="120px"
        android:layout_height="50px"
        android:layout_marginRight="10px"
        android:layout_marginBottom="10px"
        android:background="@drawable/wallpaper_bg_text"
        android:gravity="center"
        android:text="@string/wallpaper_bt_stop"
        android:textColor="#f34949"
        android:textSize="20px"
        app:layout_constraintBottom_toBottomOf="@+id/imTemplate"
        app:layout_constraintRight_toRightOf="parent" />

    <ProgressBar
        android:id="@+id/progressBar"
        style="@style/Widget.AppCompat.ProgressBar"
        android:layout_width="70px"
        android:layout_height="70px"
        android:layout_gravity="center"
        android:indeterminateTint="@color/bg_main"
        android:indeterminateTintMode="src_atop"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>