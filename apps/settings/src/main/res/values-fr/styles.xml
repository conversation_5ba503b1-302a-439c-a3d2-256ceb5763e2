<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="add_image_style">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginLeft">40px</item>
    </style>
    <style name="tv_menu_category">
        <item name="android:layout_width">168px</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:paddingLeft">37px</item>
    </style>
    <style name="view_menu_sub">
        <item name="layout_goneMarginLeft">168px</item>
        <item name="android:includeFontPadding">true</item>
        <item name="android:layout_height">34px</item>
    </style>
    <style name="dialog_qrcode_select_titleTv_style">
        <item name="android:textSize">20px</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:gravity">center</item>
        <item name="android:layout_marginTop">20px</item>
    </style>
    <style name="wallpaper_style_sort_pop_layout">
        <item name="android:layout_width">400px</item>
        <item name="android:layout_height">60px</item>
        <item name="android:paddingLeft">28px</item>
        <item name="android:paddingRight">15px</item>
        <item name="android:orientation">horizontal</item>
        <item name="android:gravity">center</item>
    </style>
    <style name="memory_info_sum_tv_style">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">23px</item>
    </style>
    <style name="new_version_space_style">
        <item name="android:layout_width">0px</item>
        <item name="android:layout_height">80px</item>
    </style>
    <style name="TVSettingItemTitle">
        <item name="android:textColor">@color/text_common</item>
        <item name="android:textSize">26px</item>
        <item name="android:textStyle">bold</item>
    </style>
</resources>