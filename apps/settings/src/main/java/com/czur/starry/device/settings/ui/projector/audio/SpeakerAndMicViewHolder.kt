package com.czur.starry.device.settings.ui.projector.audio

import com.czur.starry.device.baselib.utils.doOnItemClick
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.settings.databinding.FragmentSpeakerMicChooseBinding
import com.czur.starry.device.settings.utils.isBuiltinDevice
import com.czur.uilib.extension.rv.addCornerRadius
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

/**
 * Created by 陈丰尧 on 2025/4/10
 */
class SpeakerAndMicViewHolder(
    private val scope: CoroutineScope,
    val binding: FragmentSpeakerMicChooseBinding,
    val viewModel: SpeakerAndMicChooseViewModel,
) {
    val audioDeviceInfoAdapter = AudioDeviceInfoAdapter()


    private fun FragmentSpeakerMicChooseBinding.initBindingViews() {

    }

    fun initBindingViews() {
        binding.initBindingViews()
    }
}