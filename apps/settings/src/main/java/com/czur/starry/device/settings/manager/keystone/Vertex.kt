package com.czur.starry.device.settings.manager.keystone

import java.lang.Exception

/**
 * Created by 陈丰尧 on 2/3/21
 * 记录梯形矫正的顶点坐标信息
 */
class Vertex {
    var x = 0L
    var y = 0L

    constructor(x: <PERSON>, y: <PERSON>) {
        this.x = x
        this.y = y
    }

    constructor(vertexStr: String) {
        try {
            val v = vertexStr.split(",")
            this.x = v[0].toLong()
            this.y = v[1].toLong()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun toString() = "$x,$y"
}