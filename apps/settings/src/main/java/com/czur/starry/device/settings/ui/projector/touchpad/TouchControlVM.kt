package com.czur.starry.device.settings.ui.projector.touchpad

import android.app.Application
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.IBinder
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asFlow
import androidx.lifecycle.viewModelScope
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.common.Constants.OTA_BASE_URL
import com.czur.starry.device.baselib.network.HttpManager
import com.czur.starry.device.baselib.utils.DifferentLiveData
import com.czur.starry.device.baselib.utils.appContext
import com.czur.starry.device.baselib.utils.data.LiveDataDelegate
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.otalib.OTAHandler
import com.czur.starry.device.settings.ITouchPad
import com.czur.starry.device.settings.ITouchPadStatusCallback
import com.czur.starry.device.settings.model.TouchPadVersionCheckInfo
import com.czur.starry.device.settings.network.api.ITouchVersionServer
import com.czur.starry.device.settings.touchpad.TouchPadInfoManager
import com.czur.starry.device.settings.touchpad.TouchPadService
import com.czur.starry.device.settings.touchpad.loadDockWakeValue
import com.czur.starry.device.settings.touchpad.updateDockWakeValue
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.withContext

/**
 * Created by 陈丰尧 on 2021/10/18
 */
class TouchControlVM(application: Application) : AndroidViewModel(application) {

    companion object {
        private const val TAG = "TouchControlVM"
    }

    val batteryLevelLive = DifferentLiveData(TouchPadInfoManager.BATTERY_DID_NOT_GET)
    var batteryLevel by LiveDataDelegate(batteryLevelLive)
        private set

    val connectedLive = DifferentLiveData(false)
    private var connected by LiveDataDelegate(connectedLive)

    val versionLive = DifferentLiveData("")
    var version: String by LiveDataDelegate(versionLive)
        private set

    /**
     * 触控板升级信息
     */
    val touchPadVersionCheckInfoLive = MutableLiveData<TouchPadVersionCheckInfo?>(null)


    private var iTouchPad: ITouchPad? = null

    /**
     * 进行网络请求使用的
     */
    private val touchNetService: ITouchVersionServer by lazy { HttpManager.getService(BASE_URL = OTA_BASE_URL) }

    lateinit var updateInfo: Deferred<String>

    // 触控板的硬件版本
    private val touchPadHardwareVersionFlow = versionLive.asFlow().map {
        val lastLetter = (it.lastOrNull()?.toString() ?: "").lowercase()
        when {
            TouchPadHardwareVersion.V1.versionStr.contains(lastLetter) -> TouchPadHardwareVersion.V1
            TouchPadHardwareVersion.V2.versionStr.contains(lastLetter) -> TouchPadHardwareVersion.V2
            else -> TouchPadHardwareVersion.UNKNOWN
        }
    }
        .flowOn(Dispatchers.Default)
        .stateIn(viewModelScope, SharingStarted.Eagerly, TouchPadHardwareVersion.UNKNOWN)
    val touchPadHardwareVersion: TouchPadHardwareVersion
        get() = touchPadHardwareVersionFlow.value

    private val conn = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            iTouchPad = ITouchPad.Stub.asInterface(service)

            iTouchPad?.let {
                it.registerStatusCallback(cb)
                it.checkTouchPadStatus()
            }
        }

        override fun onServiceDisconnected(name: ComponentName?) {
            iTouchPad = null
        }

    }

    private val cb = object : ITouchPadStatusCallback.Stub() {

        override fun onConnectChange(connect: Boolean) {
            connected = connect
            if (!connect) {
                // 没有连接触控板
                version = ""
                touchPadVersionCheckInfoLive.postValue(null)
            }
        }

        override fun onBatteryLevelChange(newLevel: Int) {
            batteryLevel = newLevel
        }

        override fun onVersionLevelChange(newVersion: String?) {
            version = newVersion.orEmpty()
        }

    }

    init {
        bindService()

        launch {
            versionLive.asFlow().collect {
                if (it.isEmpty()) {
                    touchPadVersionCheckInfoLive.postValue(null)
                } else {
                    val body = checkUpdateInfo(it)
                    // 0 表示不用升级
                    val needUpdate = body?.update ?: 0 != 0
                    if (needUpdate) {
                        touchPadVersionCheckInfoLive.postValue(body)
                        body?.let {
                            // 提前预加载升级信息
                            updateInfo = async {
                                loadUpdateLog(body.version)
                            }
                        }
                        OTAHandler.newTouchPadVersion = true    // 以页面更新为准
                    } else {
                        touchPadVersionCheckInfoLive.postValue(null)
                        OTAHandler.newTouchPadVersion = false
                    }
                }
            }
        }
    }

    private suspend fun loadUpdateLog(newVersion: String): String {
        logTagD(TAG, "加载新版本的log")
        return withContext(Dispatchers.IO) {
            touchNetService.logInfo(newVersion).body ?: ""
        }
    }


    /**
     * 检查升级信息
     * 如果请求不成功, 就返回null
     */
    private suspend fun checkUpdateInfo(version: String): TouchPadVersionCheckInfo? {
        logTagD(TAG, "检查触控板新版本")
        return withContext(Dispatchers.IO) {
            touchNetService.versionCheck(version).body
        }
    }

    private fun bindService() {
        logTagD(TAG, "绑定蓝牙Service")
        val intent = Intent(appContext, TouchPadService::class.java)
        appContext.bindService(intent, conn, Context.BIND_AUTO_CREATE)
    }

    private fun unBindService() {
        logTagD(TAG, "解绑蓝牙Service")
        iTouchPad?.unregisterStatusCallback(cb)
        appContext.unbindService(conn)
    }


    /**
     * 获取是否放回触控板 默认唤醒
     */
    suspend fun getDockWakeValue(): Boolean = loadDockWakeValue()

    /**
     * 设置是否放回触控板的值
     */
    suspend fun setDockWakeValue(newValue: Boolean) = updateDockWakeValue(newValue)

    override fun onCleared() {
        unBindService()
        super.onCleared()
    }
}