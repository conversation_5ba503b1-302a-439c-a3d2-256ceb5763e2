package com.czur.starry.device.settings.ui.projector.bluetooth

import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.bluetooth.BluetoothProfile
import android.os.Bundle
import android.view.animation.AccelerateDecelerateInterpolator
import androidx.core.animation.doOnCancel
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.closeDefChangeAnimations
import com.czur.starry.device.baselib.utils.doOnItemClick
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.baselib.utils.repeatOnResume
import com.czur.starry.device.baselib.view.dialog.LoadingDialog
import com.czur.starry.device.baselib.view.floating.common.SingleBtnCommonFloat
import com.czur.starry.device.settings.R
import com.czur.starry.device.settings.adapter.BTBindAdapter
import com.czur.starry.device.settings.adapter.BTOtherAdapter
import com.czur.starry.device.settings.base.BaseBindingMenuFragment
import com.czur.starry.device.settings.databinding.FragmentBtKeyboardBinding
import com.czur.starry.device.settings.model.BTBoundDeviceEntity
import com.czur.uilib.extension.rv.addCornerRadius
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * Created by 陈丰尧 on 1/28/21
 */
private const val TAG = "BTKeyboardFragment"

class BTKeyboardFragment : BaseBindingMenuFragment<FragmentBtKeyboardBinding>() {
    private val btDeviceViewModel: BTDeviceViewModel by viewModels()
    private val otherAdapter = BTOtherAdapter()
    private val bindAdapter = BTBindAdapter()

    private val loadingDialog by lazy { LoadingDialog() }

    private val scanAnim by lazy {
        val anim = ObjectAnimator.ofFloat(binding.refreshBtIv, "rotation", 0F, 360F)
        anim.repeatCount = ValueAnimator.INFINITE
        anim.repeatMode = ValueAnimator.RESTART
        anim.duration = 1000
        anim.interpolator = AccelerateDecelerateInterpolator()
        anim.doOnCancel {
            binding.refreshBtIv.rotation = 0F
        }
        anim
    }

    @SuppressLint("MissingPermission")
    override fun FragmentBtKeyboardBinding.initBindingViews() {


        otherBtDevicesRv.addCornerRadius(backgroundColor = requireContext().getColor(R.color.bg_setting_normal))
        bindBtDevicesRv.addCornerRadius(backgroundColor = requireContext().getColor(R.color.bg_setting_normal))

        otherBtDevicesRv.adapter = otherAdapter
        bindBtDevicesRv.adapter = bindAdapter

        otherBtDevicesRv.closeDefChangeAnimations()
        bindBtDevicesRv.closeDefChangeAnimations()

//        // 未连接设备的点击事件
        otherBtDevicesRv.doOnItemClick { vh, view ->
            val pos = vh.bindingAdapterPosition
            val device = otherAdapter.getData(pos)
            btDeviceViewModel.connect(device)
            true
        }
//        // 已绑定设备的点击事件
        bindBtDevicesRv.doOnItemClick { vh, view ->
            val pos = vh.bindingAdapterPosition
            val device = bindAdapter.getData(pos)
            when (view.id) {
                R.id.btDetailIv -> {
                    showBtDetailFloat(device)
                    true
                }
                else -> {
                    if (!device.cachedDevice.isConnected ) {
                        launch {
                            loadingDialog.show()
                            btDeviceViewModel.connect(device.cachedDevice)
                            delay(15000)
                            loadingDialog.dismissImmediate()
                        }
                    }
                    true
                }
            }
        }

        refreshBtIv.setOnClickListener {
            logTagI(TAG, "点击刷新蓝牙设备")
            if (!btDeviceViewModel.isScanning) {
                btDeviceViewModel.startScan()
            } else {
                logTagV(TAG, "正在蓝牙扫描中，不处理")
            }
        }

        lifecycleScope.launch {
            btDeviceViewModel.connectResultFlow.collect { result ->
                if (result != BluetoothProfile.STATE_CONNECTING) {
                    loadingDialog?.dismissImmediate()
                }
            }
        }


    }

    override fun initData(savedInstanceState: Bundle?) {
        repeatOnResume {
            delay(ONE_SECOND)   // 等待蓝牙键盘取消的弹框消失
            // 每次进入页面都会开启蓝牙扫描
            logTagV(TAG, "每次进入页面时开启蓝牙扫描")
            btDeviceViewModel.startScan()
        }
        launch {
            btDeviceViewModel.whiteList = getWhiteList()

            repeatCollectOnResume(btDeviceViewModel.isScanningFlow) {
                if (it) {
                    logTagI(TAG, "开始扫描蓝牙设备")
                    startScanAnim()
                } else {
                    logTagI(TAG, "停止扫描蓝牙设备")
                    stopScanAnim()
                }
            }

            // 可用设备
            repeatCollectOnResume(btDeviceViewModel.availableDevicesFlow) {
                otherAdapter.setData(it)
            }

            // 我的设备
            repeatCollectOnResume(btDeviceViewModel.myDeviceFlow) {
                bindAdapter.setData(it)
            }
        }
    }

    private suspend fun getWhiteList() = withContext(Dispatchers.IO) {
        try {
            btDeviceViewModel.whiteListService.getWhiteList().withCheck().bodyList ?: emptyList()
        } catch (e: Exception) {
            e.printStackTrace()
            emptyList()
        }

    }

    private fun startScanAnim() {
        if (!scanAnim.isRunning) {
            binding.refreshBtIv.rotation = 0F
            scanAnim.start()
        }
    }

    private fun stopScanAnim() {
        scanAnim.cancel()
    }

    /**
     * 展示蓝牙详情对话框
     */
    private fun showBtDetailFloat(device: BTBoundDeviceEntity) {
        BTDetailFloat(device,
            onIgnoreClick = { dialog, cachedDevice ->
                dialog.dismiss()
                btDeviceViewModel.ignoreDevice(cachedDevice)
            }).show()
    }

    override fun onDetach() {
        super.onDetach()
        btDeviceViewModel.stopScan()
    }

    override fun onStop() {
        super.onStop()
    }
}