package com.czur.starry.device.settings.model

import android.graphics.drawable.Drawable
import com.czur.starry.device.baselib.utils.ONE_MB_SI
import com.czur.starry.device.settings.R

/**
 * Created by wangh 22.0516
 */
data class StorageInfoEntity(
    var systemSize: Long = 0L,               // 系统
    var appsSize: Long = 0L,                // 程序
    var mediaSize: Long = 0L,               // 媒体
    var picSize: Long = 0L,                 // 图片
    var documentSize: Long = 0L,            // 文档
    var otherSize: Long = 0L,               // 其他
    var useSize: Long = 0L,               // 已使用内存
    var freeSize: Long = 0L,               // 剩余内存
    var totalSize: Long = 1L,               // 内存
)

data class LocalAppInfo(
    val appName: String,
    val pkgName: String,
    val appIcon: Drawable,
    val versionName: String,
    val versionCode: Int,
    val totalSize: Long,
    var uninstalling: Boolean = false
)

data class CacheInfo(
    var item: Pair<CacheFirstData, MutableList<CacheSecondData>>?,
)

data class CacheFirstData(
    var name: String,
    var size: Long,
    var isChecked: Boolean = true
)

/**
 * 可清理的类别
 */
enum class CleanUpStorageCategory(val nameRes: Int) {
    APP_CACHE(R.string.memory_app_cache),
    APK_FILE(R.string.memory_app_package),
    UNINSTALL_RESIDUE(R.string.memory_app_uninstall_remain),
    RUN_PROGRAM(R.string.memory_app_running)
}

data class CacheSecondData(
    val pkgName: String,
    val name: String,
    val rawSize: Long,
    val isChecked: Boolean = true
) {
    // 应用缓存这里,1MB以下的不显示
    val size: Long
        get() = if (rawSize <= ONE_MB_SI) 0L else rawSize
}
