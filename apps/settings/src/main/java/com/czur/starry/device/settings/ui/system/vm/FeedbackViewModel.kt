package com.czur.starry.device.settings.ui.system.vm

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.hw.StudioSeries
import com.czur.starry.device.settings.manager.LanguageManager
import com.czur.starry.device.settings.model.FeedbackEntity
import com.czur.starry.device.settings.model.FeedbackItem
import com.czur.starry.device.settings.ui.system.feedback.FeedbackCategoryShowEntity
import com.czur.starry.device.settings.ui.system.feedback.FeedbackItemShowEntity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.decodeFromStream

/**
 * Created by 陈丰尧 on 2021/9/27
 */
const val DEF_UPLOAD_LOG = true
private const val TAG = "FeedbackViewModel"

class FeedbackViewModel(application: Application) : AndroidViewModel(application) {

    /**
     * 用户输入的反馈内容
     */
    private val feedbackContentFlow = MutableStateFlow("")

    /**
     * 是否上传日志内容
     */
    private val uploadLogFlow = MutableStateFlow(DEF_UPLOAD_LOG)

    private var contactInformation: String = ""

    private lateinit var defFeedbackCategory: FeedbackEntity

    private val json = Json {
        ignoreUnknownKeys = true
    }
    private val feedbackListFlow = flow {
        // 这里的feedback.json是放在assets目录下的
        // 后期需要对应英文文言的时候, 需要在assets目录下再放翻译的json文件
        // 查询当前语言
        val currentLanguage = LanguageManager.getNameCode(LanguageManager.getCheckedLocale())
        val fileJson = "feedback_$currentLanguage.json"
        val feedbackEntities: MutableList<FeedbackEntity> =
            withContext<List<FeedbackEntity>>(Dispatchers.IO) {
                application.assets.open(fileJson).use(json::decodeFromStream)
            }.toMutableList()

        if (Constants.starryHWInfo.series == StudioSeries) {
            logTagD(TAG, "Studio系列")
            // Studio系列删除对焦,画面的反馈
            feedbackEntities.removeIf {
                it.categoryKey == "Focus" || it.categoryKey == "Image"
            }
        }

        emit(feedbackEntities)
        defFeedbackCategory = feedbackEntities.first()
        selFeedbackCategoryFlow.value = defFeedbackCategory
    }.flowOn(Dispatchers.IO)

    // 当前选择的反馈类型
    private val selFeedbackCategoryFlow = MutableStateFlow(FeedbackEntity.createDefault())
    private val selFeedbackItemListFlow =
        selFeedbackCategoryFlow.map { it.feedbackItems }.flowOn(Dispatchers.IO)
    private val selFeedbackItemsFlow = MutableStateFlow<Set<FeedbackItem>>(emptySet())

    val showCategoryFlow =
        combine(selFeedbackCategoryFlow, feedbackListFlow) { selFeedbackCategory, feedbackList ->
            feedbackList.map {
                FeedbackCategoryShowEntity(
                    it,
                    it.categoryKey == selFeedbackCategory.categoryKey
                )
            }
        }.flowOn(Dispatchers.IO)

    val showItemsFlow = selFeedbackItemListFlow.map { selFeedbackItemList ->
        val selFeedbackItemSet = selFeedbackItemsFlow.value
        selFeedbackItemList.map {
            FeedbackItemShowEntity(
                it,
                selFeedbackItemSet.contains(it),
                refreshTime = it.refreshTime
            )
        }
    }.flowOn(Dispatchers.IO)

    /**
     * 提交按钮是否可用
     */
    val submitEnableFlow = combine(
        feedbackContentFlow,
        uploadLogFlow,
        selFeedbackItemsFlow
    ) { feedbackContent: String, uploadLog: Boolean, selFeedbackItems: Set<FeedbackItem> ->
        feedbackContent.isNotBlank() || uploadLog || selFeedbackItems.isNotEmpty()
    }.distinctUntilChanged()
        .flowOn(Dispatchers.IO)


    fun selectCategory(selectEntity: FeedbackEntity) {
        selFeedbackCategoryFlow.value = selectEntity
    }

    fun selectItem(selectItem: FeedbackItem, select: Boolean) {
        val selFeedbackItemSet = selFeedbackItemsFlow.value.toMutableSet()
        if (select) {
            selFeedbackItemSet.add(selectItem)
        } else {
            selFeedbackItemSet.remove(selectItem)
        }
        selFeedbackItemsFlow.value = selFeedbackItemSet
    }

    fun updateFeedbackContent(content: String) {
        feedbackContentFlow.value = content
    }

    fun updateContactInformation(contactInformation: String) {
        this.contactInformation = contactInformation
    }

    fun updateUploadLog(uploadLog: Boolean) {
        uploadLogFlow.value = uploadLog
    }

    fun getFeedbackContent(): String? {
        return feedbackContentFlow.value.ifEmpty {
            null
        }
    }

    fun getUploadLog(): Boolean {
        return uploadLogFlow.value
    }

    fun getFeedbackItems(): String? {
        return if (selFeedbackItemsFlow.value.isEmpty()) {
            null
        } else {
            selFeedbackItemsFlow.value.joinToString(separator = ",") {
                it.categoryKey + "-" + it.feedbackKey
            }
        }
    }

    fun getContactInformation(): String? {
        return contactInformation.ifEmpty {
            null
        }
    }

    fun resetCategory() {
        if (selFeedbackItemsFlow.value.isNotEmpty()) {// 不变的话 stateflow不调用
            val copy = defFeedbackCategory.copy()
            copy.refreshTime = System.currentTimeMillis() / 1000
            for (i in copy.feedbackItems.indices) {
                val feedbackItem = copy.feedbackItems[i]
                feedbackItem.refreshTime = System.currentTimeMillis() / 1000
            }

            selFeedbackCategoryFlow.value = copy // 重置反馈类型
        } else {
            selFeedbackCategoryFlow.value = defFeedbackCategory // 重置反馈类型
        }

        selFeedbackItemsFlow.value = emptySet() // 重置反馈项
    }


}