package com.czur.starry.device.settings.ui.personalization.wallpaper

import android.graphics.BitmapFactory
import android.os.Bundle
import androidx.core.graphics.scale
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.VersionIndustry
import com.czur.starry.device.baselib.network.download.startDownload
import com.czur.starry.device.baselib.utils.InternetStatus
import com.czur.starry.device.baselib.utils.NetStatusUtil
import com.czur.starry.device.baselib.utils.doOnItemClick
import com.czur.starry.device.baselib.utils.getScreenHeight
import com.czur.starry.device.baselib.utils.getScreenWidth
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.isValidImage
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.saveToFile
import com.czur.starry.device.baselib.utils.toast
import com.czur.starry.device.file.filelib.FileType
import com.czur.starry.device.settings.R
import com.czur.starry.device.settings.app.App
import com.czur.starry.device.settings.base.BaseBindingMenuFragment
import com.czur.starry.device.settings.databinding.FragmentWallpaperListBinding
import com.czur.starry.device.settings.ui.personalization.wallpaper.adapter.WallpaperCustomAdapter
import com.czur.starry.device.settings.ui.personalization.wallpaper.adapter.WallpaperRecentAdapter
import com.czur.starry.device.settings.ui.personalization.wallpaper.bean.CustomImageEntity
import com.czur.starry.device.settings.ui.personalization.wallpaper.bean.FileEntity
import com.czur.starry.device.settings.ui.personalization.wallpaper.dialog.LocalSelectDialog
import com.czur.starry.device.settings.ui.personalization.wallpaper.dialog.ScanQRCodeDialog
import com.czur.starry.device.settings.ui.personalization.wallpaper.utils.CUSTOM_ASSETS
import com.czur.starry.device.settings.ui.personalization.wallpaper.utils.SpacesItemDecoration
import com.czur.starry.device.settings.ui.personalization.wallpaper.utils.addDataLocal
import com.czur.starry.device.settings.ui.personalization.wallpaper.utils.getValueToList
import com.czur.starry.device.settings.ui.personalization.wallpaper.utils.getWallpaperName
import com.czur.starry.device.settings.ui.personalization.wallpaper.utils.setWallpaperSystemProp
import com.czur.starry.device.settings.ui.personalization.wallpaper.vm.DisplayVM
import com.czur.starry.device.settings.ui.personalization.wallpaper.vm.WallpaperViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.coroutines.withContext
import java.io.File
import java.util.concurrent.atomic.AtomicBoolean

/**
 *  author : WangHao
 *  time   :2024/01/15
 */


private const val TAG = "WallpaperFragmentList"
private const val TEMPLATE_MAX_COUNT = 11

class WallpaperFragmentList : BaseBindingMenuFragment<FragmentWallpaperListBinding>() {
    //显示图片加载
    private val wallpaperVM: WallpaperViewModel by viewModels()

    //本地图片选择
    private val displayVM: DisplayVM by viewModels({ requireActivity() })

    private val recentAdapter = WallpaperRecentAdapter()
    private val customAdapter = WallpaperCustomAdapter()

    private var dialog: LocalSelectDialog? = null
    private var dialogQrcode: ScanQRCodeDialog? = null

    //网络监听
    private val netStatusUtil: NetStatusUtil by lazy {
        NetStatusUtil(App.context)
    }
    private var netState = true

    //是否正在处理图片（扫码）
    private var isGettingPic = AtomicBoolean(false)

    //是否正在接收扫码图片
    private var isReceiving = AtomicBoolean(false)

    //循环遍历未接受图片
    private var queryImageJob: Job? = null
    private val uploadImageMutex = Mutex()

    override fun FragmentWallpaperListBinding.initBindingViews() {

        if (Constants.versionIndustry == VersionIndustry.DEVICE_INDUSTRY_ARMY_BUILD) {
            wallpaperTips.gone()
            wallpaperTipsIV.gone()
        }

        wallpaperVM.isShowRecent.observe(viewLifecycleOwner) {
            tvTitleRecent.gone(!it)
            recycleRecent.gone(!it)
        }

        netStatusUtil.startWatching()

        //网络监听
        netStatusUtil.internetStatusLive.observe(requireActivity()) {
            logTagD(TAG, "===连接状态==${it}")
            netState = it == InternetStatus.CONNECT
        }

        //本地图片选择
        displayVM.shareFileLive.observe(viewLifecycleOwner) {
            logTagD(TAG, "=====it=$it")
            if (it == null) return@observe

            launch {
                val imgFile = File(it.absPath)
                if (!imgFile.isValidImage()) {
                    toast(R.string.toast_wallpaper_upload_damaged_image)
                    return@launch
                }
                val srcFile = FileEntity(
                    absPath = it.absPath,
                    fileType = it.fileType,
                    name = it.name,
                    belongTo = it.belongTo
                )
                delay(200)
                //确认添加本地后刷新
                showLocalDialog(srcFile)
            }

        }

        //扫码上传
        wallpaperVM.unReceivedImageLive.observe(viewLifecycleOwner) {
            lifecycleScope.launch {
                uploadImageMutex.withLock {
                    isReceiving.set(true)
                    delay(500)
                    for (i in 0 until it.size) {
                        while (isGettingPic.get()) delay(200)
                        getImageFromUrl(it[i])
                    }
                    isReceiving.set(false)
                }
            }
        }

        recycleRecent.doOnItemClick { _, view ->
            if (R.id.tvStop == view.id) {
                setWallpaperSystemProp(false)
                updateRecent()
            }
            true
        }
        //自定义
        recycleCustom.doOnItemClick(true) { vh, view ->
            val pos = vh.bindingAdapterPosition
            if (pos < 0) return@doOnItemClick false
            when (view.id) {
                R.id.imLocal,
                R.id.tvLocal,
                R.id.layoutLocal -> {
                    uploadFromLocal()
                    true
                }

                R.id.imQrcode,
                R.id.tvQrcode,
                R.id.layoutQrcode -> {
                    uploadFromQRCode()
                    true
                }

                else -> {
                    if (pos == 0) return@doOnItemClick false
                    //展示图片
                    val data = customAdapter.getData(pos)
                    WallpaperPreviewActivity.startWithFilePath(
                        requireContext(),
                        data.copy(),
                    )
                    true
                }
            }

        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        launch {
            //加载模板
            initRecent()

            initCustom()
        }
    }

    override fun onResume() {
        super.onResume()
        //更新最近
        updateRecent()
        //更新自定义
        updateCustom()

        queryImageJob = lifecycleScope.launch(Dispatchers.IO) {
            while (isActive) {
                delay(3000)
                if (!isReceiving.get() && netState) {
                    wallpaperVM.queryUnReceived()
                }

            }
        }
    }

    private fun updateRecent() {
        launch {
            delay(500L)
            recentAdapter.refreshData(wallpaperVM.getRecentData())
        }
    }

    private fun updateCustom() {
        launch {
            customAdapter.refreshData(wallpaperVM.getCustomData())
        }
    }


    override fun onPause() {
        super.onPause()
        queryImageJob?.cancel()
        displayVM.shareFile = null
        wallpaperVM.unReceivedImageLive.value?.clear()
    }

    override fun onDestroy() {
        super.onDestroy()
        netStatusUtil.stopWatching()
    }

    //本地图片上传
    private fun uploadFromLocal() {
        launch {
            val list = getValueToList(CUSTOM_ASSETS)
            if (list.size >= TEMPLATE_MAX_COUNT) {
                toast(R.string.wallpaper_create_temp_filed)
            } else {
                //本地上传
                showLocalDialog()
            }
        }
    }

    //二维码扫描上传
    private fun uploadFromQRCode() {
        launch {
            val list = getValueToList(CUSTOM_ASSETS)
            if (list.size >= TEMPLATE_MAX_COUNT) {
                toast(R.string.wallpaper_create_temp_filed)
            } else if (!netState) {
                toast(R.string.wallpaper_net_not_work)
            } else {
                //扫码上传
                showQRCodeDialog()
            }
        }
    }

    //扫码下载
    private suspend fun getImageFromUrl(entity: CustomImageEntity) = withContext(Dispatchers.IO) {
        logTagD(TAG, "=======getImageFromUrl=$entity")
        isGettingPic.set(true)
        if (customAdapter.itemCount > TEMPLATE_MAX_COUNT) {
            lifecycleScope.launch {
                val id = entity.id
                wallpaperVM.responseReceived(id)
                toast(R.string.wallpaper_create_temp_filed)
                dialogQrcode?.dismiss()
                isGettingPic.set(false)
            }
        } else {
            val url = entity.directory
            val id = entity.id
            var imageFormat = getString(R.string.jpg)
            if (url.contains(getString(R.string.png))) {
                imageFormat = getString(R.string.png)
            }
            val list = getValueToList(CUSTOM_ASSETS)
            val name = getWallpaperName(list)
            val path = name + imageFormat
            val desFile = File(wallpaperVM.wallpaperPath, path)
            if (startDownload(url, desFile)) {
                // 下载完成
                resizeUploadImg(desFile)
                val desc =
                    FileEntity(
                        absPath = desFile.absolutePath,
                        name = name,
                        fileType = FileType.IMAGE
                    )
                logTagD(TAG, "=====desc=absPath=${desc.absPath}")
                logTagD(TAG, "=====desc=name=${desc.name}")
                lifecycleScope.launch {
                    addDataLocal(desc)
                    val data = wallpaperVM.getCustomData()
                    customAdapter.refreshData(data)
                    toast(R.string.str_create_temp_success1)
                    wallpaperVM.responseReceived(id)
                    isGettingPic.set(false)
                }
            } else {
                isGettingPic.set(false)
                logTagD(TAG, "===图片下载失败=")
            }
        }
    }

    /**
     * 将扫码上传的图片缩放到屏幕大小
     */
    private suspend fun resizeUploadImg(imgFile: File) = withContext(Dispatchers.IO) {
        val opt = BitmapFactory.Options().apply {
            inJustDecodeBounds = true
        }
        BitmapFactory.decodeFile(imgFile.absolutePath, opt)
        logTagD(TAG, "===图片宽度=${opt.outWidth}, 高度=${opt.outHeight}")
        if (opt.outWidth != getScreenWidth() || opt.outHeight != getScreenHeight()) {
            // 如果图片尺寸不符合要求，则进行缩放处理
            val bitmap = BitmapFactory.decodeFile(imgFile.absolutePath)
                .scale(getScreenWidth(), getScreenHeight())
            // 保存缩放后的图片
            imgFile.delete()
            imgFile.createNewFile()
            bitmap.saveToFile(imgFile)
        }
    }

    //本地上传dialog
    private fun showLocalDialog(it: FileEntity = FileEntity()) {
        launch {
            if (dialog?.isVisible == true) {
                return@launch
            }

            dialog = LocalSelectDialog.Builder()
                .setSelectClickListener {
                    showFileFragment()
                }.setConfirmClickListener {

                    if (it.absPath == "") return@setConfirmClickListener
                    //复制图片
                    doCopyTask(it)
                }.setImageName(requireContext(), it.name)
                .setImageView(requireContext(), it.absPath)
                .build()
            dialog?.show()
        }
    }

    //扫码上传dialog
    private fun showQRCodeDialog() {
        launch {
            val url = wallpaperVM.getUploadUrl()
            if (url == null) {
                toast(R.string.wallpaper_geturl_failed)
                return@launch
            }
            if (dialogQrcode?.isVisible == true) {
                return@launch
            }
            val qrCode = url.toString() + Constants.SERIAL
            dialogQrcode = ScanQRCodeDialog.Builder()
                .setSelectClickListener {
                    showFileFragment()
                }.setConfirmClickListener {
                    dialogQrcode?.dismiss()
                    dialogQrcode = null
                }.setLink(qrCode)
                .build()
            dialogQrcode?.show()
        }
    }

    //显示文件选择
    private fun showFileFragment() {
        displayVM.setFileType(FileType.IMAGE)
        FileChooseFloatFragment(R.string.choose_folder_screensaver).show()
        dialog?.dismiss()
    }

    private fun doCopyTask(it: FileEntity = FileEntity()) {
        launch {
            uploadImageMutex.withLock {
                delay(500)
                if (customAdapter.itemCount > TEMPLATE_MAX_COUNT) {
                    toast(R.string.wallpaper_create_temp_filed)
                } else {
                    try {
                        val targetDir = wallpaperVM.copyToLocal(it)
                        addDataLocal(targetDir)
                        val data = wallpaperVM.getCustomData()
                        customAdapter.refreshData(data)
                    } catch (e: Exception) {
                        e.printStackTrace()
                        toast(R.string.toast_wallpaper_upload_damaged_image)
                    }

                }
            }
        }
    }

    private suspend fun initRecent() = withContext(Dispatchers.Main) {
        //最近使用
        binding.recycleRecent.layoutManager = GridLayoutManager(context, 3)
        binding.recycleRecent.addItemDecoration(SpacesItemDecoration())

        recentAdapter.setData(requireContext(), wallpaperVM.getRecentData())
        binding.recycleRecent.adapter = recentAdapter
    }

    private suspend fun initCustom() = withContext(Dispatchers.Main) {
        binding.recycleCustom.layoutManager = GridLayoutManager(context, 3)
        binding.recycleCustom.addItemDecoration(SpacesItemDecoration())

        customAdapter.setData(requireContext(), wallpaperVM.getCustomData())
        binding.recycleCustom.adapter = customAdapter
    }
}