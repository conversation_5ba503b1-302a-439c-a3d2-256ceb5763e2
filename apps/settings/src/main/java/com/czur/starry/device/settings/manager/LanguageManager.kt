package com.czur.starry.device.settings.manager

import android.os.LocaleList
import com.android.internal.app.LocalePicker
import com.android.internal.app.LocaleStore
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.utils.SettingHandler
import java.util.Locale

/**
 * Created by 陈丰尧 on 2/7/21
 * 语言相关功能
 */
object LanguageManager {
    const val TAG = "LanguageManager"

    // 提供的系统语言
    val localeList = listOf(
        // 简体中文
        Locale.Builder().setLanguage("zh")
            .setScript("Hans")
            .setRegion("CN")
            .build(),
        // 繁体中文
        Locale.Builder()
            .setLanguage("zh")
            .setRegion("TW")
            .setScript("Hant")
            .build(),
        // 英文
        Locale.US,
        //日语
        Locale.JAPANESE,
        // 俄语
        Locale("ru"),
        // 意大利语
        Locale.ITALIAN,
        //德语
        Locale.GERMAN,
        //韩语
        Locale.KOREA,
        //法语
        Locale.FRANCE,
        //西班牙语
        Locale("es")
    )

    // 初期设定 使用的语言
    val startupLocaleList = localeList

    fun getDisplayNameInUiLang(locale: Locale): String {
        val info = LocaleStore.getLocaleInfo(locale)
        val name = info.fullNameInUiLanguage
        return when {
            SettingHandler.czurLang.webCode == "ko" -> {
                name
            }
            name.contains(" ") -> {
                name.split(" ")[0]
            }
            name.contains("（") -> { // 全角的括号
                name.split("（")[0]
            }
            else -> {
                name
            }
        }
    }

    fun getDisplayName(locale: Locale): String {
        val info = LocaleStore.getLocaleInfo(locale)
        val name = info.fullNameNative

        logTagD(TAG, "---------")

        logTagD(
            TAG,
            "localList: ${info.fullNameNative},${info.fullNameInUiLanguage}"
        )

        logTagD(TAG, "---------")

        return when {
            name.contains(" ") -> {
                name.split(" ")[0]
            }
            name.contains("（") -> { // 全角的括号
                name.split("（")[0]
            }
            else -> {
                name
            }
        }
    }

    fun getNameCode(local: Locale): String {
        val result = when (val language = local.language) {
            "en" -> language
            "ja" -> language
            "ru" -> language
            "it" -> language
            "de" -> language
            "ko" -> language
            "fr" -> language
            "es" -> language
            "zh" -> getChineseType(local)
            else -> ""
        }
        return result
    }

    private fun getChineseType(locale: Locale): String {
        return when (locale.script.lowercase(Locale.getDefault())) {
            "hans" -> "zh"
            "hant" -> "zh_TW"
            else -> ""
        }
    }

    /**
     * 获取当前选中的Locale
     */
    fun getCheckedLocale(): Locale {
        val pickerList = LocalePicker.getLocales()

        return pickerList[0]
    }

    /**
     * 更新系统语言
     */
    fun updateLanguage(locale: Locale) {
        if (getCheckedLocale() == locale) {
            logTagD(TAG, "语言没有改变, 不做任何处理")
            return
        }
        val ll = LocaleList(locale)
        LocaleList.setDefault(ll)
        try {
            LocalePicker.updateLocales(ll)
        } catch (e: Exception) {
            logTagW(TAG, "设置系统语言错误", tr = e)
        }

        logTagD(TAG, "code:${SettingHandler.languageCode}")
    }

    /**
     * 语言是否有所改变
     */
    fun isLanguageChange(locale: Locale): Boolean {
        return getCheckedLocale() != locale
    }

}