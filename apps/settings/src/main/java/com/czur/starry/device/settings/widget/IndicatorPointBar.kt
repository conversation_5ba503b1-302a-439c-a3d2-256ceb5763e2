package com.czur.starry.device.settings.widget

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.RadioButton
import android.widget.RadioGroup
import androidx.core.content.res.ResourcesCompat
import com.czur.starry.device.settings.R
import kotlin.math.max

/**
 * Created by 陈丰尧 on 2023/2/3
 */
class IndicatorPointBar @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null
) : RadioGroup(context, attrs) {
    var pageCount = 0
        set(value) {
            if (field != value) {
                field = value
                updateIndexView()
                if (selPage >= pageCount) {
                    selPage = max(0, pageCount - 1)
                }
            }
        }

    var selPage = 0
        set(value) {
            if (field != value) {
                field = value
                updateSel()
            }
        }

    var onCheckedChanged: ((checkedPos: Int) -> Unit)? = null

    init {
        orientation = HORIZONTAL    // 横向排列
        showDividers = SHOW_DIVIDER_MIDDLE  // 中间设置分割线
        dividerDrawable =
            ResourcesCompat.getDrawable(resources, R.drawable.divider_indicator_point_bar, null)
    }

    private fun updateIndexView() {
        removeAllViews()
        repeat(pageCount) { index ->
            val itemView = LayoutInflater.from(context)
                .inflate(R.layout.view_indicator_point, this, false) as RadioButton
            val id = generateViewId()
            itemView.id = id
            addView(itemView)
            itemView.setOnClickListener {
                onCheckedChanged?.invoke(index)
            }
        }
        updateSel()
    }

    private fun updateSel() {
        (getChildAt(selPage) as? RadioButton)?.isChecked = true
    }
}