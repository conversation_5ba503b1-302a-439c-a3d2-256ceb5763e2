package com.czur.starry.device.settings.ui.projector.bluetooth.net

import com.czur.starry.device.baselib.network.core.MiaoHttpEntity
import com.czur.starry.device.baselib.network.core.MiaoHttpGet
import com.czur.starry.device.settings.ui.projector.bluetooth.WhiteListItem
import java.lang.reflect.Type
import com.google.gson.reflect.TypeToken
/**
 *  author : <PERSON><PERSON><PERSON>
 *  time   :2024/03/28
 */


interface IWhiteListService {

    /**
     * 获取可显示的蓝牙设备列表
     */
    @MiaoHttpGet("/api/ota/bluetoothWhiteList")
    fun getWhiteList(
        type: Type = object :
            TypeToken<List<WhiteListItem>>() {}.type
    ): MiaoHttpEntity<WhiteListItem>
}