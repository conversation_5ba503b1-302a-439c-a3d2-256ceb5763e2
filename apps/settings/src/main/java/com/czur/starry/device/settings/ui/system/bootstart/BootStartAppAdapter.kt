package com.czur.starry.device.settings.ui.system.bootstart

import android.annotation.SuppressLint
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.core.content.ContextCompat
import com.czur.starry.device.baselib.base.BaseDifferAdapter
import com.czur.starry.device.baselib.base.BaseVH
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.settings.R
import com.czur.starry.device.settings.model.BootStartAppEntity

/**
 * Created by 陈丰尧 on 2023/2/3
 */
@SuppressLint("NotifyDataSetChanged")
class BootStartAppAdapter : BaseDifferAdapter<BootStartAppEntity>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseVH =
        BaseVH(R.layout.item_boot_start_app, parent).apply {
            itemView.setOnHoverListener { _, event ->
                val markView = getView<ImageView>(R.id.appIconHoverIv)
                when (event.action) {
                    android.view.MotionEvent.ACTION_HOVER_ENTER -> {
                        // 只有当元素没有被选中时，才会显示悬停效果
                        val data = getData(bindingAdapterPosition)
                        if (!data.isSelect) {
                            val drawable = ContextCompat.getDrawable(
                                context,
                                R.drawable.mark_hover_boot_start_app
                            )
                            markView.setImageDrawable(drawable)
                            markView.show()
                        }
                    }

                    android.view.MotionEvent.ACTION_HOVER_EXIT -> {
                        markView.gone()
                    }
                }
                true
            }
        }

    override fun areItemsTheSame(
        oldItem: BootStartAppEntity,
        newItem: BootStartAppEntity
    ): Boolean {
        return oldItem.pkgName == newItem.pkgName
    }

    override fun bindViewHolder(holder: BaseVH, position: Int, itemData: BootStartAppEntity) {
        holder.setImgDrawable(itemData.appIcon, R.id.appIconIV)
        holder.visible(itemData.isSelect, R.id.appIconSelectIv)
        if (itemData.isSelect) {
            holder.getView<View>(R.id.appIconHoverIv).gone()
        }
    }
}