package com.czur.starry.device.settings.model

/**
 * Created by 陈丰尧 on 1/27/21
 */
data class WifiInfoEntity(
    val ssid: String,               // 信道名称
    val levelRaw: Int,                 // 信号强度
    val locked: Boolean,            // 是否是加密的
    val bssid:String,               // bssid
    var connecting: Boolean = false, // 是否正在连接
    var save: Boolean = false, // 是否保存
    var isConnectedFailed: Boolean = false //是否连接失败
) : Comparable<WifiInfoEntity> {
    override fun compareTo(other: WifiInfoEntity): Int {
        // 是否连接
        var result = connecting.compareTo(other.connecting)
        // 保存有先
        if (result == 0) {
            result = save.compareTo(other.save)
        }
        // 信号强度
        if (result == 0) {
            result = levelRaw.compareTo(other.levelRaw)
        }

        // ssid名称
        if (result == 0) {
            result = -ssid.compareTo(other.ssid)
        }

        return result
    }
}
