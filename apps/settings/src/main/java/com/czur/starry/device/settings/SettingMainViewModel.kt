package com.czur.starry.device.settings

import android.app.Application
import android.content.Intent
import androidx.lifecycle.AndroidViewModel
import com.czur.czurutils.log.LogLevel
import com.czur.czurutils.log.logIntent
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.common.BootParam.BOOT_KEY_PAGE_MENU_NAME
import com.czur.starry.device.baselib.common.BootParam.BOOT_KEY_PAGE_MENU_NAVIGATE
import com.czur.starry.device.baselib.common.BootParam.BOOT_KEY_SETTING_PAGE_MENU_KEY
import com.czur.starry.device.settings.utils.MenuUtil
import com.czur.starry.device.settings.utils.SubMenu
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.filterNotNull

/**
 * Created by 陈丰尧 on 2024/7/25
 */
private const val TAG = "SettingMainViewModel"

class SettingMainViewModel(application: Application) : AndroidViewModel(application) {
    private var _currentSubMenuFlow = MutableStateFlow<SubMenu?>(null)
    val currentSubMenuFlow = _currentSubMenuFlow.filterNotNull()

    private var _currentViewNavigateFlow = MutableStateFlow<String?>(null)
    var currentViewNavigateFlow = _currentViewNavigateFlow.filterNotNull()


    private val menuUtil = MenuUtil()

    fun handleIntent(intent: Intent) {
        val subMenuKey = intent.getStringExtra(BOOT_KEY_SETTING_PAGE_MENU_KEY) ?: ""
        val subMenu =
            menuUtil.getSubMenuByKey(subMenuKey) ?: menuUtil.menuSheet.first().subMenus.first()
        _currentSubMenuFlow.value = subMenu

        handleNewIntent(intent)

    }

    private fun handleNewIntent(intent: Intent) {
        intent.getStringExtra(BOOT_KEY_PAGE_MENU_NAME)?.let {
            logTagD(TAG, "intent数据改变，BOOT_KEY_PAGE_MENU_NAME = $it")
            val subMenu =
                menuUtil.getSubMenuByName(it) ?: menuUtil.menuSheet.first().subMenus.first()
            _currentSubMenuFlow.value = subMenu
        }

        intent.getStringExtra(BOOT_KEY_PAGE_MENU_NAVIGATE)?.let {
            logTagD(TAG, "intent数据改变，BOOT_KEY_PAGE_MENU_NAVIGATE = $it")
            _currentViewNavigateFlow.value = it
        }
    }

    fun onSubMenuSel(subMenu: SubMenu) {
        _currentSubMenuFlow.value = subMenu
    }

    fun onNavigateReset() {
        _currentViewNavigateFlow.value = null
    }
}