package com.czur.starry.device.settings.model

import com.czur.starry.device.bluetoothlib.bluetooth.CachedBluetoothDevice

/**
 * Created by 陈丰尧 on 2022/11/28
 */
data class BTBoundDeviceEntity(
    val batteryLevel: Int,
    val cachedDevice: CachedBluetoothDevice
) {
    companion object {
        const val BATTERY_LEVEL_NONE = -1
    }

    val name: String
        get() = cachedDevice.name
    val address: String
        get() = cachedDevice.device.address
}