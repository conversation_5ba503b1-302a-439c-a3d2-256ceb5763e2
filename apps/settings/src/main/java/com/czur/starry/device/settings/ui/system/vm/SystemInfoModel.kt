package com.czur.starry.device.settings.ui.system.vm

import android.app.ActivityManager
import android.app.Application
import android.content.Context
import android.os.Build
import android.text.format.Formatter
import androidx.lifecycle.AndroidViewModel
import com.czur.starry.device.baselib.base.CZURAtyManager
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.VersionIndustry
import com.czur.starry.device.baselib.common.hw.StarryModel
import com.czur.starry.device.baselib.utils.DifferentLiveData
import com.czur.starry.device.baselib.utils.ONE_GB
import com.czur.starry.device.baselib.utils.appContext
import com.czur.starry.device.baselib.utils.fw.proxy.SystemManagerProxy
import com.czur.starry.device.settings.manager.NetManager
import com.czur.starry.device.settings.manager.PrivateStorageInfo
import com.czur.starry.device.sharescreen.esharelib.util.isEShareActive
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * Created by 陈丰尧 on 3/8/21
 */
class SystemInfoModel(application: Application) : AndroidViewModel(application) {
    private val systemManager: SystemManagerProxy by lazy {
        SystemManagerProxy()
    }
    private val activityManager: ActivityManager by lazy {
        application.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
    }

    // 固件版本
    val fwVersion: String = Constants.FIRMWARE_NAME

    // 存储容量
    val storeValue = DifferentLiveData<Pair<String, String>>()

    // wifi mac地址
    val wifiMac = DifferentLiveData<String>()

    val ethernetMac = DifferentLiveData<String>()

    val serialNo = DifferentLiveData<String>()

    val deviceTypeNameLive = DifferentLiveData<StarryModel>()

    /**
     * 宜享是否激活
     */
    val eShareActiveFlow = MutableStateFlow(false)

    // 内存信息
    private val memSizeFlow = MutableStateFlow(4 * ONE_GB)
    val memSizeStrFlow = memSizeFlow.map {
        Formatter.formatShortFileSize(application, it)
    }.map {
        // 格式化, 将小数点后统一修改为0 例如6.2修改为6.0
        it.replace(Regex("\\.\\d"), ".0")
    }


    suspend fun initData() =
        withContext(Dispatchers.Default) {
            launch {
                // 获取容量信息
                val info = PrivateStorageInfo.getInstance()
                val sizeInfo = Pair(
                    Formatter.formatFileSize(CZURAtyManager.appContext, info.totalBytes),
                    Formatter.formatFileSize(CZURAtyManager.appContext, info.freeBytes),
                )
                storeValue.postValue(sizeInfo)
            }
            launch {
                // 获取wifi mac地址
                if (Constants.versionIndustry != VersionIndustry.DEVICE_INDUSTRY_ARMY_BUILD) {
                    val macAddr = NetManager.getWIFIMacAddress()
                    wifiMac.postValue(macAddr.trim())
                }
            }

            launch {
                // 获取有线网mac地址
                ethernetMac.postValue(systemManager.getEthernetMac().trim())
            }

            launch {
                // 获取SN
                serialNo.postValue(Constants.SERIAL)
                // 获取设备信息
                deviceTypeNameLive.postValue(Constants.starryHWInfo.series.model)
            }

            launch {
                // 获取宜享是否激活
                refreshEShareActive()
            }

            launch {
                // 获取内存大小
                val memInfo = ActivityManager.MemoryInfo()
                activityManager.getMemoryInfo(memInfo)
                memSizeFlow.value = memInfo.totalMem
            }
        }

    suspend fun refreshEShareActive() {
        eShareActiveFlow.value = isEShareActive(appContext)
    }
}