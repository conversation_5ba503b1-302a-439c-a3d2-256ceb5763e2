package com.czur.starry.device.settings.utils.datetime

import android.content.Context
import com.czur.starry.device.settings.R
import com.czur.starry.device.settings.model.TimeZoneEntity
import com.github.promeg.pinyinhelper.Pinyin
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Created by 陈丰尧 on 2023/6/20
 */

suspend fun getSortedTimeZoneList(context: Context): List<TimeZoneEntity> =
    withContext(Dispatchers.IO) {
        val list = getTimeZoneList(context)
        list.sortedBy { it.pinyin } // 按拼音排序
    }

suspend fun getTimeZoneList(context: Context): List<TimeZoneEntity> = withContext(Dispatchers.IO) {
    ZoneGetter.getZonesList(context).map {
        val name = it["name"] as String
        val gmt = it["gmt"] as String
        val id = it["id"] as String
        val offset = it["offset"] as Int
        val pinyin = Pinyin.toPinyin(name, "")
        TimeZoneEntity(
            id = id, name = name, pinyin = pinyin, gmt = gmt, offset = offset
        )
    }
}