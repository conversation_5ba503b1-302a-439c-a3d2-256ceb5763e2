package com.czur.starry.device.settings.ui.personalization.backdrop

import android.app.Application
import android.app.WallpaperManager
import android.graphics.Bitmap
import android.os.Environment
import androidx.core.graphics.drawable.toBitmap
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.czur.czurutils.global.globalAppCtx
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.network.HttpManager
import com.czur.starry.device.baselib.utils.data.NullableLiveDataDelegate
import com.czur.starry.device.settings.model.BackdropListItem
import com.czur.starry.device.settings.model.BackdropTag
import com.czur.starry.device.settings.model.BackdropTagEntity
import com.czur.starry.device.settings.model.BackdropType
import com.czur.starry.device.settings.ui.personalization.wallpaper.bean.CustomImageEntity
import com.czur.starry.device.settings.ui.personalization.wallpaper.bean.FileEntity
import com.czur.starry.device.settings.ui.personalization.wallpaper.`interface`.InnerServices
import com.czur.starry.device.settings.ui.personalization.wallpaper.utils.copyTask
import com.czur.starry.device.settings.ui.personalization.wallpaper.utils.getScreenDropName
import com.czur.starry.device.settings.ui.personalization.wallpaper.vm.DisplayVM
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.withContext
import java.io.File

/**
 * Created by 陈丰尧 on 2024/7/23
 */

private const val TAG = "ScreenBackdropViewModel"

class ScreenBackdropViewModel(application: Application) : AndroidViewModel(application) {

    companion object {
        //扫码上传二维码url拼接
        private const val BACKDROP_SN = "wallpaper?sn="
        private const val IMAGE_TYPE_BACKDROP = 3 //1：欢庆屏保，2：唤醒屏保， 3：壁纸
    }

    private val wallpaperManager: WallpaperManager by lazy(LazyThreadSafetyMode.NONE) {
        WallpaperManager.getInstance(globalAppCtx)
    }

    val customPath by lazy {
        File(
            Environment.getExternalStoragePublicDirectory(
                Environment.DIRECTORY_PICTURES
            ), CUSTOM_ASSETS
        ).also{
            if (!it.exists()) {
                it.mkdir()
            }
        }
    }

    // 当前壁纸
    private val _currentScreenBackdropBmpFlow = MutableStateFlow<Bitmap?>(null)
    val currentScreenBackdropBmpFlow = _currentScreenBackdropBmpFlow.filterNotNull()

    // 全部tag
    private val _allBackdropTagFlow = MutableStateFlow(BackdropTag.allBackdropTag)
    val allBackdropTagFlow = _allBackdropTagFlow.asStateFlow()

    // 选中的tag
    private val selectedTagFlow = MutableStateFlow<BackdropTagEntity>(BackdropTag.All)
    val selectedTag: BackdropTagEntity
        get() = selectedTagFlow.value

    // 壁纸前选中的tag
    var lastSelectTab = 0

    val unReceivedImageLive: LiveData<MutableList<CustomImageEntity>> = MutableLiveData()
    private var unReceivedImage by NullableLiveDataDelegate(unReceivedImageLive, null)

    suspend fun getCurrentScreenBackdropBmp() = withContext(Dispatchers.Default) {
        wallpaperManager.drawable?.let {
            val wallpaperBitmap = it.toBitmap()
            _currentScreenBackdropBmpFlow.value = wallpaperBitmap
        }
    }

    // 更新选中的tag
    fun updateSelectedTag(selIndex: Int) {
        lastSelectTab = selIndex
        selectedTagFlow.value = allBackdropTagFlow.value[selIndex]
    }

    // 当前选择的是否是自定义tag
    fun currentTagIsCustom() = allBackdropTagFlow.value[lastSelectTab] == BackdropTag.Custom

    suspend fun getCurrentClickTagData() = withContext(Dispatchers.IO)  {
        val currentList = getAllData()
        if (selectedTag == BackdropTag.Custom) {
            currentList.add(0, BackdropListItem(
                fileType = BackdropType.CUSTOM,
                customEntity = FileEntity())
            )
        }
        when(selectedTag) {
            BackdropTag.All -> currentList
            BackdropTag.Custom -> currentList.filter { it.fileType == BackdropType.CUSTOM }
            else -> currentList.filter { it.fileType == BackdropType.FIXED && it.fixedEntity?.tag == selectedTag }
        }
    }

    //本地复制
    suspend fun copyToLocal(it: FileEntity) = withContext(Dispatchers.IO) {
        //复制图片
        val name = getScreenDropName(getNormalCustomData())
        val cName =
            "$name.${it.name.substringAfterLast('.', "")}"
        val desc =
            FileEntity(
                absPath = "${customPath}/$cName",
                name = name,
                fileType = it.fileType
            )

        logTagD(TAG, "======absPath==${desc.absPath}")
        logTagD(TAG, "======copyToLocal==${desc.name}")
        doCopy(it, desc)
    }

    //复制
    private suspend fun doCopy(
        srcFile: FileEntity,
        targetFile: FileEntity,
    ) = withContext(Dispatchers.IO) {

        mkRootDir()
        val copyTask = copyTask(srcFile)
        val sumSizeMap = copyTask.computeSumSize(arrayListOf(srcFile))
        copyTask.doCopy(
            srcFile,
            targetFile,
            sumSizeMap[srcFile.absPath] ?: 0L,
        ) {}
        targetFile
    }

    fun mkRootDir() {
        if (!DisplayVM.rootDir.exists()) {
            DisplayVM.rootDir.mkdirs()
        }
    }

    // 查询未接收图片
    suspend fun queryUnReceived() = withContext(Dispatchers.IO) {
        try {
            val entity =
                HttpManager.getService<InnerServices>(Constants.OTA_BASE_URL)
                    .getUnreceivedImage(Constants.SERIAL, IMAGE_TYPE_BACKDROP)
            logTagV(TAG, "entity==${entity.code}")
            if (entity.isSuccess) {
                if (entity.bodyList.size > 0) {
                    unReceivedImage = entity.withCheck().bodyList
                }else {
                    unReceivedImage?.clear()
                }
                logTagV(TAG, "entity.withCheck().body==${entity.withCheck().bodyList}")
            } else {
                logTagD(TAG, "responseRecived失败")
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    // 响应收到图片URL
    suspend fun responseReceived(id: String) = withContext(Dispatchers.IO) {
        try {
            val entity =
                HttpManager.getService<InnerServices>(Constants.OTA_BASE_URL)
                    .responseImageReceived(id)
            logTagD(TAG, "entity==" + entity.code)
            if (entity.isSuccess) {
                logTagD(TAG, "responseRecived成功")
            } else {
                logTagD(TAG, "responseRecived失败")
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    // 获取扫码上传url
    suspend fun getUploadUrl() = withContext(Dispatchers.IO) {
        try {
            val entity =
                HttpManager.getService<InnerServices>(Constants.BASE_URL)
                    .getImageUploadUrl()
            logTagD(TAG, "entity==" + entity.code)
            if (entity.isSuccess) {
                logTagD(TAG, "getUploadUrl成功")
                //过滤末尾#符号后拼接地址
                val url = entity.body.toString().trimEnd('#') + BACKDROP_SN
                url
            } else {
                logTagD(TAG, "getUploadUrl失败")
                null
            }

        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }
}