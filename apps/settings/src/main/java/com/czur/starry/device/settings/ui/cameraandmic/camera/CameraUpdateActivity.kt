package com.czur.starry.device.settings.ui.cameraandmic.camera

import android.annotation.SuppressLint
import android.content.ComponentName
import android.content.Intent
import android.content.ServiceConnection
import android.os.Bundle
import android.os.IBinder
import android.view.KeyEvent
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.base.v2.aty.CZViewBindingAty
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.toast
import com.czur.starry.device.otalib.OTAHandler
import com.czur.starry.device.otalib.OTAHandler.cameraUpgradeSuccess
import com.czur.starry.device.otalib.OTAHandler.cameraUpgradeSuccessLive
import com.czur.starry.device.settings.R
import com.czur.starry.device.settings.databinding.ActivityCameraUpdateBinding
import com.czur.starry.device.settings.ui.cameraandmic.UPDATE_DELAY_TIME
import com.czur.starry.device.settings.ui.cameraandmic.UPDATE_TOTAL_TIME
import com.czur.starry.device.update.IUpdateManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import java.io.File

/**
 *  author : WangHao
 *  time   :2023/11/08
 */


class CameraUpdateActivity : CZViewBindingAty<ActivityCameraUpdateBinding>() {
    companion object {
        private const val TAG = "CameraUpdateActivity"
        private const val ACTION_UPDATE_SERVICE = "android.starry.update.service"
        private const val PACKAGE_UPDATE_SERVICE = "com.czur.starry.device.update"
    }

    private var updateManager: IUpdateManager? = null
    private var updateProcess: Int = 0
    private var delayTime = 1000L
    private var totalTime = 2 //默认升级时间2分钟


    @SuppressLint("SuspiciousIndentation")
    override fun ActivityCameraUpdateBinding.initBindingViews() {
        val file = File(Constants.PATH_SDCARD_OTA_CAMERA)
        if (file.exists()) {
            delayTime = intent.getLongExtra(UPDATE_DELAY_TIME,1000)
            totalTime = intent.getIntExtra(UPDATE_TOTAL_TIME,2)
            binding.warnHintTv.text = getString(R.string.camera_upgrade_warning_content, totalTime)
        } else {
            finishSelf(false)
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        launch {
            bindService()
        }

        cameraUpgradeSuccessLive.observe(this) {
            //这里只回调失败，成功默认按固定时间结束即可
            if (!it) {
                finishSelf(it)
                cameraUpgradeSuccess = true //这里是失败后重新置回默认值
            }

        }

    }

    override fun onDestroy() {
        super.onDestroy()
        unbindService()
    }


    private fun startDelay() = launch {
        //由于没有提供升级进度接口，目前按升级文件大小写成固定时间，执行完则成功。
        while (updateProcess < 100) {
            delay(delayTime)
            startUpdateProcess(updateProcess + 1)
        }
        finishSelf(true)
    }

    private fun finishSelf(success: Boolean) {
        if (success) {
            //升级成功当前camera版本号置为新版本号
            OTAHandler.currentCameraVersion = OTAHandler.newCameraVersion
            toast(R.string.camera_upgrade_success_toast)
        } else {
            toast(R.string.camera_upgrade_failed_toast)
        }
        OTAHandler.newCameraVersion = "null"
        File(Constants.PATH_SDCARD_OTA_CAMERA).delete()
        finish()
    }

    private fun startUpdateProcess(progress: Int) {
        updateProcess = progress
        val showProgress = if (progress < 0) {
            0
        } else if (progress > 99) {
            99
        } else {
            progress
        }

        binding.updateProgress.progress = showProgress.toLong()

        val progressNum = if (showProgress < 10) {
            "0${showProgress}"
        } else {
            showProgress.toString()
        }
        binding.updateNumTv.text = progressNum
    }


    private suspend fun bindService() = withContext(Dispatchers.IO) {
        val intent = Intent()
        intent.action = ACTION_UPDATE_SERVICE
        intent.`package` = PACKAGE_UPDATE_SERVICE
        bindService(intent, mUpdateServiceConnection, BIND_AUTO_CREATE)
    }

    private fun unbindService() {
        unbindService(mUpdateServiceConnection)
    }

    private val mUpdateServiceConnection = object : ServiceConnection {
        override fun onServiceDisconnected(name: ComponentName?) {
            updateManager = null
        }

        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            updateManager = IUpdateManager.Stub.asInterface(service)
            if (updateManager != null) {
                updateManager!!.upgradeCamera()
                logTagD(TAG, "====upgradeCamera=")
                startDelay()
            }
        }
    }

    /**
     * 拦截返回键
     */
    override fun onInterceptKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        return keyCode == KeyEvent.KEYCODE_BACK
    }
}