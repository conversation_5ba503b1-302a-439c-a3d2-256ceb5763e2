package com.czur.starry.device.settings.ui.personalization.wallpaper.model


import com.czur.starry.device.settings.app.App.Companion.app
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancel
import java.util.concurrent.atomic.AtomicReference

/**
 *
 */
object ModelManager : Model() {
    private var scope = CoroutineScope(Job())

    private var _shareModel = AtomicReference<ShareModel>()

    private inline fun <T> getModel(localProp: AtomicReference<T>, instanceBlock: () -> T): T {
        while (true) {
            val current = localProp.get()
            if (current != null) {
                return current
            }
            val newInstance = instanceBlock()
            if (localProp.compareAndSet(null, newInstance)) {
                return newInstance
            }
        }
    }

    val shareModel: ShareModel
        get() = getModel(_shareModel) { ShareModel(app) }

    override fun doClear() {
        shareModel.doClear()


        _shareModel.set(null)

        scope.cancel()
        scope = CoroutineScope(Job())
    }


}