package com.czur.starry.device.settings.widget

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatImageView
import com.czur.starry.device.settings.R

/**
 * Created by 陈丰尧 on 2022/11/28
 */
class BTBatteryView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0,
) : AppCompatImageView(context, attrs, defStyleAttr) {

    private companion object {
        private const val BATTERY_MAX_HEIGHT = 23F
        private const val BATTERY_WIDTH = 8F
        private const val BATTERY_PADDING_BOTTOM = 3F
    }

    var batteryLevel: Int = 100

    private val paint = Paint().apply {
        color = Color.WHITE
        style = Paint.Style.FILL
    }

    init {
        setImageResource(R.drawable.bt_battery_bg)
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        if (batteryLevel > 0) {
            val batteryPadHorizontal = (width - BATTERY_WIDTH) / 2F
            val batteryHeight = batteryLevel / 100F * BATTERY_MAX_HEIGHT
            val batteryTop = height - batteryHeight - BATTERY_PADDING_BOTTOM
            canvas.drawRoundRect(
                batteryPadHorizontal,
                batteryTop,
                width - batteryPadHorizontal,
                height - BATTERY_PADDING_BOTTOM,
                0.5F,
                0.5F,
                paint
            )
        }
    }
}