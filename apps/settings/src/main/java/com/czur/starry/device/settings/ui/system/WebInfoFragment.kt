package com.czur.starry.device.settings.ui.system

import android.graphics.Color
import android.os.Bundle
import android.webkit.WebResourceError
import android.webkit.WebResourceRequest
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.core.os.bundleOf
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.utils.WebViewUtil
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.settings.base.BaseBindingMenuFragment
import com.czur.starry.device.settings.databinding.FragmentWebInfoBinding

/**
 * Created by 陈丰尧 on 3/8/21
 * 法律信息页面
 */
class WebInfoFragment : BaseBindingMenuFragment<FragmentWebInfoBinding>() {
    companion object {
        private const val TAG = "WebInfoFragment"

        private const val KEY_URL = "webUrl"
        private const val KEY_TITLE = "title"

        private const val TEXT_COLOR_KEY = "textColor"

        fun getInstance(webUrl: String, title: String): WebInfoFragment {
            return WebInfoFragment().apply {
                arguments = bundleOf(
                    KEY_URL to webUrl,
                    KEY_TITLE to title
                )
            }
        }
    }

    private var webUrl: String = ""
    private var title: String = ""
    override fun setCustomTitle(): String = title

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        WebViewUtil.hookWebView()

        arguments?.let {
            webUrl = it.getString(KEY_URL) ?: ""
            title = it.getString(KEY_TITLE) ?: ""
        }
    }


    override fun FragmentWebInfoBinding.initBindingViews() {
        initWebView()

        webview.settings.javaScriptEnabled = true
        webview.settings.layoutAlgorithm = WebSettings.LayoutAlgorithm.NORMAL
        webview.settings.textZoom = 150
        webview.setBackgroundColor(Color.TRANSPARENT)
        webview.setLayerType(WebView.LAYER_TYPE_SOFTWARE, null)

        backBtn.setOnBackClickListener {
            webview.stopLoading()
            pressBackKey()
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        val withColorUrl = addColorParams(webUrl)
        binding.webview.loadUrl(withColorUrl)
        logTagV(TAG, "withColorUrl:${withColorUrl}")
    }

    private fun addColorParams(srcUrl: String): String {
        // 这里不能用Uri 添加参数的api, url有#, 会被截断
        return if (srcUrl.contains("?")) {
            srcUrl + "&${TEXT_COLOR_KEY}=${393939}"
        } else {
            srcUrl + "?${TEXT_COLOR_KEY}=${393939}"
        }
    }

    private fun initWebView() {
        binding.webview.setBackgroundColor(0)

        val webViewClient = object : WebViewClient() {

            override fun onReceivedError(
                view: WebView,
                request: WebResourceRequest,
                error: WebResourceError
            ) {
                super.onReceivedError(view, request, error)
                val host = request.url.host ?: ""
                if (host.contains("czur", true)) {
                    // 只有加载 host有czur的页面出错时才会显示错误信息
                    if (binding.progressBar != null) {
                        binding.progressBar.gone()
                        processError()
                    }
                }
            }

            override fun onPageFinished(view: WebView?, url: String?) {
                super.onPageFinished(view, url)
                if (binding.progressBar != null) {
                    binding.progressBar.gone()
                }
            }
        }
        binding.webview.setWebViewClient(webViewClient)
    }

    private fun processError() {
        binding.webview.gone()
        binding.errorLayout.show()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        logTagD(TAG,"onDestroyView")
        binding.webview.destroy()
        binding.webInfoLayout.removeView(binding.webview)
    }

}