package com.czur.starry.device.settings.startup

import android.view.KeyEvent
import android.view.inputmethod.EditorInfo
import androidx.core.widget.doAfterTextChanged
import androidx.fragment.app.viewModels
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.base.v2.fragment.CZViewBindingFragment
import com.czur.starry.device.baselib.utils.keyboard.focusAndShowKeyboard
import com.czur.starry.device.baselib.utils.keyboard.keyboardHide
import com.czur.starry.device.settings.R
import com.czur.starry.device.settings.databinding.FragmentStartupWifiPwdBinding

/**
 * Created by 陈丰尧 on 2/25/21
 */
class StartUpWifiPwdFragment : CZViewBindingFragment<FragmentStartupWifiPwdBinding>() {
    companion object {
        private const val TAG = "StartUpWifiPwdFragment"
    }

    private val model: StartUpWifiVM by viewModels({ requireActivity() })
    var selSSID: String = ""

    override fun FragmentStartupWifiPwdBinding.initBindingViews() {
        model.selBssid.observe(viewLifecycleOwner) {
            val ssid = model.getSSID(it)
            selSSID = ssid
            if (ssid.isNotEmpty()) {
                startUpSSIDTv.text = ssid
            } else {
                startUpSSIDTv.setText(R.string.str_startup_wifi_sel_unavailable)
            }


            clearLastPwd()
        }
        startUpPwdEt.doAfterTextChanged {
            val password = it?.toString() ?: ""
            model.setPwd(password)
        }

        startUpShowPwdIv.bindToEditText(startUpPwdEt)

        startUpPwdEt.setOnEditorActionListener { _, actionId, event ->
            if (actionId == EditorInfo.IME_ACTION_DONE  // 输入法软键盘的发送按钮
                || (event?.keyCode == KeyEvent.KEYCODE_ENTER && event.action == KeyEvent.ACTION_DOWN) // 键盘回车键
            ) {
                logTagV(TAG, "收起键盘")
                startUpPwdEt.keyboardHide()
                if (!model.pwd.value.isNullOrEmpty() && selSSID.isNotEmpty()) {
                    (activity as? StartUpWifiActivity)?.joinWifi()
                }
                true
            } else {
                true
            }
        }
    }

    /**
     * 显示软键盘
     */
    fun showKeyBoard() {
        binding.startUpPwdEt.focusAndShowKeyboard()
    }

    fun clearLastPwd() {
        binding.startUpPwdEt.setText("")
    }
}