package com.czur.starry.device.settings.provider

import android.content.ContentValues
import android.content.Context
import android.content.UriMatcher
import android.database.Cursor
import android.database.MatrixCursor
import android.net.Uri
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.intPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import com.czur.czurutils.base.CZURContentProvider
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.handler.createDefCorruptionHandler
import com.czur.starry.device.baselib.utils.SettingHandler.AUTHORITY
import com.czur.starry.device.baselib.utils.SettingHandler.COLUMN_NAME
import com.czur.starry.device.baselib.utils.SettingHandler.SETTING_LANGUAGE
import com.czur.starry.device.baselib.utils.SettingHandler.SETTING_VALUES
import com.czur.starry.device.baselib.utils.SettingHandler.SETTING_VALUES_CODE
import com.czur.starry.device.settings.manager.LanguageManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.runBlocking

class CZURSettingsProvider : CZURContentProvider(), CoroutineScope by MainScope() {
    companion object {
        const val TAG = "CZURSettingsProvider"

        // 对焦
        const val CATEGORY_FOCUS = "focus"
        private const val CODE_FOCUS = 1
        const val COLUMN_FOCUS_STATUS = "focusStatus"
        const val FOCUS_STATUS_IDLE = 0 //对焦空闲
        const val FOCUS_STATUS_WORKING = 1 //正在对焦中
        val KEY_FOCUS = intPreferencesKey(COLUMN_FOCUS_STATUS)
    }

    private lateinit var mContext: Context
    private val uriMatcher = UriMatcher(UriMatcher.NO_MATCH)
    private val Context.myDataStore: DataStore<Preferences> by preferencesDataStore(
        "settings",
        corruptionHandler = createDefCorruptionHandler("settings")
    )
    private val dataStore: DataStore<Preferences> by lazy {
        mContext.myDataStore
    }

    init {
        uriMatcher.addURI(AUTHORITY, CATEGORY_FOCUS, CODE_FOCUS)
        uriMatcher.addURI(AUTHORITY, SETTING_VALUES, SETTING_VALUES_CODE)
    }

    override fun delete(uri: Uri, selection: String?, selectionArgs: Array<String>?): Int {
        return 0
    }

    override fun getType(uri: Uri): String? {
        return null
    }

    override fun insert(uri: Uri, values: ContentValues?): Uri? {
        return null
    }

    override fun onCreate(): Boolean {
        mContext = context!!
        return true
    }

    override fun query(
        uri: Uri, projection: Array<String>?, selection: String?,
        selectionArgs: Array<String>?, sortOrder: String?
    ): Cursor? {
        when (uriMatcher.match(uri)) {
            CODE_FOCUS -> {
                logTagD(TAG, "查询对焦状态>>")
                val cursor = MatrixCursor(arrayOf(COLUMN_FOCUS_STATUS))
                val focusStatusFlow = dataStore.data.map { preferences ->
                    preferences[KEY_FOCUS] ?: FOCUS_STATUS_IDLE
                }
                runBlocking {
                    val status = focusStatusFlow.first()
                    logTagD(TAG, "对焦状态:$status")
                    cursor.addRow(arrayOf(status))
                }

                logTagD(TAG, "查询对焦状态结束<<")
                return cursor
            }

            SETTING_VALUES_CODE -> {
                // 查询值
                // 值是保存在本地中的

                val matrixCursor = MatrixCursor(arrayOf(COLUMN_NAME))
                val value = querySettings(selection ?: "")
                matrixCursor.addRow(arrayOf(value))
                return matrixCursor
            }
        }
        return null
    }

    private fun querySettings(key: String): String {
        logTagD(TAG, "查询:${key}")

        return when (key) {
            SETTING_LANGUAGE -> {
                // 查询当前语言
                val local = LanguageManager.getCheckedLocale()
                LanguageManager.getNameCode(local)
            }

            else -> ""
        }
    }


    override fun update(
        uri: Uri, values: ContentValues?, selection: String?,
        selectionArgs: Array<String>?
    ): Int {
        var result = 0
        when (uriMatcher.match(uri)) {
            CODE_FOCUS -> {
                // 通知数据改变
                logTagD(TAG, "更新对焦>>>")
                if (values != null && values.containsKey(COLUMN_FOCUS_STATUS)) {
                    val status = values.getAsInteger(COLUMN_FOCUS_STATUS)
                    if (status != null) {
                        // 更新状态
                        logTagD(TAG, "更新状态:$status")
                        result = updateFocusStatus(status)
                    }
                }
                logTagD(TAG, "更新对焦结束: ${result}<<<")
            }
        }
        if (result > 0) {
            logTagD(TAG, "通知数据改变")
            mContext.contentResolver.notifyChange(uri, null)
        }
        return result
    }

    /**
     * 更新投影仪状态信息
     * @param status 新的投影仪状态
     */
    private fun updateFocusStatus(status: Int): Int {
        var result = 0

        runBlocking {
            dataStore.edit { settings ->
                val currentStatus = settings[KEY_FOCUS] ?: FOCUS_STATUS_IDLE
                logTagD(TAG, "currentStatus:${currentStatus},status:${status}")
                if (currentStatus != status) {
                    settings[KEY_FOCUS] = status
                    result = 1
                }
            }

        }
        return result
    }

}

/**
 * 根据category拼接Uri
 */
fun getProviderUri(category: String): Uri {
    return Uri.parse("content://${AUTHORITY}/${category}")
}