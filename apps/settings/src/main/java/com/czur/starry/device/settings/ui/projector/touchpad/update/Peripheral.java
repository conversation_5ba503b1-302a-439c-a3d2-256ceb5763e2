package com.czur.starry.device.settings.ui.projector.touchpad.update;

import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothGatt;
import android.bluetooth.BluetoothGattCallback;
import android.bluetooth.BluetoothGattCharacteristic;
import android.bluetooth.BluetoothGattDescriptor;
import android.bluetooth.BluetoothGattService;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;

import com.czur.czurutils.log.CZURLogUtilsKt;
import java.util.List;
import java.util.Map;
import java.util.Queue;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;

public class Peripheral extends BluetoothGattCallback {
    private static final String TAG = "Peripheral";

    private static final int CONN_STATE_IDLE = 1;
    private static final int CONN_STATE_CONNECTING = 2;
    private static final int CONN_STATE_CONNECTED = 4;
    private static final int CONN_STATE_DISCONNECTING = 8;
    private static final int CONN_STATE_CLOSED = 16;


    protected final Queue<CommandContext> mInputCommandQueue = new ConcurrentLinkedQueue<>();
    protected final Queue<CommandContext> mOutputCommandQueue = new ConcurrentLinkedQueue<>();
    protected final Map<String, CommandContext> mNotificationCallbacks = new ConcurrentHashMap<>();

    protected final Handler mTimeoutHandler = new Handler(Looper.getMainLooper());
    protected final Handler mDelayHandler = new Handler(Looper.getMainLooper());
    protected final Runnable mCommandTimeoutRunnable = new CommandTimeoutRunnable();
    protected final Runnable mCommandDelayRunnable = new CommandDelayRunnable();

    private final Object mStateLock = new Object();
    private final Object mProcessLock = new Object();

    protected BluetoothDevice device;
    protected BluetoothGatt gatt;
    protected String name;
    protected String mac;
    protected byte[] macBytes;
    protected int type;
    protected List<BluetoothGattService> mServices;

    protected Boolean processing = false;

    protected int commandTimeoutMill = 10 * 1000;

    private int mConnState = CONN_STATE_IDLE;

    public Peripheral(BluetoothDevice device) {

        this.device = device;

        this.name = device.getName();
        this.mac = device.getAddress();
        this.type = device.getType();
    }

    /********************************************************************************
     * Public API
     *******************************************************************************/

    public BluetoothDevice getDevice() {
        return this.device;
    }

    public String getDeviceName() {
        return this.name;
    }

    public String getMacAddress() {
        return this.mac;
    }

    public List<BluetoothGattService> getServices() {
        return mServices;
    }

    public byte[] getMacBytes() {

        if (this.macBytes == null) {
            String[] strArray = this.getMacAddress().split(":");
            int length = strArray.length;
            this.macBytes = new byte[length];

            for (int i = 0; i < length; i++) {
                this.macBytes[i] = (byte) (Integer.parseInt(strArray[i], 16) & 0xFF);
            }

            Arrays.reverse(this.macBytes, 0, length - 1);
        }

        return this.macBytes;
    }

    public int getType() {
        return this.type;
    }


    public boolean isConnected() {
        synchronized (this.mStateLock) {
            return this.mConnState == CONN_STATE_CONNECTED;
        }
    }

    public void connect(Context context) {

        synchronized (this.mStateLock) {
            if (this.mConnState == CONN_STATE_IDLE) {
                logD(TAG, "connect " + this.getDeviceName() + " -- "
                        + this.getMacAddress(), null);
                this.mConnState = CONN_STATE_CONNECTING;
                this.gatt = this.device.connectGatt(context, false, this);
                //this.requestConnectionPriority(CONNECTION_PRIORITY_HIGH);
                if (this.gatt == null) {
                    this.disconnect();
                    this.mConnState = CONN_STATE_IDLE;
                    this.onDisconnect();
                }
            }
        }
    }

    public void disconnect() {

        synchronized (this.mStateLock) {
            if (this.mConnState != CONN_STATE_CONNECTING && this.mConnState != CONN_STATE_CONNECTED)
                return;
        }

        logD(TAG, "disconnect " + this.getDeviceName() + " -- "
                + this.getMacAddress(), null);

        this.clear();

        synchronized (this.mStateLock) {
            if (this.gatt != null) {
                int connState = this.mConnState;
                if (connState == CONN_STATE_CONNECTED) {
                    this.gatt.disconnect();
                    this.mConnState = CONN_STATE_DISCONNECTING;
                } else {
                    this.gatt.disconnect();
                    this.gatt.close();
                    this.mConnState = CONN_STATE_CLOSED;
                }
            } else {
                this.mConnState = CONN_STATE_IDLE;
            }
        }
    }

    private void clear() {
        this.processing = false;
        this.cancelCommandTimeoutTask();
        this.mInputCommandQueue.clear();
        this.mOutputCommandQueue.clear();
        this.mNotificationCallbacks.clear();
        this.mDelayHandler.removeCallbacksAndMessages(null);
    }

    public boolean sendCommand(Command.Callback callback, Command command) {

        synchronized (this.mStateLock) {
            if (this.mConnState != CONN_STATE_CONNECTED)
                return false;
        }

        CommandContext commandContext = new CommandContext(callback, command);
        this.postCommand(commandContext);

        return true;
    }


    public final boolean requestConnectionPriority(int connectionPriority) {
        return this.gatt.requestConnectionPriority(connectionPriority);
    }

    /********************************************************************************
     * Protected API
     *******************************************************************************/

    protected void onConnect() {
    }

    protected void onDisconnect() {
    }

    protected void onServicesDiscovered(List<BluetoothGattService> services) {
    }

    protected void onNotify(byte[] data, UUID serviceUUID,
                            UUID characteristicUUID, Object tag) {
    }

    /********************************************************************************
     * Command Handler API
     *******************************************************************************/

    private void postCommand(CommandContext commandContext) {
        CZURLogUtilsKt.logTagI(TAG, new String[]{"postCommand"}, null);
        this.mInputCommandQueue.add(commandContext);

        synchronized (this.mProcessLock) {
            if (!this.processing) {
                this.processCommand();
            }
        }
    }

    private void processCommand() {
        logD(TAG, "processing : " + this.processing, null);

        CommandContext commandContext;
        Command.CommandType commandType;

        synchronized (mInputCommandQueue) {
            if (this.mInputCommandQueue.isEmpty())
                return;
            commandContext = this.mInputCommandQueue.poll();
        }

        if (commandContext == null)
            return;

        commandType = commandContext.command.type;

        if (commandType != Command.CommandType.ENABLE_NOTIFY && commandType != Command.CommandType.DISABLE_NOTIFY) {
            this.mOutputCommandQueue.add(commandContext);

            synchronized (this.mProcessLock) {
                if (!this.processing)
                    this.processing = true;
            }
        }

        int delay = commandContext.command.delay;
        if (delay > 0) {
            this.mDelayHandler.postDelayed(this.mCommandDelayRunnable, delay);
        } else {
            this.processCommand(commandContext);
        }
    }

    synchronized private void processCommand(CommandContext commandContext) {

        Command command = commandContext.command;
        Command.CommandType commandType = command.type;

        logD(TAG, "processCommand : " + command.toString(), null);

        switch (commandType) {
            case READ:
                this.postCommandTimeoutTask();
                this.readCharacteristic(commandContext, command.serviceUUID,
                        command.characteristicUUID);
                break;
            case WRITE:
                this.postCommandTimeoutTask();
                this.writeCharacteristic(commandContext, command.serviceUUID,
                        command.characteristicUUID,
                        BluetoothGattCharacteristic.WRITE_TYPE_DEFAULT,
                        command.data);
                break;
            case WRITE_NO_RESPONSE:
                this.postCommandTimeoutTask();
                this.writeCharacteristic(commandContext, command.serviceUUID,
                        command.characteristicUUID,
                        BluetoothGattCharacteristic.WRITE_TYPE_NO_RESPONSE,
                        command.data);
                break;
            case ENABLE_NOTIFY:
                this.enableNotification(commandContext, command.serviceUUID,
                        command.characteristicUUID);
                break;
            case DISABLE_NOTIFY:
                this.disableNotification(commandContext, command.serviceUUID,
                        command.characteristicUUID);
                break;
        }
    }

    private void commandCompleted() {

        logD(TAG, "commandCompleted", null);

        synchronized (this.mProcessLock) {
            if (this.processing)
                this.processing = false;
        }

        this.processCommand();
    }

    private void commandSuccess(CommandContext commandContext, Object data) {

        logD(TAG, "commandSuccess", null);

        if (commandContext != null) {

            Command command = commandContext.command;
            Command.Callback callback = commandContext.callback;
            commandContext.clear();

            if (callback != null) {
                callback.success(this, command,
                        data);
            }
        }
    }

    private void commandSuccess(Object data) {

        CommandContext commandContext;
        commandContext = this.mOutputCommandQueue.poll();
        this.commandSuccess(commandContext, data);
    }

    private void commandError(CommandContext commandContext, String errorMsg) {

        CZURLogUtilsKt.logTagW(TAG, new String[]{"commandError:" + errorMsg}, null);

        if (commandContext != null) {

            Command command = commandContext.command;
            Command.Callback callback = commandContext.callback;
            commandContext.clear();

            if (callback != null) {
                callback.error(this, command,
                        errorMsg);
            }
        }
    }

    private void commandError(String errorMsg) {

        CommandContext commandContext;
        commandContext = this.mOutputCommandQueue.poll();
        this.commandError(commandContext, errorMsg);
    }

    private boolean commandTimeout(CommandContext commandContext) {
        logD(TAG, "commandTimeout", null);

        if (commandContext != null) {

            Command command = commandContext.command;
            Command.Callback callback = commandContext.callback;
            commandContext.clear();

            if (callback != null) {
                return callback.timeout(this, command);
            }
        }

        return false;
    }

    private void postCommandTimeoutTask() {

        if (this.commandTimeoutMill <= 0)
            return;

        this.mTimeoutHandler.removeCallbacksAndMessages(null);
        this.mTimeoutHandler.postDelayed(this.mCommandTimeoutRunnable, this.commandTimeoutMill);
    }

    private void cancelCommandTimeoutTask() {
        this.mTimeoutHandler.removeCallbacksAndMessages(null);
    }

    /********************************************************************************
     * Private API
     *******************************************************************************/

    private void readCharacteristic(CommandContext commandContext,
                                    UUID serviceUUID, UUID characteristicUUID) {

        boolean success = true;
        String errorMsg = "";

        BluetoothGattService service = this.gatt.getService(serviceUUID);

        if (service != null) {

            BluetoothGattCharacteristic characteristic = service
                    .getCharacteristic(characteristicUUID);

            if (characteristic != null) {

                if (!this.gatt.readCharacteristic(characteristic)) {
                    success = false;
                    errorMsg = "read characteristic error";
                }

            } else {
                success = false;
                errorMsg = "read characteristic error";
            }
        } else {
            success = false;
            errorMsg = "service is not offered by the remote device";
        }

        if (!success) {
            this.commandError(errorMsg);
            this.commandCompleted();
        }
    }

    private void writeCharacteristic(CommandContext commandContext,
                                     UUID serviceUUID, UUID characteristicUUID, int writeType,
                                     byte[] data) {

        boolean success = true;
        String errorMsg = "";

        BluetoothGattService service = this.gatt.getService(serviceUUID);

        if (service != null) {
            BluetoothGattCharacteristic characteristic = this
                    .findWritableCharacteristic(service, characteristicUUID,
                            writeType);
            if (characteristic != null) {

                characteristic.setValue(data);
                characteristic.setWriteType(writeType);

                if (!this.gatt.writeCharacteristic(characteristic)) {
                    success = false;
                    errorMsg = "write characteristic error";
                }

            } else {
                success = false;
                errorMsg = "no characteristic";
            }
        } else {
            success = false;
            errorMsg = "service is not offered by the remote device";
        }

        if (!success) {
            this.commandError(errorMsg);
            this.commandCompleted();
        }
    }

    private void enableNotification(CommandContext commandContext,
                                    UUID serviceUUID, UUID characteristicUUID) {

        boolean success = true;
        String errorMsg = "";

        BluetoothGattService service = this.gatt.getService(serviceUUID);

        if (service != null) {

            BluetoothGattCharacteristic characteristic = this
                    .findNotifyCharacteristic(service, characteristicUUID);

            if (characteristic != null) {

                if (!this.gatt.setCharacteristicNotification(characteristic,
                        true)) {
                    success = false;
                    errorMsg = "enable notification error";
                } else {
                    String key = this.generateHashKey(serviceUUID,
                            characteristic);
                    this.mNotificationCallbacks.put(key, commandContext);
                }

            } else {
                success = false;
                errorMsg = "no characteristic";
            }

        } else {
            success = false;
            errorMsg = "service is not offered by the remote device";
        }

        if (!success) {
            this.commandError(commandContext, errorMsg);
        }

        this.commandCompleted();
    }

    private void disableNotification(CommandContext commandContext,
                                     UUID serviceUUID, UUID characteristicUUID) {

        boolean success = true;
        String errorMsg = "";

        BluetoothGattService service = this.gatt.getService(serviceUUID);

        if (service != null) {

            BluetoothGattCharacteristic characteristic = this
                    .findNotifyCharacteristic(service, characteristicUUID);

            if (characteristic != null) {

                String key = this.generateHashKey(serviceUUID, characteristic);
                this.mNotificationCallbacks.remove(key);

                if (!this.gatt.setCharacteristicNotification(characteristic,
                        false)) {
                    success = false;
                    errorMsg = "disable notification error";
                }

            } else {
                success = false;
                errorMsg = "no characteristic";
            }

        } else {
            success = false;
            errorMsg = "service is not offered by the remote device";
        }

        if (!success) {
            this.commandError(commandContext, errorMsg);
        }

        this.commandCompleted();
    }

    private BluetoothGattCharacteristic findWritableCharacteristic(
            BluetoothGattService service, UUID characteristicUUID, int writeType) {

        BluetoothGattCharacteristic characteristic = null;

        int writeProperty = BluetoothGattCharacteristic.PROPERTY_WRITE;

        if (writeType == BluetoothGattCharacteristic.WRITE_TYPE_NO_RESPONSE) {
            writeProperty = BluetoothGattCharacteristic.PROPERTY_WRITE_NO_RESPONSE;
        }

        List<BluetoothGattCharacteristic> characteristics = service
                .getCharacteristics();

        for (BluetoothGattCharacteristic c : characteristics) {
            if ((c.getProperties() & writeProperty) != 0
                    && characteristicUUID.equals(c.getUuid())) {
                characteristic = c;
                break;
            }
        }

        return characteristic;
    }

    private BluetoothGattCharacteristic findNotifyCharacteristic(
            BluetoothGattService service, UUID characteristicUUID) {

        BluetoothGattCharacteristic characteristic = null;

        List<BluetoothGattCharacteristic> characteristics = service
                .getCharacteristics();

        for (BluetoothGattCharacteristic c : characteristics) {
            if ((c.getProperties() & BluetoothGattCharacteristic.PROPERTY_NOTIFY) != 0
                    && characteristicUUID.equals(c.getUuid())) {
                characteristic = c;
                break;
            }
        }

        if (characteristic != null)
            return characteristic;

        for (BluetoothGattCharacteristic c : characteristics) {
            if ((c.getProperties() & BluetoothGattCharacteristic.PROPERTY_INDICATE) != 0
                    && characteristicUUID.equals(c.getUuid())) {
                characteristic = c;
                break;
            }
        }

        return characteristic;
    }

    private String generateHashKey(BluetoothGattCharacteristic characteristic) {
        return this.generateHashKey(characteristic.getService().getUuid(),
                characteristic);
    }

    private String generateHashKey(UUID serviceUUID,
                                   BluetoothGattCharacteristic characteristic) {
        return String.valueOf(serviceUUID) + "|" + characteristic.getUuid()
                + "|" + characteristic.getInstanceId();
    }

    /********************************************************************************
     * Implements BluetoothGattCallback API
     *******************************************************************************/

    @Override
    public void onConnectionStateChange(BluetoothGatt gatt, int status,
                                        int newState) {

        logD(TAG, "onConnectionStateChange  status :" + status + " state : "
                + newState, null);

        if (newState == BluetoothGatt.STATE_CONNECTED) {

            synchronized (this.mStateLock) {
                this.mConnState = CONN_STATE_CONNECTED;
            }

            if (this.gatt == null || !this.gatt.discoverServices()) {
                logD(TAG, "remote service discovery has been stopped status = "
                        + newState, null);

                this.disconnect();

            } else {
                this.onConnect();
            }

        } else {

            synchronized (this.mStateLock) {
                logD(TAG, "Close", null);

                if (this.gatt != null) {
                    this.gatt.close();
                    this.mConnState = CONN_STATE_CLOSED;
                }

                this.clear();
                this.mConnState = CONN_STATE_IDLE;
                this.onDisconnect();
            }
        }
    }

    @Override
    public void onCharacteristicChanged(BluetoothGatt gatt,
                                        BluetoothGattCharacteristic characteristic) {
        super.onCharacteristicChanged(gatt, characteristic);

        String key = this.generateHashKey(characteristic);
        CommandContext commandContext = this.mNotificationCallbacks.get(key);

        if (commandContext != null) {

            this.onNotify(characteristic.getValue(),
                    commandContext.command.serviceUUID,
                    commandContext.command.characteristicUUID,
                    commandContext.command.tag);
        }
    }

    @Override
    public void onCharacteristicRead(BluetoothGatt gatt,
                                     BluetoothGattCharacteristic characteristic, int status) {
        super.onCharacteristicRead(gatt, characteristic, status);

        this.cancelCommandTimeoutTask();

        if (status == BluetoothGatt.GATT_SUCCESS) {
            byte[] data = characteristic.getValue();
            this.commandSuccess(data);
        } else {
            this.commandError("read characteristic failed");
        }

        this.commandCompleted();
    }

    @Override
    public void onCharacteristicWrite(BluetoothGatt gatt,
                                      BluetoothGattCharacteristic characteristic, int status) {
        super.onCharacteristicWrite(gatt, characteristic, status);

        this.cancelCommandTimeoutTask();

        if (status == BluetoothGatt.GATT_SUCCESS) {
            this.commandSuccess(null);
        } else {
            this.commandError("write characteristic fail");
        }

        logD(TAG, "onCharacteristicWrite newStatus : " + status, null);

        this.commandCompleted();
    }

    @Override
    public void onDescriptorRead(BluetoothGatt gatt,
                                 BluetoothGattDescriptor descriptor, int status) {
        super.onDescriptorRead(gatt, descriptor, status);

        this.cancelCommandTimeoutTask();

        if (status == BluetoothGatt.GATT_SUCCESS) {
            byte[] data = descriptor.getValue();
            this.commandSuccess(data);
        } else {
            this.commandError("read description failed");
        }

        this.commandCompleted();
    }

    @Override
    public void onDescriptorWrite(BluetoothGatt gatt,
                                  BluetoothGattDescriptor descriptor, int status) {
        super.onDescriptorWrite(gatt, descriptor, status);

        this.cancelCommandTimeoutTask();

        if (status == BluetoothGatt.GATT_SUCCESS) {
            this.commandSuccess(null);
        } else {
            this.commandError("write description failed");
        }

        this.commandCompleted();
    }

    @Override
    public void onServicesDiscovered(BluetoothGatt gatt, int status) {
        super.onServicesDiscovered(gatt, status);

        if (status == BluetoothGatt.GATT_SUCCESS) {
            List<BluetoothGattService> services = gatt.getServices();
            this.mServices = services;
            this.onServicesDiscovered(services);
        } else {
            logD(TAG, "Service discovery failed", null);
            this.disconnect();
        }
    }


    @Override
    public void onMtuChanged(BluetoothGatt gatt, int mtu, int status) {
        super.onMtuChanged(gatt, mtu, status);
        logD(TAG, "mtu changed : " + mtu, null);
    }

    @Override
    public void onReliableWriteCompleted(BluetoothGatt gatt, int status) {
        super.onReliableWriteCompleted(gatt, status);
    }

    private final class CommandContext {

        public Command command;
        public Command.Callback callback;

        public CommandContext(Command.Callback callback, Command command) {
            this.callback = callback;
            this.command = command;
        }

        public void clear() {
            this.command = null;
            this.callback = null;
        }
    }


    private final class CommandTimeoutRunnable implements Runnable {

        @Override
        public void run() {

            synchronized (mOutputCommandQueue) {

                CommandContext commandContext = mOutputCommandQueue.peek();

                if (commandContext != null) {

                    Command command = commandContext.command;
                    Command.Callback callback = commandContext.callback;

                    boolean retry = commandTimeout(commandContext);

                    if (retry) {
                        commandContext.command = command;
                        commandContext.callback = callback;
                        processCommand(commandContext);
                    } else {
                        mOutputCommandQueue.poll();
                        commandCompleted();
                    }
                }
            }
        }
    }

    private final class CommandDelayRunnable implements Runnable {

        @Override
        public void run() {
            synchronized (mOutputCommandQueue) {
                CommandContext commandContext = mOutputCommandQueue.peek();
                processCommand(commandContext);
            }
        }
    }

    private void logD(String tag, String message, Throwable tr) {
        CZURLogUtilsKt.logTagD(tag, new String[]{message}, tr);
    }
}

