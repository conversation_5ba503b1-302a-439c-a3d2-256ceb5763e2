package com.czur.starry.device.settings.model

import com.google.gson.annotations.SerializedName

/**
 * Created by 陈丰尧 on 3/9/21
 */
data class OSSTokenEntity(
    @SerializedName("AccessKeyId")
    val accessKeyId: String,
    @SerializedName("AccessKeySecret")
    val accessKeySecret: String,
    @SerializedName("BucketName")
    val bucketName: String,
    @SerializedName("Expiration")
    val expiration: String,
    @SerializedName("Prefix")
    val prefix: String,
    @SerializedName("RequestId")
    val requestId: String,
    @SerializedName("SecurityToken")
    val securityToken: String,
)