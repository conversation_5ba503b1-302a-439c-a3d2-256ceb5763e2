package com.czur.starry.device.settings.ui.personalization.backdrop

import android.os.Bundle
import androidx.core.graphics.scale
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.network.download.startDownload
import com.czur.starry.device.baselib.utils.InternetStatus
import com.czur.starry.device.baselib.utils.NetStatusUtil
import com.czur.starry.device.baselib.utils.addItemDecoration
import com.czur.starry.device.baselib.utils.closeDefChangeAnimations
import com.czur.starry.device.baselib.utils.doOnItemClick
import com.czur.starry.device.baselib.utils.isValidImage
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.baselib.utils.saveToFile
import com.czur.starry.device.baselib.utils.takeScreenShot
import com.czur.starry.device.baselib.utils.toast
import com.czur.starry.device.file.filelib.FileType
import com.czur.starry.device.settings.R
import com.czur.starry.device.settings.SettingMainViewModel
import com.czur.starry.device.settings.app.App
import com.czur.starry.device.settings.base.BaseBindingMenuFragment
import com.czur.starry.device.settings.databinding.FragmentScreenBackdropBinding
import com.czur.starry.device.settings.model.BackdropType
import com.czur.starry.device.settings.ui.personalization.wallpaper.FileChooseFloatFragment
import com.czur.starry.device.settings.ui.personalization.wallpaper.bean.CustomImageEntity
import com.czur.starry.device.settings.ui.personalization.wallpaper.bean.FileEntity
import com.czur.starry.device.settings.ui.personalization.wallpaper.dialog.LocalSelectDialog
import com.czur.starry.device.settings.ui.personalization.wallpaper.dialog.ScanQRCodeDialog
import com.czur.starry.device.settings.ui.personalization.wallpaper.utils.SpacesItemDecoration
import com.czur.starry.device.settings.ui.personalization.wallpaper.utils.getScreenDropName
import com.czur.starry.device.settings.ui.personalization.wallpaper.utils.getValueToList
import com.czur.starry.device.settings.ui.personalization.wallpaper.vm.DisplayVM
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.coroutines.withContext
import java.io.File
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.random.Random

/**
 * Created by 陈丰尧 on 2024/7/23
 */

private const val TAG = "ScreenBackdropFragment"
private const val CUSTOM_MAX_COUNT = 20

class ScreenBackdropFragment : BaseBindingMenuFragment<FragmentScreenBackdropBinding>() {

    private val mainViewModel: SettingMainViewModel by viewModels({ requireActivity() })
    //本地图片选择
    private val displayVM: DisplayVM by viewModels({ requireActivity() })
    private val viewModel: ScreenBackdropViewModel by viewModels<ScreenBackdropViewModel>()

    private val screenBackdropAdapter = ScreenBackdropAdapter()
    private val screenBackCustomAdapter = ScreenBackCustomAdapter()

    //网络监听
    private val netStatusUtil: NetStatusUtil by lazy { NetStatusUtil(App.context) }
    private var netState = true

    private var dialog: LocalSelectDialog? = null
    private var dialogQrcode: ScanQRCodeDialog? = null

    //是否正在处理图片（扫码）
    private var isGettingPic = AtomicBoolean(false)
    //是否正在接收扫码图片
    private var isReceiving = AtomicBoolean(false)

    //循环遍历未接受图片
    private var queryImageJob: Job? = null
    private val uploadImageMutex = Mutex()

    override fun FragmentScreenBackdropBinding.initBindingViews() {

        launch {
            backdropRv.closeDefChangeAnimations()
            backdropRv.layoutManager = GridLayoutManager(context, 3)
            backdropRv.addItemDecoration(RecyclerView.VERTICAL, 30)
            backdropRv.addItemDecoration(RecyclerView.HORIZONTAL, 30)
            backdropRv.addItemDecoration(SpacesItemDecoration())
            backdropRv.adapter = screenBackdropAdapter

            screenBackdropAdapter.setData(getAllData())

        }

        // 更新选中的tag
        backdropTabBar.setOnSelChangeListener { selPos ->
            viewModel.updateSelectedTag(selPos)
            updateData(true)
        }

        netStatusUtil.startWatching()

        //网络监听
        netStatusUtil.internetStatusLive.observe(requireActivity()) {
            logTagD(TAG, "===连接状态==${it}")
            netState = it == InternetStatus.CONNECT
        }

        //本地图片选择
        displayVM.shareFileLive.observe(viewLifecycleOwner) {
            logTagD(TAG, "=====it=$it")
            if (it == null) return@observe

            launch {
                val imgFile = File(it.absPath)
                if (!imgFile.isValidImage()) {
                    toast(R.string.toast_wallpaper_upload_damaged_image)
                    return@launch
                }
                val srcFile = FileEntity(
                    absPath = it.absPath,
                    fileType = it.fileType,
                    name = it.name,
                    belongTo = it.belongTo
                )
                delay(200)
                //确认添加本地后刷新
                showLocalDialog(srcFile)
            }

        }

        //扫码上传
        viewModel.unReceivedImageLive.observe(viewLifecycleOwner) {
            lifecycleScope.launch {
                uploadImageMutex.withLock {
                    isReceiving.set(true)
                    delay(500)
                    for (i in 0 until it.size) {
                        while (isGettingPic.get()) delay(200)
                        getImageFromUrl(it[i])
                    }
                    isReceiving.set(false)
                }
            }
        }

        //自定义
        backdropRv.doOnItemClick(true) { vh, view ->
            val pos = vh.bindingAdapterPosition
            if (pos < 0) return@doOnItemClick false
            when (view.id) {
                R.id.imLocal,
                R.id.tvLocal,
                R.id.layoutLocal -> {
                    uploadFromLocal()
                    true
                }

                R.id.imQrcode,
                R.id.tvQrcode,
                R.id.layoutQrcode -> {
                    uploadFromQRCode()
                    true
                }

                else -> {
                    if (viewModel.currentTagIsCustom() && pos == 0) {
                        return@doOnItemClick true
                    }
                    takeRecentScreenShot()
                    val backdropEntity = when(viewModel.currentTagIsCustom()) {
                        true -> screenBackCustomAdapter.getData(pos)
                        false -> screenBackdropAdapter.getData(pos)
                    }
                    val backdropName = when(backdropEntity.fileType) {
                        BackdropType.CUSTOM -> backdropEntity.customEntity?.name
                        BackdropType.FIXED -> backdropEntity.fixedEntity?.name
                    }
                    backdropName?.let {
                        ScreenBackdropPreviewAty.start(
                            requireContext(),
                            viewModel.selectedTag.key,
                            it,
                            backdropEntity.fileType
                        )
                    }
                    true
                }
            }

        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        // 当前使用的壁纸
        repeatCollectOnResume(viewModel.currentScreenBackdropBmpFlow) {
            binding.usingImgIv.setImageBitmap(it)
        }

        // tag
        repeatCollectOnResume(viewModel.allBackdropTagFlow) {
            val titles = it.map { backdropTagEntity -> getString(backdropTagEntity.name) }
            binding.backdropTabBar.setTitles(titles, viewModel.lastSelectTab)
        }

        //获取当前壁纸
        launch {
            viewModel.getCurrentScreenBackdropBmp()
        }

        repeatCollectOnResume(mainViewModel.currentViewNavigateFlow) {
            logTagD(TAG, "currentViewNavigateFlow: $it")
            mainViewModel.onNavigateReset()
            if (it == getString(R.string.voice_change_wallpaper)) {
                while (screenBackdropAdapter.itemCount == 0) {
                    delay(2000)
                }
                val pos = Random.nextInt(screenBackdropAdapter.itemCount)
                val backdropEntity = screenBackdropAdapter.getData(pos)

                backdropEntity.fixedEntity?.name?.let {
                    ScreenBackdropPreviewAty.start(
                        requireContext(),
                        viewModel.selectedTag.key,
                        it,
                        backdropEntity.fileType,
                        true
                    )
                }
            }
        }
    }

    //本地图片上传
    private fun uploadFromLocal() {
        launch {
            val list = getValueToList(CUSTOM_ASSETS)
            if (list.size >= CUSTOM_MAX_COUNT) {
                toast(R.string.wallpaper_create_temp_filed)
            } else {
                //本地上传
                showLocalDialog()
            }
        }
    }

    //二维码扫描上传
    private fun uploadFromQRCode() {
        launch {
            val list = getValueToList(CUSTOM_ASSETS)
            if (list.size >= CUSTOM_MAX_COUNT) {
                toast(R.string.wallpaper_create_temp_filed)
            } else if (!netState) {
                toast(R.string.wallpaper_net_not_work)
            } else {
                //扫码上传
                showQRCodeDialog()
            }
        }
    }

    //扫码下载
    private suspend fun getImageFromUrl(entity: CustomImageEntity) = withContext(Dispatchers.IO) {
        logTagD(TAG, "=======getImageFromUrl=$entity")
        isGettingPic.set(true)
        if (screenBackCustomAdapter.itemCount > CUSTOM_MAX_COUNT) {
            lifecycleScope.launch {
                val id = entity.id
                viewModel.responseReceived(id)
                toast(R.string.wallpaper_create_temp_filed)
                dialogQrcode?.dismiss()
                isGettingPic.set(false)
            }
        } else {
            val url = entity.directory
            val id = entity.id
            var imageFormat = getString(R.string.jpg)
            if (url.contains(getString(R.string.png))) {
                imageFormat = getString(R.string.png)
            }
            val list = getValueToList(CUSTOM_ASSETS)
            val name = getScreenDropName(list)
            val path = name + imageFormat
            val desFile = File(viewModel.customPath, path)
            if (startDownload(url, desFile)) {
                val desc =
                    FileEntity(
                        absPath = desFile.absolutePath,
                        name = name,
                        fileType = FileType.IMAGE
                    )
                logTagD(TAG, "=====desc=absPath=${desc.absPath}")
                logTagD(TAG, "=====desc=name=${desc.name}")
                lifecycleScope.launch {
                    addDataLocal(desc)
                    val data = getHasAvailableCustomData()
                    screenBackCustomAdapter.refreshData(data)
                    toast(R.string.str_create_temp_success1)
                    viewModel.responseReceived(id)
                    isGettingPic.set(false)
                }
            } else {
                isGettingPic.set(false)
                logTagD(TAG, "===图片下载失败=")
            }
        }
    }

    //本地上传dialog
    private fun showLocalDialog(it: FileEntity = FileEntity()) {
        launch {
            if (dialog?.isVisible == true) {
                return@launch
            }

            dialog = LocalSelectDialog.Builder()
                .setSelectClickListener {
                    showFileFragment()
                }.setConfirmClickListener {
                    if (it.absPath == "") return@setConfirmClickListener
                    //复制图片
                    doCopyTask(it)
                }.setImageName(requireContext(), it.name)
                .setImageView(requireContext(), it.absPath)
                .build()
            dialog?.show()
        }
    }

    //扫码上传dialog
    private fun showQRCodeDialog() {
        launch {
            val url = viewModel.getUploadUrl()
            if (url == null) {
                toast(R.string.wallpaper_geturl_failed)
                return@launch
            }
            if (dialogQrcode?.isVisible == true) {
                return@launch
            }
            val qrCode = url.toString() + Constants.SERIAL
            dialogQrcode = ScanQRCodeDialog.Builder()
                .setSelectClickListener {
                    showFileFragment()
                }.setConfirmClickListener {
                    dialogQrcode?.dismiss()
                    dialogQrcode = null
                }.setLink(qrCode)
                .build()
            dialogQrcode?.show()
        }
    }

    //显示文件选择
    private fun showFileFragment() {
        displayVM.setFileType(FileType.IMAGE)
        FileChooseFloatFragment(R.string.choose_folder_screen_backdrop).show()
        dialog?.dismiss()
    }

    private fun doCopyTask(it: FileEntity = FileEntity()) {
        launch {
            uploadImageMutex.withLock {
                delay(500)
                if (screenBackCustomAdapter.itemCount > CUSTOM_MAX_COUNT) {
                    toast(R.string.wallpaper_create_temp_filed)
                } else {
                    try {
                        val targetDir = viewModel.copyToLocal(it)
                        addDataLocal(
                            targetDir
                        )
                        val data = getHasAvailableCustomData()
                        screenBackCustomAdapter.refreshData(data)
                    } catch (e: Exception) {
                        e.printStackTrace()
                        toast(R.string.toast_wallpaper_upload_damaged_image)
                    }


                }
            }
        }
    }

    private fun takeRecentScreenShot() {
        launch {
            logTagD(TAG, "截图")
            val bitmap = takeScreenShot()
            val scaleBmp = bitmap?.scale(500, 252)
            scaleBmp?.saveToFile(File("sdcard/recent/lastTaskScreenshot.png"))
            bitmap?.recycle()
            scaleBmp?.recycle()
        }
    }

    private fun updateData(isSelectBar: Boolean = false) {
        launch {
            val currentList = viewModel.getCurrentClickTagData()
            if (viewModel.currentTagIsCustom()) {
                screenBackCustomAdapter.setData(requireContext(), getHasAvailableCustomData())
                if (binding.backdropRv.adapter != screenBackCustomAdapter) {
                    binding.backdropRv.adapter = screenBackCustomAdapter
                }
                if (isSelectBar) {
                    binding.backdropRv.adapter = screenBackCustomAdapter
                }
            } else {
                screenBackdropAdapter.setData(currentList)
                if (isSelectBar) {
                    delay(100L)
                    binding.backdropRv.adapter = screenBackdropAdapter
                }
            }

        }
    }

    override fun onResume() {
        super.onResume()
        //更新自定义
        updateData()

        queryImageJob = lifecycleScope.launch(Dispatchers.IO) {
            while (isActive) {
                delay(3000)
                if (!isReceiving.get() && netState) {
                    viewModel.queryUnReceived()
                }
            }
        }
    }

    override fun onPause() {
        super.onPause()
        queryImageJob?.cancel()
        displayVM.shareFile = null
        viewModel.unReceivedImageLive.value?.clear()
    }

    override fun onDestroy() {
        super.onDestroy()
        netStatusUtil.stopWatching()
    }
}