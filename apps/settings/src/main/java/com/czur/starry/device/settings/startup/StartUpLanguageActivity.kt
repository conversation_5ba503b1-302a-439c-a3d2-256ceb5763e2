package com.czur.starry.device.settings.startup

import android.content.res.Configuration
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.ScrollView
import android.widget.TextView
import androidx.vectordrawable.graphics.drawable.Animatable2Compat
import androidx.vectordrawable.graphics.drawable.AnimatedVectorDrawableCompat
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.base.BaseStartupActivity
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.StarryDevLocale
import com.czur.starry.device.baselib.utils.view.findView
import com.czur.starry.device.settings.R
import com.czur.starry.device.settings.manager.LanguageManager
import java.util.Locale

/**
 * Created by 陈丰尧 on 2/24/21
 */
private const val HOVER_BG_COLOR = 0x0D000000
private const val TAG = "StartUpLanguageActivity"

class StartUpLanguageActivity : BaseStartupActivity() {
    override fun getLayout() = R.layout.activity_startup_launguage
    private var isFirstLaunch = true

    // 关闭启动动画
    override val closeBootAnim: Boolean = true

    private val langList = LanguageManager.startupLocaleList

    private val startupLangLl by findView<LinearLayout>(R.id.startupLangLl)
    private val startupLanguageTv by findView<TextView>(R.id.startupLanguageTv)
    private val startupLanguageNextTv by findView<TextView>(R.id.startupLanguageNextTv)

    private val itemList = mutableListOf<View>()

    override fun initViews() {
        super.initViews()

        val scrollView = findViewById<ScrollView>(R.id.startupScrollView)
        scrollView.layoutParams = scrollView.layoutParams.apply {
            height = resources.getDimensionPixelSize(R.dimen.scroll_max_height)
        }

        startupLanguageNextTv.setOnClickListener {
            moveToNextStep()
        }
    }

    override fun onWindowFocusChanged(hasFocus: Boolean) {
        super.onWindowFocusChanged(hasFocus)
        if (hasFocus && isFirstLaunch) {
            isFirstLaunch = false
            langList.forEachIndexed { index, locale ->
                // 添加语言布局
                val itemView = LayoutInflater.from(this@StartUpLanguageActivity)
                    .inflate(R.layout.item_startup_language, startupLangLl, false)
                val itemLanguageTv = itemView.findViewById(R.id.itemLanguageTv) as TextView
                itemLanguageTv.text = LanguageManager.getDisplayName(locale)
                val itemLanguageIv = itemView.findViewById(R.id.itemLanguageIv) as ImageView

                if (isSupportLanguage(index)) {
                    // 添加鼠标悬停监听
                    addHoverListener(itemView)

                    // 添加触摸监听, 否则按下鼠标时会触发ACTION_HOVER_EXIT,导致样式改变
                    addTouchListener(itemView)
                    // 添加所有item 以便处理
                    itemList.add(itemView)
                    // 添加点击事件监听
                    addClickListener(itemView, locale)
                } else {
                    // 只支持简体中文
                    itemView.alpha = 0.5F
                }

                // 添加布局
                startupLangLl.addView(itemView)

                // 国内默认选择汉语， 海外默认选择英语。
                when (Constants.starryHWInfo.salesLocale) {
                    StarryDevLocale.Mainland -> {
                        if (index == 0) {
                            LanguageManager.updateLanguage(LanguageManager.localeList[index])
                            itemLanguageIv.visibility = View.VISIBLE
                        }
                    }
                    StarryDevLocale.Overseas -> {
                        if (index == 2) {
                            LanguageManager.updateLanguage(LanguageManager.localeList[index])
                            itemLanguageIv.visibility = View.VISIBLE
                        } else {
                            itemLanguageIv.visibility = View.GONE
                        }
                    }
                }

            }
        }
    }

    /**
     * 判断是否支持当前语言
     */
    private fun isSupportLanguage(index: Int): Boolean {
        return when (Constants.starryHWInfo.salesLocale) {
            StarryDevLocale.Mainland -> index == 0  // 国内只支持汉语
            StarryDevLocale.Overseas -> true        // 海外版支持所有语言
        }
    }

    private fun addClickListener(itemView: View, locale: Locale) {
        itemView.setOnClickListener {
            itemList.forEach { item ->
                val itemLanguageIv = item.findViewById(R.id.itemLanguageIv) as ImageView
                itemLanguageIv.visibility = View.GONE
            }

            val itemLanguageIv = it.findViewById(R.id.itemLanguageIv) as ImageView
            // 变更语言
            LanguageManager.updateLanguage(locale)
            // 播放动画
            val drawableCompat = AnimatedVectorDrawableCompat
                .create(it.context, R.drawable.icon_selected_anim)!!
            itemLanguageIv.setImageDrawable(drawableCompat)
            drawableCompat.registerAnimationCallback(object :
                Animatable2Compat.AnimationCallback() {
            })
            drawableCompat.start()
            itemLanguageIv.visibility = View.VISIBLE
        }
    }

    private fun addTouchListener(itemView: View) {
        itemView.setOnTouchListener { v, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    v.setBackgroundColor(HOVER_BG_COLOR)
                    true
                }

                MotionEvent.ACTION_UP -> {
                    v.performClick()
                }

                MotionEvent.ACTION_CANCEL -> {
                    v.setBackgroundColor(0)
                    false
                }

                else -> false

            }
        }
    }

    private fun addHoverListener(itemView: View) {
        itemView.setOnHoverListener { view, event ->
            when (event.action) {
                MotionEvent.ACTION_HOVER_ENTER -> view.setBackgroundColor(HOVER_BG_COLOR)
                MotionEvent.ACTION_HOVER_EXIT -> view.setBackgroundColor(0)
            }
            false
        }
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            logTagD(TAG, "设定语言页面,禁止按返回键")
            return true
        }
        return super.onKeyDown(keyCode, event)
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        // 切换语言后, 变更加标题
        val title =
            createConfigurationContext(newConfig).resources.getString(R.string.setup_lang_title)
        startupLanguageTv.text = title

        val text =
            createConfigurationContext(newConfig).resources.getString(R.string.startup_next_step)
        startupLanguageNextTv.text = text
    }

}