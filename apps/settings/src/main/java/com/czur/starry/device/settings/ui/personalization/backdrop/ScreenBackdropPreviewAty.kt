package com.czur.starry.device.settings.ui.personalization.backdrop

import android.animation.Animator
import android.animation.Animator.AnimatorListener
import android.content.Context
import android.content.Intent
import android.content.pm.ActivityInfo
import android.content.res.Configuration
import android.os.Bundle
import android.view.View
import androidx.activity.viewModels
import androidx.core.net.toUri
import com.bumptech.glide.Glide
import com.czur.czurutils.extension.platform.startActivity
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.base.v2.aty.CZViewBindingAty
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.performTouch
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.baselib.utils.setDebounceTouchClickListener
import com.czur.starry.device.baselib.utils.toast
import com.czur.starry.device.settings.R
import com.czur.starry.device.settings.databinding.ActivityScreenBackdropPreviewBinding
import com.czur.starry.device.settings.model.BackdropListItem
import com.czur.starry.device.settings.model.BackdropTag
import com.czur.starry.device.settings.model.BackdropType
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext

/**
 * Created by 陈丰尧 on 2024/7/24
 */

const val CURRENT_USED_BACKDROP_DATA = "ScreenBackDropCurrent"
const val CURRENT_BACKDROP_Name = "ScreenBackDropName"

class ScreenBackdropPreviewAty : CZViewBindingAty<ActivityScreenBackdropPreviewBinding>() {
    companion object {
        private const val TAG = "ScreenBackdropPreviewAty"
        private const val KEY_FILTER_TAG_KEY = "filterTagName"
        private const val KEY_CURRENT_NAME = "currentName"
        private const val KEY_BACKDROP_TYPE = "backdropType"
        private const val KEY_USE_NOW = "backdropUsedNow"

        fun start(context: Context, filterTagKey: String, currentName: String, backdropType: BackdropType, isUseNow: Boolean = false) {
            context.startActivity<ScreenBackdropPreviewAty>(
                KEY_FILTER_TAG_KEY to filterTagKey,
                KEY_CURRENT_NAME to currentName,
                KEY_BACKDROP_TYPE to backdropType.name,
                KEY_USE_NOW to isUseNow
            )
        }
    }

    private val viewModel: ScreenBackdropPreviewViewModel by viewModels<ScreenBackdropPreviewViewModel>()


    override fun handlePreIntent(preIntent: Intent) {
        super.handlePreIntent(preIntent)
        val tagKey = preIntent.getStringExtra(KEY_FILTER_TAG_KEY) ?: BackdropTag.All.key
        val currentName = preIntent.getStringExtra(KEY_CURRENT_NAME) ?: ""
        val backdropType = BackdropType.valueOf(preIntent.getStringExtra(KEY_BACKDROP_TYPE) ?: "")
        val isUseNow = preIntent.getBooleanExtra(KEY_USE_NOW, false)
        viewModel.isUseCurrentBackdropFlow.value = isUseNow
        logTagV(TAG, "tagKey = $tagKey, currentName = $currentName, backdropType = ${backdropType}, isUseNow = $isUseNow")
        viewModel.handelParams(tagKey, currentName, backdropType)
    }

    override fun ActivityScreenBackdropPreviewBinding.initBindingViews() {
        // 返回
        backClickView.setDebounceTouchClickListener {
            finish()
        }

        preIv.setDebounceTouchClickListener {
            viewModel.previousPage()
        }

        nextIv.setDebounceTouchClickListener {
            viewModel.nextPage()
        }

        useNowBtn.setDebounceTouchClickListener {
            backIv.animate().xBy(-300F).start()
            backTv.animate().xBy(-300F).start()
            nextIv.animate().xBy(300F).start()
            preIv.animate().xBy(-300F).start()
            loadingGroup.animate().alpha(1F).start()
            useNowBtn.animate().yBy(300F).setListener(object : AnimatorListener {
                override fun onAnimationStart(animation: Animator) {

                }

                override fun onAnimationEnd(animation: Animator) {
                    logTagD(TAG, "动画完成, 启动Service开始设置壁纸")
                    BackdropChangeService.start(
                        this@ScreenBackdropPreviewAty,
                        viewModel.currentBackdropEntity
                    )
                    launch {
                        withContext(Dispatchers.IO) {
                            saveCurrentBackDropData(viewModel.currentBackdropEntity)
                        }
                        delay(800)  // 等待Service启动完成
                        logTagD(TAG, "保存当前壁纸数据完成")
                        finish()
                    }
                }

                override fun onAnimationCancel(animation: Animator) {
                }

                override fun onAnimationRepeat(animation: Animator) {
                }

            }).start()
        }

        deleteBtn.setDebounceTouchClickListener {
            launch {
                if (checkCurrentUsedData()) {
                    toast(R.string.toast_wallpaper_custom_delete_error)
                } else {
                    removeCustomData(viewModel.currentBackdropEntity)
                    finish()
                }
            }
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)

        val currentConfig = Configuration(resources.configuration)
        val diff = currentConfig.diff(viewModel.config)
        if (diff and ActivityInfo.CONFIG_ASSETS_PATHS != 0) {
            logTagD(TAG, "壁纸改变 finish")
            finish()
            return
        }
        viewModel.config.setTo(resources.configuration)

        // 当前的画面
        repeatCollectOnResume(viewModel.currentBackdropFlow) {
            when(it.fileType) {
                BackdropType.FIXED -> {
                    it.fixedEntity?.imgRes?.let { fixed -> binding.previewIv.setImageResource(fixed) }
                }
                BackdropType.CUSTOM -> {
                    it.customEntity?.absPath?.let { custom ->
                        Glide.with(this)
                            .load(custom)
                            .into(binding.previewIv)
                    }
                }
            }
            if (it.fileType == BackdropType.FIXED) {
                binding.deleteBtn.visibility = View.GONE
            } else {
                binding.deleteBtn.visibility = View.VISIBLE
            }
        }

        repeatCollectOnResume(viewModel.preEnableFlow) { enable ->
            binding.preIv.gone(!enable)
        }

        repeatCollectOnResume(viewModel.nextEnableFlow) { enable ->
            binding.nextIv.gone(!enable)
        }

        repeatCollectOnResume(viewModel.isUseCurrentBackdropFlow) { isUseNow ->
            logTagD(TAG, "=========isUseNow = $isUseNow")
            if (isUseNow) {
                delay(2000)
                binding.useNowBtn.performTouch()
            }

        }
    }



    private fun saveCurrentBackDropData(data: BackdropListItem) {
        val sharedPref = getSharedPreferences(CURRENT_USED_BACKDROP_DATA, Context.MODE_PRIVATE)
        with (sharedPref.edit()) {
            when(data.fileType) {
                BackdropType.FIXED -> putString(CURRENT_BACKDROP_Name, data.fixedEntity?.name)
                BackdropType.CUSTOM -> putString(CURRENT_BACKDROP_Name, data.customEntity?.name)
            }
            apply()
        }
    }

    private fun checkCurrentUsedData():Boolean {
        val currentData = viewModel.currentBackdropEntity
        val dataName = when(currentData.fileType) {
            BackdropType.FIXED -> currentData.fixedEntity?.name
            BackdropType.CUSTOM -> currentData.customEntity?.name
        }
        
        val sharedPref = getSharedPreferences(CURRENT_USED_BACKDROP_DATA, Context.MODE_PRIVATE)
        val usedName = sharedPref.getString(CURRENT_BACKDROP_Name, "")
        return dataName == usedName
    }

}