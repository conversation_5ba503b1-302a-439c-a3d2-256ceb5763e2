package com.czur.starry.device.settings.ui.personalization.backdrop

import android.app.ActivityManager
import android.app.WallpaperManager
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.view.View
import android.widget.ImageView
import androidx.core.graphics.scale
import androidx.core.net.toUri
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.load.resource.bitmap.DownsampleStrategy
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import com.czur.czurutils.extension.platform.startService
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.base.AlertWindowService
import com.czur.starry.device.baselib.utils.getScreenHeight
import com.czur.starry.device.baselib.utils.getScreenWidth
import com.czur.starry.device.baselib.utils.keyboard.injectKeyBack
import com.czur.starry.device.baselib.utils.keyboard.injectKeyHome
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.settings.R
import com.czur.starry.device.settings.model.BackdropListItem
import com.czur.starry.device.settings.model.BackdropType
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileInputStream
import kotlin.time.Duration.Companion.microseconds
import kotlin.time.Duration.Companion.seconds

/**
 * Created by 陈丰尧 on 2024/7/30
 */
class BackdropChangeService : AlertWindowService() {

    companion object {
        private const val TAG = "BackdropChangeService"
        private const val KEY_BACKDROP_ENTITY = "currentBackdropEntity"

        fun start(context: Context, backdropItem: BackdropListItem) {
            context.startService<BackdropChangeService>(
                KEY_BACKDROP_ENTITY to backdropItem
            )
        }
    }

    override val layoutId: Int
        get() = R.layout.service_backdrop_change
    override val windowWidthParam: Int
        get() = getScreenWidth()
    override val windowHeightParam: Int
        get() = getScreenHeight()

    private val wpm by lazy {
        application.getSystemService(Context.WALLPAPER_SERVICE) as WallpaperManager
    }

    private val activityManager by lazy {
        application.getSystemService(ActivityManager::class.java)
    }

    private val previewIv: ImageView by ViewFinder(R.id.previewIv)
    private val floatingColorView: View by ViewFinder(R.id.floatingColorView)

    private var customBmp: Bitmap? = null

    override fun onDataRefresh(intent: Intent?) {
        super.onDataRefresh(intent)

        val backdropItem = intent?.getParcelableExtra<BackdropListItem>(KEY_BACKDROP_ENTITY)
        backdropItem?.let { item ->
            when (backdropItem.fileType) {
                BackdropType.FIXED -> item.fixedEntity?.imgRes?.let { handleBackdropItem(item) }
                BackdropType.CUSTOM -> item.customEntity?.absPath?.let { handleBackdropItem(item) }
            } ?: run {
                logTagW(TAG, "壁纸ID错误")
                stopSelf()
            }
        }
    }

    private fun handleBackdropItem(backdropItem: BackdropListItem) {
        setPreviewImage(backdropItem)
        launch {
            delay(100)  // 等待预览图加载完成
            setCurrentBackDrop(backdropItem)
            injectKeyBack() // 返回键  为了让Setting可以被销毁
            injectKeyBack() // 返回键
            injectKeyHome() // Home键
            checkLauncherAndFinish()
        }
    }

    private fun setPreviewImage(backdropItem: BackdropListItem) {
        when (backdropItem.fileType) {
            BackdropType.FIXED -> {
                previewIv.setImageResource(backdropItem.fixedEntity!!.imgRes)
                floatingColorView.show()
            }

            BackdropType.CUSTOM -> {
                val filePath = backdropItem.customEntity!!.absPath
                // 使用Glide加载图片,并拉伸/压缩到 1920x1080
                Glide.with(this)
                    .asBitmap()
                    .load(filePath)
                    .override(1920, 1080) // 设置预览图的大小
                    .addListener(object : RequestListener<Bitmap> {
                        override fun onLoadFailed(
                            e: GlideException?,
                            model: Any?,
                            target: Target<Bitmap?>,
                            isFirstResource: Boolean
                        ): Boolean {
                            return false
                        }

                        override fun onResourceReady(
                            resource: Bitmap,
                            model: Any,
                            target: Target<Bitmap?>?,
                            dataSource: DataSource,
                            isFirstResource: Boolean
                        ): Boolean {
                            customBmp = resource
                            floatingColorView.show()
                            return false
                        }

                    })
                    .into(previewIv)
            }
        }
    }

    override fun View.initViews() {

    }

    private suspend fun checkLauncherAndFinish(maxSecond: Int = 10) {
        withContext(Dispatchers.IO) {
            // 检查Launcher是否在前台
            var second = 0
            while (isActive) {
                if (topClassIsLauncher()) {
                    delay(2.seconds)
                    stopSelf()
                    return@withContext
                }
                delay(1.seconds)
                injectKeyHome()
                second++
                if (second >= maxSecond) {
                    logTagW(TAG, "Launcher未在前台")
                    repeat(3) {
                        injectKeyBack()
                        delay(500.microseconds)
                    }
                    stopSelf()
                    return@withContext
                }
            }
        }
    }

    private fun topClassIsLauncher(): Boolean {
        val tasks = activityManager.getRunningTasks(1)
        if (!tasks.isNullOrEmpty()) {
            val topPackageName = tasks[0].topActivity?.packageName
            logTagV(TAG, "检查:topPackageName = $topPackageName")
            return topPackageName?.contains("launcher", true) ?: false
        }
        return false
    }

    /**
     * 这里设置壁纸是为了在启动Launcher时不会看见黑屏
     * Launcher的壁纸是由自己去管理的
     */
    private suspend fun setCurrentBackDrop(backdropItem: BackdropListItem) {
        logTagD(TAG, "设置壁纸")
        withContext(Dispatchers.IO) {
            when (backdropItem.fileType) {
                BackdropType.FIXED -> {
                    wpm.setResource(backdropItem.fixedEntity!!.imgRes)
                }

                BackdropType.CUSTOM -> {
                    val bitmap =
                        (customBmp ?: BitmapFactory.decodeFile(backdropItem.customEntity!!.absPath))
                            .scale(1920, 1080) // 缩放到1920x1080
                    wpm.setBitmap(bitmap)
                    delay(100)  // 防止太快, 视觉感受不好
                }
            }
            logTagD(TAG, "设置壁纸完成")
        }
    }

}