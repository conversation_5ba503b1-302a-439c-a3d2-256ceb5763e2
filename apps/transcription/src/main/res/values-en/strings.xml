<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">Record Local Meeting</string>
    <string name="str_time_watermark">Time watermark</string>
    <string name="toast_video_file_saved">%s recording file has been saved.</string>
    <string name="toast_video_file_save_failed">Fail to save the recording file.</string>
    <string name="toast_storage_not_enough_start_record">Cannot record. Remaining storage room is less than 500MB.</string>
    <string name="toast_storage_not_enough_start_camera">Cannot take photo. Remaining storage room is less than 500MB.</string>
    <string name="toast_rec_storage_not_enough">Recording will end in 5 minutes because remaining storage room is less then 500MB.</string>
    <string name="toast_rec_storage_stop_recording">Insufficient remaining storage room. Recoding has ended!</string>
    <string name="toast_rec_time_not_enough">Reminder : recording will end in %s minutes. Please save the recording file asap.</string>

    <string name="tips_camera_mode">Video recording mode</string>
    <string name="tips_screen_mode">Screen recording mode</string>
    <string name="tips_audio_mode">Audio recording mode</string>
    <string name="toast_conflict_with_meeting">Cannot start local meeting recording because video meeting is in progress.</string>
    <string name="dialog_mic_occupy_hint">%s is using the microphone. Would you like to end it and start recording instead?</string>
    <string name="dialog_mic_occupy_hint_init">%s is using the microphone. Would you like to end it and start local meeting instead?</string>
    <string name="dialog_stop_record_hint">End recording?</string>
    <string name="dialog_refuse_eshare">Cannot cast screen because video meeting recording is in progress.</string>

    <string name="str_is_recording_audio">Audio recording in progress.</string>
    <string name="str_is_recording_screen">Screen recording in progress.</string>
    <string name="str_resume_record_screen">Continue recording.</string>
    <string name="str_pause_record_screen">Pause recording</string>
    <string name="str_stop_record_screen">Complete recording</string>

    <string name="str_can_record_background">Background recording</string>
    <string name="toast_recording_fail">Fail to start recording. Please try again.</string>
    <string name="toast_init_camera_fail">Fail to activate camera. Please restart the app.</string>

    <string name="str_function_simultaneousRecording_pictureInPicture">Record video (PIP)</string>
    <string name="str_function_simultaneousRecording">Record audio</string>
    <string name="str_function_recorder">Record audio</string>
    <string name="str_function_screen_recorder">Record screen</string>
    <string name="str_function_video">Record video</string>
    <string name="str_function_photo">Take a photo</string>

    <string name="str_rename">Rename</string>
    <string name="str_rename_inputet">Enter file name</string>
    <string name="str_save_file">Rename File</string>

    <string name="toast_init_camera_fail_100">Fail to initiate camera. Please restart this app.</string>
    <string name="str_alert_dialog_title">Notification</string>
    <string name="str_dialog_sure">OK</string>
    <string name="str_dialog_cancel">Cancel</string>
    <string name="str_flip_lens">Video mirroring</string>
    <string name="toast_record_error">Failed to record video. Please try again.</string>
</resources>