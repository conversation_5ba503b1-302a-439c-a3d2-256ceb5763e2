package com.czur.starry.device.transcription.util

import android.app.ActivityManager
import android.app.ActivityManager.RECENT_IGNORE_UNAVAILABLE
import android.app.ActivityManager.RecentTaskInfo
import android.app.ActivityOptions
import android.app.ActivityTaskManager
import android.app.ActivityTaskManager.getService
import android.content.Context
import android.graphics.Bitmap
import android.graphics.Color
import android.graphics.Point
import android.os.Build
import android.os.RemoteException
import android.util.Log
import android.window.TaskSnapshot
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.starry.device.baselib.base.CZURAtyManager
import com.czur.starry.device.baselib.data.provider.TransHandler
import com.czur.starry.device.baselib.utils.focusStop
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlin.collections.forEach

/**
 *  author : <PERSON><PERSON><PERSON>
 *  time   :2024/05/24
 */


class ActivityManagerWrapper {
    companion object {
        private const val TAG = "ActivityManagerWrapper"
        private val sInstance = ActivityManagerWrapper()

        fun getInstance(): ActivityManagerWrapper {
            return sInstance
        }
    }

    private val mAtm: ActivityTaskManager = ActivityTaskManager.getInstance()
    /**
     * @return the current user's id.
     */
    private fun getCurrentUserId(): Int {
        try {
            val activityManager = CZURAtyManager.appContext.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val currentUserMethod = activityManager.javaClass.getDeclaredMethod("getCurrentUser")
            val userId = currentUserMethod.invoke(activityManager) as Int
            return userId
        } catch (e: RemoteException) {
            throw e.rethrowFromSystemServer()
        }
    }

    /**
     * @return a list of the recents tasks.
     */
    fun getRecentTasks(numTasks: Int): List<RecentTaskInfo> {
        return mAtm.getRecentTasks(numTasks, RECENT_IGNORE_UNAVAILABLE, getCurrentUserId())
    }

    /**
     * @return the top running task (can be {@code null}).
     */
    fun getRunningTask(): ActivityManager.RunningTaskInfo? {
        return getRunningTask(false /* filterVisibleRecents */)
    }

    fun getRunningTaskList(): List<ActivityManager.RunningTaskInfo> {
        return getRunningTaskList(false /* filterVisibleRecents */)
    }

    /**
     * @return the top running task filtering only for tasks that can be visible in the recent tasks
     * list (can be `null`).
     */
    private fun getRunningTask(filterOnlyVisibleRecents: Boolean): ActivityManager.RunningTaskInfo? {
        // Note: The set of running tasks from the system is ordered by recency
        val tasks: List<ActivityManager.RunningTaskInfo> =
            mAtm.getTasks(2, filterOnlyVisibleRecents)
        if (tasks.isEmpty()) {
            return null
        }
        return tasks[1]
    }

    private fun getRunningTaskList(filterOnlyVisibleRecents: Boolean): List<ActivityManager.RunningTaskInfo> {
        // Note: The set of running tasks from the system is ordered by recency
        val tasks: List<ActivityManager.RunningTaskInfo> =
            mAtm.getTasks(10, filterOnlyVisibleRecents)
        if (tasks.isEmpty()) {
            return listOf()
        }
        return tasks
    }

    /**
     * Starts a task from Recents synchronously.
     */
    fun startActivityFromRecent(taskId: Int, options: ActivityOptions?): Boolean {
        try {
            val optsBundle = options?.toBundle()
            getService().startActivityFromRecents(taskId, optsBundle)
            return true
        } catch (e: Exception) {
            return false
        }
    }

    /**
     * Removes a task by id.
     */
//    fun removeTask(task: RecentTask) {
//        try {
//            mAtm.removeTask(task.taskId)
//            focusStopApp(task.pkgName)
//        } catch (e: RemoteException) {
//            logTagE(TAG, "Failed to remove task=${task}", tr = e)
//        }
//    }

    /**
     * Removes all the recent tasks.
     */
//    suspend fun removeAllRecentTasks(tasks: List<RecentTask>) = withContext(Dispatchers.Default) {
//        try {
//            tasks.forEach {
//                if (it.pkgName == "com.czur.starry.device.transcription"){
//                    TransHandler.stopTrans = true
//                }
//                mAtm.removeTask(it.taskId)
//                focusStopApp(it.pkgName)
//            }
//        } catch (e: RemoteException) {
//            logTagE(TAG, "Failed to remove all tasks", tr = e)
//        }
//    }

    private fun focusStopApp(pkgName: String) {
        if (!pkgName.contains(".czur.")) {
            // 不是我们自差的应用, 直接停掉进程
            focusStop(pkgName)
        }
    }

    fun getTaskThumbnail(taskId: Int, isLowResolution: Boolean = true): Bitmap? {
        var snapshot: TaskSnapshot? = null
        try {
            snapshot = if (Build.VERSION.SDK_INT >= 33) {
                getService().getTaskSnapshot(taskId, isLowResolution, true)
            }else {
                getService().getTaskSnapshot(taskId, isLowResolution)
            }
        } catch (e: RemoteException) {
            logTagE(TAG, "Failed to retrieve task snapshot=$e")
        }
        return if (snapshot != null) {
            logTagD(TAG, "====snapshot != null==" + taskId)
            makeThumbnail(snapshot)
        } else {
            null
        }
    }

    private fun makeThumbnail(snapshot: TaskSnapshot): Bitmap {
        var thumbnail: Bitmap? = null
        try {
            snapshot.hardwareBuffer.use { buffer ->
                if (buffer != null) {
                    thumbnail =
                        Bitmap.wrapHardwareBuffer(buffer, snapshot.colorSpace)
                }
            }
        } catch (ex: IllegalArgumentException) {
            // TODO(b/157562905): Workaround for a crash when we get a snapshot without this state
            Log.e(
                "ThumbnailData", "Unexpected snapshot without USAGE_GPU_SAMPLED_IMAGE: "
                        + snapshot.hardwareBuffer, ex
            )
        }
        if (thumbnail == null) {
            val taskSize: Point = snapshot.taskSize
            thumbnail = Bitmap.createBitmap(taskSize.x, taskSize.y, Bitmap.Config.ARGB_8888)
            thumbnail!!.eraseColor(Color.BLACK)
        }
        return thumbnail!!
    }





}