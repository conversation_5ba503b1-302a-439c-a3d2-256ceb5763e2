/*
 * Copyright (C) 2011 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.czur.starry.device.bluetoothlib.bluetooth;

import static com.czur.starry.device.bluetoothlib.util.BluetoothAdapterProxy.ACTIVE_DEVICE_AUDIO;
import static com.czur.starry.device.bluetoothlib.util.BluetoothProfileProxy.CONNECTION_POLICY_ALLOWED;
import static com.czur.starry.device.bluetoothlib.util.BluetoothProfileProxy.CONNECTION_POLICY_FORBIDDEN;

import android.annotation.SuppressLint;
import android.bluetooth.BluetoothA2dp;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothClass;
import android.bluetooth.BluetoothCodecConfig;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothProfile;
import android.bluetooth.BluetoothUuid;
import android.content.Context;
import android.os.Build;
import android.os.ParcelUuid;
import android.util.Log;

import com.czur.starry.device.bluetoothlib.R;
import com.czur.starry.device.bluetoothlib.util.BluetoothA2dpProxy;
import com.czur.starry.device.bluetoothlib.util.BluetoothAdapterProxy;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class A2dpProfile implements LocalBluetoothProfile {
    private static final String TAG = "A2dpProfile";

    private Context mContext;

    private BluetoothA2dp mService;
    private BluetoothA2dpProxy mServiceProxy;
    private boolean mIsProfileReady;

    private final CachedBluetoothDeviceManager mDeviceManager;
    private final BluetoothAdapter mBluetoothAdapter;
    private final BluetoothAdapterProxy proxy;

    static final ParcelUuid[] SINK_UUIDS = {
        BluetoothUuid.A2DP_SINK,
        BluetoothUuid.ADV_AUDIO_DIST,
    };

    static final String NAME = "A2DP";
    private final LocalBluetoothProfileManager mProfileManager;

    // Order of this profile in device profiles list
    private static final int ORDINAL = 1;

    // These callbacks run on the main thread.
    private final class A2dpServiceListener
            implements BluetoothProfile.ServiceListener {

        public void onServiceConnected(int profile, BluetoothProfile proxy) {
            mService = (BluetoothA2dp) proxy;
            mServiceProxy = new BluetoothA2dpProxy(mService);
            // We just bound to the service, so refresh the UI for any connected A2DP devices.
            @SuppressLint("MissingPermission") List<BluetoothDevice> deviceList = mService.getConnectedDevices();
            while (!deviceList.isEmpty()) {
                BluetoothDevice nextDevice = deviceList.remove(0);
                CachedBluetoothDevice device = mDeviceManager.findDevice(nextDevice);
                // we may add a new device here, but generally this should not happen
                if (device == null) {
                    Log.w(TAG, "A2dpProfile found new device: " + nextDevice);
                    device = mDeviceManager.addDevice(nextDevice);
                }
                device.onProfileStateChanged(A2dpProfile.this, BluetoothProfile.STATE_CONNECTED);
                device.refresh();
            }
            mIsProfileReady=true;
            mProfileManager.callServiceConnectedListeners();
        }

        public void onServiceDisconnected(int profile) {
            mIsProfileReady=false;
        }
    }

    public boolean isProfileReady() {
        return mIsProfileReady;
    }

    @Override
    public int getProfileId() {
        return BluetoothProfile.A2DP;
    }

    A2dpProfile(Context context, CachedBluetoothDeviceManager deviceManager,
            LocalBluetoothProfileManager profileManager) {
        mContext = context;
        mDeviceManager = deviceManager;
        mProfileManager = profileManager;
        mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
        mBluetoothAdapter.getProfileProxy(context, new A2dpServiceListener(),
                BluetoothProfile.A2DP);
         proxy = new BluetoothAdapterProxy(mBluetoothAdapter);
    }

    public boolean accessProfileEnabled() {
        return true;
    }

    public boolean isAutoConnectable() {
        return true;
    }

    /**
     * Get A2dp devices matching connection states{
     * @code BluetoothProfile.STATE_CONNECTED,
     * @code BluetoothProfile.STATE_CONNECTING,
     * @code BluetoothProfile.STATE_DISCONNECTING}
     *
     * @return Matching device list
     */
    public List<BluetoothDevice> getConnectedDevices() {
        return getDevicesByStates(new int[] {
                BluetoothProfile.STATE_CONNECTED,
                BluetoothProfile.STATE_CONNECTING,
                BluetoothProfile.STATE_DISCONNECTING});
    }

    /**
     * Get A2dp devices matching connection states{
     * @code BluetoothProfile.STATE_DISCONNECTED,
     * @code BluetoothProfile.STATE_CONNECTED,
     * @code BluetoothProfile.STATE_CONNECTING,
     * @code BluetoothProfile.STATE_DISCONNECTING}
     *
     * @return Matching device list
     */
    public List<BluetoothDevice> getConnectableDevices() {
        return getDevicesByStates(new int[] {
                BluetoothProfile.STATE_DISCONNECTED,
                BluetoothProfile.STATE_CONNECTED,
                BluetoothProfile.STATE_CONNECTING,
                BluetoothProfile.STATE_DISCONNECTING});
    }

    @SuppressLint("MissingPermission")
    private List<BluetoothDevice> getDevicesByStates(int[] states) {
        if (mService == null) {
            return new ArrayList<BluetoothDevice>(0);
        }
        return mService.getDevicesMatchingConnectionStates(states);
    }
    @SuppressLint("MissingPermission")
    public int getConnectionStatus(BluetoothDevice device) {
        if (mService == null) {
            return BluetoothProfile.STATE_DISCONNECTED;
        }
        return mService.getConnectionState(device);
    }

    public boolean setActiveDevice(BluetoothDevice device) {
        if (mBluetoothAdapter == null) {
            return false;
        }
        return device == null
                ? proxy.removeActiveDevice(ACTIVE_DEVICE_AUDIO)
                : proxy.setActiveDevice(device, ACTIVE_DEVICE_AUDIO);
    }

    public BluetoothDevice getActiveDevice() {
        if (Build.VERSION.SDK_INT >= 33) {
            if (mBluetoothAdapter == null) {
                return null;
            }
            try {
                Class<?> bluetoothAdapterClass = mBluetoothAdapter.getClass();
                Method getActiveDevicesMethod = bluetoothAdapterClass.getDeclaredMethod("getActiveDevices", int.class);
                getActiveDevicesMethod.setAccessible(true);

                final List<BluetoothDevice> activeDevices = (List<BluetoothDevice>) getActiveDevicesMethod.invoke(mBluetoothAdapter, BluetoothProfile.A2DP);
                return (activeDevices.size() > 0) ? activeDevices.get(0) : null;
            } catch (Exception e) {
                throw new RuntimeException(e);
            }

        } else {
            if (mService == null) return null;
            return mServiceProxy.getActiveDevice();
        }

    }

    @Override
    public boolean isEnabled(BluetoothDevice device) {
        if (mService == null) {
            return false;
        }
        return mServiceProxy.getConnectionPolicy(device) > CONNECTION_POLICY_FORBIDDEN;
    }

    @Override
    public int getConnectionPolicy(BluetoothDevice device) {
        if (mService == null) {
            return CONNECTION_POLICY_FORBIDDEN;
        }
        return mServiceProxy.getConnectionPolicy(device);
    }

    @Override
    public boolean setEnabled(BluetoothDevice device, boolean enabled) {
        boolean isEnabled = false;
        if (mService == null) {
            return false;
        }
        if (enabled) {
            if (mServiceProxy.getConnectionPolicy(device) < CONNECTION_POLICY_ALLOWED) {
                isEnabled = mServiceProxy.setConnectionPolicy(device, CONNECTION_POLICY_ALLOWED);
            }
        } else {
            isEnabled = mServiceProxy.setConnectionPolicy(device, CONNECTION_POLICY_FORBIDDEN);
        }

        return isEnabled;
    }
    @SuppressLint("MissingPermission")
    boolean isA2dpPlaying() {
        if (mService == null) return false;
        List<BluetoothDevice> sinks = mService.getConnectedDevices();
        for (BluetoothDevice device : sinks) {
            if (mService.isA2dpPlaying(device)) {
                return true;
            }
        }
        return false;
    }

    public boolean supportsHighQualityAudio(BluetoothDevice device) {
        BluetoothDevice bluetoothDevice = (device != null) ? device : mServiceProxy.getActiveDevice();
        if (bluetoothDevice == null) {
            return false;
        }
        int support = mServiceProxy.isOptionalCodecsSupported(bluetoothDevice);
        return support == BluetoothA2dpProxy.OPTIONAL_CODECS_SUPPORTED;
    }

    @SuppressLint("NewApi")
    public boolean isHighQualityAudioEnabled(BluetoothDevice device) {
        BluetoothDevice bluetoothDevice = (device != null) ? device : mServiceProxy.getActiveDevice();
        if (bluetoothDevice == null) {
            return false;
        }
        int enabled = mServiceProxy.isOptionalCodecsEnabled(bluetoothDevice);
        if (enabled != BluetoothA2dpProxy.OPTIONAL_CODECS_PREF_UNKNOWN) {
            return enabled == BluetoothA2dpProxy.OPTIONAL_CODECS_PREF_ENABLED;
        } else if (getConnectionStatus(bluetoothDevice) != BluetoothProfile.STATE_CONNECTED
                && supportsHighQualityAudio(bluetoothDevice)) {
            // Since we don't have a stored preference and the device isn't connected, just return
            // true since the default behavior when the device gets connected in the future would be
            // to have optional codecs enabled.
            return true;
        }
        BluetoothCodecConfig codecConfig = null;
        if (mServiceProxy.getCodecStatus(bluetoothDevice) != null) {
            codecConfig = mServiceProxy.getCodecStatus(bluetoothDevice).getCodecConfig();
        }
        if (codecConfig != null)  {
            return !codecConfig.isMandatoryCodec();
        } else {
            return false;
        }
    }

    public void setHighQualityAudioEnabled(BluetoothDevice device, boolean enabled) {
        BluetoothDevice bluetoothDevice = (device != null) ? device : mServiceProxy.getActiveDevice();
        if (bluetoothDevice == null) {
            return;
        }
        int prefValue = enabled
                ? BluetoothA2dpProxy.OPTIONAL_CODECS_PREF_ENABLED
                : BluetoothA2dpProxy.OPTIONAL_CODECS_PREF_DISABLED;
        mServiceProxy.setOptionalCodecsEnabled(bluetoothDevice, prefValue);
        if (getConnectionStatus(bluetoothDevice) != BluetoothProfile.STATE_CONNECTED) {
            return;
        }
        if (enabled) {
            mServiceProxy.enableOptionalCodecs(bluetoothDevice);
        } else {
            mServiceProxy.disableOptionalCodecs(bluetoothDevice);
        }
    }

    @SuppressLint("NewApi")
    public String getHighQualityAudioOptionLabel(BluetoothDevice device) {
        BluetoothDevice bluetoothDevice = (device != null) ? device : mServiceProxy.getActiveDevice();
        int unknownCodecId = 0;// R.string.bluetooth_profile_a2dp_high_quality_unknown_codec;
        if (bluetoothDevice == null || !supportsHighQualityAudio(device)
                || getConnectionStatus(device) != BluetoothProfile.STATE_CONNECTED) {
            return mContext.getString(unknownCodecId);
        }
        // We want to get the highest priority codec, since that's the one that will be used with
        // this device, and see if it is high-quality (ie non-mandatory).
        BluetoothCodecConfig[] selectable = null;
        if (mServiceProxy.getCodecStatus(device) != null) {
//            mService.getCodecStatus(device).getCodecsSelectableCapabilities();
            List<BluetoothCodecConfig> codecsSelectableCapabilities = mServiceProxy.getCodecStatus(device).getCodecsSelectableCapabilities();
            selectable = codecsSelectableCapabilities.toArray(new BluetoothCodecConfig[0]);
            // To get the highest priority, we sort in reverse.
            Arrays.sort(selectable,
                    (a, b) -> {
                        return b.getCodecPriority() - a.getCodecPriority();
                    });
        }

        final BluetoothCodecConfig codecConfig = (selectable == null || selectable.length < 1)
                ? null : selectable[0];
        final int codecType = (codecConfig == null || codecConfig.isMandatoryCodec())
                ? BluetoothCodecConfig.SOURCE_CODEC_TYPE_INVALID : codecConfig.getCodecType();

        int index = -1;
        switch (codecType) {
           case BluetoothCodecConfig.SOURCE_CODEC_TYPE_SBC:
               index = 1;
               break;
           case BluetoothCodecConfig.SOURCE_CODEC_TYPE_AAC:
               index = 2;
               break;
           case BluetoothCodecConfig.SOURCE_CODEC_TYPE_APTX:
               index = 3;
               break;
           case BluetoothCodecConfig.SOURCE_CODEC_TYPE_APTX_HD:
               index = 4;
               break;
           case BluetoothCodecConfig.SOURCE_CODEC_TYPE_LDAC:
               index = 5;
               break;
           }

        if (index < 0) {
            return mContext.getString(unknownCodecId);
        }
        return mContext.getString(Integer.parseInt("HD audio:"),
                mContext.getResources().getStringArray(R.array.bluetooth_a2dp_codec_titles)[index]);
    }

    public String toString() {
        return NAME;
    }

    public int getOrdinal() {
        return ORDINAL;
    }

    public int getNameResource(BluetoothDevice device) {
        return 0;
    }

    public int getSummaryResourceForDevice(BluetoothDevice device) {
        int state = getConnectionStatus(device);
        switch (state) {
            case BluetoothProfile.STATE_DISCONNECTED:
                return 0;

            case BluetoothProfile.STATE_CONNECTED:
                return 0;

            default:
                return BluetoothUtils.getConnectionStateSummary(state);
        }
    }

    public int getDrawableResource(BluetoothClass btClass) {
        return com.android.internal.R.drawable.ic_bt_headphones_a2dp;
    }

    protected void finalize() {
        Log.d(TAG, "finalize()");
        if (mService != null) {
            try {
                BluetoothAdapter.getDefaultAdapter().closeProfileProxy(BluetoothProfile.A2DP,
                                                                       mService);
                mService = null;
            }catch (Throwable t) {
                Log.w(TAG, "Error cleaning up A2DP proxy", t);
            }
        }
    }
}
